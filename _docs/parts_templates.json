[
  {
    "created_by": 1,
    "name": "invoice",
    "description": "Invoice table",
    "lang": "pl",
    "json_metadata": {
      "columns": [
        {"name": "name",
          "type": "text",
          "headerPl": "Nazwa",
          "headerEn": "Name",
          "width": 55
        },
        {
          "name": "quantity",
          "type": "number",
          "headerPl": "Ilość",
          "headerEn": "Quantity",
          "width": 15
        },
        {
          "name": "price",
          "type": "price",
          "headerPl": "Cena",
          "headerEn": "Price",
          "width": 15
        },
        {
          "name": "value",
          "type": "price",
          "headerPl": "Wartość",
          "headerEn": "Value",
          "width": 15
        }
      ],
      "sample_data": [
        {
          "name": "Service A",
          "quantity": 10,
          "price": 100,
          "value": 1000
        },
        {
          "name": "Service B",
          "quantity": 5,
          "price": 200,
          "value": 1000
        }
      ]
    },
    "org_id": 1,

    "doc_type": "invoice",
    "type": "content_table",
    "is_template": true
  },
  {
    "created_by": 1,
    "name": "prices",
    "description": "Prices table",
    "lang": "pl",
    "json_metadata": {
      "columns": [
        {
          "name": "name",
          "type": "text",
          "headerPl": "Nazwa",
          "headerEn": "Name",
          "width": 85
        },

        {
          "name": "price",
          "type": "price",
          "headerPl": "Cena",
          "headerEn": "Price",
          "width": 15
        }

      ],
      "sample_data": [
        {
          "name": "Product A",
          "price": 100
        },
        {
          "name": "Product B",
          "price": 200
        }
      ]
    },
    "org_id": 1,

    "doc_type": "prices",
    "type": "content_table",
    "is_template": true
  }
]

"html": "string",
"text": "string",
"mjml": {},

// header template
[
{
  "created_by": 1,
  "name": "invoice",
  "description": "Invoice table",
  "lang": "pl",
  "org_id": 1,
  "doc_type": "prices",
  "type": "header",
  "is_template": true,
  "json_metadata": {
    "template": true,
    "rows": [
      {
        "name": "",
        "type": "text",
        label: "",
        value: "",
        font_size: "16px",
        css: ""
      }
    ]

  },
]

// footer template
[
{
  "created_by": 1,
  "name": "invoice",
  "description": "Invoice table",
  "lang": "pl",
  "org_id": 1,
  "doc_type": "prices",
  "type": "header",
  "is_template": true,
  "json_metadata": {
    "template": true,
    "rows": [
      {
        "name": "",
        "type": "text",
        "label": "",
        "value": "",
        "font_size": "16px",
        "css": ""
      }
    ]

  },
]

types:[
  "text",
  "number",
  "nip",
  "regon",
  "bdo",
  "dow_sobisty",
  "date",
  "time",
  "datetime",
  "phone",
  "email",
  "url",
]
