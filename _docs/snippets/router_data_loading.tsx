import { createFileRoute } from '@tanstack/react-router';
import { getJobsAllApiV1CoreJobsGetAllPostOptions, getVariantsAllApiV1CoreVariantsGetAllPostOptions } from '../api'; // Adjust based on your API setup
import { queryClient } from '../queryClient'; // Assuming this is imported from your setup
import { searchSchema } from '../schemas'; // Adjust based on your schema setup
import { cleanEmptyParams } from '../utils'; // Adjust based on your utility functions
import RouteComponent from './RouteComponent'; // Adjust based on your component setup

export const Route = createFileRoute("/main/jobs/")({
    loader: async ({ context: { queryClient, curr_org_id, profile_id } }) => {
        const defaultParams = cleanEmptyParams(defaultVariant.client_ops);

        try {
            // Fetch variants first
            const variantsData = await queryClient.fetchQuery(
                getVariantsAllApiV1CoreVariantsGetAllPostOptions({
                    body: {
                        filters: { profile_id: profile_id, path: "/main/jobs/" },
                    },
                })
            );

            // Find active variant or fallback to default params
            const activeVariant = variantsData.data.find((variant) => variant.is_active);
            const dataQueryParams = activeVariant?.client_ops
                ? cleanEmptyParams(activeVariant.client_ops)
                : defaultParams;

            // Fetch jobs data with the determined params
            const jobsData = await queryClient.fetchQuery(
                getJobsAllApiV1CoreJobsGetAllPostOptions({
                    body: dataQueryParams,
                })
            );

            return {
                variantsData,
                jobsData,
                isLoading: false,
                error: null
            };
        } catch (error) {
            return {
                variantsData: null,
                jobsData: [],
                isLoading: false,
                error: error.message || 'Failed to load data'
            };
        }
    },
    validateSearch: searchSchema,
    errorComponent: ({ error }) => (
        <div role="alert" className="alert alert-error">
            {error}
        </div>
    ),
    pendingComponent: () => (
        <div className="flex items-center justify-center p-4">
            <div className="loading loading-spinner loading-lg"></div>
        </div>
    ),
    component: ({ useLoader }) => {
        const { variantsData, jobsData, isLoading, error } = useLoader();

        if (isLoading) {
            return <Route.pendingComponent />;
        }

        if (error) {
            return <Route.errorComponent error={error} />;
        }

        return <RouteComponent variantsData={variantsData} jobsData={jobsData} />;
    },
});