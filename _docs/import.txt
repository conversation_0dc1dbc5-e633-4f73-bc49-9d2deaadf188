
 table org_transactions
(
    id             serial
        primary key,
    created_at     timestamp with time zone default now()        not null,
    updated_at     timestamp with time zone default now()        not null,
    created_by     integer                                       not null
        references auth.users
            on update cascade on delete restrict,
    updated_by     integer
        references auth.users
            on update cascade on delete restrict,
    lang           varchar(255)             default 'pl'::text   not null,
    json_metadata  jsonb,
    name           varchar(255)                                  not null,
    description    text,
    date           date                                          not null,
    type           varchar(255),
    org_id         integer                                       not null
        references core.orgs
            on update cascade on delete cascade,
    due_date       date,
    amount         integer                                       not null,
    job_id         integer
        references core.jobs
            on update cascade on delete restrict,
    comment        text,
    object_id      integer
        references core.objects
            on update cascade on delete restrict,
    is_saved       boolean                                       not null,
    is_schedule    boolean                                       not null,
    posted_at      timestamp                default now()        not null,
    cust_ref       varchar(255),
    our_ref        varchar(255),
    memo           varchar(255),
    contrahent_id  integer,
    set_id         integer
                                                                 references tpl_sets
                                                                     on update cascade on delete set null,
    set_item_id    integer,
    template_used  timestamp,
    sch_start_date date,
    sch_end_date   date,
    is_regular     boolean                  default false        not null,
    sch_dates      date[]                   default '{}'::date[] not null,
    sch_interval   varchar(255),
    trip_id        integer
        references trips
            on update cascade on delete cascade
);

table org_splits
(
    id             serial
        primary key,
    created_at     timestamp with time zone default now()      not null,
    updated_at     timestamp with time zone default now()      not null,
    created_by     integer                                     not null
        references auth.users
            on update cascade on delete restrict,
    updated_by     integer
        references auth.users
            on update cascade on delete restrict,
    lang           varchar(255)             default 'pl'::text not null,
    json_metadata  jsonb,
    name           varchar(255)                                not null,
    description    text,
    account_id     integer                                     not null
        references org_accounts
            on update cascade on delete cascade,
    transaction_id integer                                     not null
        references org_transactions
            on update cascade on delete cascade,
    debit          integer,
    credit         integer,
    due_date       date,
    memo           text,
    date           date,
    is_debit_minus boolean                                     not null,
    is_schedule    boolean                  default false      not null,
    set_id         integer
                                                               references tpl_sets
                                                                   on update cascade on delete set null,
    set_item_id    integer,
    org_id         integer                                     not null
        references core.orgs
            on update cascade on delete cascade,
    is_saved       boolean                                     not null
);

table org_accounts
(
    id             serial
        primary key,
    created_at     timestamp with time zone default now()        not null,
    updated_at     timestamp with time zone default now()        not null,
    created_by     integer                                       not null
        references auth.users
            on update cascade on delete restrict,
    updated_by     integer
        references auth.users
            on update cascade on delete restrict,
    lang           varchar(255)             default 'pl'::text   not null,
    json_metadata  jsonb,
    name           varchar(255)                                  not null,
    description    text,
    org_id         integer                                       not null
        constraint orgs_id_fkey
            references core.orgs
            on update cascade on delete cascade,
    name_short     varchar(255),
    label          varchar(255),
    label_short    varchar(255),
    group_id       integer
        references acc_groups
            on update cascade on delete restrict,
    type           varchar(255)                                  not null,
    is_debit_minus boolean                                       not null,
    acc_number     varchar(255)             default '0000'::text not null,
    currency       varchar(255)             default 'pln'::text  not null,
    parent_id      integer
        references org_accounts
            on update cascade on delete cascade,
    is_branch      boolean                  default false        not null,
    is_placeholder boolean                  default false        not null,
    is_active      boolean                  default true         not null,
    balance        integer                  default 0            not null,
    is_syntetyczne boolean                  default true         not null,
    enabled        boolean                  default true         not null,
    set_id         integer
        constraint tpl_sets_id_fkey
            references tpl_sets
            on update cascade on delete set null,
    set_item_id    integer,
    level          integer                  default 0,
    keywords       character varying[]      default '{}'::text[] not null
);

we need to generate array of transaction from imported csv file

the array is of structure: WE MUST INCLUDE ALL FIELDS FROM THIS TEMPLATE
   [
  {
    "created_by": user.id,
    "lang": "pl",
    "json_metadata": null,
    "name": < description column >,
    "description": null,
    "date": < date column >,
    "type": < type column >,
    "org_id": curr_org_id,
    "due_date": null,
    "amount": 0,
    "job_id": null,
    "comment": null,
    "object_id": null,
    "is_saved": false,
    "is_schedule": false,
    "is_regular": false,
    "sch_dates": null,
    "sch_interval": null,
    "sch_start_date": null,
    "sch_end_date": null,
    "cust_ref": null,
    "our_ref": null,
    "memo": null,
    "contrahent_id": <contrahent_id column>,
    "set_id": null,
    "set_item_id": null,
    "template_used": null,
    "org_splits": [
      {
        "created_by": user.id,
        "lang": "pl",
        "json_metadata": null,
        "name": < description column >,
        "description": null,
        "account_id": importAccount.id,
        "transaction_id": ,
        "debit": ,
        "credit": ,
        "due_date": null,
        "memo": null,
        "date": < date column >,
        "is_debit_minus": false,
        "is_schedule": false,
        "is_saved": false,
        "org_id": curr_org_id,
        "set_id": null,
        "set_item_id": null
      },
      {
        "created_by": user.id,
        "lang": "pl",
        "json_metadata": null,
        "name": < description column >,
        "description": null,
        "account_id": < id of matched account from account.keyword in csv column "payee" or null,
        "transaction_id": ,
        "debit": ,
        "credit": ,
        "due_date": null,
        "memo": null,
        "date": < date column >,
        "is_debit_minus": false,
        "is_schedule": false,
        "is_saved": false,
        "org_id": curr_org_id,
        "set_id": null,
        "set_item_id": null
      }
    ]
  }
]

the csv file columns are:
date, payee, description, ammount (positive or negative)

from each csv row we generate one transaction and two splits
first split is for the importAccount
second split is for the splitAccount

each split contains debit and credit fields,we need to decide which one to use
we check the type of importAccount, it may be "ASSET", "LIABILITY", "EQUITY" or "INCOME" or "EXPENSE"
for "ASSET", "LIABILITY", "EQUITY" 
when value in column "ammount" is > 0 we set debit=0, credit=amount (absolute value)
when value in column "ammount" is < 0 we set debit=amount (absolute value), credit=0

for "INCOME", "EXPENSE" we set debit=amount, credit=0
when value in column "ammount" is > 0 we set debit=amount (absolute value), credit=0
when value in column "ammount" is < 0 we set debit=0, credit=amount (absolute value)

we need to get the splitAccount ID
we have array of accounts available, each account has "keywords" updateSystemField
for each csv row we check if column "payee" contains any of the "keywords", for first match found we set the id of account as splitAccount ID
if there is no match we set splitAccount ID as null (user will set it manually)

we need to set the date:
date from column "date" we place in:
- transaction date field

the value from column "description" we place as name field of the transaction and name of each split

the created array we set as importData state variable(setImportData function)

finish the generateImportData function body
