import { render } from "@react-email/render";
import NowyWniosekEmail from "@/templates/emails/NowyWniosekEmail.tsx";
import NowaAnkietaEmail from "@/templates/emails/NowaAnkietaEmail.tsx";
import KoniecAnkietyEmail from "@/templates/emails/KoniecAnkietyEmail.tsx";
import KoniecGlosowaniaEmail from "@/templates/emails/KoniecGlosowaniaEmail.tsx";
import NowaOfertaEmail from "@/templates/emails/NowaOfertaEmail.tsx";
import NowyKomentarzEmail from "@/templates/emails/NowyKomentarzEmail.tsx";
import NoweForumEmail from "@/templates/emails/NoweForumEmail.tsx";
import NoweZlecenieEmail from "@/templates/emails/NoweZlecenieEmail.tsx";
import NowaOperacjaEmail from "@/templates/emails/NowaOperacjaEmail.tsx";
import React from "react";

export default function getEmailContent(type_id, emailProps) {
  switch (type_id) {
    // Nowy wniosek formalny
    case 1: {
      const html = render(<NowyWniosekEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NowyWniosekEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN - Nowy wniosek formalny";
      return { text, html, subject };
    }

    // Wniosek formalny - koniec glosowania
    case 2: {
      const html = render(<KoniecGlosowaniaEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<KoniecGlosowaniaEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN -Koniec glosowania - wniosek formalny";
      return { text, html, subject };
    }
    // Nowa oferta
    case 8: {
      const subject = "ADMIN - Nowa oferta";
      const html = render(<NowaOfertaEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NowaOfertaEmail {...emailProps} />, {
        plainText: true,
      });
      return { text, html, subject };
    }
    // Nowa znaczaca operacja
    case 9: {
      const html = render(<NowaOperacjaEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NowaOperacjaEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN - Nowa operacja";
      return { text, html, subject };
    }
    // Nowe zlecenie
    case 7: {
      const html = render(<NoweZlecenieEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NoweZlecenieEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN - Nowe zlecenie";
      return { text, html, subject };
    }
    // Nowy komentarz na forum
    case 4: {
      const html = render(<NowyKomentarzEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NowyKomentarzEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN - Nowy komentarz na forum";
      return { text, html, subject };
    }
    // Nowy temat na forum
    case 3: {
      const html = render(<NoweForumEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NoweForumEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN - Nowy temat na forum";
      return { text, html, subject };
    }
    // Nowy wniosek nieformalny
    case 5: {
      const html = render(<NowyWniosekEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NowyWniosekEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN - Nowy wniosek nieformalny";
      return { text, html, subject };
    }

    // Nowy wniosek nieformalny  - koniec glosowania
    case 6: {
      const html = render(<KoniecGlosowaniaEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<KoniecGlosowaniaEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN Koniec głosowania - Wniosek nieformalny";
      return { text, html, subject };
    }

    // Nowa ankieta
    case 10: {
      const html = render(<NowaAnkietaEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<NowaAnkietaEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN Nowa ankieta";
      return { text, html, subject };
    }

    // Koniec ankiety
    case 11: {
      const html = render(<KoniecAnkietyEmail {...emailProps} />, {
        pretty: true,
      });

      const text = render(<KoniecAnkietyEmail {...emailProps} />, {
        plainText: true,
      });
      const subject = "ADMIN Koniec ankiety";
      return { text, html, subject };
    }

    default:
      return { html: null, text: null, subject: null };
  }
}
