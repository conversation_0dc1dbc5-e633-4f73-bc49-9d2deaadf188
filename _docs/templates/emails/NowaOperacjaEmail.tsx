import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

interface MyExtendedCSSProperties extends React.CSSProperties {
  whiteSpace?: "normal" | "nowrap" | "pre" | "pre-wrap" | "pre-line";
}

const baseUrl = "https://administrator.biostrefa.org";

export const NowaOperacjaEmail = ({ date, description, comment, kwota }) => {
  return (
    <Html>
      <Head />
      <Preview>Nowa operacja</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${baseUrl}/logo.png`}
            width="40"
            height="33"
            alt="Administrator"
          />
          <Section>
            <Text style={subjectStyle}>ADMIN: NOWE OPERACJA:</Text>
            <Text style={userStyle}>{date}</Text>

            <Text style={textStyle}>{description}</Text>
            <Text style={textStyle}>Kwota operacji: {kwota}</Text>
            <Text style={textStyle}>{comment}</Text>

            <Text style={smallTextStyle}>
              Je<PERSON><PERSON> nie chcesz otrzymywać powiadomień o nowych zleceniach możesz
              wyłączyć je w ustawieniach użytkownika:{" "}
              <Link style={anchor} href={`${baseUrl}`}>
                Konfiguracja konta
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default NowaOperacjaEmail;

const main = {
  backgroundColor: "#f6f9fc",
  padding: "10px 0",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #f0f0f0",
  padding: "45px",
};

const subjectStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#757575",
  lineHeight: "26px",
};
const titleStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
};
const textStyle: MyExtendedCSSProperties = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
};
const userStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
};
const czynnoscStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
};
const smallTextStyle = {
  fontSize: "12px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
};

const button = {
  backgroundColor: "#007ee6",
  borderRadius: "4px",
  color: "#fff",
  fontFamily: "'Open Sans', 'Helvetica Neue', Arial",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "210px",
  padding: "14px 7px",
  marginTop: "20px",
};

const anchor = {
  textDecoration: "underline",
};
