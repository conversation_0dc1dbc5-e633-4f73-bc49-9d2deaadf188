import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  Html,
  Img,
  <PERSON>,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

interface MyExtendedCSSProperties extends React.CSSProperties {
  whiteSpace?: "normal" | "nowrap" | "pre" | "pre-wrap" | "pre-line";
}

const baseUrl = "https://administrator.biostrefa.org";

export const NowyWniosekEmail = ({
  date = "2023-10-23",
  user = "Kowalski Jan",
  title = "Trawnik przy drodze",
  description = "Wynajac grodnika do dbania o trawnik",
  motivation = "Zeby nasza posesja ladniej wygladala...",
  wniosekId = 0,
}) => {
  const wniosekUrl = `${baseUrl}/wnioski/${wniosekId}`;
  return (
    <Html>
      <Head />
      <Preview>Nowy wniosek</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${baseUrl}/logo.png`}
            width="40"
            height="33"
            alt="Administrator"
          />
          <Section>
            <Text style={subjectStyle}>ADMINISTRATOR: NOWY WNIOSEK:</Text>
            <Text style={userStyle}>
              {date} - {user}
            </Text>
            <Text style={titleStyle}>{title}</Text>
            <Text style={textStyle}>{description}</Text>
            <Text style={motivationStyle}>{motivation}</Text>
            <Button style={button} href={wniosekUrl} pY={10}>
              Głosuj
            </Button>
            <Text style={smallTextStyle}>
              Jeśli nie chcesz otrzymywać powiadomień o nowych wnioskach możesz
              wyłączyć je w ustawieniach użytkownika:{" "}
              <Link style={anchor} href={`${baseUrl}`}>
                Konfiguracja konta
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default NowyWniosekEmail;

const main = {
  backgroundColor: "#f6f9fc",
  padding: "10px 0",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #f0f0f0",
  padding: "45px",
};

const subjectStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#757575",
  lineHeight: "26px",
};
const titleStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
};
const textStyle: MyExtendedCSSProperties = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  whiteSpace: "pre-line",
};
const userStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
};
const motivationStyle: MyExtendedCSSProperties = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  marginTop: 0,
  whiteSpace: "pre-line",
};
const smallTextStyle = {
  fontSize: "12px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
};

const button = {
  backgroundColor: "#007ee6",
  borderRadius: "4px",
  color: "#fff",
  fontFamily: "'Open Sans', 'Helvetica Neue', Arial",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "210px",
  padding: "14px 7px",
};

const anchor = {
  textDecoration: "underline",
};
