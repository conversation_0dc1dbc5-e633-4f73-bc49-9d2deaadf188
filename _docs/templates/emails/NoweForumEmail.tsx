import { <PERSON>, <PERSON><PERSON>, Con<PERSON>er, Head, Html, Img, Link, Preview, Section, Text } from "@react-email/components";
import * as React from "react";

// const baseUrl = process.env.PUBLIC_BASE_URL
//   ? `http://${process.env.PUBLIC_BASE_URL}`
//   : "";

const baseUrl = "https://administrator.biostrefa.org";
interface MyExtendedCSSProperties extends React.CSSProperties {
  whiteSpace?: "normal" | "nowrap" | "pre" | "pre-wrap" | "pre-line";
}
export const NoweForumEmail = ({
  subject = "Uzupelnij to !!!!",
  description = "Tu tez czegos brakuje....",
  forumId = 0,
}) => {
  const forumUrl = `${baseUrl}/forum/${forumId}`;
  return (
    <Html>
      <Head />
      <Preview>Nowe forum</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img src={`${baseUrl}/logo.png`} width="40" height="33" alt="Administrator" />
          <Section>
            <Text style={subjectStyle}>ADMINISTRATOR: NOWA DYSKUSJA NA FORUM:</Text>
            <Text style={titleStyle}>{subject}</Text>
            <Text style={textStyle}>{description}</Text>
            <Button style={button} href={forumUrl} pY={10}>
              Dodaj komentarz
            </Button>
            <Text style={smallTextStyle}>
              Jeśli nie chcesz otrzymywać powiadomień o nowych dyskusjach możesz wyłączyć je w ustawieniach użytkownika:{" "}
              <Link style={anchor} href={`${baseUrl}`}>
                Konfiguracja konta
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default NoweForumEmail;

const main = {
  backgroundColor: "#f6f9fc",
  padding: "10px 0",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #f0f0f0",
  padding: "45px",
};

const subjectStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#757575",
  lineHeight: "26px",
};
const titleStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
};
const textStyle: MyExtendedCSSProperties = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  whiteSpace: "pre-line",
};
const smallTextStyle = {
  fontSize: "12px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
};

const button = {
  backgroundColor: "#007ee6",
  borderRadius: "4px",
  color: "#fff",
  fontFamily: "'Open Sans', 'Helvetica Neue', Arial",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "210px",
  padding: "14px 7px",
};

const anchor = {
  textDecoration: "underline",
};
