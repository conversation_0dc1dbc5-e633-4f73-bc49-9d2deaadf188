import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
  Hr,
} from "@react-email/components";
import * as React from "react";

interface MyExtendedCSSProperties extends React.CSSProperties {
  whiteSpace?: "normal" | "nowrap" | "pre" | "pre-wrap" | "pre-line";
}

const baseUrl = "https://admin.biostrefa.org";

export const NowaOfertaEmail = ({
  title,
  description,
  czynnosci,
  zlecenieId,
  ofertaDate,
  ofertaName,
  price,
  phone,
  email,
  comment,
}) => {
  // console.log("cyzynnosci", czynnosci);
  const zlecenieUrl = `${baseUrl}/zlecenia/${zlecenieId}`;
  return (
    <Html>
      <Head />
      <Preview>Nowy wniosek</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${baseUrl}/logo.png`}
            width="40"
            height="33"
            alt="Administrator"
          />
          <Section>
            <Text style={subjectStyle}>ADMINISTRATOR: NOWA OFERTA:</Text>

            <Text style={titleStyle}>Zlecenie: {title}</Text>
            <Text style={textStyle}>{description}</Text>
            {czynnosci?.map((cz) => (
              <Text key={cz.id} style={czynnoscStyle}>
                {cz.description}
              </Text>
            ))}
            <Hr />
            <Text style={titleStyle}>{ofertaDate} Oferta:</Text>
            <Text style={czynnoscStyle}>Wykonawca: {ofertaName}</Text>
            <Text style={czynnoscStyle}>Cena: {price}</Text>
            <Text style={czynnoscStyle}>Telefon: {phone}</Text>
            <Text style={czynnoscStyle}>Email: {email}</Text>
            <Text style={czynnoscStyle}>Uwagi: {comment}</Text>

            {/*<Button style={button} href={zlecenieUrl} pY={10}>*/}
            {/*  Zobacz zlecenie*/}
            {/*</Button>*/}
            <Text style={smallTextStyle}>
              Link do zlecenia:{" "}
              <Link style={anchor} href={zlecenieUrl}>
                {title}
              </Link>
            </Text>
            <Text style={smallTextStyle}>
              Jeśli nie chcesz otrzymywać powiadomień o nowych ofertach możesz
              wyłączyć je w ustawieniach użytkownika:{" "}
              <Link
                style={anchor}
                href={`${baseUrl}/userprofile/mailing/opcjonalne`}
              >
                Konfiguracja konta
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default NowaOfertaEmail;

const main = {
  backgroundColor: "#f6f9fc",
  padding: "10px 0",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #f0f0f0",
  padding: "45px",
};

const subjectStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#757575",
  lineHeight: "26px",
};
const titleStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
};
const textStyle: MyExtendedCSSProperties = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  whiteSpace: "pre-line",
};
const userStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
};
const czynnoscStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
};
const smallTextStyle = {
  fontSize: "12px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
};

const button = {
  backgroundColor: "#007ee6",
  borderRadius: "4px",
  color: "#fff",
  fontFamily: "'Open Sans', 'Helvetica Neue', Arial",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "210px",
  padding: "14px 7px",
  marginTop: "20px",
};

const anchor = {
  textDecoration: "underline",
};
