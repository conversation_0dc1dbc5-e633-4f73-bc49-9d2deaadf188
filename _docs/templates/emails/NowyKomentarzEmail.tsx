import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  Html,
  Img,
  <PERSON>,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

interface MyExtendedCSSProperties extends React.CSSProperties {
  whiteSpace?: "normal" | "nowrap" | "pre" | "pre-wrap" | "pre-line";
}

const baseUrl = "https://administrator.biostrefa.org";

export const NowyKomentarzEmail = ({
  subject = "Nowy komentarz",
  description = "To jest opis dyskusji",
  comment = "nowy komentarz",

  user = "<PERSON><PERSON>ski Jan",
  forumId = 0,
}) => {
  const forumUrl = `${baseUrl}/forum/${forumId}`;
  // @ts-ignore
  return (
    <Html>
      <Head />
      <Preview>Nowy komentarz</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={`${baseUrl}/logo.png`}
            width="40"
            height="33"
            alt="Administrator"
          />
          <Section>
            <Text style={subjectStyle}>ADMINISTRATOR: NOWY KOMENTARZ:</Text>
          </Section>
          <Section style={section}>
            <Text style={titleStyle}>{subject}</Text>
            <Text style={textStyle}>{description}</Text>
          </Section>
          <Section>
            <Text style={userStyle}>{user}</Text>
            <Text style={commentStyle}>{comment}</Text>
            <Button style={button} href={forumUrl} pY={10}>
              Dodaj komentarz
            </Button>
            <Text style={smallTextStyle}>
              Jeśli nie chcesz otrzymywać powiadomień o nowych komentarzach
              możesz wyłączyć je w ustawieniach użytkownika:{" "}
              <Link style={anchor} href={`${baseUrl}`}>
                Konfiguracja konta
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default NowyKomentarzEmail;

const main = {
  backgroundColor: "#f6f9fc",
  padding: "10px 0",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #f0f0f0",
  padding: "45px",
};

const section = {
  backgroundColor: "#eeeeee",
  padding: "0 10px",
  margin: 0,
};

const subjectStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
};
const titleStyle = {
  fontSize: "14px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
};
const textStyle: MyExtendedCSSProperties = {
  fontSize: "14px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  margin: 0,
  whiteSpace: "pre-line",
};
const userStyle = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "700",
  color: "#404040",
  lineHeight: "26px",
  margin: "20px 0 0 0",
};
const commentStyle: MyExtendedCSSProperties = {
  fontSize: "16px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
  marginTop: 0,
  whiteSpace: "pre-line",
};
const smallTextStyle = {
  fontSize: "12px",
  fontFamily:
    "'Open Sans', 'HelveticaNeue-Light', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif",
  fontWeight: "300",
  color: "#404040",
  lineHeight: "26px",
};

const button = {
  backgroundColor: "#007ee6",
  borderRadius: "4px",
  color: "#fff",
  fontFamily: "'Open Sans', 'Helvetica Neue', Arial",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "210px",
  padding: "14px 7px",
};

const anchor = {
  textDecoration: "underline",
};
