import React, { useState, useEffect } from "react";
import styles from "./WynikiAnkiety.module.css";
import useData from "./useData.ts";

import { useUserData } from "@nhost/react";
import { useNavigate } from "react-router-dom";

import dayjs from "dayjs";

function WynikiAnkiety() {
  const navigate = useNavigate();
  const userData = useUserData();

  const [rows, setRows] = React.useState([]);

  return (
    <>
      <div>WynikiAnkiety</div>
    </>
  );
}

export default WynikiAnkiety;
