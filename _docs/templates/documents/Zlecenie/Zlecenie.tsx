import { Button } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useUserData } from "@nhost/react";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import ObjectZlecenie from "@/components/ObjectZlecenie/ObjectZlecenie.tsx";
import ZlecenieFiles from "@/components/ZlecenieFiles/ZlecenieFiles.tsx";
import OfertaForm from "@/forms/OfertaForm/OfertaForm.tsx";
import { RootStateTypes } from "@/utils/redux/store.ts";
import useData from "./useData.ts";
import styles from "./Zlecenie.module.css";

function Zlecenie({ context = "html" }) {
  const [wspId, setWspId] = useState(0);
  const userProfile = useSelector((state: RootStateTypes) => state.userProfile);
  const { zlecenieId, wspolnotaId } = useParams();
  const numericZlecenieId = +zlecenieId;
  const numericWspolnotaId = +wspolnotaId;

  const navigate = useNavigate();
  const userData = useUserData();

  const { getZleceniaId, getWspolnotaInfo } = useData(numericZlecenieId, wspId);
  const { data: dataZlecenie, isLoading: isLoadingZlecenie, error: errorZlecenie } = getZleceniaId;

  const { data: dataWspolnota, isLoading: isLoadingWspolnota, error: errorWspolnota } = getWspolnotaInfo;

  useEffect(() => {
    if (wspolnotaId) {
      setWspId(numericWspolnotaId);
    } else {
      setWspId(userProfile.wspolnota_id);
    }
  }, [wspolnotaId, userProfile]);

  if (isLoadingZlecenie) return <div>Loading zlecenie...</div>;
  if (isLoadingWspolnota) return <div>Loading. wspolnota..</div>;
  if (errorZlecenie || errorWspolnota) return <div>Error </div>;

  async function copyUrl() {
    try {
      await navigator.clipboard.writeText(window.location.href);
      // console.log("Link copied to clipboard");
      notifications.show({
        id: "hello-there",
        withCloseButton: true,
        autoClose: 3000,
        message: "Link został skopiowany",
        color: "green",
      });
    } catch (err) {
      console.error("Error in copying link: ", err);
      notifications.show({
        id: "hello-there",
        withCloseButton: true,
        autoClose: 3000,
        message: `Nie udało sie... ${err}`,
        color: "red",
      });
    }
  }

  const sendEmail = () => {
    const subject = encodeURIComponent("Re: Oferta na wykonanie zlecenia");
    const currentUrl = window.location.href;
    const body = encodeURIComponent(
      `Prosze o złożenie oferty na wykonanie zlecenia.\n\nLink do zlecenia: \n\n${currentUrl}`,
    );
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  };

  return (
    <div className={styles.wrapper}>
      <header className={styles.header}>
        <div className={styles.logo}>
          <img src="https://administrator.biostrefa.org/logo.png" alt="logo" width="100px" />
        </div>
        <div className={styles.wspolnota_info}>
          <div className={styles.wspolnota_name}>{dataWspolnota?.name}</div>
          <div className={styles.wspolnota_address}>
            {dataWspolnota?.address.postal_code} {dataWspolnota?.address.city}
          </div>
          <div className={styles.wspolnota_address}>
            {dataWspolnota?.address.street} {dataWspolnota?.address.street_no}
            {dataWspolnota?.address.local_no ? `/${dataWspolnota?.address.local_no}` : null}
          </div>
          <div className={styles.wspolnota_address}>
            NIP: {dataWspolnota?.nip} REGON: {dataWspolnota?.regon}
          </div>
          <div className={styles.wspolnota_kontakt}>
            {/*{userData?.wspolnota?.name}*/}

            <div className={styles.wspolnota_email}>Email:{dataWspolnota?.contacts[0]?.email}</div>
            <div className={styles.wspolnota_phone}>
              Telefon:
              {dataWspolnota?.contacts[0]?.phone}
            </div>
          </div>
        </div>
      </header>

      <div className={styles.header2}>Zlecenie</div>
      <section>
        <div className={styles.daty}>
          <div> Proponowana data startu: {dayjs(dataZlecenie?.start_date).format("DD-MM-YYYY")}</div>
          {dataZlecenie?.finish_date && (
            <div> Proponowana data zakończenia: {dayjs(dataZlecenie?.finish_date).format("DD-MM-YYYY")}</div>
          )}
        </div>
      </section>
      <section className={styles.zlecenie_info}>
        <div className={styles.title}>{dataZlecenie?.title}</div>
        {/*<div className={styles.description}>{dataZlecenie?.description}</div>*/}
      </section>
      <section className={styles.zlecenie_obiekty}>
        {dataZlecenie?.zlecenia_obiekties.length > 0 && <h4>PRZEDMIOT(Y) ZLECENIA:</h4>}
        {dataZlecenie?.zlecenia_obiekties.map((obiekt) => (
          <ObjectZlecenie key={obiekt.id} object={obiekt} handleEditObject={() => {}} variant="html" />
        ))}
      </section>
      <section className={styles.zlecenie_czynnosci}>
        <h4>ZAKRES ZLECENIA:</h4>
        {dataZlecenie?.zlecenia_czynnoscis.map((czynosc) => (
          <div key={czynosc.id}>
            <div className={styles.czynnosc_name}>{czynosc.name}</div>
            <div className={styles.czynnosc_description}>{czynosc.description}</div>
          </div>
        ))}
      </section>

      <section className={styles.zlecenie_photos}>
        {dataZlecenie?.zlecenia_files.length > 0 && (
          <>
            <h4>ZDJĘCIA DO ZLECENIA:</h4>

            <ZlecenieFiles
              title="Dodatkowe zdjęcia"
              files={dataZlecenie?.zlecenia_files.map((file) => ({
                id: file.id,
                url: file.file.url,
                th_url: file.file.th_url,
                name: file.file.name,
              }))}
              variant="html"
            />
          </>
        )}
      </section>
      {context === "html" && (
        <>
          <div className={styles.buttons}>
            <Button onClick={copyUrl}>Kopiuj link zlecenia</Button>
            <Button onClick={sendEmail}>Prześlij zlecenia mailem</Button>
          </div>

          <OfertaForm zlecenieId={numericZlecenieId} wspolnotaId={numericWspolnotaId} zlecenie={dataZlecenie} />
        </>
      )}
    </div>
  );
}

export default Zlecenie;
