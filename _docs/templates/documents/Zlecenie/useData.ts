//Zlecenie.tsx

import { notifications } from "@mantine/notifications";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import {
  GetForumItemQuery,
  GetWspolnotaInfoQuery,
  GetZleceniaIdQuery,
  InsertOfertyMutation,
  InsertWnioskiMutationVariables,
  UpdateForumStatusMutationVariables,
} from "@/gql/graphql.ts";
import useTQuery from "@/hooks/useTQuery";
import { INSERT_OFERTY } from "@/utils/data/mutations.ts";
import { GET_WSPOLNOTA_INFO, GET_ZLECENIA_ID } from "@/utils/data/queries.ts";
import { RootStateTypes } from "@/utils/redux/store";

type GetZleceniaId = GetZleceniaIdQuery["admin_zlecenia"][0];
interface GetZleceniaIdData {
  admin_zlecenia: GetZleceniaId;
}

type GetWspolnotaInfo = GetWspolnotaInfoQuery["admin_wspolnoty"][0];
interface GetWspolnotaInfoData {
  admin_wspolnoty: GetWspolnotaInfo;
}

function useData(zlecenieId, wspolnotaId) {
  const { tanstackClient, qFn, mFn } = useTQuery();

  // GET_ZLECENIA_ID
  const getZleceniaId = useQuery({
    queryKey: ["forum-item", zlecenieId, wspolnotaId],
    queryFn: async (): Promise<GetZleceniaId> => {
      const data = (await qFn(GET_ZLECENIA_ID, {
        id: zlecenieId,
      })) as GetZleceniaIdData;
      return data.admin_zlecenia[0];
    },
    enabled: !!zlecenieId,
  });

  // GET_WSPOLNOTA_INFO
  const getWspolnotaInfo = useQuery({
    queryKey: ["wspolnota-info", wspolnotaId],
    queryFn: async (): Promise<GetWspolnotaInfo> => {
      const data = (await qFn(GET_WSPOLNOTA_INFO, {
        id: wspolnotaId,
      })) as GetWspolnotaInfoData;
      return data.admin_wspolnoty[0];
    },
    enabled: !!wspolnotaId,
  });

  // // INSERT_OFERTY
  // const insertOferty: any = useMutation<InsertOfertyMutation, Error, InsertWnioskiMutationVariables>({
  //   mutationFn: (variables) =>
  //     mFn(INSERT_OFERTY, variables),
  //   // onSuccess: () => {
  //   //   tanstackClient.invalidateQueries();
  //   //   // .then(() => console.log("ok, vars: "));
  //   //   // reset();
  //   // },
  //   onError: (error) => {
  //     console.log("mutation error", error);
  //     notifications.show({
  //       id: "hello-there",
  //       withCloseButton: true,
  //       // onClose: () => console.log("unmounted"),
  //       // onOpen: () => console.log("mounted"),
  //       autoClose: false,
  //       title: "Błąd zapisu",
  //       message: ` Przekaż administratorowi: ${error}`,
  //       color: "red",
  //       //   icon: <IconX />,
  //       //   className: 'my-notification-class',
  //       //   style: { backgroundColor: 'red' },
  //       //   loading: false,
  //     });
  //   },
  // });

  return { getZleceniaId, getWspolnotaInfo };
}

export default useData;
