//Uchwala.tsx

import { notifications } from "@mantine/notifications";
import { useUserData } from "@nhost/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { Admin_Wspolnoty, GetDocumentByIdQuery, GetDocumentByKeyQuery } from "@/gql/graphql.ts";
import useTQuery from "@/hooks/useTQuery";
import { INSERT_FORUM } from "@/utils/data/mutations.ts";
import { GET_DOCUMENT_BY_ID, GET_DOCUMENT_BY_KEY } from "@/utils/data/queries";
import { RootStateTypes } from "@/utils/redux/store";

function useData({ id, key }) {
  console.log("useData", id, key);
  const user = useUserData();
  // console.log("user", user);
  const { tanstackClient, qFn, mFn } = useTQuery();
  const wspolnotaId = useSelector((state: RootStateTypes) => state.userProfile.wspolnota_id);

  type GetDocumentById = GetDocumentByIdQuery["admin_documents"][0];
  interface GetDocumentByIdData {
    admin_documents: GetDocumentById;
  }
  type GetDocumentByKey = GetDocumentByKeyQuery["admin_files"][0];
  interface GetDocumentByKeyData {
    admin_files: GetDocumentByKey;
  }
  // GET_DOCUMENT_BY_ID
  const getDocumentById = useQuery({
    queryKey: ["document", id],
    queryFn: async (): Promise<GetDocumentById> => {
      const data = (await qFn(GET_DOCUMENT_BY_ID, {
        id: id,
      })) as GetDocumentByIdData;
      return data?.admin_documents[0];
    },
    enabled: !!id,
  });

  // GET_DOCUMENT_BY_KEY
  const getDocumentByKey = useQuery({
    queryKey: ["document", key],
    queryFn: async (): Promise<GetDocumentByKey> => {
      const data = (await qFn(GET_DOCUMENT_BY_KEY, {
        key: key,
      })) as GetDocumentByKeyData;
      return data?.admin_files[0];
    },
    enabled: !!key,
  });

  // // INSERT FORUM
  // const insertForum = useMutation({
  //   mutationFn: (variables) =>
  //     mFn(INSERT_FORUM, { ...variables, wspolnota_id: wspolnotaId }),
  //   onSuccess: () => {
  //     tanstackClient.invalidateQueries("forums");
  //     // .then(() => console.log("ok, vars: "));
  //     // reset();
  //   },
  //   onError: (error) => {
  //     console.log("mutation error", error);
  //     notifications.show({
  //       id: "hello-there",
  //       withCloseButton: true,
  //       // onClose: () => console.log("unmounted"),
  //       // onOpen: () => console.log("mounted"),
  //       autoClose: false,
  //       title: "Błąd zapisu",
  //       message: ` Przekaż administratorowi: ${error}`,
  //       color: "red",
  //       //   icon: <IconX />,
  //       //   className: 'my-notification-class',
  //       //   style: { backgroundColor: 'red' },
  //       //   loading: false,
  //     });
  //   },
  // });

  return { getDocumentById, getDocumentByKey };
}

export default useData;
