import React, { useState, useEffect } from "react";
import styles from "./Uchwala.module.css";
import useData from "./useData.ts";
import { useUserData } from "@nhost/react";
import { useNavigate, useParams } from "react-router-dom";
import dayjs from "dayjs";
import { GetDocumentByIdQuery, GetDocumentByKeyQuery } from "@/gql/graphql.ts";

type GetDocumentById = GetDocumentByIdQuery["admin_documents"][0];
type GetDocumentByKey = GetDocumentByKeyQuery["admin_files"][0];
// type DocDataType = GetDocumentById | GetDocumentByKey;
function Uchwala() {
  const { doc_id, key } = useParams();
  const numericDocId = +doc_id;

  const navigate = useNavigate();
  const userData = useUserData();
  const [glosowa<PERSON><PERSON><PERSON><PERSON>, setGlosowanieJawne] = useState(true);
  const [docData, setDocData] = useState({} as GetDocumentById);
  const { getDocumentById, getDocumentByKey } = useData({
    id: numericDocId,
    key: key,
  });
  // console.log("docData", docData);
  const {
    data: dataDocsId,
    error: errorDocsId,
    isLoading: isLoadingDocsId,
  } = getDocumentById;

  const {
    data: dataDocsKey,
    error: errorDocsKey,
    isLoading: isLoadingDocsKey,
  } = getDocumentByKey;

  useEffect(() => {
    if (dataDocsId) {
      setDocData(dataDocsId);
    } else if (dataDocsKey) {
      // const documentData = dataDocsKey.map
      // setDocData(dataDocsKey);
    }
  }, [dataDocsId, dataDocsKey]);

  const wniosek = {
    title: "Uchwala ",
    description:
      "W okresie od 1 stycznia 2022 do 31 grudnia 2022 wspolnota glosowała na wnioskiem:",
  };
  function getVoteLabel(result: "yes" | "no" | "neutral") {
    if (result === "yes") {
      return "ZA";
    } else if (result === "no") {
      return "PRZECIW";
    } else if (result === "neutral") {
      return "NEUTRALNY";
    } else {
      return "NIE GŁOSOWAŁ/A";
    }
  }

  if ((numericDocId && isLoadingDocsId) || (key && isLoadingDocsKey))
    return <div>Loading...</div>;
  if ((numericDocId && errorDocsId) || (key && errorDocsKey))
    return <div>Error </div>;

  return (
    <div className={styles.wrapper}>
      <h1>Uchwała wspólnoty {docData?.number_string}</h1>
      <p>
        Dnia {dayjs().format("DD.MM.YYYY")} zakończono głosowanie nad wnioskiem:
      </p>

      <div className={styles.wniosek_wrapper}>
        <div className={styles.wniosek_title}>{docData?.metadata?.title}</div>
        <div className={styles.wniosek_description}>
          <pre>{docData?.metadata?.description}</pre>
        </div>
      </div>
      <p>
        W wyniku głosowania wniosek został{" "}
        {docData?.metadata?.raport.result === "VOTED_YES"
          ? "PRZJĘTY"
          : "ODRZUCONY"}
      </p>
      {glosowanieJawne && (
        <>
          <p>Zapisano głosy: </p>
          <ul>
            {docData?.metadata?.raport.votes.map((row) => (
              <li key={row.name}>
                {row.name} - {getVoteLabel(row.vote)}
              </li>
            ))}
          </ul>
        </>
      )}
    </div>
  );
}

export default Uchwala;
