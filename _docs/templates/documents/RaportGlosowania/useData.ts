//RaportGlosowania.tsx

import { notifications } from "@mantine/notifications";
import { useUserData } from "@nhost/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { Admin_Wspolnoty } from "@/gql/graphql.ts";
import useTQuery from "@/hooks/useTQuery";
import { INSERT_FORUM } from "@/utils/data/mutations.ts";
import { GET_WSPOLNOTY } from "@/utils/data/queries";
import { RootStateTypes } from "@/utils/redux/store";

interface WspolnotyTypes {
  admin_wspolnoty: Admin_Wspolnoty[];
}

function useData(id) {
  const user = useUserData();
  // console.log("user", user);
  const { tanstackClient, qFn, mFn } = useTQuery();
  const wspolnotaId = useSelector((state: RootStateTypes) => state.userProfile.wspolnota_id);

  // GET_WSPOLNOTY
  const getWspolnoty = useQuery({
    queryKey: ["wspolnoty", wspolnotaId],
    queryFn: async (): Promise<Admin_Wspolnoty[]> => {
      const data = (await qFn(GET_WSPOLNOTY, {})) as WspolnotyTypes;
      return data?.admin_wspolnoty;
    },
    enabled: !!user,
    onSuccess: (data) => {
      console.log("wspolnoty data ", data);
      // tanstackClient
      //   .invalidateQueries("forums")
      //   // .then(() => console.log("ok, vars: "));
    },
    onError: (error) => {
      console.log("query error", error);
      notifications.show({
        id: "hello-there",
        withCloseButton: true,
        // onClose: () => console.log("unmounted"),
        // onOpen: () => console.log("mounted"),
        autoClose: false,
        title: "Błąd!!!",
        message: ` Przekaż administratorowi: ${error}`,
        color: "red",
        //   icon: <IconX />,
        //   className: 'my-notification-class',
        //   style: { backgroundColor: 'red' },
        //   loading: false,
      });
    },
  });

  // // INSERT FORUM
  // const insertForum = useMutation({
  //   mutationFn: (variables) =>
  //     mFn(INSERT_FORUM, { ...variables, wspolnota_id: wspolnotaId }),
  //   onSuccess: () => {
  //     tanstackClient.invalidateQueries("forums");
  //     // .then(() => console.log("ok, vars: "));
  //     // reset();
  //   },
  //   onError: (error) => {
  //     console.log("mutation error", error);
  //     notifications.show({
  //       id: "hello-there",
  //       withCloseButton: true,
  //       // onClose: () => console.log("unmounted"),
  //       // onOpen: () => console.log("mounted"),
  //       autoClose: false,
  //       title: "Błąd zapisu",
  //       message: ` Przekaż administratorowi: ${error}`,
  //       color: "red",
  //       //   icon: <IconX />,
  //       //   className: 'my-notification-class',
  //       //   style: { backgroundColor: 'red' },
  //       //   loading: false,
  //     });
  //   },
  // });

  return { getWspolnoty };
}

export default useData;
