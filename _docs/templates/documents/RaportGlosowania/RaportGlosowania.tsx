import React, { useState, useEffect } from "react";
import styles from "./RaportGlosowania.module.css";
import useData from "./useData.ts";

import { useUserData } from "@nhost/react";
import { useNavigate } from "react-router-dom";

import dayjs from "dayjs";

function RaportGlosowania() {
  const navigate = useNavigate();
  const userData = useUserData();

  const [rows, setRows] = React.useState([]);

  return (
    <>
      <div>RaportGlosowania</div>
    </>
  );
}

export default RaportGlosowania;
