import Keycloak from "keycloak-js";
import { useCallback, useEffect, useState } from "react";

export const keycloak = new Keycloak({
  url: `${import.meta.env.PUBLIC_KC_URL}`,
  realm: `${import.meta.env.PUBLIC_KC_REALM}`,
  clientId: `${import.meta.env.PUBLIC_KC_CLIENT_ID}`,
});

// Initialize Keycloak
export const initKeycloak = async () => {
  try {
    await keycloak.init({
      onLoad: "check-sso",
      silentCheckSsoRedirectUri: window.location.origin + "/silent-check-sso.html",
      pkceMethod: "S256",
      checkLoginIframe: false,
      enableLogging: true,
    });
    return keycloak;
  } catch (error) {
    console.error("Failed to initialize Keycloak:", error);
    throw error;
  }
};

export function useAuth() {
  const isLoggedIn = keycloak.authenticated;

  useEffect(() => {
    const refreshToken = setInterval(() => {
      if (keycloak.authenticated) {
        keycloak.updateToken(70).catch(() => {
          console.error("Failed to refresh token");
        });
      }
    }, 60000); // Check token every minute

    return () => {
      clearInterval(refreshToken);
    };
  }, []);

  const login = useCallback(async () => {
    try {
      await keycloak.login();
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await keycloak.logout();
    } catch (error) {
      console.error("Logout failed:", error);
      throw error;
    }
  }, []);

  const getToken = useCallback(async () => {
    try {
      await keycloak.updateToken(5);
      return keycloak.token;
    } catch (error) {
      console.error("Failed to refresh token:", error);
      throw error;
    }
  }, []);

  return {
    login,
    logout,
    getToken,
    isLoggedIn,
    user: keycloak,
  };
}

export default useAuth;
