import { defineConfig } from "orval";

export default defineConfig({
  dosiero: {
    input: {
      target: "http://localhost:8005/openapi.json",
    },
    output: {
      mode: "split",
      namingConvention: "PascalCase",
      target: "./src/_gen/endpoints",
      schemas: "./src/_gen/schemas",
      fileExtension: ".ts",
      client: "react-query",
      httpClient: "fetch",
      mock: false,
      clean: true,
      biome: true,
      // override: {
      //   mutator: {
      //     path: "./src/api/mutator/custom-instance.ts",
      //     name: "customInstance",
      //   },
      // },
      override: {
        operations: {
          query: {
            query: {
              useQuery: true,
            },
          },
        },
      },
    },
    // hooks: {
    //   afterAllFilesWrite: "biome --write",
    // },
  },
  dosieroZed: {
    input: {
      target: "http://localhost:8005/openapi.json",
    },
    output: {
      mode: "tags",
      target: "./src/_gen/zod",
      fileExtension: ".ts",
      client: "zod",
    },
  },
});
