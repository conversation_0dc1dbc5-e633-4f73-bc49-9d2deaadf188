// async function handleResponseStatus(response: Response): Promise<void> {
//   if (!response.ok) {
//     const errorMessage = await getErrorMessage(response);
//     throw new Error(`HTTP error ${response.status}: ${errorMessage}`);
//   }
// }

// async function getErrorMessage(response: Response): Promise<string> {
//   const contentType = response.headers.get("content-type");
//   if (contentType?.includes("application/json")) {
//     const errorData: ErrorResponse = await response.json();
//     return errorData.message || response.statusText;
//   }
//   return await response.text();
// }

// async function parseResponseBody<T>(response: Response): Promise<T> {
//   const contentType = response.headers.get("content-type");
//   if (contentType?.includes("application/json")) {
//     return await response.json();
//   }
//   const responseText = await response.text();
//   throw new Error(`Unexpected response format: ${responseText}`);
// }

// function handleFetchError(error: Error): void {
//   if (error instanceof TypeError) {
//     console.error("Network error:", error);
//     toast.error(`Network error: ${error.message}`);
//   } else {
//     console.error("HTTP error:", error);
//     toast.error(`HTTP error: ${error.message}`);
//   }
// }
//

import toast from "react-hot-toast";

async function handleResponseStatus(response: Response): Promise<void> {
  if (response.ok) {
    toast.success(`Request successful: ${response.status} ${response.statusText}`);
  } else {
    const errorMessage = await getErrorMessage(response);
    toast.error(`HTTP error ${response.status}: ${errorMessage}`);
    throw new Error(`HTTP error ${response.status}: ${errorMessage}`);
  }
}

async function getErrorMessage(response: Response): Promise<string> {
  const contentType = response.headers.get("content-type");
  if (contentType?.includes("application/json")) {
    const errorData: ErrorResponse = await response.json();
    const message = errorData.message || response.statusText;
    toast.error(`Error: ${message}`);
    return message;
  }
  const text = await response.text();
  toast.error(`Error: ${text}`);
  return text;
}

async function parseResponseBody<T>(response: Response): Promise<T> {
  const contentType = response.headers.get("content-type");
  if (contentType?.includes("application/json")) {
    const data = await response.json();

    return data;
  }
  const responseText = await response.text();
  toast.error(`Unexpected response format: ${responseText}`);
  throw new Error(`Unexpected response format: ${responseText}`);
}

function handleFetchError(error: Error): void {
  if (error instanceof TypeError) {
    console.error("Network error:", error);
    toast.error(`Network error: ${error.message}`);
  } else {
    console.error("HTTP error:", error);
    toast.error(`HTTP error: ${error.message}`);
  }
}

interface ErrorResponse {
  message?: string;
}

const baseApiPath = import.meta.env.PUBLIC_API_BASE_URL;

async function getFn<T>(url: string, token: string): Promise<T> {
  const fullUrl = `${baseApiPath}${url}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(fullUrl, { method: "GET", headers });
    await handleResponseStatus(response);
    return await parseResponseBody<T>(response);
  } catch (error) {
    handleFetchError(error as Error);
    throw error;
  }
}

async function postFn<T>(url: string, data: unknown, token: string): Promise<T> {
  const fullUrl = `${baseApiPath}${url}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    // Log the data before stringifying
    console.log("Data being sent:", data);

    const stringifiedData = JSON.stringify(data);

    // Log the stringified data
    console.log("Stringified data:", stringifiedData);

    const response = await fetch(fullUrl, {
      method: "POST",
      headers,
      body: stringifiedData,
    });
    await handleResponseStatus(response);
    return await parseResponseBody<T>(response);
  } catch (error) {
    handleFetchError(error as Error);
    throw error;
  }
}

// New putDevFn function
async function putFn<T>(url: string, data: unknown, token: string): Promise<T> {
  const fullUrl = `${baseApiPath}${url}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(fullUrl, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });
    await handleResponseStatus(response);
    return await parseResponseBody<T>(response);
  } catch (error) {
    handleFetchError(error as Error);
    throw error;
  }
}

async function deleteFn<T>(url: string, id: string | number, token: string): Promise<T> {
  const fullUrl = `${baseApiPath}${url}/${id}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(fullUrl, {
      method: "DELETE",
      headers,
    });
    await handleResponseStatus(response);
    return await parseResponseBody<T>(response);
  } catch (error) {
    handleFetchError(error as Error);
    throw error;
  }
}

export { getFn, postFn, putFn, deleteFn };
