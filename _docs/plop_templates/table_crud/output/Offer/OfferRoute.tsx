import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createOffersV1CoreOffersPostMutation,
  readOffersAllV1CoreOffersGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import OffersTable from "./-components/OffersTable";
import { useOfferData } from "./-data_hooks/useOfferData";

export const Route = createFileRoute("/dashboard/offers/")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readOffersAllV1CoreOffersGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "dashboard" }));
  dispatch(updateSystemField({ pageTitle: "Offer Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/dashboard/offers/"
      if (toLocation.pathname !== "/dashboard/offers/") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataOffers, createOfferMutation, updateOfferMutation, deleteOfferMutation } = useOfferData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <OffersTable
          data={dataOffers.data}
          createOfferMutation={createOfferMutation}
          updateOfferMutation={updateOfferMutation}
          deleteOfferMutation={deleteOfferMutation}
        />
      </div>
    </Suspense>
  );
}
