[{"name": "id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.any().nullable().optional()"}, {"name": "job_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "contractor_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "contact_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "org_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "price", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "start_date", "inputType": "date", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "finish_date", "inputType": "date", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "expiry_date", "inputType": "date", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "status", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}]