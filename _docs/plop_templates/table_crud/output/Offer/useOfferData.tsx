import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createOffersV1CoreOffersPostMutation,
  deleteOffersV1CoreOffersItemIdDeleteMutation,
  readOffersAllV1CoreOffersGetOptions,
  updateOffersV1CoreOffersPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { OfferCreateTypes, OfferUpdateTypes } from "@/client";

// type Props = {
//   updateData: OfferUpdateTypes
//   createData: OfferCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useOfferData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataOffers, error: errorOffers } = useSuspenseQuery(
    readOffersAllV1CoreOffersGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorOffers) {
    toast.error(errorOffers.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataOffers);

  // CREATE
  const createOfferMutation = useMutation({
    ...createOffersV1CoreOffersPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOffersAllV1CoreOffersGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createOfferMutation", createOfferMutation);

  // UPDATE

  const updateOfferMutation = useMutation({
    ...updateOffersV1CoreOffersPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOffersAllV1CoreOffersGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateOfferMutation", updateOfferMutation);

  // DELETE
  const deleteOfferMutation = useMutation({
    ...deleteOffersV1CoreOffersItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOffersAllV1CoreOffersGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteOffer.mutateAsync({
  //       path: { item_id: (data as OfferDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataOffers,
    createOfferMutation,
    updateOfferMutation,
    deleteOfferMutation,
  };
}
