import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createCommentsV1CoreCommentsPostMutation,
  deleteCommentsV1CoreCommentsItemIdDeleteMutation,
  readCommentsAllV1CoreCommentsGetOptions,
  updateCommentsV1CoreCommentsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { CommentCreateTypes, CommentUpdateTypes } from "@/client";

// type Props = {
//   updateData: CommentUpdateTypes
//   createData: CommentCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useCommentData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataComments, error: errorComments } = useSuspenseQuery(
    readCommentsAllV1CoreCommentsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorComments) {
    toast.error(errorComments.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataComments);

  // CREATE
  const createCommentMutation = useMutation({
    ...createCommentsV1CoreCommentsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readCommentsAllV1CoreCommentsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createCommentMutation", createCommentMutation);

  // UPDATE

  const updateCommentMutation = useMutation({
    ...updateCommentsV1CoreCommentsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readCommentsAllV1CoreCommentsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateCommentMutation", updateCommentMutation);

  // DELETE
  const deleteCommentMutation = useMutation({
    ...deleteCommentsV1CoreCommentsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readCommentsAllV1CoreCommentsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteComment.mutateAsync({
  //       path: { item_id: (data as CommentDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataComments,
    createCommentMutation,
    updateCommentMutation,
    deleteCommentMutation,
  };
}
