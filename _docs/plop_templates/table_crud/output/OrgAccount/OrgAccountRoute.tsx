import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createOrgAccountsV1MoneyOrgAccountsPostMutation,
  readOrgAccountsAllV1MoneyOrgAccountsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import OrgAccountsTable from "./-components/OrgAccountsTable";
import { useOrgAccountData } from "./-data_hooks/useOrgAccountData";

export const Route = createFileRoute("/money/accounts")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readOrgAccountsAllV1MoneyOrgAccountsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "OrgAccount Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/money/accounts"
      if (toLocation.pathname !== "/money/accounts") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataOrgAccounts, createOrgAccountMutation, updateOrgAccountMutation, deleteOrgAccountMutation } =
    useOrgAccountData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <OrgAccountsTable
          data={dataOrgAccounts.data}
          createOrgAccountMutation={createOrgAccountMutation}
          updateOrgAccountMutation={updateOrgAccountMutation}
          deleteOrgAccountMutation={deleteOrgAccountMutation}
        />
      </div>
    </Suspense>
  );
}
