import { Button, NumberInput, Select, Switch, Textarea, TextInput } from "@mantine/core";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import { deleteOrgAccountsV1MoneyOrgAccountsItemIdDeleteMutation } from "@/api/_client/@tanstack/react-query.gen";
import { OrgAccountCreateTypes, OrgAccountDisplayTypes, OrgAccountUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultOrgAccount = {
  lang: null,
  name: null,
  name_short: null,
  label: null,
  label_short: null,
  description: null,
  group_id: null,
  level: null,
  type: null,
  is_debit_minus: null,
  acc_number: null,
  currency: null,
  parent_id: null,
  is_branch: null,
  is_placeholder: null,
  is_active: null,
  balance: null,
  is_syntetyczne: null,
  enabled: null,
  set_id: null,
  set_item_id: null,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    lang: z.string().nullable().optional(),
    name: z.string().nullable().optional(),
    name_short: z.string().nullable().optional(),
    label: z.string().nullable().optional(),
    label_short: z.string().nullable().optional(),
    description: z.string().nullable().optional(),
    group_id: z.union([z.number(), z.string()]).nullable().optional(),
    level: z.union([z.number(), z.string()]).nullable().optional(),
    type: z.string().nullable().optional(),
    is_debit_minus: z.boolean().nullable().optional(),
    acc_number: z.string().nullable().optional(),
    currency: z.string().nullable().optional(),
    parent_id: z.union([z.number(), z.string()]).nullable().optional(),
    is_branch: z.boolean().nullable().optional(),
    is_placeholder: z.boolean().nullable().optional(),
    is_active: z.boolean().nullable().optional(),
    balance: z.union([z.number(), z.string()]).nullable().optional(),
    is_syntetyczne: z.boolean().nullable().optional(),
    enabled: z.boolean().nullable().optional(),
    set_id: z.union([z.number(), z.string()]).nullable().optional(),
    set_item_id: z.union([z.number(), z.string()]).nullable().optional(),
  })
  .passthrough();

interface PropsTypes {
  data?: OrgAccountDisplayTypes;
  variant: "new" | "edit";
  setEditingRow?: (row: any) => void;
  setCreatingRow?: (row: any) => void;
  createOrgAccountMutation: any;
  updateOrgAccountMutation: any;
}

type FormData = z.infer<typeof formSchema>;

function OrgAccountForm({
  data = defaultOrgAccount,
  variant = "edit",
  setEditingRow,
  setCreatingRow,
  createOrgAccountMutation,
  updateOrgAccountMutation,
}: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as OrgAccountCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as OrgAccountUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  async function handleCreate(formData: OrgAccountCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createOrgAccountMutation.mutateAsync(
      {
        body: [
          {
            ...formData,
            created_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setCreatingRow?.(null);
        },
      },
    );
  }

  async function handleEdit(formData: OrgAccountUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]]),
    ) as OrgAccountUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateOrgAccountMutation.mutateAsync(
      {
        body: [
          {
            ...updatedFields,
            id: data?.id,
            updated_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setEditingRow?.(null);
        },
      },
    );
  }

  const deleteOrgAccount = useMutation({
    ...deleteOrgAccountsV1MoneyOrgAccountsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgAccountsAllV1MoneyOrgAccountsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteOrgAccount.mutateAsync({
        path: { item_id: (data as OrgAccountDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit" ? t("forms.OrgAccountForm.titleEdit.label") : t("forms.OrgAccountForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
            {/* LANG */}
            <div className={styles.span4}>
              <form.Field
                name="lang"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.lang.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("lang");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* NAME */}
            <div className={styles.span4}>
              <form.Field
                name="name"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.name.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* NAME_SHORT */}
            <div className={styles.span4}>
              <form.Field
                name="name_short"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.nameShort.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name_short");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* LABEL */}
            <div className={styles.span4}>
              <form.Field
                name="label"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.label.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("label");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* LABEL_SHORT */}
            <div className={styles.span4}>
              <form.Field
                name="label_short"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.labelShort.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("label_short");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DESCRIPTION */}
            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.OrgAccountForm.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* GROUP_ID */}
            <div className={styles.span4}>
              <form.Field
                name="group_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Select
                      label={t("forms.OrgAccountForm.groupId.label")}
                      value={state.value || ""}
                      placeholder={t("forms.OrgAccountForm.groupId.placeholder.label")}
                      data={objectTypes.map((type) => ({
                        value: type.name,
                        label: type.label,
                      }))}
                      onChange={(value) => {
                        handleChange(value);
                        onFieldChange("group_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* LEVEL */}
            <div className={styles.span4}>
              <form.Field
                name="level"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgAccountForm.level.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("level");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* TYPE */}
            <div className={styles.span4}>
              <form.Field
                name="type"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.type.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("type");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* IS_DEBIT_MINUS */}
            <div className={styles.span4}>
              <form.Field
                name="is_debit_minus"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgAccountForm.isDebitMinus.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_debit_minus");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* ACC_NUMBER */}
            <div className={styles.span4}>
              <form.Field
                name="acc_number"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.accNumber.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("acc_number");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* CURRENCY */}
            <div className={styles.span4}>
              <form.Field
                name="currency"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgAccountForm.currency.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("currency");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* PARENT_ID */}
            <div className={styles.span4}>
              <form.Field
                name="parent_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgAccountForm.parentId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("parent_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* IS_BRANCH */}
            <div className={styles.span4}>
              <form.Field
                name="is_branch"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgAccountForm.isBranch.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_branch");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* IS_PLACEHOLDER */}
            <div className={styles.span4}>
              <form.Field
                name="is_placeholder"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgAccountForm.isPlaceholder.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_placeholder");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* IS_ACTIVE */}
            <div className={styles.span4}>
              <form.Field
                name="is_active"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgAccountForm.isActive.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_active");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* BALANCE */}
            <div className={styles.span4}>
              <form.Field
                name="balance"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgAccountForm.balance.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("balance");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* IS_SYNTETYCZNE */}
            <div className={styles.span4}>
              <form.Field
                name="is_syntetyczne"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgAccountForm.isSyntetyczne.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_syntetyczne");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* ENABLED */}
            <div className={styles.span4}>
              <form.Field
                name="enabled"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgAccountForm.enabled.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("enabled");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* SET_ID */}
            <div className={styles.span4}>
              <form.Field
                name="set_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgAccountForm.setId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("set_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* SET_ITEM_ID */}
            <div className={styles.span4}>
              <form.Field
                name="set_item_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgAccountForm.setItemId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("set_item_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0} loading={isSubmitting}>
                  {variant === "edit" ? t("common.save.label") : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" && (data as OrgAccountDisplayTypes)?.id !== user.curr_org_id && (
              <Button
                color="red"
                loading={updateOrgAccountMutation.isPending || createOrgAccountMutation.isPending}
                // disabled
                onClick={handleDelete}
              >
                {t("common.delete.label")}
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
}

export default OrgAccountForm;
