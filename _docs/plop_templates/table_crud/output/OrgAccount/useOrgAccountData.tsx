import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createOrgAccountsV1MoneyOrgAccountsPostMutation,
  deleteOrgAccountsV1MoneyOrgAccountsItemIdDeleteMutation,
  readOrgAccountsAllV1MoneyOrgAccountsGetOptions,
  updateOrgAccountsV1MoneyOrgAccountsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { OrgAccountCreateTypes, OrgAccountUpdateTypes } from "@/client";

// type Props = {
//   updateData: OrgAccountUpdateTypes
//   createData: OrgAccountCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useOrgAccountData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataOrgAccounts, error: errorOrgAccounts } = useSuspenseQuery(
    readOrgAccountsAllV1MoneyOrgAccountsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorOrgAccounts) {
    toast.error(errorOrgAccounts.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataOrgAccounts);

  // CREATE
  const createOrgAccountMutation = useMutation({
    ...createOrgAccountsV1MoneyOrgAccountsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgAccountsAllV1MoneyOrgAccountsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createOrgAccountMutation", createOrgAccountMutation);

  // UPDATE

  const updateOrgAccountMutation = useMutation({
    ...updateOrgAccountsV1MoneyOrgAccountsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgAccountsAllV1MoneyOrgAccountsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateOrgAccountMutation", updateOrgAccountMutation);

  // DELETE
  const deleteOrgAccountMutation = useMutation({
    ...deleteOrgAccountsV1MoneyOrgAccountsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgAccountsAllV1MoneyOrgAccountsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteOrgAccount.mutateAsync({
  //       path: { item_id: (data as OrgAccountDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataOrgAccounts,
    createOrgAccountMutation,
    updateOrgAccountMutation,
    deleteOrgAccountMutation,
  };
}
