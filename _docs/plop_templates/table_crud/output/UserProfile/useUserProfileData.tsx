import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createUserProfilesV1CoreUserProfilesPostMutation,
  deleteUserProfilesV1CoreUserProfilesItemIdDeleteMutation,
  readUserProfilesAllV1CoreUserProfilesGetOptions,
  updateUserProfilesV1CoreUserProfilesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { UserProfileCreateTypes, UserProfileUpdateTypes } from "@/client";

// type Props = {
//   updateData: UserProfileUpdateTypes
//   createData: UserProfileCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useUserProfileData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataUserProfiles, error: errorUserProfiles } = useSuspenseQuery(
    readUserProfilesAllV1CoreUserProfilesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorUserProfiles) {
    toast.error(errorUserProfiles.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataUserProfiles);

  // CREATE
  const createUserProfileMutation = useMutation({
    ...createUserProfilesV1CoreUserProfilesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readUserProfilesAllV1CoreUserProfilesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createUserProfileMutation", createUserProfileMutation);

  // UPDATE

  const updateUserProfileMutation = useMutation({
    ...updateUserProfilesV1CoreUserProfilesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readUserProfilesAllV1CoreUserProfilesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateUserProfileMutation", updateUserProfileMutation);

  // DELETE
  const deleteUserProfileMutation = useMutation({
    ...deleteUserProfilesV1CoreUserProfilesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readUserProfilesAllV1CoreUserProfilesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteUserProfile.mutateAsync({
  //       path: { item_id: (data as UserProfileDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataUserProfiles,
    createUserProfileMutation,
    updateUserProfileMutation,
    deleteUserProfileMutation,
  };
}
