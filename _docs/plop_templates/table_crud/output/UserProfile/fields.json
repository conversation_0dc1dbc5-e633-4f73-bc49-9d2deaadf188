[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "created_at", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "updated_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "lang", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string()"}, {"name": "display_name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string()"}, {"name": "is_company", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}, {"name": "is_voting", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}, {"name": "shares", "inputType": "number", "defaultValue": "1", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "org_id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "is_current", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}, {"name": "org_name", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "object_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "znaczaca_operacja_limit", "inputType": "number", "defaultValue": "500", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}]