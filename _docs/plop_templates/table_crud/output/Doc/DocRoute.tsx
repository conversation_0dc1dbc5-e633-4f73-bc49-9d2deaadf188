import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createDocsV1CoreDocsPostMutation,
  readDocsAllV1CoreDocsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import DocsTable from "./-components/DocsTable";
import { useDocData } from "./-data_hooks/useDocData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readDocsAllV1CoreDocsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Doc Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataDocs, createDocMutation, updateDocMutation, deleteDocMutation } = useDocData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <DocsTable
          data={dataDocs.data}
          createDocMutation={createDocMutation}
          updateDocMutation={updateDocMutation}
          deleteDocMutation={deleteDocMutation}
        />
      </div>
    </Suspense>
  );
}
