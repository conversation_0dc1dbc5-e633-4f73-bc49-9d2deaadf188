import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createDocsV1CoreDocsPostMutation,
  deleteDocsV1CoreDocsItemIdDeleteMutation,
  readDocsAllV1CoreDocsGetOptions,
  updateDocsV1CoreDocsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { DocCreateTypes, DocUpdateTypes } from "@/client";

// type Props = {
//   updateData: DocUpdateTypes
//   createData: DocCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useDocData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataDocs, error: errorDocs } = useSuspenseQuery(
    readDocsAllV1CoreDocsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorDocs) {
    toast.error(errorDocs.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataDocs);

  // CREATE
  const createDocMutation = useMutation({
    ...createDocsV1CoreDocsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocsAllV1CoreDocsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createDocMutation", createDocMutation);

  // UPDATE

  const updateDocMutation = useMutation({
    ...updateDocsV1CoreDocsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocsAllV1CoreDocsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateDocMutation", updateDocMutation);

  // DELETE
  const deleteDocMutation = useMutation({
    ...deleteDocsV1CoreDocsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocsAllV1CoreDocsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteDoc.mutateAsync({
  //       path: { item_id: (data as DocDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataDocs,
    createDocMutation,
    updateDocMutation,
    deleteDocMutation,
  };
}
