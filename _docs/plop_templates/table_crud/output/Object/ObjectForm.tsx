import { Button, NumberInput, Switch, Textarea, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createObjectsV1CoreObjectsPostMutation,
  deleteObjectsV1CoreObjectsItemIdDeleteMutation,
  updateObjectsV1CoreObjectsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { ObjectCreateTypes, ObjectDisplayTypes, ObjectUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultObject = {
  id: 0,
  created_at: "",
  updated_at: "",
  created_by: 0,
  updated_by: 0,
  lang: "pl",
  id: 0,
  created_at: "new Date().toISOString()",
  updated_at: "new Date().toISOString()",
  created_by: 0,
  updated_by: 0,
  json_metadata: "null",
  name: "",
  description: "null",
  object_type: "",
  width: "null",
  length: "null",
  height: "null",
  parent_id: 0,
  tree_level: 0,
  org_id: 0,
  surface: "null",
  volume: "null",
  circumference: "null",
  is_branch: false,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    id: z.union([z.number(), z.string()]),
    created_at: z.string(),
    updated_at: z.string(),
    created_by: z.union([z.number(), z.string()]),
    updated_by: z.union([z.number(), z.string()]).nullable().optional(),
    json_metadata: z.any().nullable().optional(),
    name: z.string(),
    description: z.string().nullable().optional(),
    object_type: z.string(),
    width: z.string().nullable().optional(),
    length: z.string().nullable().optional(),
    height: z.string().nullable().optional(),
    parent_id: z.union([z.number(), z.string()]).nullable().optional(),
    tree_level: z.union([z.number(), z.string()]),
    org_id: z.union([z.number(), z.string()]),
    surface: z.string().nullable().optional(),
    volume: z.string().nullable().optional(),
    circumference: z.string().nullable().optional(),
    is_branch: z.boolean().optional(),
  })
  .passthrough();

interface PropsTypes {
  data?: ObjectDisplayTypes;
  variant: "new" | "edit";
}

type FormData = z.infer<typeof formSchema>;

function ObjectForm({ data = defalultObject, variant = "edit" }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as ObjectCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as ObjectUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  const createObject = useMutation({
    ...createObjectsV1CoreObjectsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectsAllV1CoreObjectsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleCreate(formData: ObjectCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createObject.mutateAsync({
      body: [
        {
          ...formData,
          created_by: user?.id,
        },
      ],
    });
  }

  const updateObject = useMutation({
    ...updateObjectsV1CoreObjectsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectsAllV1CoreObjectsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleEdit(formData: ObjectUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]]),
    ) as ObjectUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateObject.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
        },
      ],
    });
  }

  const deleteObject = useMutation({
    ...deleteObjectsV1CoreObjectsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectsAllV1CoreObjectsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteObject.mutateAsync({
        path: { item_id: (data as ObjectDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit" ? t("forms.ObjectForm.titleEdit.label") : t("forms.ObjectForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
            {/* ID */}
            <div className={styles.span4}>
              <form.Field
                name="id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectForm.id.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* CREATED_AT */}
            <div className={styles.span4}>
              <form.Field
                name="created_at"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.ObjectForm.createdAt.label")}
                      value={new Date(state.value)}
                      onChange={(value) => {
                        handleChange(value);
                        onFieldChange("created_at");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* UPDATED_AT */}
            <div className={styles.span4}>
              <form.Field
                name="updated_at"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.ObjectForm.updatedAt.label")}
                      value={new Date(state.value)}
                      onChange={(value) => {
                        handleChange(value);
                        onFieldChange("updated_at");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* CREATED_BY */}
            <div className={styles.span4}>
              <form.Field
                name="created_by"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectForm.createdBy.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("created_by");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* UPDATED_BY */}
            <div className={styles.span4}>
              <form.Field
                name="updated_by"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectForm.updatedBy.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("updated_by");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* JSON_METADATA */}
            <div className={styles.span4}>
              <form.Field
                name="json_metadata"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.ObjectForm.jsonMetadata.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("json_metadata");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* NAME */}
            <div className={styles.span4}>
              <form.Field
                name="name"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.name.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DESCRIPTION */}
            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.ObjectForm.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* OBJECT_TYPE */}
            <div className={styles.span4}>
              <form.Field
                name="object_type"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.objectType.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("object_type");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* WIDTH */}
            <div className={styles.span4}>
              <form.Field
                name="width"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.width.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("width");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* LENGTH */}
            <div className={styles.span4}>
              <form.Field
                name="length"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.length.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("length");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* HEIGHT */}
            <div className={styles.span4}>
              <form.Field
                name="height"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.height.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("height");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* PARENT_ID */}
            <div className={styles.span4}>
              <form.Field
                name="parent_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectForm.parentId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("parent_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* TREE_LEVEL */}
            <div className={styles.span4}>
              <form.Field
                name="tree_level"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectForm.treeLevel.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("tree_level");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* ORG_ID */}
            <div className={styles.span4}>
              <form.Field
                name="org_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectForm.orgId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("org_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* SURFACE */}
            <div className={styles.span4}>
              <form.Field
                name="surface"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.surface.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("surface");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* VOLUME */}
            <div className={styles.span4}>
              <form.Field
                name="volume"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.volume.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("volume");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* CIRCUMFERENCE */}
            <div className={styles.span4}>
              <form.Field
                name="circumference"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectForm.circumference.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("circumference");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* IS_BRANCH */}
            <div className={styles.span4}>
              <form.Field
                name="is_branch"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.ObjectForm.isBranch.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_branch");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0} loading={isSubmitting}>
                  {variant === "edit" ? t("common.save.label") : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" && (data as ObjectDisplayTypes)?.id !== user.curr_org_id && (
              <Button
                color="red"
                loading={updateObject.isPending || createObject.isPending}
                // disabled
                onClick={handleDelete}
              >
                {t("common.delete.label")}
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
}

export default ObjectForm;
