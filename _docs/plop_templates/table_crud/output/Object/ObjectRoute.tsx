import { createFileRoute } from "@tanstack/react-router";

import { Suspense } from "react";
import {
  createObjectsV1CoreObjectsPostMutation,
  readObjectsAllV1CoreObjectsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import ObjectsTable from "@/routes/objects/-components/ObjectsTable";
import { useObjectData } from "../-data_hooks/useObjectData";

export const Route = createFileRoute("/objects/list/")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readObjectsAllV1CoreObjectsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const { dataObjects, createObjectMutation, updateObjectMutation, deleteObjectMutation } = useObjectData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <h3>Users</h3>

        <ObjectsTable
          data={dataObjects.data}
          createObjectMutation={createObjectMutation}
          updateObjectMutation={updateObjectMutation}
          deleteObjectMutation={deleteObjectMutation}
        />
      </div>
    </Suspense>
  );
}
