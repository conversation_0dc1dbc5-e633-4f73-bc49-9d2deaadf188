"use no memo";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex, Text, Tooltip } from "@mantine/core";
import { ObjectDisplayTypes, ObjectUpdateTypes } from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import ObjectForm from "@/routes/objects/-forms/ObjectForm";
import type { RootStateTypes } from "@/utils/redux/store";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import { modals } from "@mantine/modals";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import { useMemo } from "react";
import { useSelector } from "react-redux";

type PropsTypes = {
  data: ObjectDisplayTypes[];
  createObjectMutation: any;
  updateObjectMutation: any;
  deleteObjectMutation: any;
};

const ObjectsTable = ({ data, createObjectMutation, updateObjectMutation, deleteObjectMutation }: PropsTypes) => {
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<ObjectDisplayTypes>[]>(
    () => [
      {
        accessorKey: "id",
        header: "Id",
      },
      {
        accessorKey: "created_at",
        header: "Created_at",
      },
      {
        accessorKey: "updated_at",
        header: "Updated_at",
      },
      {
        accessorKey: "created_by",
        header: "Created_by",
      },
      {
        accessorKey: "updated_by",
        header: "Updated_by",
      },
      {
        accessorKey: "json_metadata",
        header: "Json_metadata",
      },
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "object_type",
        header: "Object_type",
      },
      {
        accessorKey: "width",
        header: "Width",
      },
      {
        accessorKey: "length",
        header: "Length",
      },
      {
        accessorKey: "height",
        header: "Height",
      },
      {
        accessorKey: "parent_id",
        header: "Parent_id",
      },
      {
        accessorKey: "tree_level",
        header: "Tree_level",
      },
      {
        accessorKey: "org_id",
        header: "Org_id",
      },
      {
        accessorKey: "surface",
        header: "Surface",
      },
      {
        accessorKey: "volume",
        header: "Volume",
      },
      {
        accessorKey: "circumference",
        header: "Circumference",
      },
      {
        accessorKey: "is_branch",
        header: "Is_branch",
      },
    ],
    [],
  );

  //CREATE action
  const handleCreateUser: MRT_TableOptions<ObjectUpdateTypes>["onCreatingRowSave"] = async ({
    values,
    exitCreatingMode,
  }) => {
    // await createObjectMutation.mutateAsync({
    //   body: [
    //     {
    //       ...values,
    //       created_by: user?.id,
    //       org_id: user?.curr_org_id,
    //     },
    //   ],
    // });
    exitCreatingMode();
  };

  //UPDATE action
  const handleUpdateUser: MRT_TableOptions<ObjectUpdateTypes>["onEditingRowSave"] = async ({ values, table }) => {
    console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
    console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

    modals.open({
      title: "Edit user profile",
      children: (
        <>
          <ObjectForm
            data={values}
            variant="edit"
            setEditingRow={table.setEditingRow}
            createObjectMutation={createObjectMutation}
            updateObjectMutation={updateObjectMutation}
          />
        </>
      ),
    });
  };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<ObjectUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: <Text>Are you sure you want to delete {row.original.display_name} </Text>,
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteObjectMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization: currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreateUser,
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdateUser,
    renderCreateRowModalContent: ({ table, row }) => (
      <ObjectForm
        data={row.original}
        variant="new"
        setEditingRow={table.setEditingRow}
        createObjectMutation={createObjectMutation}
        updateObjectMutation={updateObjectMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <ObjectForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        createObjectMutation={createObjectMutation}
        updateObjectMutation={updateObjectMutation}
      />
    ),
    renderRowActions: ({ row, table }) => (
      <Flex gap="md">
        <Tooltip label="Edit">
          <ActionIcon
            onClick={() => {
              console.log("row", row);
              table.setEditingRow(row);
            }}
          >
            <IconEdit />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon color="red" onClick={() => openDeleteConfirmModal(row)}>
            <IconTrash />
          </ActionIcon>
        </Tooltip>
      </Flex>
    ),
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Object
      </Button>
    ),
  });
  console.log("table", table);
  console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default ObjectsTable;
