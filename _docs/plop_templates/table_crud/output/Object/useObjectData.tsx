import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createObjectsV1CoreObjectsPostMutation,
  deleteObjectsV1CoreObjectsItemIdDeleteMutation,
  readObjectsAllV1CoreObjectsGetOptions,
  updateObjectsV1CoreObjectsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ObjectCreateTypes, ObjectUpdateTypes } from "@/client";

// type Props = {
//   updateData: ObjectUpdateTypes
//   createData: ObjectCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useObjectData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataObjects, error: errorObjects } = useSuspenseQuery(
    readObjectsAllV1CoreObjectsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorObjects) {
    toast.error(errorObjects.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataObjects);

  // CREATE
  const createObjectMutation = useMutation({
    ...createObjectsV1CoreObjectsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectsAllV1CoreObjectsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createObjectMutation", createObjectMutation);

  // UPDATE

  const updateObjectMutation = useMutation({
    ...updateObjectsV1CoreObjectsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectsAllV1CoreObjectsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateObjectMutation", updateObjectMutation);

  // DELETE
  const deleteObjectMutation = useMutation({
    ...deleteObjectsV1CoreObjectsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectsAllV1CoreObjectsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteObject.mutateAsync({
  //       path: { item_id: (data as ObjectDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataObjects,
    createObjectMutation,
    updateObjectMutation,
    deleteObjectMutation,
  };
}
