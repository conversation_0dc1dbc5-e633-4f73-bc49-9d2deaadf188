[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "created_at", "inputType": "date", "defaultValue": "new Date().toISOString()", "valueType": "string", "zodType": "z.string()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "new Date().toISOString()", "valueType": "string", "zodType": "z.string()"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "updated_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.any().nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "object_type", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "width", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "length", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "height", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "parent_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "tree_level", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "org_id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "surface", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "volume", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "circumference", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "is_branch", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}]