import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createObjectTypesV1CoreObjectTypesPostMutation,
  deleteObjectTypesV1CoreObjectTypesItemIdDeleteMutation,
  readObjectTypesAllV1CoreObjectTypesGetOptions,
  updateObjectTypesV1CoreObjectTypesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ObjectTypeCreateTypes, ObjectTypeUpdateTypes } from "@/client";

// type Props = {
//   updateData: ObjectTypeUpdateTypes
//   createData: ObjectTypeCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useObjectTypeData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataObjectTypes, error: errorObjectTypes } = useSuspenseQuery(
    readObjectTypesAllV1CoreObjectTypesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorObjectTypes) {
    toast.error(errorObjectTypes.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataObjectTypes);

  // CREATE
  const createObjectTypeMutation = useMutation({
    ...createObjectTypesV1CoreObjectTypesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createObjectTypeMutation", createObjectTypeMutation);

  // UPDATE

  const updateObjectTypeMutation = useMutation({
    ...updateObjectTypesV1CoreObjectTypesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateObjectTypeMutation", updateObjectTypeMutation);

  // DELETE
  const deleteObjectTypeMutation = useMutation({
    ...deleteObjectTypesV1CoreObjectTypesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteObjectType.mutateAsync({
  //       path: { item_id: (data as ObjectTypeDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataObjectTypes,
    createObjectTypeMutation,
    updateObjectTypeMutation,
    deleteObjectTypeMutation,
  };
}
