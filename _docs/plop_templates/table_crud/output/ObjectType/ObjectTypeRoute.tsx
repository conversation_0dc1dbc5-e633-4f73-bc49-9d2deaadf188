import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createObjectTypesV1CoreObjectTypesPostMutation,
  readObjectTypesAllV1CoreObjectTypesGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import ObjectTypesTable from "../-components/UsersTable/ObjectTypesTable";
import { useObjectTypeData } from "./-data_hooks/useObjectTypeData";

export const Route = createFileRoute("/config/object-types")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readObjectTypesAllV1CoreObjectTypesGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "ObjectType Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/object-types"
      if (toLocation.pathname !== "/config/object-types") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataObjectTypes, createObjectTypeMutation, updateObjectTypeMutation, deleteObjectTypeMutation } =
    useObjectTypeData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <h3>Users</h3>

        <ObjectTypesTable
          data={dataObjectTypes.data}
          createObjectTypeMutation={createObjectTypeMutation}
          updateObjectTypeMutation={updateObjectTypeMutation}
          deleteObjectTypeMutation={deleteObjectTypeMutation}
        />
      </div>
    </Suspense>
  );
}
