import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createContrahentTypesV1CrmContrahentTypesPostMutation,
  deleteContrahentTypesV1CrmContrahentTypesItemIdDeleteMutation,
  readContrahentTypesAllV1CrmContrahentTypesGetOptions,
  updateContrahentTypesV1CrmContrahentTypesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ContrahentTypeCreateTypes, ContrahentTypeUpdateTypes } from "@/client";

// type Props = {
//   updateData: ContrahentTypeUpdateTypes
//   createData: ContrahentTypeCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useContrahentTypeData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataContrahentTypes, error: errorContrahentTypes } = useSuspenseQuery(
    readContrahentTypesAllV1CrmContrahentTypesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorContrahentTypes) {
    toast.error(errorContrahentTypes.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataContrahentTypes);

  // CREATE
  const createContrahentTypeMutation = useMutation({
    ...createContrahentTypesV1CrmContrahentTypesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentTypesAllV1CrmContrahentTypesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createContrahentTypeMutation", createContrahentTypeMutation);

  // UPDATE

  const updateContrahentTypeMutation = useMutation({
    ...updateContrahentTypesV1CrmContrahentTypesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentTypesAllV1CrmContrahentTypesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateContrahentTypeMutation", updateContrahentTypeMutation);

  // DELETE
  const deleteContrahentTypeMutation = useMutation({
    ...deleteContrahentTypesV1CrmContrahentTypesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentTypesAllV1CrmContrahentTypesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteContrahentType.mutateAsync({
  //       path: { item_id: (data as ContrahentTypeDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataContrahentTypes,
    createContrahentTypeMutation,
    updateContrahentTypeMutation,
    deleteContrahentTypeMutation,
  };
}
