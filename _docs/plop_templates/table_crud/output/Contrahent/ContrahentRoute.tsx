import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createContrahentsV1CrmContrahentsPostMutation,
  readContrahentsAllV1CrmContrahentsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import ContrahentsTable from "./-components/ContrahentsTable";
import { useContrahentData } from "./-data_hooks/useContrahentData";

export const Route = createFileRoute("/contrahents/")({
  loader: async ({
    context: { queryClient, curr_org_id, curr_profile_id },
  }) => {
    try {
      await queryClient.prefetchQuery(
        readContrahentsAllV1CrmContrahentsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        })
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Contrahent Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe(
      "onBeforeNavigate",
      ({ toLocation }) => {
        // Check if navigating away
        if (toLocation.pathname !== "/contrahents/") {
          dispatch(updateSystemField({ pageTitle: "" }));
        }
      }
    );

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const {
    dataContrahents,
    createContrahentMutation,
    updateContrahentMutation,
    deleteContrahentMutation,
  } useContrahentData({});

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <ContrahentsTable
          data={dataContrahents.data}
          createContrahentMutation={createContrahentMutation}
          updateContrahentMutation={updateContrahentMutation}
          deleteContrahentMutation={deleteContrahentMutation}
        />
      </div>
    </Suspense>
  );
}
