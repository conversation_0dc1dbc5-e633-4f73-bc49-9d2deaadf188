import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createContrahentsV1CrmContrahentsPostMutation,
  deleteContrahentsV1CrmContrahentsItemIdDeleteMutation,
  readContrahentsAllV1CrmContrahentsGetOptions,
  updateContrahentsV1CrmContrahentsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ContrahentCreateTypes, ContrahentUpdateTypes } from "@/client";

// type Props = {
//   updateData: ContrahentUpdateTypes
//   createData: ContrahentCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useContrahentData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataContrahents, error: errorContrahents } = useSuspenseQuery(
    readContrahentsAllV1CrmContrahentsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorContrahents) {
    toast.error(errorContrahents.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataContrahents);

  // CREATE
  const createContrahentMutation = useMutation({
    ...createContrahentsV1CrmContrahentsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentsAllV1CrmContrahentsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createContrahentMutation", createContrahentMutation);

  // UPDATE

  const updateContrahentMutation = useMutation({
    ...updateContrahentsV1CrmContrahentsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentsAllV1CrmContrahentsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateContrahentMutation", updateContrahentMutation);

  // DELETE
  const deleteContrahentMutation = useMutation({
    ...deleteContrahentsV1CrmContrahentsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentsAllV1CrmContrahentsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteContrahent.mutateAsync({
  //       path: { item_id: (data as ContrahentDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataContrahents,
    createContrahentMutation,
    updateContrahentMutation,
    deleteContrahentMutation,
  };
}
