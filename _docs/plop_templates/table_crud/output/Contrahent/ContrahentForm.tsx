import {
  But<PERSON>,
  Checkbox,
  InputBase,
  NumberInput,
  Radio,
  Select,
  Switch,
  Textarea,
  TextInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm, useStore } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { IMaskInput } from "react-imask";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createContrahentsV1CrmContrahentsPostMutation,
  deleteContrahentsV1CrmContrahentsItemIdDeleteMutation,
  updateContrahentsV1CrmContrahentsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import {
  ContrahentCreateTypes,
  ContrahentDisplayTypes,
  ContrahentUpdateTypes,
} from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultContrahent = {

    id: null,
    created_at: null,
    updated_at: null,
    created_by: null,
    updated_by: null,
    name: null,
    description: null,
    lang: null,
    json_metadata: null,
    region: null,
    is_business: null,
    nip: null,
    is_vendor: null,
    is_contractor: null,
    is_regular: null,
    org_id: null,
    user_profile_id: null,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    id: z.union([z.number(), z.string()]).nullable().optional(),
    created_at: z.union([z.string(), z.date()]).nullable().optional(),
    updated_at: z.union([z.string(), z.date()]).nullable().optional(),
    created_by: z.union([z.number(), z.string()]).nullable().optional(),
    updated_by: z.union([z.number(), z.string()]).nullable().optional(),
    name: z.string().nullable().optional(),
    description: z.string().nullable().optional(),
    lang: z.string().nullable().optional(),
    json_metadata: z.record(z.any()).nullable().optional(),
    region: z.string().nullable().optional(),
    is_business: z.boolean().nullable().optional(),
    nip: z.string().nullable().optional(),
    is_vendor: z.boolean().nullable().optional(),
    is_contractor: z.boolean().nullable().optional(),
    is_regular: z.boolean().nullable().optional(),
    org_id: z.union([z.number(), z.string()]).nullable().optional(),
    user_profile_id: z.union([z.number(), z.string()]).nullable().optional(),
 })
  .passthrough();

interface PropsTypes {
  data?: ContrahentDisplayTypes;
  variant: "new" | "edit";
  setEditingRow?: (row: any) => void;
  setCreatingRow?: (row: any) => void;
  createContrahentMutation: any;
  updateContrahentMutation: any;
}

type FormData = z.infer<typeof formSchema>;

function ContrahentForm({ 
  data = defaultContrahent,
  variant = "edit",
  setEditingRow,
  setCreatingRow,
  createContrahentMutation,
  updateContrahentMutation }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(
    new Set()
  );
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as ContrahentCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as ContrahentUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);



  async function handleCreate(formData: ContrahentCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createContrahentMutation.mutateAsync({
      body: [
        {
          ...formData,
          created_by: user?.id,
         
        },
      ],
    },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setCreatingRow?.(null);
        },
      });
  }



  async function handleEdit(formData: ContrahentUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]])
    ) as ContrahentUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateContrahentMutation.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
         
        },
      ],
    },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setEditingRow?.(null);
        },
      });
  }

  const deleteContrahent = useMutation({
    ...deleteContrahentsV1CrmContrahentsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContrahentsAllV1CrmContrahentsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteContrahent.mutateAsync({
        path: { item_id: (data as ContrahentDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);


   return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit"
              ? t("forms.ContrahentForm.titleEdit.label")
              : t("forms.ContrahentForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
              {/* ID */}
              <div className={styles.span4}>
                <form.Field
                  name="id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.ContrahentForm.id.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* CREATED_AT */}
              <div className={styles.span4}>
                  <form.Field
                  name="created_at"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <DatePickerInput
                          label={t("forms.ContrahentForm.createdAt.label")}
                          value={state.value ? new Date(state.value) : null}
                          onChange={(value) => {
                          handleChange(value?.toISOString() || "" );
                          onFieldChange("created_at");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      
                      );
                  }}
                  />
              </div>              {/* UPDATED_AT */}
              <div className={styles.span4}>
                  <form.Field
                  name="updated_at"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <DatePickerInput
                          label={t("forms.ContrahentForm.updatedAt.label")}
                          value={state.value ? new Date(state.value) : null}
                          onChange={(value) => {
                          handleChange(value?.toISOString() || "" );
                          onFieldChange("updated_at");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      
                      );
                  }}
                  />
              </div>              {/* CREATED_BY */}
              <div className={styles.span4}>
                <form.Field
                  name="created_by"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.ContrahentForm.createdBy.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("created_by");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* UPDATED_BY */}
              <div className={styles.span4}>
                <form.Field
                  name="updated_by"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.ContrahentForm.updatedBy.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("updated_by");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              
              
              
              
              
              {/* NAME */}
              <div className={styles.span4}>
                  <form.Field
                  name="name"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.ContrahentForm.name.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("name");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* DESCRIPTION */}
              <div className={styles.span4}>
                  <form.Field
                  name="description"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <Textarea
                          label={t("forms.ContrahentForm.description.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("description");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Description"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              
              
              
              
              
              {/* LANG */}
              <div className={styles.span4}>
                  <form.Field
                  name="lang"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.ContrahentForm.lang.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("lang");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* JSON_METADATA */}
              <div className={styles.span4}>
                  <form.Field
                  name="json_metadata"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <Textarea
                          label={t("forms.ContrahentForm.jsonMetadata.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("json_metadata");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Description"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              
              
              
              
              
              {/* REGION */}
              <div className={styles.span4}>
                  <form.Field
                  name="region"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.ContrahentForm.region.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("region");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* IS_BUSINESS */}
              <div className={styles.span4}>
                  <form.Field
                    name="is_business"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.ContrahentForm.isBusiness.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("is_business");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
              {/* NIP */}
              <div className={styles.span4}>
              <form.Field
                name="nip"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 000"
                      label={t("forms.ContrahentForm.nip.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("nip");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      onFieldChange("nip");
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />              {/* IS_VENDOR */}
              <div className={styles.span4}>
                  <form.Field
                    name="is_vendor"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.ContrahentForm.isVendor.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("is_vendor");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
              {/* IS_CONTRACTOR */}
              <div className={styles.span4}>
                  <form.Field
                    name="is_contractor"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.ContrahentForm.isContractor.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("is_contractor");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
              {/* IS_REGULAR */}
              <div className={styles.span4}>
                  <form.Field
                    name="is_regular"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.ContrahentForm.isRegular.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("is_regular");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
              {/* ORG_ID */}
              <div className={styles.span4}>
                <form.Field
                  name="org_id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.ContrahentForm.orgId.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("org_id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* USER_PROFILE_ID */}
              <div className={styles.span4}>
                <form.Field
                  name="user_profile_id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.ContrahentForm.userProfileId.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("user_profile_id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={!canSubmit || changedFields.size === 0}
                  loading={isSubmitting}
                >
                  {variant === "edit"
                    ? t("common.save.label")
                    : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" &&
              (data as ContrahentDisplayTypes)?.id !== user.curr_org_id && (
                <Button
                  color="red"
                  loading={updateContrahentMutation.isPending || createContrahentMutation.isPending}
                  // disabled
                  onClick={handleDelete}
                >
                  {t("common.delete.label")}
                </Button>
              )}
          </div>
        </form>
      </div>
    </>
  );
};

export default ContrahentForm;

