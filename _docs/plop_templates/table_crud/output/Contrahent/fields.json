[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "created_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "region", "inputType": "text", "defaultValue": "eu", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "is_business", "inputType": "switch", "defaultValue": "true", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "nip", "inputType": "numberMask", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "is_vendor", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_contractor", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_regular", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "org_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "user_profile_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}]