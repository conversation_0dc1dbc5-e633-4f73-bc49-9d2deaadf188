import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createOrgSplitsV1MoneyOrgSplitsPostMutation,
  deleteOrgSplitsV1MoneyOrgSplitsItemIdDeleteMutation,
  readOrgSplitsAllV1MoneyOrgSplitsGetOptions,
  updateOrgSplitsV1MoneyOrgSplitsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { OrgSplitCreateTypes, OrgSplitUpdateTypes } from "@/client";

// type Props = {
//   updateData: OrgSplitUpdateTypes
//   createData: OrgSplitCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useOrgSplitData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataOrgSplits, error: errorOrgSplits } = useSuspenseQuery(
    readOrgSplitsAllV1MoneyOrgSplitsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorOrgSplits) {
    toast.error(errorOrgSplits.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataOrgSplits);

  // CREATE
  const createOrgSplitMutation = useMutation({
    ...createOrgSplitsV1MoneyOrgSplitsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgSplitsAllV1MoneyOrgSplitsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createOrgSplitMutation", createOrgSplitMutation);

  // UPDATE

  const updateOrgSplitMutation = useMutation({
    ...updateOrgSplitsV1MoneyOrgSplitsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgSplitsAllV1MoneyOrgSplitsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateOrgSplitMutation", updateOrgSplitMutation);

  // DELETE
  const deleteOrgSplitMutation = useMutation({
    ...deleteOrgSplitsV1MoneyOrgSplitsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgSplitsAllV1MoneyOrgSplitsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteOrgSplit.mutateAsync({
  //       path: { item_id: (data as OrgSplitDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataOrgSplits,
    createOrgSplitMutation,
    updateOrgSplitMutation,
    deleteOrgSplitMutation,
  };
}
