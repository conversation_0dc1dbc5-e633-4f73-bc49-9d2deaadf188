import { Button, NumberInput, Select, Switch, Textarea, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import { deleteOrgSplitsV1MoneyOrgSplitsItemIdDeleteMutation } from "@/api/_client/@tanstack/react-query.gen";
import { OrgSplitCreateTypes, OrgSplitDisplayTypes, OrgSplitUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultOrgSplit = {
  id: null,
  created_at: null,
  updated_at: null,
  created_by: null,
  updated_by: null,
  lang: null,
  json_metadata: null,
  name: null,
  description: null,
  account_id: null,
  transaction_id: null,
  debit: null,
  credit: null,
  due_date: null,
  memo: null,
  date: null,
  is_debit_minus: null,
  is_schedule: null,
  set_id: null,
  set_item_id: null,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    id: z.union([z.number(), z.string()]).nullable().optional(),
    created_at: z.union([z.string(), z.date()]).nullable().optional(),
    updated_at: z.union([z.string(), z.date()]).nullable().optional(),
    created_by: z.union([z.number(), z.string()]).nullable().optional(),
    updated_by: z.union([z.number(), z.string()]).nullable().optional(),
    lang: z.string().nullable().optional(),
    json_metadata: z.record(z.any()).nullable().optional(),
    name: z.string().nullable().optional(),
    description: z.string().nullable().optional(),
    account_id: z.union([z.number(), z.string()]).nullable().optional(),
    transaction_id: z.union([z.number(), z.string()]).nullable().optional(),
    debit: z.union([z.number(), z.string()]).nullable().optional(),
    credit: z.union([z.number(), z.string()]).nullable().optional(),
    due_date: z.union([z.string(), z.date()]).nullable().optional(),
    memo: z.string().nullable().optional(),
    date: z.union([z.string(), z.date()]).nullable().optional(),
    is_debit_minus: z.boolean().nullable().optional(),
    is_schedule: z.boolean().nullable().optional(),
    set_id: z.union([z.number(), z.string()]).nullable().optional(),
    set_item_id: z.union([z.number(), z.string()]).nullable().optional(),
  })
  .passthrough();

interface PropsTypes {
  data?: OrgSplitDisplayTypes;
  variant: "new" | "edit";
  setEditingRow?: (row: any) => void;
  setCreatingRow?: (row: any) => void;
  createOrgSplitMutation: any;
  updateOrgSplitMutation: any;
}

type FormData = z.infer<typeof formSchema>;

function OrgSplitForm({
  data = defaultOrgSplit,
  variant = "edit",
  setEditingRow,
  setCreatingRow,
  createOrgSplitMutation,
  updateOrgSplitMutation,
}: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as OrgSplitCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as OrgSplitUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  async function handleCreate(formData: OrgSplitCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createOrgSplitMutation.mutateAsync(
      {
        body: [
          {
            ...formData,
            created_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setCreatingRow?.(null);
        },
      },
    );
  }

  async function handleEdit(formData: OrgSplitUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]]),
    ) as OrgSplitUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateOrgSplitMutation.mutateAsync(
      {
        body: [
          {
            ...updatedFields,
            id: data?.id,
            updated_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setEditingRow?.(null);
        },
      },
    );
  }

  const deleteOrgSplit = useMutation({
    ...deleteOrgSplitsV1MoneyOrgSplitsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgSplitsAllV1MoneyOrgSplitsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteOrgSplit.mutateAsync({
        path: { item_id: (data as OrgSplitDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit" ? t("forms.OrgSplitForm.titleEdit.label") : t("forms.OrgSplitForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
            {/* ID */}
            <div className={styles.span4}>
              <form.Field
                name="id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.id.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* CREATED_AT */}
            <div className={styles.span4}>
              <form.Field
                name="created_at"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.OrgSplitForm.createdAt.label")}
                      value={state.value ? new Date(state.value) : null}
                      onChange={(value) => {
                        handleChange(value?.toISOString() || "");
                        onFieldChange("created_at");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* UPDATED_AT */}
            <div className={styles.span4}>
              <form.Field
                name="updated_at"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.OrgSplitForm.updatedAt.label")}
                      value={state.value ? new Date(state.value) : null}
                      onChange={(value) => {
                        handleChange(value?.toISOString() || "");
                        onFieldChange("updated_at");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* CREATED_BY */}
            <div className={styles.span4}>
              <form.Field
                name="created_by"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.createdBy.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("created_by");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* UPDATED_BY */}
            <div className={styles.span4}>
              <form.Field
                name="updated_by"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.updatedBy.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("updated_by");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* LANG */}
            <div className={styles.span4}>
              <form.Field
                name="lang"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgSplitForm.lang.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("lang");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* JSON_METADATA */}
            <div className={styles.span4}>
              <form.Field
                name="json_metadata"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.OrgSplitForm.jsonMetadata.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("json_metadata");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* NAME */}
            <div className={styles.span4}>
              <form.Field
                name="name"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgSplitForm.name.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DESCRIPTION */}
            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.OrgSplitForm.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* ACCOUNT_ID */}
            <div className={styles.span4}>
              <form.Field
                name="account_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Select
                      label={t("forms.OrgSplitForm.accountId.label")}
                      value={state.value || ""}
                      placeholder={t("forms.OrgSplitForm.accountId.placeholder.label")}
                      data={[]}
                      onChange={(value) => {
                        handleChange(value);
                        onFieldChange("account_id");
                      }}
                      jhkjhlkjh
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* TRANSACTION_ID */}
            <div className={styles.span4}>
              <form.Field
                name="transaction_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.transactionId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("transaction_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DEBIT */}
            <div className={styles.span4}>
              <form.Field
                name="debit"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.debit.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("debit");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* CREDIT */}
            <div className={styles.span4}>
              <form.Field
                name="credit"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.credit.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("credit");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DUE_DATE */}
            <div className={styles.span4}>
              <form.Field
                name="due_date"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.OrgSplitForm.dueDate.label")}
                      value={state.value ? new Date(state.value) : null}
                      onChange={(value) => {
                        handleChange(value?.toISOString() || "");
                        onFieldChange("due_date");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* MEMO */}
            <div className={styles.span4}>
              <form.Field
                name="memo"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.OrgSplitForm.memo.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("memo");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DATE */}
            <div className={styles.span4}>
              <form.Field
                name="date"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.OrgSplitForm.date.label")}
                      value={state.value ? new Date(state.value) : null}
                      onChange={(value) => {
                        handleChange(value?.toISOString() || "");
                        onFieldChange("date");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* IS_DEBIT_MINUS */}
            <div className={styles.span4}>
              <form.Field
                name="is_debit_minus"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgSplitForm.isDebitMinus.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_debit_minus");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* IS_SCHEDULE */}
            <div className={styles.span4}>
              <form.Field
                name="is_schedule"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.OrgSplitForm.isSchedule.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_schedule");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* SET_ID */}
            <div className={styles.span4}>
              <form.Field
                name="set_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.setId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("set_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* SET_ITEM_ID */}
            <div className={styles.span4}>
              <form.Field
                name="set_item_id"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.OrgSplitForm.setItemId.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("set_item_id");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0} loading={isSubmitting}>
                  {variant === "edit" ? t("common.save.label") : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" && (data as OrgSplitDisplayTypes)?.id !== user.curr_org_id && (
              <Button
                color="red"
                loading={updateOrgSplitMutation.isPending || createOrgSplitMutation.isPending}
                // disabled
                onClick={handleDelete}
              >
                {t("common.delete.label")}
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
}

export default OrgSplitForm;
