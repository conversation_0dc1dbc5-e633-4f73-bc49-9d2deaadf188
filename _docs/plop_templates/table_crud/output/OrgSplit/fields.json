[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "created_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "account_id", "inputType": "select", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "transaction_id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "debit", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "credit", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "due_date", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "memo", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "date", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "is_debit_minus", "inputType": "switch", "defaultValue": "false", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_schedule", "inputType": "switch", "defaultValue": "false", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "set_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "set_item_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}]