import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createOrgSplitsV1MoneyOrgSplitsPostMutation,
  readOrgSplitsAllV1MoneyOrgSplitsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import OrgSplitsTable from "./-components/OrgSplitsTable";
import { useOrgSplitData } from "./-data_hooks/useOrgSplitData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readOrgSplitsAllV1MoneyOrgSplitsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "OrgSplit Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataOrgSplits, createOrgSplitMutation, updateOrgSplitMutation, deleteOrgSplitMutation } = useOrgSplitData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <OrgSplitsTable
          data={dataOrgSplits.data}
          createOrgSplitMutation={createOrgSplitMutation}
          updateOrgSplitMutation={updateOrgSplitMutation}
          deleteOrgSplitMutation={deleteOrgSplitMutation}
        />
      </div>
    </Suspense>
  );
}
