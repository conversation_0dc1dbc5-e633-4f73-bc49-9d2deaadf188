[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "created_at", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.union([z.date(), z.string()])"}, {"name": "updated_at", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.union([z.date(), z.string()])"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "updated_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).optional()"}, {"name": "name", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "pl", "valueType": "string", "zodType": "z.string()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.any().optional()"}, {"name": "proposal_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).optional()"}, {"name": "status", "inputType": "text", "defaultValue": "draft", "valueType": "string", "zodType": "z.string()"}, {"name": "start_date", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.union([z.date(), z.string()])"}, {"name": "end_date", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.union([z.date(), z.string()])"}, {"name": "budget", "inputType": "number", "defaultValue": "0.0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "org_id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "accepted_offer_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).optional()"}]