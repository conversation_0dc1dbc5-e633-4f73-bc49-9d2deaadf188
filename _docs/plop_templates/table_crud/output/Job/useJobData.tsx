import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createJobsV1CoreJobsPostMutation,
  deleteJobsV1CoreJobsItemIdDeleteMutation,
  readJobsAllV1CoreJobsGetOptions,
  updateJobsV1CoreJobsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { JobCreateTypes, JobUpdateTypes } from "@/client";

// type Props = {
//   updateData: JobUpdateTypes
//   createData: JobCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useJobData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataJobs, error: errorJobs } = useSuspenseQuery(
    readJobsAllV1CoreJobsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorJobs) {
    toast.error(errorJobs.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataJobs);

  // CREATE
  const createJobMutation = useMutation({
    ...createJobsV1CoreJobsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readJobsAllV1CoreJobsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createJobMutation", createJobMutation);

  // UPDATE

  const updateJobMutation = useMutation({
    ...updateJobsV1CoreJobsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readJobsAllV1CoreJobsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateJobMutation", updateJobMutation);

  // DELETE
  const deleteJobMutation = useMutation({
    ...deleteJobsV1CoreJobsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readJobsAllV1CoreJobsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteJob.mutateAsync({
  //       path: { item_id: (data as JobDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataJobs,
    createJobMutation,
    updateJobMutation,
    deleteJobMutation,
  };
}
