import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createJobsV1CoreJobsPostMutation,
  readJobsAllV1CoreJobsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import JobsTable from "./-components/JobsTable";
import { useJobData } from "./-data_hooks/useJobData";

export const Route = createFileRoute("/dashboard/jobs/")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readJobsAllV1CoreJobsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Job Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/dashboard/jobs/"
      if (toLocation.pathname !== "/dashboard/jobs/") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataJobs, createJobMutation, updateJobMutation, deleteJobMutation } = useJobData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <JobsTable
          data={dataJobs.data}
          createJobMutation={createJobMutation}
          updateJobMutation={updateJobMutation}
          deleteJobMutation={deleteJobMutation}
        />
      </div>
    </Suspense>
  );
}
