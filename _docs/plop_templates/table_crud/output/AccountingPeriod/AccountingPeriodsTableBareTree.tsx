"use no memo";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import {
  ActionIcon,
  Button,
  Flex,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import {
  IconAd,
  IconCornerDownRightDouble,
  IconEdit,
  IconPlus,
  IconSearch,
  IconTrash,
} from "@tabler/icons-react";
import { useNavigate } from "@tanstack/react-router";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  MRT_EditActionButtons,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  AccountingPeriodDisplayTypes,
  AccountingPeriodTypeDisplayTypes,
  AccountingPeriodUpdateTypes,
} from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import AccountingPeriodForm from "@/routes/objects/-forms/AccountingPeriodForm";
import type { RootStateTypes } from "@/utils/redux/store";

type PropsTypes = {
  data: AccountingPeriodDisplayTypes[];
  objectTypes: AccountingPeriodTypeDisplayTypes[];
  createAccountingPeriodMutation: any;
  updateAccountingPeriodMutation: any;
  deleteAccountingPeriodMutation: any;
};

const AccountingPeriodsTable = ({
  data,
  objectTypes,
  createAccountingPeriodMutation,
  updateAccountingPeriodMutation,
  deleteAccountingPeriodMutation,
}: PropsTypes) => {
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);
  const navigate = useNavigate();

  function getNestedMargin(level: number) {
    return level * 30;
  }

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<AccountingPeriodDisplayTypes>[]>(
    () => [
      {
      accessorKey: "id",
      header: "Id",
      },
      {
      accessorKey: "created_at",
      header: "Created_at",
      },
      {
      accessorKey: "updated_at",
      header: "Updated_at",
      },
      {
      accessorKey: "created_by",
      header: "Created_by",
      },
      {
      accessorKey: "updated_by",
      header: "Updated_by",
      },
      {
      accessorKey: "lang",
      header: "Lang",
      },
      {
      accessorKey: "json_metadata",
      header: "Json_metadata",
      },
      {
      accessorKey: "name",
      header: "Name",
      },
      {
      accessorKey: "description",
      header: "Description",
      },
      {
      accessorKey: "org_id",
      header: "Org_id",
      },
      {
      accessorKey: "start",
      header: "Start",
      },
      {
      accessorKey: "end",
      header: "End",
      },
      {
        accessorKey: "name",
        header: "Name",
        mantineTableBodyCellProps: {
          style: { width: 500 },
        },
        Cell: ({ row }) => (
          <Text
            style={{{
              width: "100%",
              fontWeight:
                row.getCanExpand() && !row.getIsExpanded() ? "bold" : "normal",
              fontSize: "18px",
              marginLeft: getNestedMargin(row.original.tree_level),
            }}}
          >
            {row.original.name}
          </Text>
        ),
      },

      {
        accessorKey: "object_type",
        header: "Type",
        mantineTableBodyCellProps: {
          style: { width: 100 },
        },
      },

      {
        id: "actions",
        accessorKey: "actions",
        header: "",
        enableColumnOrdering: false,
        enableEditing: false,

        enableSorting: false,
        mantineTableBodyCellProps: {
          style: { width: 50 },
        },
        Cell: ({ row }) => (
          <Flex gap="md" justify="center">
            <Tooltip label="View">
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate({ to: `/objects/list/${row.original.id}` })
                }}
              >
                <IconSearch />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Add sub object">
              <ActionIcon
                variant="subtle"
                color="violet"
                onClick={() =>
                  navigate({
                    to: `/objects/new/?parentId=${row.original.id}&treeLevel=${row.original.tree_level + 1}`,
                  })
                }
              >
                <IconCornerDownRightDouble />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Edit">
              <ActionIcon
                variant="subtle"
                onClick={(e) => {
                  e.stopPropagation();
                  table.setEditingRow(row);
                }}
              >
                <IconEdit />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Delete">
              <ActionIcon
                variant="subtle"
                color="red"
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteConfirmModal(row);
                }}
                disabled={row.getCanExpand()}
              >
                <IconTrash />
              </ActionIcon>
            </Tooltip>
          </Flex>
        ),
      },
    ],
    []
  );

  //CREATE action
  const handleCreateAccountingPeriod: MRT_TableOptions<AccountingPeriodUpdateTypes>["onCreatingRowSave"] =
    async ({ values, exitCreatingMode }) => {
      // await createAccountingPeriodMutation.mutateAsync({
      //   body: [
      //     {
      //       ...values,
      //       created_by: user?.id,
      //       org_id: user?.curr_org_id,
      //     },
      //   ],
      // });
      exitCreatingMode();
    };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<AccountingPeriodUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: (
        <Text>Are you sure you want to delete {row.original.name} </Text>
      ),
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteAccountingPeriodMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization:
      currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL,
    enableBottomToolbar: false, // FOOTER
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    enableGlobalFilter: false,
    enableHiding: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      // striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    // enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    enableExpanding: true,
    getSubRows: (originalRow) => originalRow.subRows,
    onCreatingRowSave: handleCreateAccountingPeriod,
    // onEditingRowCancel: () => setValidationErrors({}),
    // onEditingRowSave: handleUpdateAccountingPeriod,
     renderCreateRowModalContent: ({ table, row }) => (
      <AccountingPeriodForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createAccountingPeriodMutation={createAccountingPeriodMutation}
        updateAccountingPeriodMutation={updateAccountingPeriodMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <AccountingPeriodForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createAccountingPeriodMutation={createAccountingPeriodMutation}
        updateAccountingPeriodMutation={updateAccountingPeriodMutation}
      />
    ),

    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Bazowy AccountingPeriod
      </Button>
    ),
    mantineTableBodyRowProps: ({ row, table }) => ({
      onClick: (event) => {
        console.info(event, row.id);
        row.toggleExpanded(!row.getIsExpanded());
      },
      style: {
        cursor: row.getCanExpand() ? "pointer" : "default", //you might want to change the cursor too when adding an onClick
      },
    }),
  });

  return <MantineReactTable table={table} />;
};

export default AccountingPeriodsTable;
