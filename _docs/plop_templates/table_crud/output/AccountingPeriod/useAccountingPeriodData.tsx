import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createAccountingPeriodsV1MoneyAccountingPeriodsPostMutation,
  deleteAccountingPeriodsV1MoneyAccountingPeriodsItemIdDeleteMutation,
  readAccountingPeriodsAllV1MoneyAccountingPeriodsGetOptions,
  updateAccountingPeriodsV1MoneyAccountingPeriodsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { AccountingPeriodCreateTypes, AccountingPeriodUpdateTypes } from "@/client";

// type Props = {
//   updateData: AccountingPeriodUpdateTypes
//   createData: AccountingPeriodCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useAccountingPeriodData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataAccountingPeriods, error: errorAccountingPeriods } = useSuspenseQuery(
    readAccountingPeriodsAllV1MoneyAccountingPeriodsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorAccountingPeriods) {
    toast.error(errorAccountingPeriods.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataAccountingPeriods);

  // CREATE
  const createAccountingPeriodMutation = useMutation({
    ...createAccountingPeriodsV1MoneyAccountingPeriodsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAccountingPeriodsAllV1MoneyAccountingPeriodsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createAccountingPeriodMutation", createAccountingPeriodMutation);

  // UPDATE

  const updateAccountingPeriodMutation = useMutation({
    ...updateAccountingPeriodsV1MoneyAccountingPeriodsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAccountingPeriodsAllV1MoneyAccountingPeriodsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateAccountingPeriodMutation", updateAccountingPeriodMutation);

  // DELETE
  const deleteAccountingPeriodMutation = useMutation({
    ...deleteAccountingPeriodsV1MoneyAccountingPeriodsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAccountingPeriodsAllV1MoneyAccountingPeriodsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteAccountingPeriod.mutateAsync({
  //       path: { item_id: (data as AccountingPeriodDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataAccountingPeriods,
    createAccountingPeriodMutation,
    updateAccountingPeriodMutation,
    deleteAccountingPeriodMutation,
  };
}
