[{"name": "id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "title", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "motivation", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "org_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "votes_yes", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "votes_no", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "votes_neutral", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "proposed_budget", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "budget", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}, {"name": "status", "inputType": "select", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "is_formal", "inputType": "switch", "defaultValue": null, "valueType": "boolean", "zodType": "z.boolean().nullish().optional()"}, {"name": "vote_open", "inputType": "switch", "defaultValue": null, "valueType": "boolean", "zodType": "z.boolean().nullish().optional()"}, {"name": "voting_by_shares", "inputType": "switch", "defaultValue": null, "valueType": "boolean", "zodType": "z.boolean().nullish().optional()"}, {"name": "rownowaga_resolution", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "proposed_end_date", "inputType": "date", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullish().optional()"}, {"name": "parent_wniosek_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullish().optional()"}]