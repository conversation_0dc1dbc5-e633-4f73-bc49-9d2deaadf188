import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createProposalsV1CoreProposalsPostMutation,
  readProposalsAllV1CoreProposalsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import ProposalsTable from "./-components/ProposalsTable";
import { useProposalData } from "./-data_hooks/useProposalData";

export const Route = createFileRoute("/proposals/")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readProposalsAllV1CoreProposalsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "proposals" }));
  dispatch(updateSystemField({ pageTitle: "Proposal Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/proposals/"
      if (toLocation.pathname !== "/proposals/") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataProposals, createProposalMutation, updateProposalMutation, deleteProposalMutation } = useProposalData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <ProposalsTable
          data={dataProposals.data}
          createProposalMutation={createProposalMutation}
          updateProposalMutation={updateProposalMutation}
          deleteProposalMutation={deleteProposalMutation}
        />
      </div>
    </Suspense>
  );
}
