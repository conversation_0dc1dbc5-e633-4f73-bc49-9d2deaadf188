"use no memo";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import {
  ActionIcon,
  Button,
  Flex,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import {
  IconAd,
  IconCornerDownRightDouble,
  IconEdit,
  IconPlus,
  IconSearch,
  IconTrash,
} from "@tabler/icons-react";
import { useNavigate } from "@tanstack/react-router";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  MRT_EditActionButtons,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  ProposalDisplayTypes,
  ProposalTypeDisplayTypes,
  ProposalUpdateTypes,
} from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import ProposalForm from "@/routes/objects/-forms/ProposalForm";
import type { RootStateTypes } from "@/utils/redux/store";

type PropsTypes = {
  data: ProposalDisplayTypes[];
  objectTypes: ProposalTypeDisplayTypes[];
  createProposalMutation: any;
  updateProposalMutation: any;
  deleteProposalMutation: any;
};

const ProposalsTable = ({
  data,
  objectTypes,
  createProposalMutation,
  updateProposalMutation,
  deleteProposalMutation,
}: PropsTypes) => {
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);
  const navigate = useNavigate();

  function getNestedMargin(level: number) {
    return level * 30;
  }

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<ProposalDisplayTypes>[]>(
    () => [
      {
      accessorKey: "id",
      header: "Id",
      },
      {
      accessorKey: "created_by",
      header: "Created_by",
      },
      {
      accessorKey: "json_metadata",
      header: "Json_metadata",
      },
      {
      accessorKey: "title",
      header: "Title",
      },
      {
      accessorKey: "description",
      header: "Description",
      },
      {
      accessorKey: "motivation",
      header: "Motivation",
      },
      {
      accessorKey: "org_id",
      header: "Org_id",
      },
      {
      accessorKey: "votes_yes",
      header: "Votes_yes",
      },
      {
      accessorKey: "votes_no",
      header: "Votes_no",
      },
      {
      accessorKey: "votes_neutral",
      header: "Votes_neutral",
      },
      {
      accessorKey: "proposed_budget",
      header: "Proposed_budget",
      },
      {
      accessorKey: "budget",
      header: "Budget",
      },
      {
      accessorKey: "status",
      header: "Status",
      },
      {
      accessorKey: "is_formal",
      header: "Is_formal",
      },
      {
      accessorKey: "vote_open",
      header: "Vote_open",
      },
      {
      accessorKey: "voting_by_shares",
      header: "Voting_by_shares",
      },
      {
      accessorKey: "rownowaga_resolution",
      header: "Rownowaga_resolution",
      },
      {
      accessorKey: "proposed_end_date",
      header: "Proposed_end_date",
      },
      {
      accessorKey: "parent_wniosek_id",
      header: "Parent_wniosek_id",
      },
      {
        accessorKey: "name",
        header: "Name",
        mantineTableBodyCellProps: {
          style: { width: 500 },
        },
        Cell: ({ row }) => (
          <Text
            style={{{
              width: "100%",
              fontWeight:
                row.getCanExpand() && !row.getIsExpanded() ? "bold" : "normal",
              fontSize: "18px",
              marginLeft: getNestedMargin(row.original.tree_level),
            }}}
          >
            {row.original.name}
          </Text>
        ),
      },

      {
        accessorKey: "object_type",
        header: "Type",
        mantineTableBodyCellProps: {
          style: { width: 100 },
        },
      },

      {
        id: "actions",
        accessorKey: "actions",
        header: "",
        enableColumnOrdering: false,
        enableEditing: false,

        enableSorting: false,
        mantineTableBodyCellProps: {
          style: { width: 50 },
        },
        Cell: ({ row }) => (
          <Flex gap="md" justify="center">
            <Tooltip label="View">
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={() =>
                  navigate({ to: `/objects/list/${row.original.id}` })
                }
              >
                <IconSearch />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Add sub object">
              <ActionIcon
                variant="subtle"
                color="violet"
                onClick={() =>
                  navigate({
                    to: `/objects/new/?parentId=${row.original.id}&treeLevel=${row.original.tree_level + 1}`,
                  })
                }
              >
                <IconCornerDownRightDouble />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Edit">
              <ActionIcon
                variant="subtle"
                onClick={() => {
                  console.log("row", row);
                  table.setEditingRow(row);
                }}
              >
                <IconEdit />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Delete">
              <ActionIcon
                variant="subtle"
                color="red"
                onClick={() => openDeleteConfirmModal(row)}
                disabled={row.getCanExpand()}
              >
                <IconTrash />
              </ActionIcon>
            </Tooltip>
          </Flex>
        ),
      },
    ],
    []
  );

  //CREATE action
  const handleCreateProposal: MRT_TableOptions<ProposalUpdateTypes>["onCreatingRowSave"] =
    async ({ values, exitCreatingMode }) => {
      // await createProposalMutation.mutateAsync({
      //   body: [
      //     {
      //       ...values,
      //       created_by: user?.id,
      //       org_id: user?.curr_org_id,
      //     },
      //   ],
      // });
      exitCreatingMode();
    };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<ProposalUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: (
        <Text>Are you sure you want to delete {row.original.name} </Text>
      ),
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteProposalMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization:
      currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL,
    enableBottomToolbar: false, // FOOTER
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    enableGlobalFilter: false,
    enableHiding: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      // striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    // enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    enableExpanding: true,
    getSubRows: (originalRow) => originalRow.subRows,
    onCreatingRowSave: handleCreateProposal,
    // onEditingRowCancel: () => setValidationErrors({}),
    // onEditingRowSave: handleUpdateProposal,
     renderCreateRowModalContent: ({ table, row }) => (
      <ProposalForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createProposalMutation={createProposalMutation}
        updateProposalMutation={updateProposalMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <ProposalForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createProposalMutation={createProposalMutation}
        updateProposalMutation={updateProposalMutation}
      />
    ),

    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Bazowy Proposal
      </Button>
    ),
    mantineTableBodyRowProps: ({ row, table }) => ({
      onClick: (event) => {
        console.info(event, row.id);
        row.toggleExpanded(!row.getIsExpanded());
      },
      style: {
        cursor: row.getCanExpand() ? "pointer" : "default", //you might want to change the cursor too when adding an onClick
      },
    }),
  });

  return <MantineReactTable table={table} />;
};

export default ProposalsTable;
