import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createProposalsV1CoreProposalsPostMutation,
  deleteProposalsV1CoreProposalsItemIdDeleteMutation,
  readProposalsAllV1CoreProposalsGetOptions,
  updateProposalsV1CoreProposalsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ProposalCreateTypes, ProposalUpdateTypes } from "@/client";

// type Props = {
//   updateData: ProposalUpdateTypes
//   createData: ProposalCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useProposalData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataProposals, error: errorProposals } = useSuspenseQuery(
    readProposalsAllV1CoreProposalsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorProposals) {
    toast.error(errorProposals.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataProposals);

  // CREATE
  const createProposalMutation = useMutation({
    ...createProposalsV1CoreProposalsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProposalsAllV1CoreProposalsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createProposalMutation", createProposalMutation);

  // UPDATE

  const updateProposalMutation = useMutation({
    ...updateProposalsV1CoreProposalsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProposalsAllV1CoreProposalsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateProposalMutation", updateProposalMutation);

  // DELETE
  const deleteProposalMutation = useMutation({
    ...deleteProposalsV1CoreProposalsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProposalsAllV1CoreProposalsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteProposal.mutateAsync({
  //       path: { item_id: (data as ProposalDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataProposals,
    createProposalMutation,
    updateProposalMutation,
    deleteProposalMutation,
  };
}
