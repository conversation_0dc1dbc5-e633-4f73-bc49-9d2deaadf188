import { <PERSON><PERSON>, Checkbox, InputBase, NumberInput, Radio, Select, Switch, Textarea, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm, useStore } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { IMaskInput } from "react-imask";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createProblemsV1CoreProblemsPostMutation,
  deleteProblemsV1CoreProblemsItemIdDeleteMutation,
  updateProblemsV1CoreProblemsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { ProblemCreateTypes, ProblemDisplayTypes, ProblemUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultProblem = {
  description: null,
  status: null,
  closed_date: null,
  objects: null,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    description: z.string().nullable().optional(),
    status: z.string(),
    closed_date: z.string().nullable().optional(),
    objects: z.array(z.any()).nullable().optional(),
  })
  .passthrough();

interface PropsTypes {
  data?: ProblemDisplayTypes;
  variant: "new" | "edit";
  setEditingRow?: (row: any) => void;
  setCreatingRow?: (row: any) => void;
  createProblemMutation: any;
  updateProblemMutation: any;
}

type FormData = z.infer<typeof formSchema>;

function ProblemForm({
  data = defaultProblem,
  variant = "edit",
  setEditingRow,
  setCreatingRow,
  createProblemMutation,
  updateProblemMutation,
}: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as ProblemCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as ProblemUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  async function handleCreate(formData: ProblemCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createProblemMutation.mutateAsync(
      {
        body: [
          {
            ...formData,
            created_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setCreatingRow?.(null);
        },
      },
    );
  }

  async function handleEdit(formData: ProblemUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]]),
    ) as ProblemUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateProblemMutation.mutateAsync(
      {
        body: [
          {
            ...updatedFields,
            id: data?.id,
            updated_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setEditingRow?.(null);
        },
      },
    );
  }

  const deleteProblem = useMutation({
    ...deleteProblemsV1CoreProblemsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProblemsAllV1CoreProblemsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteProblem.mutateAsync({
        path: { item_id: (data as ProblemDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit" ? t("forms.ProblemForm.titleEdit.label") : t("forms.ProblemForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
            {/* DESCRIPTION */}
            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.ProblemForm.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* STATUS */}
            <div className={styles.span4}>
              <form.Field
                name="status"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Select
                      label={t("forms.ProblemForm.status.label")}
                      value={state.value || ""}
                      placeholder={t("forms.ProblemForm.status.placeholder.label")}
                      data={objectTypes.map((type) => ({
                        value: type.name,
                        label: type.label,
                      }))}
                      onChange={(value) => {
                        handleChange(value);
                        onFieldChange("status");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* CLOSED_DATE */}
            <div className={styles.span4}>
              <form.Field
                name="closed_date"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.ProblemForm.closedDate.label")}
                      value={state.value ? new Date(state.value) : null}
                      onChange={(value) => {
                        handleChange(value?.toISOString() || "");
                        onFieldChange("closed_date");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* OBJECTS */}
            <div className={styles.span4}>
              <form.Field
                name="objects"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.ProblemForm.objects.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("objects");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0} loading={isSubmitting}>
                  {variant === "edit" ? t("common.save.label") : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" && (data as ProblemDisplayTypes)?.id !== user.curr_org_id && (
              <Button
                color="red"
                loading={updateProblemMutation.isPending || createProblemMutation.isPending}
                // disabled
                onClick={handleDelete}
              >
                {t("common.delete.label")}
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
}

export default ProblemForm;
