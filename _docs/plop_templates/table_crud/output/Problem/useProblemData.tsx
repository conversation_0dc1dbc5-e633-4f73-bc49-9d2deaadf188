import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createProblemsV1CoreProblemsPostMutation,
  deleteProblemsV1CoreProblemsItemIdDeleteMutation,
  readProblemsAllV1CoreProblemsGetOptions,
  updateProblemsV1CoreProblemsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ProblemCreateTypes, ProblemUpdateTypes } from "@/client";

// type Props = {
//   updateData: ProblemUpdateTypes
//   createData: ProblemCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useProblemData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataProblems, error: errorProblems } = useSuspenseQuery(
    readProblemsAllV1CoreProblemsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorProblems) {
    toast.error(errorProblems.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataProblems);

  // CREATE
  const createProblemMutation = useMutation({
    ...createProblemsV1CoreProblemsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProblemsAllV1CoreProblemsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createProblemMutation", createProblemMutation);

  // UPDATE

  const updateProblemMutation = useMutation({
    ...updateProblemsV1CoreProblemsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProblemsAllV1CoreProblemsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateProblemMutation", updateProblemMutation);

  // DELETE
  const deleteProblemMutation = useMutation({
    ...deleteProblemsV1CoreProblemsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProblemsAllV1CoreProblemsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteProblem.mutateAsync({
  //       path: { item_id: (data as ProblemDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataProblems,
    createProblemMutation,
    updateProblemMutation,
    deleteProblemMutation,
  };
}
