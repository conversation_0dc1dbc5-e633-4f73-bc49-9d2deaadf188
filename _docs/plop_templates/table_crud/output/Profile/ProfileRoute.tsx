import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createProfilesV1CrmProfilesPostMutation,
  readProfilesAllV1CrmProfilesGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import ProfilesTable from "./-components/ProfilesTable";
import { useProfileData } from "./-data_hooks/useProfileData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readProfilesAllV1CrmProfilesGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Profile Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataProfiles, createProfileMutation, updateProfileMutation, deleteProfileMutation } = useProfileData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <ProfilesTable
          data={dataProfiles.data}
          createProfileMutation={createProfileMutation}
          updateProfileMutation={updateProfileMutation}
          deleteProfileMutation={deleteProfileMutation}
        />
      </div>
    </Suspense>
  );
}
