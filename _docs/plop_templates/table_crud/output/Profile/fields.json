[{"name": "id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.number().nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "user_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.number().nullable().optional()"}, {"name": "org_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.number().nullable().optional()"}, {"name": "type_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.number().nullable().optional()"}, {"name": "display_name", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "is_company", "inputType": "switch", "defaultValue": null, "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_current", "inputType": "switch", "defaultValue": null, "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}]