"use no memo";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import { ActionIcon, Button, Flex, Stack, Text, TextInput, Title, Tooltip } from "@mantine/core";
import { modals } from "@mantine/modals";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  MRT_EditActionButtons,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { ProfileDisplayTypes, ProfileUpdateTypes } from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import type { RootStateTypes } from "@/utils/redux/store";
import ProfileForm from "../../-forms/ProfileForm";

type PropsTypes = {
  data: ProfileDisplayTypes[];
  createProfileMutation: any;
  updateProfileMutation: any;
  deleteProfileMutation: any;
};

const ProfilesTable = ({ data, createProfileMutation, updateProfileMutation, deleteProfileMutation }: PropsTypes) => {
  console.log(">>>>>>>>>>>>   data", data);
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<ProfileDisplayTypes>[]>(
    () => [
      {
        accessorKey: "id",
        header: "Id",
      },
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "lang",
        header: "Lang",
      },
      {
        accessorKey: "json_metadata",
        header: "Json_metadata",
      },
      {
        accessorKey: "user_id",
        header: "User_id",
      },
      {
        accessorKey: "org_id",
        header: "Org_id",
      },
      {
        accessorKey: "type_id",
        header: "Type_id",
      },
      {
        accessorKey: "display_name",
        header: "Display_name",
      },
      {
        accessorKey: "is_company",
        header: "Is_company",
      },
      {
        accessorKey: "is_current",
        header: "Is_current",
      },
    ],
    [],
  );

  //CREATE action
  const handleCreateProfile: MRT_TableOptions<ProfileUpdateTypes>["onCreatingRowSave"] = async ({
    values,
    exitCreatingMode,
  }) => {
    // await createProfileMutation.mutateAsync({
    //   body: [
    //     {
    //       ...values,
    //       created_by: user?.id,
    //       org_id: user?.curr_org_id,
    //     },
    //   ],
    // });
    exitCreatingMode();
  };

  //UPDATE action
  const handleUpdateProfile: MRT_TableOptions<ProfileUpdateTypes>["onEditingRowSave"] = async ({ values, table }) => {
    console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
    console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

    modals.open({
      title: "Edit user profile",
      children: (
        <>
          <ProfileForm
            data={values}
            variant="edit"
            setEditingRow={table.setEditingRow}
            createProfileMutation={createProfileMutation}
            updateProfileMutation={updateProfileMutation}
          />
        </>
      ),
    });
  };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<ProfileUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: <Text>Are you sure you want to delete {row.original.display_name} </Text>,
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteProfileMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization: currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreateProfile,
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdateProfile,
    renderCreateRowModalContent: ({ table, row }) => (
      <ProfileForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createProfileMutation={createProfileMutation}
        updateProfileMutation={updateProfileMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <ProfileForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createProfileMutation={createProfileMutation}
        updateProfileMutation={updateProfileMutation}
      />
    ),
    renderRowActions: ({ row, table }) => (
      <Flex gap="md">
        <Tooltip label="Edit">
          <ActionIcon
            variant="subtle"
            onClick={() => {
              console.log("row", row);
              table.setEditingRow(row);
            }}
          >
            <IconEdit />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon variant="subtle" color="red" onClick={() => openDeleteConfirmModal(row)}>
            <IconTrash />
          </ActionIcon>
        </Tooltip>
      </Flex>
    ),
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Profile
      </Button>
    ),
  });
  console.log("table", table);
  console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default ProfilesTable;
