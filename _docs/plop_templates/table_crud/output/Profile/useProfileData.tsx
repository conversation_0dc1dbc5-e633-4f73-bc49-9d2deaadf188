import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createProfilesV1CrmProfilesPostMutation,
  deleteProfilesV1CrmProfilesItemIdDeleteMutation,
  readProfilesAllV1CrmProfilesGetOptions,
  updateProfilesV1CrmProfilesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ProfileCreateTypes, ProfileUpdateTypes } from "@/client";

// type Props = {
//   updateData: ProfileUpdateTypes
//   createData: ProfileCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useProfileData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataProfiles, error: errorProfiles } = useSuspenseQuery(
    readProfilesAllV1CrmProfilesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorProfiles) {
    toast.error(errorProfiles.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataProfiles);

  // CREATE
  const createProfileMutation = useMutation({
    ...createProfilesV1CrmProfilesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProfilesAllV1CrmProfilesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createProfileMutation", createProfileMutation);

  // UPDATE

  const updateProfileMutation = useMutation({
    ...updateProfilesV1CrmProfilesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProfilesAllV1CrmProfilesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateProfileMutation", updateProfileMutation);

  // DELETE
  const deleteProfileMutation = useMutation({
    ...deleteProfilesV1CrmProfilesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProfilesAllV1CrmProfilesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteProfile.mutateAsync({
  //       path: { item_id: (data as ProfileDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataProfiles,
    createProfileMutation,
    updateProfileMutation,
    deleteProfileMutation,
  };
}
