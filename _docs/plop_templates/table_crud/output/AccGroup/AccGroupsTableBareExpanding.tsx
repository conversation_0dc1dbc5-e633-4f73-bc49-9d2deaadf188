"use no memo";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import {
  ActionIcon,
  Button,
  Checkbox,
  Flex,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  MRT_EditActionButtons,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { AccGroupDisplayTypes, AccGroupUpdateTypes } from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import type { RootStateTypes } from "@/utils/redux/store";
import AccGroupForm from "../-forms/AccGroupForm";

type PropsTypes = {
  data: AccGroupDisplayTypes[];
  createAccGroupMutation: any;
  updateAccGroupMutation: any;
  deleteAccGroupMutation: any;
};

const AccGroupsTable = ({
  data,
  createAccGroupMutation,
  updateAccGroupMutation,
  deleteAccGroupMutation,
}: PropsTypes) => {
  console.log(">>>>>>  AccGroup data >>>>>>", data);
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

  
  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<AccGroupDisplayTypes>[]>(
    () => [
        {
        accessorKey: "id",
        header: "Id",
        },
        {
        accessorKey: "name",
        header: "Name",
        },
        {
        accessorKey: "number",
        header: "Number",
        },
        {
        accessorKey: "description",
        header: "Description",
        },
        {
        accessorKey: "lang",
        header: "Lang",
        },
        {
        accessorKey: "json_metadata",
        header: "Json_metadata",
        },
    
      {
        accessorKey: "name",
        header: "",
        mantineTableBodyCellProps: {
          style: { width: "100%", fontWeight: "bold", fontSize: "18px" }, // Now the width applies correctly
        },
      },
     
      {
        id: "actions", // Important: give the column an ID
        accessorKey: "actions", // You can use a dummy accessorKey if needed
        header: "", // Optional header
        enableColumnOrdering: false, // Prevent user from reordering
        enableEditing: false, // Prevent editing this "column"

        enableSorting: false, // Prevent sorting on this "column"
        mantineTableBodyCellProps: {
          style: { width: "50px" }, // Now the width applies correctly
        },
        Cell: ({ row }) =>
          
            <Flex gap="md" justify="center">
              <Tooltip label="Edit">
                <ActionIcon
                  variant="subtle"
                  onClick={() => table.setEditingRow(row)}
                >
                  <IconEdit />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Delete">
                <ActionIcon
                  variant="subtle"
                  color="red"
                  disabled={row.original.id === user?.curr_org_id}
                  onClick={() => openDeleteConfirmModal(row)}
                >
                  <IconTrash />
                </ActionIcon>
              </Tooltip>
            </Flex>
          ,
      },
    ],
    []
  );

  //CREATE action
  const handleCreateUser: MRT_TableOptions<AccGroupUpdateTypes>["onCreatingRowSave"] =
    async ({ values, exitCreatingMode }) => {
      // await createAccGroupMutation.mutateAsync({
      //   body: [
      //     {
      //       ...values,
      //       created_by: user?.id,
      //       org_id: user?.curr_org_id,
      //     },
      //   ],
      // });
      exitCreatingMode();
    };

  //UPDATE action
  const handleUpdateUser: MRT_TableOptions<AccGroupUpdateTypes>["onEditingRowSave"] =
    async ({ values, table }) => {
      console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
      console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

      modals.open({
        title: "Edit user profile",
        children: (
          <>
            <AccGroupForm
              data={values}
              variant="edit"
              setEditingRow={table.setEditingRow}
              createAccGroupMutation={createAccGroupMutation}
              updateAccGroupMutation={updateAccGroupMutation}
            />
          </>
        ),
      });
    };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<AccGroupUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: (
        <Text>
          Are you sure you want to delete {row.original.display_name}{" "}
        </Text>
      ),
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteAccGroupMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization:
      currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableBottomToolbar: false,
    enableColumnActions: false,
    enableColumnFilters: false,
    enableGlobalFilter: false,
    enablePagination: false,
    enableSorting: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    enableHiding: false,
    renderToolbarInternalActions: () => null,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      // striped: "odd",
      // withColumnBorders: true,
      // withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: false,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreateUser,
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdateUser,
    renderCreateRowModalContent: ({ table, row }) => (
      <AccGroupForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createAccGroupMutation={createAccGroupMutation}
        updateAccGroupMutation={updateAccGroupMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <AccGroupForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createAccGroupMutation={createAccGroupMutation}
        updateAccGroupMutation={updateAccGroupMutation}
      />
    ),
    positionExpandColumn: "first",
    renderDetailPanel: ({ row }) => (
      <div
        style={{{
          display: "grid",
          margin: "auto",
          gridTemplateColumns: "1fr 1fr 1fr",
          width: "100%",
        }}}
      >
        <Checkbox
          label="Public"
          checked={row.original.is_public}
          onChange={() => null}
        />
        <Checkbox
          label="Formal"
          checked={row.original.is_formal}
          onChange={() => null}
        />
        <Text>NIP: {row.original.nip}</Text>
        <Text>Regon: {row.original.regon}</Text>
        <Text>Image width: {row.original.max_img_width}</Text>
        <Text>Shares: {row.original.total_shares}</Text>
        <Text>Voting: {row.original.voting_days}</Text>
        <Checkbox
          label="Members by admin"
          checked={row.original.members_by_admin}
          onChange={() => null}
        />
      </div>
    ),

    positionActionsColumn: "last",
    rowActionsColumnWidth: 50,
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create AccGroup
      </Button>
    ),
     mantineTableBodyRowProps: ({ row, table }) => ({
      onClick: (event) => {
        console.info(event, row.id);
        row.toggleExpanded(!row.getIsExpanded());
      },
      style: {
        cursor: "pointer",
      },
    }),
  });
  // console.log("table", table);
  // console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default AccGroupsTable;
