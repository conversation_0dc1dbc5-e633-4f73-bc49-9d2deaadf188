import { createFile<PERSON>oute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createAccGroupsV1MoneyAccGroupsPostMutation,
  readAccGroupsAllV1MoneyAccGroupsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import AccGroupsTable from "./-components/AccGroupsTable";
import { useAccGroupData } from "./-data_hooks/useAccGroupData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readAccGroupsAllV1MoneyAccGroupsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "AccGroup Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataAccGroups, createAccGroupMutation, updateAccGroupMutation, deleteAccGroupMutation } = useAccGroupData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <AccGroupsTable
          data={dataAccGroups.data}
          createAccGroupMutation={createAccGroupMutation}
          updateAccGroupMutation={updateAccGroupMutation}
          deleteAccGroupMutation={deleteAccGroupMutation}
        />
      </div>
    </Suspense>
  );
}
