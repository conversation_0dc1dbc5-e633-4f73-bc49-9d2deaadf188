import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createAccGroupsV1MoneyAccGroupsPostMutation,
  deleteAccGroupsV1MoneyAccGroupsItemIdDeleteMutation,
  readAccGroupsAllV1MoneyAccGroupsGetOptions,
  updateAccGroupsV1MoneyAccGroupsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { AccGroupCreateTypes, AccGroupUpdateTypes } from "@/client";

// type Props = {
//   updateData: AccGroupUpdateTypes
//   createData: AccGroupCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useAccGroupData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataAccGroups, error: errorAccGroups } = useSuspenseQuery(
    readAccGroupsAllV1MoneyAccGroupsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorAccGroups) {
    toast.error(errorAccGroups.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataAccGroups);

  // CREATE
  const createAccGroupMutation = useMutation({
    ...createAccGroupsV1MoneyAccGroupsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAccGroupsAllV1MoneyAccGroupsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createAccGroupMutation", createAccGroupMutation);

  // UPDATE

  const updateAccGroupMutation = useMutation({
    ...updateAccGroupsV1MoneyAccGroupsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAccGroupsAllV1MoneyAccGroupsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateAccGroupMutation", updateAccGroupMutation);

  // DELETE
  const deleteAccGroupMutation = useMutation({
    ...deleteAccGroupsV1MoneyAccGroupsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAccGroupsAllV1MoneyAccGroupsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteAccGroup.mutateAsync({
  //       path: { item_id: (data as AccGroupDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataAccGroups,
    createAccGroupMutation,
    updateAccGroupMutation,
    deleteAccGroupMutation,
  };
}
