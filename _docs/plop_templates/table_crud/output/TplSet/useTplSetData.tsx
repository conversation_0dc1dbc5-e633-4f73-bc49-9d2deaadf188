import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createTplSetsV1MoneyTplSetsPostMutation,
  deleteTplSetsV1MoneyTplSetsItemIdDeleteMutation,
  readTplSetsAllV1MoneyTplSetsGetOptions,
  updateTplSetsV1MoneyTplSetsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { TplSetCreateTypes, TplSetUpdateTypes } from "@/client";

// type Props = {
//   updateData: TplSetUpdateTypes
//   createData: TplSetCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useTplSetData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataTplSets, error: errorTplSets } = useSuspenseQuery(
    readTplSetsAllV1MoneyTplSetsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorTplSets) {
    toast.error(errorTplSets.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataTplSets);

  // CREATE
  const createTplSetMutation = useMutation({
    ...createTplSetsV1MoneyTplSetsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplSetsAllV1MoneyTplSetsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createTplSetMutation", createTplSetMutation);

  // UPDATE

  const updateTplSetMutation = useMutation({
    ...updateTplSetsV1MoneyTplSetsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplSetsAllV1MoneyTplSetsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateTplSetMutation", updateTplSetMutation);

  // DELETE
  const deleteTplSetMutation = useMutation({
    ...deleteTplSetsV1MoneyTplSetsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplSetsAllV1MoneyTplSetsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteTplSet.mutateAsync({
  //       path: { item_id: (data as TplSetDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataTplSets,
    createTplSetMutation,
    updateTplSetMutation,
    deleteTplSetMutation,
  };
}
