import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createTplSetsV1MoneyTplSetsPostMutation,
  readTplSetsAllV1MoneyTplSetsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import TplSetsTable from "./-components/TplSetsTable";
import { useTplSetData } from "./-data_hooks/useTplSetData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readTplSetsAllV1MoneyTplSetsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "TplSet Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataTplSets, createTplSetMutation, updateTplSetMutation, deleteTplSetMutation } = useTplSetData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <TplSetsTable
          data={dataTplSets.data}
          createTplSetMutation={createTplSetMutation}
          updateTplSetMutation={updateTplSetMutation}
          deleteTplSetMutation={deleteTplSetMutation}
        />
      </div>
    </Suspense>
  );
}
