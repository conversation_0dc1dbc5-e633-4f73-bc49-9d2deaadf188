[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "created_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "phone", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "email", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "region", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}]