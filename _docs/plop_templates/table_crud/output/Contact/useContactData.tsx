import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createContactsV1CrmContactsPostMutation,
  deleteContactsV1CrmContactsItemIdDeleteMutation,
  readContactsAllV1CrmContactsGetOptions,
  updateContactsV1CrmContactsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ContactCreateTypes, ContactUpdateTypes } from "@/client";

// type Props = {
//   updateData: ContactUpdateTypes
//   createData: ContactCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useContactData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataContacts, error: errorContacts } = useSuspenseQuery(
    readContactsAllV1CrmContactsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorContacts) {
    toast.error(errorContacts.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataContacts);

  // CREATE
  const createContactMutation = useMutation({
    ...createContactsV1CrmContactsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContactsAllV1CrmContactsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createContactMutation", createContactMutation);

  // UPDATE

  const updateContactMutation = useMutation({
    ...updateContactsV1CrmContactsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContactsAllV1CrmContactsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateContactMutation", updateContactMutation);

  // DELETE
  const deleteContactMutation = useMutation({
    ...deleteContactsV1CrmContactsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readContactsAllV1CrmContactsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteContact.mutateAsync({
  //       path: { item_id: (data as ContactDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataContacts,
    createContactMutation,
    updateContactMutation,
    deleteContactMutation,
  };
}
