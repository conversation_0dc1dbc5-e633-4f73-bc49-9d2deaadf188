import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createProposalUserVotesV1CoreProposalUserVotesPostMutation,
  readProposalUserVotesAllV1CoreProposalUserVotesGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import ProposalUserVotesTable from "./-components/ProposalUserVotesTable";
import { useProposalUserVoteData } from "./-data_hooks/useProposalUserVoteData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readProposalUserVotesAllV1CoreProposalUserVotesGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "ProposalUserVote Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const {
    dataProposalUserVotes,
    createProposalUserVoteMutation,
    updateProposalUserVoteMutation,
    deleteProposalUserVoteMutation,
  } = useProposalUserVoteData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <ProposalUserVotesTable
          data={dataProposalUserVotes.data}
          createProposalUserVoteMutation={createProposalUserVoteMutation}
          updateProposalUserVoteMutation={updateProposalUserVoteMutation}
          deleteProposalUserVoteMutation={deleteProposalUserVoteMutation}
        />
      </div>
    </Suspense>
  );
}
