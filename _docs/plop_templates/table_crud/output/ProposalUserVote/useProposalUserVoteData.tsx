import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createProposalUserVotesV1CoreProposalUserVotesPostMutation,
  deleteProposalUserVotesV1CoreProposalUserVotesItemIdDeleteMutation,
  readProposalUserVotesAllV1CoreProposalUserVotesGetOptions,
  updateProposalUserVotesV1CoreProposalUserVotesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { ProposalUserVoteCreateTypes, ProposalUserVoteUpdateTypes } from "@/client";

// type Props = {
//   updateData: ProposalUserVoteUpdateTypes
//   createData: ProposalUserVoteCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useProposalUserVoteData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataProposalUserVotes, error: errorProposalUserVotes } = useSuspenseQuery(
    readProposalUserVotesAllV1CoreProposalUserVotesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorProposalUserVotes) {
    toast.error(errorProposalUserVotes.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataProposalUserVotes);

  // CREATE
  const createProposalUserVoteMutation = useMutation({
    ...createProposalUserVotesV1CoreProposalUserVotesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProposalUserVotesAllV1CoreProposalUserVotesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createProposalUserVoteMutation", createProposalUserVoteMutation);

  // UPDATE

  const updateProposalUserVoteMutation = useMutation({
    ...updateProposalUserVotesV1CoreProposalUserVotesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProposalUserVotesAllV1CoreProposalUserVotesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateProposalUserVoteMutation", updateProposalUserVoteMutation);

  // DELETE
  const deleteProposalUserVoteMutation = useMutation({
    ...deleteProposalUserVotesV1CoreProposalUserVotesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProposalUserVotesAllV1CoreProposalUserVotesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteProposalUserVote.mutateAsync({
  //       path: { item_id: (data as ProposalUserVoteDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataProposalUserVotes,
    createProposalUserVoteMutation,
    updateProposalUserVoteMutation,
    deleteProposalUserVoteMutation,
  };
}
