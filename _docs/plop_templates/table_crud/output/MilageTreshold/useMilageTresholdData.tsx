import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createMilageTresholdsV1MoneyMilageTresholdsPostMutation,
  deleteMilageTresholdsV1MoneyMilageTresholdsItemIdDeleteMutation,
  readMilageTresholdsAllV1MoneyMilageTresholdsGetOptions,
  updateMilageTresholdsV1MoneyMilageTresholdsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { MilageTresholdCreateTypes, MilageTresholdUpdateTypes } from "@/client";

// type Props = {
//   updateData: MilageTresholdUpdateTypes
//   createData: MilageTresholdCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useMilageTresholdData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataMilageTresholds, error: errorMilageTresholds } = useSuspenseQuery(
    readMilageTresholdsAllV1MoneyMilageTresholdsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorMilageTresholds) {
    toast.error(errorMilageTresholds.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataMilageTresholds);

  // CREATE
  const createMilageTresholdMutation = useMutation({
    ...createMilageTresholdsV1MoneyMilageTresholdsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readMilageTresholdsAllV1MoneyMilageTresholdsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createMilageTresholdMutation", createMilageTresholdMutation);

  // UPDATE

  const updateMilageTresholdMutation = useMutation({
    ...updateMilageTresholdsV1MoneyMilageTresholdsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readMilageTresholdsAllV1MoneyMilageTresholdsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateMilageTresholdMutation", updateMilageTresholdMutation);

  // DELETE
  const deleteMilageTresholdMutation = useMutation({
    ...deleteMilageTresholdsV1MoneyMilageTresholdsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readMilageTresholdsAllV1MoneyMilageTresholdsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteMilageTreshold.mutateAsync({
  //       path: { item_id: (data as MilageTresholdDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataMilageTresholds,
    createMilageTresholdMutation,
    updateMilageTresholdMutation,
    deleteMilageTresholdMutation,
  };
}
