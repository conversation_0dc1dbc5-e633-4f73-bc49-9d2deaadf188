import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import { readDocumentsAllV1CoreDocumentsGetOptions } from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import DocumentsTable from "../-components/UsersTable/DocumentsTable";
import { useDocumentData } from "./-data_hooks/useDocumentData";

export const Route = createFileRoute("/dashboard/documents")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readDocumentsAllV1CoreDocumentsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Document Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/dashboard/documents"
      if (toLocation.pathname !== "/dashboard/documents") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataDocuments, createDocumentMutation, updateDocumentMutation, deleteDocumentMutation } = useDocumentData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <DocumentsTable
          data={dataDocuments.data}
          createDocumentMutation={createDocumentMutation}
          updateDocumentMutation={updateDocumentMutation}
          deleteDocumentMutation={deleteDocumentMutation}
        />
      </div>
    </Suspense>
  );
}
