import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  deleteFilesV1CoreFilesItemIdDeleteMutation,
  readDocumentsAllV1CoreDocumentsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { DocumentCreateTypes, DocumentUpdateTypes } from "@/client";

// type Props = {
//   updateData: DocumentUpdateTypes
//   createData: DocumentCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useDocumentData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataDocuments, error: errorDocuments } = useSuspenseQuery(
    readDocumentsAllV1CoreDocumentsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorDocuments) {
    toast.error(errorDocuments.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataDocuments);

  // UPDATE

  const updateDocumentMutation = useMutation({
    ...updateFilesV1CoreDocumentsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocumentsAllV1CoreDocumentsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateDocumentMutation", updateDocumentMutation);

  // DELETE
  const deleteDocumentMutation = useMutation({
    ...deleteFilesV1CoreFilesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocumentsAllV1CoreDocumentsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteDocument.mutateAsync({
  //       path: { item_id: (data as DocumentDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataDocuments,
    createDocumentMutation,
    updateDocumentMutation,
    deleteDocumentMutation,
  };
}
