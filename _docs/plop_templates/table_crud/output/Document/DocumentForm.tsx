import {
  But<PERSON>,
  Checkbox,
  InputBase,
  NumberInput,
  Radio,
  Select,
  Switch,
  Textarea,
  TextInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm, useStore } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { IMaskInput } from "react-imask";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  deleteFilesV1CoreFilesItemIdDeleteMutation,
  updateFilesV1CoreFilesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import {
  DocumentCreateTypes,
  DocumentDisplayTypes,
  DocumentUpdateTypes,
} from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultDocument = {

    id: 0,
    file_name: "",
    url: "",
    description: "",
    mime_type: "",
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    id: z.number(),
    file_name: z.string(),
    url: z.string(),
    description: z.string(),
    mime_type: z.string(),
 })
  .passthrough();

interface PropsTypes {
  data?: DocumentDisplayTypes;
  variant: "new" | "edit";
}

type FormData = z.infer<typeof formSchema>;

function DocumentForm({ data = defaultDocument, variant = "edit" }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(
    new Set()
  );
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as DocumentCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as DocumentUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

 
  const updateDocument = useMutation({
    ...updateFilesV1CoreFilesPutMutation,(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocumentsAllV1CoreDocumentsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleEdit(formData: DocumentUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]])
    ) as DocumentUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateDocument.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
         
        },
      ],
    });
  }

  const deleteDocument = useMutation({
    ...deleteFilesV1CoreFilesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocumentsAllV1CoreDocumentsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteDocument.mutateAsync({
        path: { item_id: (data as DocumentDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);


   return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit"
              ? t("forms.DocumentForm.titleEdit.label")
              : t("forms.DocumentForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
              {/* ID */}
              <div className={styles.span4}>
                <form.Field
                  name="id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.DocumentForm.id.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              
              
              
              
              
              {/* FILE_NAME */}
              <div className={styles.span4}>
                  <form.Field
                  name="file_name"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.DocumentForm.fileName.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("file_name");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              
              
              
              
              
              {/* URL */}
              <div className={styles.span4}>
                  <form.Field
                  name="url"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.DocumentForm.url.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("url");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* DESCRIPTION */}
              <div className={styles.span4}>
                  <form.Field
                  name="description"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <Textarea
                          label={t("forms.DocumentForm.description.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("description");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Description"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              
              
              
              
              
              {/* MIME_TYPE */}
              <div className={styles.span4}>
                  <form.Field
                  name="mime_type"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.DocumentForm.mimeType.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("mime_type");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={!canSubmit || changedFields.size === 0}
                  loading={isSubmitting}
                >
                  {variant === "edit"
                    ? t("common.save.label")
                    : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" &&
              (data as DocumentDisplayTypes)?.id !== user.curr_org_id && (
                <Button
                  color="red"
                  loading={updateDocument.isPending || createDocument.isPending}
                  // disabled
                  onClick={handleDelete}
                >
                  {t("common.delete.label")}
                </Button>
              )}
          </div>
        </form>
      </div>
    </>
  );
};

export default DocumentForm;

