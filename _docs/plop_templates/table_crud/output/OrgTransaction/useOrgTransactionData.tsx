import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createOrgTransactionsV1MoneyOrgTransactionsPostMutation,
  deleteOrgTransactionsV1MoneyOrgTransactionsItemIdDeleteMutation,
  readOrgTransactionsAllV1MoneyOrgTransactionsGetOptions,
  updateOrgTransactionsV1MoneyOrgTransactionsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { OrgTransactionCreateTypes, OrgTransactionUpdateTypes } from "@/client";

// type Props = {
//   updateData: OrgTransactionUpdateTypes
//   createData: OrgTransactionCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useOrgTransactionData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataOrgTransactions, error: errorOrgTransactions } = useSuspenseQuery(
    readOrgTransactionsAllV1MoneyOrgTransactionsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorOrgTransactions) {
    toast.error(errorOrgTransactions.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataOrgTransactions);

  // CREATE
  const createOrgTransactionMutation = useMutation({
    ...createOrgTransactionsV1MoneyOrgTransactionsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgTransactionsAllV1MoneyOrgTransactionsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createOrgTransactionMutation", createOrgTransactionMutation);

  // UPDATE

  const updateOrgTransactionMutation = useMutation({
    ...updateOrgTransactionsV1MoneyOrgTransactionsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgTransactionsAllV1MoneyOrgTransactionsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateOrgTransactionMutation", updateOrgTransactionMutation);

  // DELETE
  const deleteOrgTransactionMutation = useMutation({
    ...deleteOrgTransactionsV1MoneyOrgTransactionsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgTransactionsAllV1MoneyOrgTransactionsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteOrgTransaction.mutateAsync({
  //       path: { item_id: (data as OrgTransactionDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataOrgTransactions,
    createOrgTransactionMutation,
    updateOrgTransactionMutation,
    deleteOrgTransactionMutation,
  };
}
