"use no memo";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex, Text, Tooltip } from "@mantine/core";
import { OrgTransactionDisplayTypes, OrgTransactionUpdateTypes } from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import type { RootStateTypes } from "@/utils/redux/store";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import { modals } from "@mantine/modals";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import { useMemo } from "react";
import { useSelector } from "react-redux";
import OrgTransactionForm from "../../-forms/OrgTransactionForm";

type PropsTypes = {
  data: OrgTransactionDisplayTypes[];
  createOrgTransactionMutation: any;
  updateOrgTransactionMutation: any;
  deleteOrgTransactionMutation: any;
};

const OrgTransactionsTable = ({
  data,
  createOrgTransactionMutation,
  updateOrgTransactionMutation,
  deleteOrgTransactionMutation,
}: PropsTypes) => {
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<OrgTransactionDisplayTypes>[]>(
    () => [
      {
        accessorKey: "id",
        header: "Id",
      },
      {
        accessorKey: "created_at",
        header: "Created_at",
      },
      {
        accessorKey: "updated_at",
        header: "Updated_at",
      },
      {
        accessorKey: "created_by",
        header: "Created_by",
      },
      {
        accessorKey: "updated_by",
        header: "Updated_by",
      },
      {
        accessorKey: "lang",
        header: "Lang",
      },
      {
        accessorKey: "json_metadata",
        header: "Json_metadata",
      },
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "date",
        header: "Date",
      },
      {
        accessorKey: "type",
        header: "Type",
      },
      {
        accessorKey: "org_id",
        header: "Org_id",
      },
      {
        accessorKey: "due_date",
        header: "Due_date",
      },
      {
        accessorKey: "amount",
        header: "Amount",
      },
      {
        accessorKey: "job_id",
        header: "Job_id",
      },
      {
        accessorKey: "comment",
        header: "Comment",
      },
      {
        accessorKey: "object_id",
        header: "Object_id",
      },
      {
        accessorKey: "is_saved",
        header: "Is_saved",
      },
      {
        accessorKey: "is_schedule",
        header: "Is_schedule",
      },
      {
        accessorKey: "posted_at",
        header: "Posted_at",
      },
      {
        accessorKey: "cust_ref",
        header: "Cust_ref",
      },
      {
        accessorKey: "our_ref",
        header: "Our_ref",
      },
      {
        accessorKey: "memo",
        header: "Memo",
      },
      {
        accessorKey: "contrahent_id",
        header: "Contrahent_id",
      },
      {
        accessorKey: "template_id",
        header: "Template_id",
      },
      {
        accessorKey: "set_id",
        header: "Set_id",
      },
      {
        accessorKey: "set_item_id",
        header: "Set_item_id",
      },
    ],
    [],
  );

  //CREATE action
  const handleCreateOrgTransaction: MRT_TableOptions<OrgTransactionUpdateTypes>["onCreatingRowSave"] = async ({
    values,
    exitCreatingMode,
  }) => {
    // await createOrgTransactionMutation.mutateAsync({
    //   body: [
    //     {
    //       ...values,
    //       created_by: user?.id,
    //       org_id: user?.curr_org_id,
    //     },
    //   ],
    // });
    exitCreatingMode();
  };

  //UPDATE action
  const handleUpdateOrgTransaction: MRT_TableOptions<OrgTransactionUpdateTypes>["onEditingRowSave"] = async ({
    values,
    table,
  }) => {
    console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
    console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

    modals.open({
      title: "Edit user profile",
      children: (
        <>
          <OrgTransactionForm
            data={values}
            variant="edit"
            setEditingRow={table.setEditingRow}
            createOrgTransactionMutation={createOrgTransactionMutation}
            updateOrgTransactionMutation={updateOrgTransactionMutation}
          />
        </>
      ),
    });
  };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<OrgTransactionUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: <Text>Are you sure you want to delete {row.original.display_name} </Text>,
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteOrgTransactionMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization: currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreateOrgTransaction,
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdateOrgTransaction,
    renderCreateRowModalContent: ({ table, row }) => (
      <OrgTransactionForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createOrgTransactionMutation={createOrgTransactionMutation}
        updateOrgTransactionMutation={updateOrgTransactionMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <OrgTransactionForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createOrgTransactionMutation={createOrgTransactionMutation}
        updateOrgTransactionMutation={updateOrgTransactionMutation}
      />
    ),
    renderRowActions: ({ row, table }) => (
      <Flex gap="md">
        <Tooltip label="Edit">
          <ActionIcon
            variant="subtle"
            onClick={() => {
              console.log("row", row);
              table.setEditingRow(row);
            }}
          >
            <IconEdit />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon variant="subtle" color="red" onClick={() => openDeleteConfirmModal(row)}>
            <IconTrash />
          </ActionIcon>
        </Tooltip>
      </Flex>
    ),
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create OrgTransaction
      </Button>
    ),
  });
  console.log("table", table);
  console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default OrgTransactionsTable;
