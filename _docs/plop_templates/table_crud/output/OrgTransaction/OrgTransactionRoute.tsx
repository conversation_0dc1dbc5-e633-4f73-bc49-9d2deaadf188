import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createOrgTransactionsV1MoneyOrgTransactionsPostMutation,
  readOrgTransactionsAllV1MoneyOrgTransactionsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import OrgTransactionsTable from "./-components/OrgTransactionsTable";
import { useOrgTransactionData } from "./-data_hooks/useOrgTransactionData";

export const Route = createFileRoute("/money/transactions")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readOrgTransactionsAllV1MoneyOrgTransactionsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "OrgTransaction Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/money/transactions"
      if (toLocation.pathname !== "/money/transactions") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const {
    dataOrgTransactions,
    createOrgTransactionMutation,
    updateOrgTransactionMutation,
    deleteOrgTransactionMutation,
  } = useOrgTransactionData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <OrgTransactionsTable
          data={dataOrgTransactions.data}
          createOrgTransactionMutation={createOrgTransactionMutation}
          updateOrgTransactionMutation={updateOrgTransactionMutation}
          deleteOrgTransactionMutation={deleteOrgTransactionMutation}
        />
      </div>
    </Suspense>
  );
}
