import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createTripsV1MoneyTripsPostMutation,
  deleteTripsV1MoneyTripsItemIdDeleteMutation,
  readTripsAllV1MoneyTripsAllGetOptions,
  updateTripsV1MoneyTripsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { TripCreateTypes, TripUpdateTypes } from "@/client";

// type Props = {
//   updateData: TripUpdateTypes
//   createData: TripCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useTripData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataTrips, error: errorTrips } = useSuspenseQuery(
    readTripsAllV1MoneyTripsAllGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorTrips) {
    toast.error(errorTrips.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataTrips);

  // CREATE
  const createTripMutation = useMutation({
    ...createTripsV1MoneyTripsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTripsAllV1MoneyTripsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createTripMutation", createTripMutation);

  // UPDATE

  const updateTripMutation = useMutation({
    ...updateTripsV1MoneyTripsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTripsAllV1MoneyTripsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateTripMutation", updateTripMutation);

  // DELETE
  const deleteTripMutation = useMutation({
    ...deleteTripsV1MoneyTripsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTripsAllV1MoneyTripsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteTrip.mutateAsync({
  //       path: { item_id: (data as TripDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataTrips,
    createTripMutation,
    updateTripMutation,
    deleteTripMutation,
  };
}
