"use no memo";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import {
  ActionIcon,
  Button,
  Flex,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import {
  IconAd,
  IconCornerDownRightDouble,
  IconEdit,
  IconPlus,
  IconSearch,
  IconTrash,
} from "@tabler/icons-react";
import { useNavigate } from "@tanstack/react-router";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  MRT_EditActionButtons,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  TripDisplayTypes,
  TripTypeDisplayTypes,
  TripUpdateTypes,
} from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import TripForm from "@/routes/objects/-forms/TripForm";
import type { RootStateTypes } from "@/utils/redux/store";

type PropsTypes = {
  data: TripDisplayTypes[];
  objectTypes: TripTypeDisplayTypes[];
  createTripMutation: any;
  updateTripMutation: any;
  deleteTripMutation: any;
};

const TripsTable = ({
  data,
  objectTypes,
  createTripMutation,
  updateTripMutation,
  deleteTripMutation,
}: PropsTypes) => {
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);
  const navigate = useNavigate();

  function getNestedMargin(level: number) {
    return level * 30;
  }

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<TripDisplayTypes>[]>(
    () => [
      {
      accessorKey: "id",
      header: "Id",
      },
      {
      accessorKey: "created_at",
      header: "Created_at",
      },
      {
      accessorKey: "updated_at",
      header: "Updated_at",
      },
      {
      accessorKey: "created_by",
      header: "Created_by",
      },
      {
      accessorKey: "updated_by",
      header: "Updated_by",
      },
      {
      accessorKey: "json_metadata",
      header: "Json_metadata",
      },
      {
      accessorKey: "name",
      header: "Name",
      },
      {
      accessorKey: "description",
      header: "Description",
      },
      {
      accessorKey: "start_loc_id",
      header: "Start_loc_id",
      },
      {
      accessorKey: "end_loc_id",
      header: "End_loc_id",
      },
      {
      accessorKey: "start_odometer",
      header: "Start_odometer",
      },
      {
      accessorKey: "end_odometer",
      header: "End_odometer",
      },
      {
      accessorKey: "start_date",
      header: "Start_date",
      },
      {
      accessorKey: "end_date",
      header: "End_date",
      },
      {
      accessorKey: "trip_type",
      header: "Trip_type",
      },
      {
      accessorKey: "distance",
      header: "Distance",
      },
      {
      accessorKey: "duration",
      header: "Duration",
      },
      {
      accessorKey: "object_id",
      header: "Object_id",
      },
      {
      accessorKey: "contrahent_id",
      header: "Contrahent_id",
      },
      {
      accessorKey: "org_id",
      header: "Org_id",
      },
      {
      accessorKey: "job_id",
      header: "Job_id",
      },
      {
        accessorKey: "name",
        header: "Name",
        mantineTableBodyCellProps: {
          style: { width: 500 },
        },
        Cell: ({ row }) => (
          <Text
            style={{{
              width: "100%",
              fontWeight:
                row.getCanExpand() && !row.getIsExpanded() ? "bold" : "normal",
              fontSize: "18px",
              marginLeft: getNestedMargin(row.original.tree_level),
            }}}
          >
            {row.original.name}
          </Text>
        ),
      },

      {
        accessorKey: "object_type",
        header: "Type",
        mantineTableBodyCellProps: {
          style: { width: 100 },
        },
      },

      {
        id: "actions",
        accessorKey: "actions",
        header: "",
        enableColumnOrdering: false,
        enableEditing: false,

        enableSorting: false,
        mantineTableBodyCellProps: {
          style: { width: 50 },
        },
        Cell: ({ row }) => (
          <Flex gap="md" justify="center">
            <Tooltip label="View">
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate({ to: `/objects/list/${row.original.id}` })
                }}
              >
                <IconSearch />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Add sub object">
              <ActionIcon
                variant="subtle"
                color="violet"
                onClick={() =>
                  navigate({
                    to: `/objects/new/?parentId=${row.original.id}&treeLevel=${row.original.tree_level + 1}`,
                  })
                }
              >
                <IconCornerDownRightDouble />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Edit">
              <ActionIcon
                variant="subtle"
                onClick={(e) => {
                  e.stopPropagation();
                  table.setEditingRow(row);
                }}
              >
                <IconEdit />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Delete">
              <ActionIcon
                variant="subtle"
                color="red"
                onClick={(e) => {
                  e.stopPropagation();
                  openDeleteConfirmModal(row);
                }}
                disabled={row.getCanExpand()}
              >
                <IconTrash />
              </ActionIcon>
            </Tooltip>
          </Flex>
        ),
      },
    ],
    []
  );

  //CREATE action
  const handleCreateTrip: MRT_TableOptions<TripUpdateTypes>["onCreatingRowSave"] =
    async ({ values, exitCreatingMode }) => {
      // await createTripMutation.mutateAsync({
      //   body: [
      //     {
      //       ...values,
      //       created_by: user?.id,
      //       org_id: user?.curr_org_id,
      //     },
      //   ],
      // });
      exitCreatingMode();
    };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<TripUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: (
        <Text>Are you sure you want to delete {row.original.name} </Text>
      ),
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteTripMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization:
      currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL,
    enableBottomToolbar: false, // FOOTER
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    enableGlobalFilter: false,
    enableHiding: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      // striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    // enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    enableExpanding: true,
    getSubRows: (originalRow) => originalRow.subRows,
    onCreatingRowSave: handleCreateTrip,
    // onEditingRowCancel: () => setValidationErrors({}),
    // onEditingRowSave: handleUpdateTrip,
     renderCreateRowModalContent: ({ table, row }) => (
      <TripForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createTripMutation={createTripMutation}
        updateTripMutation={updateTripMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <TripForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createTripMutation={createTripMutation}
        updateTripMutation={updateTripMutation}
      />
    ),

    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Bazowy Trip
      </Button>
    ),
    mantineTableBodyRowProps: ({ row, table }) => ({
      onClick: (event) => {
        console.info(event, row.id);
        row.toggleExpanded(!row.getIsExpanded());
      },
      style: {
        cursor: row.getCanExpand() ? "pointer" : "default", //you might want to change the cursor too when adding an onClick
      },
    }),
  });

  return <MantineReactTable table={table} />;
};

export default TripsTable;
