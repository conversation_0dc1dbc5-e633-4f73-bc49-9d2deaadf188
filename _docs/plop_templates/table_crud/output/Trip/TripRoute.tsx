import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createTripsV1MoneyTripsPostMutation,
  readTripsAllV1MoneyTripsAllGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import TripsTable from "./-components/TripsTable";
import { useTripData } from "./-data_hooks/useTripData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id, curr_acc_period_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readTripsAllV1MoneyTripsAllGetOptions({
          query: {
            org_id: curr_org_id || 0,
            acc_period_id: curr_acc_period_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Trip Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataTrips, createTripMutation, updateTripMutation, deleteTripMutation } = useTripData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <TripsTable
          data={dataTrips.data}
          createTripMutation={createTripMutation}
          updateTripMutation={updateTripMutation}
          deleteTripMutation={deleteTripMutation}
        />
      </div>
    </Suspense>
  );
}
