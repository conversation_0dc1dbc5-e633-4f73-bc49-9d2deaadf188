[{"name": "first_name", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "last_name", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "", "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": "", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "phone", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "email", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "email_verified", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_superuser", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_admin", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_limited", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_disabled", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_in_credit", "inputType": "switch", "defaultValue": true, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_temp", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "is_deleted", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean()"}]