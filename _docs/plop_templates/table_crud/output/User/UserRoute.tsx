import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createUsersV1AuthUsersPostMutation,
  readUsersAllV1AuthUsersGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import UsersTable from "./-components/UsersTable";
import { useUserData } from "./-data_hooks/bak_useUserData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readUsersAllV1AuthUsersGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "User Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataUsers, createUserMutation, updateUserMutation, deleteUserMutation } = useUserData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <UsersTable
          data={dataUsers.data}
          createUserMutation={createUserMutation}
          updateUserMutation={updateUserMutation}
          deleteUserMutation={deleteUserMutation}
        />
      </div>
    </Suspense>
  );
}
