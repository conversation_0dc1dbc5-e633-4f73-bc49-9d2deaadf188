import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createUsersV1AuthUsersPostMutation,
  deleteUsersV1AuthUsersItemIdDeleteMutation,
  readUsersAllV1AuthUsersGetOptions,
  updateUsersV1AuthUsersPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { UserCreateTypes, UserUpdateTypes } from "@/client";

// type Props = {
//   updateData: UserUpdateTypes
//   createData: UserCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useUserData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataUsers, error: errorUsers } = useSuspenseQuery(
    readUsersAllV1AuthUsersGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorUsers) {
    toast.error(errorUsers.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataUsers);

  // CREATE
  const createUserMutation = useMutation({
    ...createUsersV1AuthUsersPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readUsersAllV1AuthUsersGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createUserMutation", createUserMutation);

  // UPDATE

  const updateUserMutation = useMutation({
    ...updateUsersV1AuthUsersPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readUsersAllV1AuthUsersGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateUserMutation", updateUserMutation);

  // DELETE
  const deleteUserMutation = useMutation({
    ...deleteUsersV1AuthUsersItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readUsersAllV1AuthUsersGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteUser.mutateAsync({
  //       path: { item_id: (data as UserDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataUsers,
    createUserMutation,
    updateUserMutation,
    deleteUserMutation,
  };
}
