import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createTypesV1CrmTypesPostMutation,
  deleteTypesV1CrmTypesItemIdDeleteMutation,
  readTypesAllV1CrmTypesGetOptions,
  updateTypesV1CrmTypesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { TypeCreateTypes, TypeUpdateTypes } from "@/client";

// type Props = {
//   updateData: TypeUpdateTypes
//   createData: TypeCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useTypeData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataTypes, error: errorTypes } = useSuspenseQuery(
    readTypesAllV1CrmTypesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorTypes) {
    toast.error(errorTypes.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataTypes);

  // CREATE
  const createTypeMutation = useMutation({
    ...createTypesV1CrmTypesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTypesAllV1CrmTypesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createTypeMutation", createTypeMutation);

  // UPDATE

  const updateTypeMutation = useMutation({
    ...updateTypesV1CrmTypesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTypesAllV1CrmTypesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateTypeMutation", updateTypeMutation);

  // DELETE
  const deleteTypeMutation = useMutation({
    ...deleteTypesV1CrmTypesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTypesAllV1CrmTypesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteType.mutateAsync({
  //       path: { item_id: (data as TypeDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataTypes,
    createTypeMutation,
    updateTypeMutation,
    deleteTypeMutation,
  };
}
