[{"name": "id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "created_at", "inputType": "date", "defaultValue": null, "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "updated_at", "inputType": "date", "defaultValue": null, "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "country", "inputType": "text", "defaultValue": "Poland", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "area1", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "area2", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "street", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "city", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "postal_code", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lon", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "lat", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "location", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "street_no", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "local_no", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}]