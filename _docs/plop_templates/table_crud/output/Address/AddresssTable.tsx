"use no memo";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex, Text, Tooltip } from "@mantine/core";
import { AddressDisplayTypes, AddressUpdateTypes } from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import type { RootStateTypes } from "@/utils/redux/store";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import { modals } from "@mantine/modals";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import { useMemo } from "react";
import { useSelector } from "react-redux";
import AddressForm from "../../-forms/AddressForm";

type PropsTypes = {
  data: AddressDisplayTypes[];
  createAddressMutation: any;
  updateAddressMutation: any;
  deleteAddressMutation: any;
};

const AddressTable = ({ data, createAddressMutation, updateAddressMutation, deleteAddressMutation }: PropsTypes) => {
  console.log(">>>>>>>>>>>>   data", data);
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<AddressDisplayTypes>[]>(
    () => [
      {
        accessorKey: "id",
        header: "Id",
      },
      {
        accessorKey: "created_at",
        header: "Created_at",
      },
      {
        accessorKey: "updated_at",
        header: "Updated_at",
      },
      {
        accessorKey: "created_by",
        header: "Created_by",
      },
      {
        accessorKey: "updated_by",
        header: "Updated_by",
      },
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "lang",
        header: "Lang",
      },
      {
        accessorKey: "json_metadata",
        header: "Json_metadata",
      },
      {
        accessorKey: "country",
        header: "Country",
      },
      {
        accessorKey: "area1",
        header: "Area1",
      },
      {
        accessorKey: "area2",
        header: "Area2",
      },
      {
        accessorKey: "street",
        header: "Street",
      },
      {
        accessorKey: "city",
        header: "City",
      },
      {
        accessorKey: "postal_code",
        header: "Postal_code",
      },
      {
        accessorKey: "lon",
        header: "Lon",
      },
      {
        accessorKey: "lat",
        header: "Lat",
      },
      {
        accessorKey: "location",
        header: "Location",
      },
      {
        accessorKey: "street_no",
        header: "Street_no",
      },
      {
        accessorKey: "local_no",
        header: "Local_no",
      },
    ],
    [],
  );

  //CREATE action
  const handleCreateAddress: MRT_TableOptions<AddressUpdateTypes>["onCreatingRowSave"] = async ({
    values,
    exitCreatingMode,
  }) => {
    // await createAddressMutation.mutateAsync({
    //   body: [
    //     {
    //       ...values,
    //       created_by: user?.id,
    //       org_id: user?.curr_org_id,
    //     },
    //   ],
    // });
    exitCreatingMode();
  };

  //UPDATE action
  const handleUpdateAddress: MRT_TableOptions<AddressUpdateTypes>["onEditingRowSave"] = async ({ values, table }) => {
    console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
    console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

    modals.open({
      title: "Edit user profile",
      children: (
        <>
          <AddressForm
            data={values}
            variant="edit"
            setEditingRow={table.setEditingRow}
            createAddressMutation={createAddressMutation}
            updateAddressMutation={updateAddressMutation}
          />
        </>
      ),
    });
  };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<AddressUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: <Text>Are you sure you want to delete {row.original.display_name} </Text>,
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteAddressMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization: currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreateAddress,
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdateAddress,
    renderCreateRowModalContent: ({ table, row }) => (
      <AddressForm
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createAddressMutation={createAddressMutation}
        updateAddressMutation={updateAddressMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <AddressForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        createAddressMutation={createAddressMutation}
        updateAddressMutation={updateAddressMutation}
      />
    ),
    renderRowActions: ({ row, table }) => (
      <Flex gap="md">
        <Tooltip label="Edit">
          <ActionIcon
            variant="subtle"
            onClick={() => {
              console.log("row", row);
              table.setEditingRow(row);
            }}
          >
            <IconEdit />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon variant="subtle" color="red" onClick={() => openDeleteConfirmModal(row)}>
            <IconTrash />
          </ActionIcon>
        </Tooltip>
      </Flex>
    ),
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Address
      </Button>
    ),
  });
  console.log("table", table);
  console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default AddressTable;
