import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createAddressesV1CrmAddressPostMutation,
  readAddressAllV1CrmAddressGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import AddressTable from "./-components/AddressTable";
import { useAddressData } from "./-data_hooks/useAddressData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readAddressAllV1CrmAddressGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Address Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataAddress, createAddressMutation, updateAddressMutation, deleteAddressMutation } = useAddressData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <AddressTable
          data={dataAddress.data}
          createAddressMutation={createAddressMutation}
          updateAddressMutation={updateAddressMutation}
          deleteAddressMutation={deleteAddressMutation}
        />
      </div>
    </Suspense>
  );
}
