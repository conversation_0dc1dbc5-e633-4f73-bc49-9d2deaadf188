import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createAddressesV1CrmAddressPostMutation,
  deleteAddressesV1CrmAddressesItemIdDeleteMutation,
  readAddressAllV1CrmAddressGetOptions,
  updateAddressesV1CrmAddressPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { AddressCreateTypes, AddressUpdateTypes } from "@/client";

// type Props = {
//   updateData: AddressUpdateTypes
//   createData: AddressCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useAddressData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataAddress, error: errorAddress } = useSuspenseQuery(
    readAddressAllV1CrmAddressGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorAddress) {
    toast.error(errorAddress.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataAddress);

  // CREATE
  const createAddressMutation = useMutation({
    ...createAddressesV1CrmAddressPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAddressAllV1CrmAddressGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createAddressMutation", createAddressMutation);

  // UPDATE

  const updateAddressMutation = useMutation({
    ...updateAddressesV1CrmAddressPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAddressAllV1CrmAddressGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateAddressMutation", updateAddressMutation);

  // DELETE
  const deleteAddressMutation = useMutation({
    ...deleteAddressesV1CrmAddressesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readAddressAllV1CrmAddressGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteAddress.mutateAsync({
  //       path: { item_id: (data as AddressDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataAddress,
    createAddressMutation,
    updateAddressMutation,
    deleteAddressMutation,
  };
}
