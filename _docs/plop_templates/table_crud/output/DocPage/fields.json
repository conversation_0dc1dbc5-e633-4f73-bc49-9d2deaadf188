[{"name": "id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "org_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "html", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "text", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "mjml", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "content", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.array(z.any()).nullable().optional()"}, {"name": "page_number", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}]