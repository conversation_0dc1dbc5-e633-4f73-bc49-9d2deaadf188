import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createDocPagesV1CoreDocPagesPostMutation,
  deleteDocPagesV1CoreDocPagesItemIdDeleteMutation,
  readDocPagesAllV1CoreDocPagesGetOptions,
  updateDocPagesV1CoreDocPagesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { DocPageCreateTypes, DocPageUpdateTypes } from "@/client";

// type Props = {
//   updateData: DocPageUpdateTypes
//   createData: DocPageCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useDocPageData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataDocPages, error: errorDocPages } = useSuspenseQuery(
    readDocPagesAllV1CoreDocPagesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorDocPages) {
    toast.error(errorDocPages.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataDocPages);

  // CREATE
  const createDocPageMutation = useMutation({
    ...createDocPagesV1CoreDocPagesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocPagesAllV1CoreDocPagesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createDocPageMutation", createDocPageMutation);

  // UPDATE

  const updateDocPageMutation = useMutation({
    ...updateDocPagesV1CoreDocPagesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocPagesAllV1CoreDocPagesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateDocPageMutation", updateDocPageMutation);

  // DELETE
  const deleteDocPageMutation = useMutation({
    ...deleteDocPagesV1CoreDocPagesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocPagesAllV1CoreDocPagesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteDocPage.mutateAsync({
  //       path: { item_id: (data as DocPageDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataDocPages,
    createDocPageMutation,
    updateDocPageMutation,
    deleteDocPageMutation,
  };
}
