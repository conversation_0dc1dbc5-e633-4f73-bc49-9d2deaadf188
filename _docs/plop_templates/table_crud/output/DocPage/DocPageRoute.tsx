import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createDocPagesV1CoreDocPagesPostMutation,
  readDocPagesAllV1CoreDocPagesGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import Loading from "@/components/_system/Loading";
import { updateSystemField } from "@/utils/redux/systemSlice";
import DocPagesTable from "./-components/DocPagesTable";
import { useDocPageData } from "./-data_hooks/useDocPageData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readDocPagesAllV1CoreDocPagesGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "DocPage Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataDocPages, createDocPageMutation, updateDocPageMutation, deleteDocPageMutation } = useDocPageData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <DocPagesTable
          data={dataDocPages.data}
          createDocPageMutation={createDocPageMutation}
          updateDocPageMutation={updateDocPageMutation}
          deleteDocPageMutation={deleteDocPageMutation}
        />
      </div>
    </Suspense>
  );
}
