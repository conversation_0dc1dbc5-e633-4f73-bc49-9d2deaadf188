[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "created_at", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "created_by", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()])"}, {"name": "updated_by", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "pl", "valueType": "string", "zodType": "z.string()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "", "valueType": "string", "zodType": "z.string()"}, {"name": "description", "inputType": "textArea", "defaultValue": null, "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "is_public", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}, {"name": "address_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "is_formal", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}, {"name": "nip", "inputType": "numberMask", "defaultValue": null, "valueType": "number", "zodType": "z.string().nullable().optional()"}, {"name": "regon", "inputType": "numberMask", "defaultValue": null, "valueType": "number", "zodType": "z.string().nullable().optional()"}, {"name": "max_img_width", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "total_shares", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "accounts_set_id", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "voting_days", "inputType": "number", "defaultValue": null, "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "members_by_admin", "inputType": "switch", "defaultValue": false, "valueType": "boolean", "zodType": "z.boolean().optional()"}]