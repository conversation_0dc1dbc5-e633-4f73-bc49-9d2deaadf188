import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createOrgsV1CoreOrgsPostMutation,
  deleteOrgsV1CoreOrgsItemIdDeleteMutation,
  readOrgsAllV1CoreOrgsGetOptions,
  updateOrgsV1CoreOrgsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { OrgCreateTypes, OrgUpdateTypes } from "@/client";

// type Props = {
//   updateData: OrgUpdateTypes
//   createData: OrgCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useOrgData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataOrgs, error: errorOrgs } = useSuspenseQuery(
    readOrgsAllV1CoreOrgsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorOrgs) {
    toast.error(errorOrgs.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataOrgs);

  // CREATE
  const createOrgMutation = useMutation({
    ...createOrgsV1CoreOrgsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createOrgMutation", createOrgMutation);

  // UPDATE

  const updateOrgMutation = useMutation({
    ...updateOrgsV1CoreOrgsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateOrgMutation", updateOrgMutation);

  // DELETE
  const deleteOrgMutation = useMutation({
    ...deleteOrgsV1CoreOrgsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteOrg.mutateAsync({
  //       path: { item_id: (data as OrgDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataOrgs,
    createOrgMutation,
    updateOrgMutation,
    deleteOrgMutation,
  };
}
