import {
  But<PERSON>,
  Checkbox,
  InputBase,
  NumberInput,
  Radio,
  Select,
  Switch,
  Textarea,
  TextInput,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm, useStore } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { IMaskInput } from "react-imask";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createOrgsV1CoreOrgsPostMutation,
  deleteOrgsV1CoreOrgsItemIdDeleteMutation,
  updateOrgsV1CoreOrgsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import {
  OrgCreateTypes,
  OrgDisplayTypes,
  OrgUpdateTypes,
} from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultOrg = {

    id: 0,
    created_at: "",
    updated_at: "",
    created_by: 0,
    updated_by: 0,
    lang: "pl",
    json_metadata: "null",
    name: "",
    description: "null",
    is_public: false,
    address_id: 0,
    is_formal: false,
    nip: 0,
    regon: 0,
    max_img_width: 0,
    total_shares: 0,
    accounts_set_id: 0,
    voting_days: 0,
    members_by_admin: false,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    id: z.union([z.number(), z.string()]),
    created_at: z.string(),
    updated_at: z.string(),
    created_by: z.union([z.number(), z.string()]),
    updated_by: z.union([z.number(), z.string()]).nullable().optional(),
    lang: z.string(),
    json_metadata: z.string().nullable().optional(),
    name: z.string(),
    description: z.string().nullable().optional(),
    is_public: z.boolean().optional(),
    address_id: z.union([z.number(), z.string()]).nullable().optional(),
    is_formal: z.boolean().optional(),
    nip: z.string().nullable().optional(),
    regon: z.string().nullable().optional(),
    max_img_width: z.union([z.number(), z.string()]).nullable().optional(),
    total_shares: z.union([z.number(), z.string()]).nullable().optional(),
    accounts_set_id: z.union([z.number(), z.string()]).nullable().optional(),
    voting_days: z.union([z.number(), z.string()]).nullable().optional(),
    members_by_admin: z.boolean().optional(),
 })
  .passthrough();

interface PropsTypes {
  data?: OrgDisplayTypes;
  variant: "new" | "edit";
}

type FormData = z.infer<typeof formSchema>;

function OrgForm({ data = defaultOrg, variant = "edit" }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(
    new Set()
  );
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as OrgCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as OrgUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  const createOrg = useMutation({
    ...createOrgsV1CoreOrgsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleCreate(formData: OrgCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createOrg.mutateAsync({
      body: [
        {
          ...formData,
          created_by: user?.id,
         
        },
      ],
    });
  }

  const updateOrg = useMutation({
    ...updateOrgsV1CoreOrgsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleEdit(formData: OrgUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]])
    ) as OrgUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateOrg.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
         
        },
      ],
    });
  }

  const deleteOrg = useMutation({
    ...deleteOrgsV1CoreOrgsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteOrg.mutateAsync({
        path: { item_id: (data as OrgDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);


   return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit"
              ? t("forms.OrgForm.titleEdit.label")
              : t("forms.OrgForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
              {/* ID */}
              <div className={styles.span4}>
                <form.Field
                  name="id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.id.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* CREATED_AT */}
              <div className={styles.span4}>
                  <form.Field
                  name="created_at"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <DatePickerInput
                          label={t("forms.OrgForm.createdAt.label")}
                          value={
                                      variant === "new" ? new Date() : new Date(state.value)
                                    }
                          onChange={(value) => {
                          handleChange(value?.toISOString() || "" );
                          onFieldChange("created_at");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      
                      );
                  }}
                  />
              </div>              {/* UPDATED_AT */}
              <div className={styles.span4}>
                  <form.Field
                  name="updated_at"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <DatePickerInput
                          label={t("forms.OrgForm.updatedAt.label")}
                          value={
                                      variant === "new" ? new Date() : new Date(state.value)
                                    }
                          onChange={(value) => {
                          handleChange(value?.toISOString() || "" );
                          onFieldChange("updated_at");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      
                      );
                  }}
                  />
              </div>              {/* CREATED_BY */}
              <div className={styles.span4}>
                <form.Field
                  name="created_by"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.createdBy.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("created_by");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* UPDATED_BY */}
              <div className={styles.span4}>
                <form.Field
                  name="updated_by"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.updatedBy.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("updated_by");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              
              
              
              
              
              {/* LANG */}
              <div className={styles.span4}>
                  <form.Field
                  name="lang"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.OrgForm.lang.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("lang");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* JSON_METADATA */}
              <div className={styles.span4}>
                  <form.Field
                  name="json_metadata"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <Textarea
                          label={t("forms.OrgForm.jsonMetadata.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("json_metadata");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Description"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              
              
              
              
              
              {/* NAME */}
              <div className={styles.span4}>
                  <form.Field
                  name="name"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <TextInput
                          label={t("forms.OrgForm.name.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("name");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Enter your name"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* DESCRIPTION */}
              <div className={styles.span4}>
                  <form.Field
                  name="description"
                  children={({ state, handleChange, handleBlur }) => {
                      return (
                      <Textarea
                          label={t("forms.OrgForm.description.label")}
                          value={state.value || ""}
                          onChange={(e) => {
                          handleChange(e.target.value);
                          onFieldChange("description");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          placeholder="Description"
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                      );
                  }}
                  />
              </div>              {/* IS_PUBLIC */}
              <div className={styles.span4}>
                  <form.Field
                    name="is_public"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.OrgForm.isPublic.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("is_public");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
              {/* ADDRESS_ID */}
              <div className={styles.span4}>
                <form.Field
                  name="address_id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.addressId.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("address_id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* IS_FORMAL */}
              <div className={styles.span4}>
                  <form.Field
                    name="is_formal"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.OrgForm.isFormal.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("is_formal");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
              {/* NIP */}
              <div className={styles.span4}>
              <form.Field
                name="nip"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 000"
                      label={t("forms.OrgForm.nip.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("nip");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      onFieldChange("nip");
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />              {/* REGON */}
              <div className={styles.span4}>
              <form.Field
                name="regon"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 000"
                      label={t("forms.OrgForm.regon.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("regon");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      onFieldChange("regon");
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />              {/* MAX_IMG_WIDTH */}
              <div className={styles.span4}>
                <form.Field
                  name="max_img_width"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.maxImgWidth.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("max_img_width");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* TOTAL_SHARES */}
              <div className={styles.span4}>
                <form.Field
                  name="total_shares"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.totalShares.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("total_shares");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* ACCOUNTS_SET_ID */}
              <div className={styles.span4}>
                <form.Field
                  name="accounts_set_id"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.accountsSetId.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("accounts_set_id");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* VOTING_DAYS */}
              <div className={styles.span4}>
                <form.Field
                  name="voting_days"
                  children={({ state, handleChange, handleBlur }) => {
                    return (
                      <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                        hideControls
                        label={t("forms.OrgForm.votingDays.label")} 
                        value={state.value || ""}
                        onChange={(value: number | string | "") => {
                          handleChange(typeof value === "number" ? value : null);
                          onFieldChange("voting_days");
                        }}
                        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                      />
                    );
                  }}
                />
              </div>              {/* MEMBERS_BY_ADMIN */}
              <div className={styles.span4}>
                  <form.Field
                    name="members_by_admin"
                    children={({ state, handleChange, handleBlur }) => {
                      return (
                        <Switch
                          label={t("forms.OrgForm.membersByAdmin.label")}
                          checked={state.value || false}
                          onChange={(e) => {
                            handleChange(e.target.checked);
                            onFieldChange("members_by_admin");
                          }}
                          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                        />
                      );
                    }}
                  />
                </div>
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={!canSubmit || changedFields.size === 0}
                  loading={isSubmitting}
                >
                  {variant === "edit"
                    ? t("common.save.label")
                    : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" &&
              (data as OrgDisplayTypes)?.id !== user.curr_org_id && (
                <Button
                  color="red"
                  loading={updateOrg.isPending || createOrg.isPending}
                  // disabled
                  onClick={handleDelete}
                >
                  {t("common.delete.label")}
                </Button>
              )}
          </div>
        </form>
      </div>
    </>
  );
};

export default OrgForm;

