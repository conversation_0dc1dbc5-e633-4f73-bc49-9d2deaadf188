"use no memo";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex, Text, Tooltip } from "@mantine/core";
import { OrgDisplayTypes, OrgUpdateTypes } from "@/api/_client/types.gen";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import type { RootStateTypes } from "@/utils/redux/store";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import { modals } from "@mantine/modals";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import i18n from "i18next";
import {
  MantineReactTable,
  type MRT_ColumnDef,
  type MRT_Row,
  type MRT_TableOptions,
  useMantineReactTable,
} from "mantine-react-table";
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import { useMemo } from "react";
import { useSelector } from "react-redux";
import OrgForm from "../../-foms/OrgForm";

type PropsTypes = {
  data: OrgDisplayTypes[];
  createOrgMutation: any;
  updateOrgMutation: any;
  deleteOrgMutation: any;
};

const OrgsTable = ({ data, createOrgMutation, updateOrgMutation, deleteOrgMutation }: PropsTypes) => {
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<OrgDisplayTypes>[]>(
    () => [
      {
        accessorKey: "id",
        header: "Id",
      },
      {
        accessorKey: "created_at",
        header: "Created_at",
      },
      {
        accessorKey: "updated_at",
        header: "Updated_at",
      },
      {
        accessorKey: "created_by",
        header: "Created_by",
      },
      {
        accessorKey: "updated_by",
        header: "Updated_by",
      },
      {
        accessorKey: "lang",
        header: "Lang",
      },
      {
        accessorKey: "json_metadata",
        header: "Json_metadata",
      },
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "is_public",
        header: "Is_public",
      },
      {
        accessorKey: "address_id",
        header: "Address_id",
      },
      {
        accessorKey: "is_formal",
        header: "Is_formal",
      },
      {
        accessorKey: "nip",
        header: "Nip",
      },
      {
        accessorKey: "regon",
        header: "Regon",
      },
      {
        accessorKey: "max_img_width",
        header: "Max_img_width",
      },
      {
        accessorKey: "total_shares",
        header: "Total_shares",
      },
      {
        accessorKey: "accounts_set_id",
        header: "Accounts_set_id",
      },
      {
        accessorKey: "voting_days",
        header: "Voting_days",
      },
      {
        accessorKey: "members_by_admin",
        header: "Members_by_admin",
      },
    ],
    [],
  );

  //CREATE action
  const handleCreateUser: MRT_TableOptions<OrgUpdateTypes>["onCreatingRowSave"] = async ({
    values,
    exitCreatingMode,
  }) => {
    // await createOrgMutation.mutateAsync({
    //   body: [
    //     {
    //       ...values,
    //       created_by: user?.id,
    //       org_id: user?.curr_org_id,
    //     },
    //   ],
    // });
    exitCreatingMode();
  };

  //UPDATE action
  const handleUpdateUser: MRT_TableOptions<OrgUpdateTypes>["onEditingRowSave"] = async ({ values, table }) => {
    console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
    console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

    modals.open({
      title: "Edit user profile",
      children: (
        <>
          <OrgForm
            data={values}
            variant="edit"
            setEditingRow={table.setEditingRow}
            createOrgMutation={createOrgMutation}
            updateOrgMutation={updateOrgMutation}
          />
        </>
      ),
    });
  };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<OrgUpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: <Text>Are you sure you want to delete {row.original.display_name} </Text>,
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        deleteOrgMutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization: currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreateUser,
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdateUser,
    renderCreateRowModalContent: ({ table, row }) => (
      <OrgForm
        data={row.original}
        variant="new"
        setEditingRow={table.setEditingRow}
        createOrgMutation={createOrgMutation}
        updateOrgMutation={updateOrgMutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <OrgForm
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        createOrgMutation={createOrgMutation}
        updateOrgMutation={updateOrgMutation}
      />
    ),
    renderRowActions: ({ row, table }) => (
      <Flex gap="md">
        <Tooltip label="Edit">
          <ActionIcon
            onClick={() => {
              console.log("row", row);
              table.setEditingRow(row);
            }}
          >
            <IconEdit />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon color="red" onClick={() => openDeleteConfirmModal(row)}>
            <IconTrash />
          </ActionIcon>
        </Tooltip>
      </Flex>
    ),
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create Org
      </Button>
    ),
  });
  console.log("table", table);
  console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default OrgsTable;
