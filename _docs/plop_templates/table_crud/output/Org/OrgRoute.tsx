import { createFileRoute } from "@tanstack/react-router";

import { Suspense } from "react";
import {
  createOrgsV1CoreOrgsPostMutation,
  readOrgsAllV1CoreOrgsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import OrgsTable from "@/routes/config/-components/UsersTable/OrgsTable";
import { useOrgData } from "./-data_hooks/useOrgData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readOrgsAllV1CoreOrgsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const { dataOrgs, createOrgMutation, updateOrgMutation, deleteOrgMutation } = useOrgData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <h3>Users</h3>

        <OrgsTable
          data={dataOrgs.data}
          createOrgMutation={createOrgMutation}
          updateOrgMutation={updateOrgMutation}
          deleteOrgMutation={deleteOrgMutation}
        />
      </div>
    </Suspense>
  );
}
