import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createJobTemplatesV1CoreJobTemplatesPostMutation,
  deleteJobTemplatesV1CoreJobTemplatesItemIdDeleteMutation,
  readJobTemplatesAllV1CoreJobTemplatesGetOptions,
  updateJobTemplatesV1CoreJobTemplatesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { JobTemplateCreateTypes, JobTemplateUpdateTypes } from "@/client";

// type Props = {
//   updateData: JobTemplateUpdateTypes
//   createData: JobTemplateCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useJobTemplateData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataJobTemplates, error: errorJobTemplates } = useSuspenseQuery(
    readJobTemplatesAllV1CoreJobTemplatesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorJobTemplates) {
    toast.error(errorJobTemplates.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataJobTemplates);

  // CREATE
  const createJobTemplateMutation = useMutation({
    ...createJobTemplatesV1CoreJobTemplatesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readJobTemplatesAllV1CoreJobTemplatesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createJobTemplateMutation", createJobTemplateMutation);

  // UPDATE

  const updateJobTemplateMutation = useMutation({
    ...updateJobTemplatesV1CoreJobTemplatesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readJobTemplatesAllV1CoreJobTemplatesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateJobTemplateMutation", updateJobTemplateMutation);

  // DELETE
  const deleteJobTemplateMutation = useMutation({
    ...deleteJobTemplatesV1CoreJobTemplatesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readJobTemplatesAllV1CoreJobTemplatesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteJobTemplate.mutateAsync({
  //       path: { item_id: (data as JobTemplateDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataJobTemplates,
    createJobTemplateMutation,
    updateJobTemplateMutation,
    deleteJobTemplateMutation,
  };
}
