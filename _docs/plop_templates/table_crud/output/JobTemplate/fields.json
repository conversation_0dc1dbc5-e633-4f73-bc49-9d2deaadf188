[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.number()"}, {"name": "created_by", "inputType": "number", "defaultValue": "1", "valueType": "number", "zodType": "z.number()"}, {"name": "name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "template_type", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string()"}, {"name": "org_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.number()"}, {"name": "is_public", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean()"}, {"name": "job_details_template", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.any().nullable().optional()"}, {"name": "tasks_template", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.array(z.any()).nullable().optional()"}, {"name": "contrahent_template", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.any().nullable().optional()"}, {"name": "transactions_template", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.array(z.any()).nullable().optional()"}, {"name": "assigned_objects_template", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.array(z.number()).nullable().optional()"}]