import { But<PERSON>, Checkbox, InputBase, NumberInput, Radio, Select, Switch, Textarea, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { useForm, useStore } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { IMaskInput } from "react-imask";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createNoticesV1CoreNoticesPostMutation,
  deleteNoticesV1CoreNoticesItemIdDeleteMutation,
  updateNoticesV1CoreNoticesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { NoticeCreateTypes, NoticeDisplayTypes, NoticeUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defaultNotice = {
  name: null,
  description: null,
  is_active: null,
  expiry_date: null,
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
    name: z.string().nullable().optional(),
    description: z.string().nullable().optional(),
    is_active: z.boolean().nullable().optional(),
    expiry_date: z.string().nullable().optional(),
  })
  .passthrough();

interface PropsTypes {
  data?: NoticeDisplayTypes;
  variant: "new" | "edit";
  setEditingRow?: (row: any) => void;
  setCreatingRow?: (row: any) => void;
  createNoticeMutation: any;
  updateNoticeMutation: any;
}

type FormData = z.infer<typeof formSchema>;

function NoticeForm({
  data = defaultNotice,
  variant = "edit",
  setEditingRow,
  setCreatingRow,
  createNoticeMutation,
  updateNoticeMutation,
}: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as NoticeCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as NoticeUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  async function handleCreate(formData: NoticeCreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createNoticeMutation.mutateAsync(
      {
        body: [
          {
            ...formData,
            created_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setCreatingRow?.(null);
        },
      },
    );
  }

  async function handleEdit(formData: NoticeUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]]),
    ) as NoticeUpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await updateNoticeMutation.mutateAsync(
      {
        body: [
          {
            ...updatedFields,
            id: data?.id,
            updated_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setEditingRow?.(null);
        },
      },
    );
  }

  const deleteNotice = useMutation({
    ...deleteNoticesV1CoreNoticesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readNoticesAllV1CoreNoticesGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteNotice.mutateAsync({
        path: { item_id: (data as NoticeDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit" ? t("forms.NoticeForm.titleEdit.label") : t("forms.NoticeForm.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
            {/* NAME */}
            <div className={styles.span4}>
              <form.Field
                name="name"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.NoticeForm.name.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* DESCRIPTION */}
            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.NoticeForm.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
            {/* IS_ACTIVE */}
            <div className={styles.span4}>
              <form.Field
                name="is_active"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.NoticeForm.isActive.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_active");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
            {/* EXPIRY_DATE */}
            <div className={styles.span4}>
              <form.Field
                name="expiry_date"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <DatePickerInput
                      label={t("forms.NoticeForm.expiryDate.label")}
                      value={state.value ? new Date(state.value) : null}
                      onChange={(value) => {
                        handleChange(value?.toISOString() || "");
                        onFieldChange("expiry_date");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>{" "}
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0} loading={isSubmitting}>
                  {variant === "edit" ? t("common.save.label") : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" && (data as NoticeDisplayTypes)?.id !== user.curr_org_id && (
              <Button
                color="red"
                loading={updateNoticeMutation.isPending || createNoticeMutation.isPending}
                // disabled
                onClick={handleDelete}
              >
                {t("common.delete.label")}
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
}

export default NoticeForm;
