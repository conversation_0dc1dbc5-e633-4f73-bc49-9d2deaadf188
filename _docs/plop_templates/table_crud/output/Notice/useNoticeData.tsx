import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createNoticesV1CoreNoticesPostMutation,
  deleteNoticesV1CoreNoticesItemIdDeleteMutation,
  readNoticesAllV1CoreNoticesGetOptions,
  updateNoticesV1CoreNoticesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { NoticeCreateTypes, NoticeUpdateTypes } from "@/client";

// type Props = {
//   updateData: NoticeUpdateTypes
//   createData: NoticeCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useNoticeData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataNotices, error: errorNotices } = useSuspenseQuery(
    readNoticesAllV1CoreNoticesGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorNotices) {
    toast.error(errorNotices.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataNotices);

  // CREATE
  const createNoticeMutation = useMutation({
    ...createNoticesV1CoreNoticesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readNoticesAllV1CoreNoticesGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createNoticeMutation", createNoticeMutation);

  // UPDATE

  const updateNoticeMutation = useMutation({
    ...updateNoticesV1CoreNoticesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readNoticesAllV1CoreNoticesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateNoticeMutation", updateNoticeMutation);

  // DELETE
  const deleteNoticeMutation = useMutation({
    ...deleteNoticesV1CoreNoticesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readNoticesAllV1CoreNoticesGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteNotice.mutateAsync({
  //       path: { item_id: (data as NoticeDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataNotices,
    createNoticeMutation,
    updateNoticeMutation,
    deleteNoticeMutation,
  };
}
