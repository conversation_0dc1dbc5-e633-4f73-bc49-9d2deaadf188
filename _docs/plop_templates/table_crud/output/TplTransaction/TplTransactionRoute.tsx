import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createTplTransactionsV1MoneyTplTransactionsPostMutation,
  readTplTransactionsAllV1MoneyTplTransactionsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import TplTransactionsTable from "./-components/TplTransactionsTable";
import { useTplTransactionData } from "./-data_hooks/useTplTransactionData";

export const Route = createFileRoute("/config/users")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readTplTransactionsAllV1MoneyTplTransactionsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "TplTransaction Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/users"
      if (toLocation.pathname !== "/config/users") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const {
    dataTplTransactions,
    createTplTransactionMutation,
    updateTplTransactionMutation,
    deleteTplTransactionMutation,
  } = useTplTransactionData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <TplTransactionsTable
          data={dataTplTransactions.data}
          createTplTransactionMutation={createTplTransactionMutation}
          updateTplTransactionMutation={updateTplTransactionMutation}
          deleteTplTransactionMutation={deleteTplTransactionMutation}
        />
      </div>
    </Suspense>
  );
}
