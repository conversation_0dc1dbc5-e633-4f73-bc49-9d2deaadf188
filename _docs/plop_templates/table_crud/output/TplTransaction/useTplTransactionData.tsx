import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createTplTransactionsV1MoneyTplTransactionsPostMutation,
  deleteTplTransactionsV1MoneyTplTransactionsItemIdDeleteMutation,
  readTplTransactionsAllV1MoneyTplTransactionsGetOptions,
  updateTplTransactionsV1MoneyTplTransactionsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { TplTransactionCreateTypes, TplTransactionUpdateTypes } from "@/client";

// type Props = {
//   updateData: TplTransactionUpdateTypes
//   createData: TplTransactionCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useTplTransactionData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataTplTransactions, error: errorTplTransactions } = useSuspenseQuery(
    readTplTransactionsAllV1MoneyTplTransactionsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorTplTransactions) {
    toast.error(errorTplTransactions.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataTplTransactions);

  // CREATE
  const createTplTransactionMutation = useMutation({
    ...createTplTransactionsV1MoneyTplTransactionsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplTransactionsAllV1MoneyTplTransactionsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createTplTransactionMutation", createTplTransactionMutation);

  // UPDATE

  const updateTplTransactionMutation = useMutation({
    ...updateTplTransactionsV1MoneyTplTransactionsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplTransactionsAllV1MoneyTplTransactionsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateTplTransactionMutation", updateTplTransactionMutation);

  // DELETE
  const deleteTplTransactionMutation = useMutation({
    ...deleteTplTransactionsV1MoneyTplTransactionsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplTransactionsAllV1MoneyTplTransactionsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteTplTransaction.mutateAsync({
  //       path: { item_id: (data as TplTransactionDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataTplTransactions,
    createTplTransactionMutation,
    updateTplTransactionMutation,
    deleteTplTransactionMutation,
  };
}
