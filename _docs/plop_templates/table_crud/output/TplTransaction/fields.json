[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "created_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "\"pl\"", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "null", "valueType": "object", "zodType": "z.record(z.any()).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "date", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "type", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "due_date", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "amount", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "job_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "comment", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "object_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "is_saved", "inputType": "switch", "defaultValue": "false", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_schedule", "inputType": "switch", "defaultValue": "false", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "posted_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.union([z.string(), z.date()]).nullable().optional()"}, {"name": "cust_ref", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "our_ref", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "memo", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "contrahent_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "set_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "splits", "inputType": "textArea", "defaultValue": "[]", "valueType": "array", "zodType": "z.array(z.any()).nullable().optional()"}]