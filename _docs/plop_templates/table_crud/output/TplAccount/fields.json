[{"name": "id", "inputType": "number", "defaultValue": "0", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "created_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "updated_at", "inputType": "date", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "created_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "updated_by", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "lang", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "json_metadata", "inputType": "textArea", "defaultValue": "null", "valueType": "object", "zodType": "z.record(z.unknown()).nullable().optional()"}, {"name": "name", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "name_short", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "label", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "label_short", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "description", "inputType": "textArea", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "group_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "level", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "type", "inputType": "select", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "is_debit_minus", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "acc_number", "inputType": "text", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "currency", "inputType": "select", "defaultValue": "null", "valueType": "string", "zodType": "z.string().nullable().optional()"}, {"name": "parent_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "is_branch", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_placeholder", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "is_active", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "balance", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}, {"name": "is_syntetyczne", "inputType": "select", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "enabled", "inputType": "switch", "defaultValue": "null", "valueType": "boolean", "zodType": "z.boolean().nullable().optional()"}, {"name": "set_id", "inputType": "number", "defaultValue": "null", "valueType": "number", "zodType": "z.union([z.number(), z.string()]).nullable().optional()"}]