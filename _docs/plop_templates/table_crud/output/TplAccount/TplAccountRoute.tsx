import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  createTplAccountsV1MoneyTplAccountsPostMutation,
  readTplAccountsAllV1MoneyTplAccountsGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/utils/redux/systemSlice";
import TplAccountsTable from "./-components/TplAccountsTable";
import { useTplAccountData } from "./-data_hooks/useTplAccountData";

export const Route = createFileRoute("/config/accounts")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(
        readTplAccountsAllV1MoneyTplAccountsGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        }),
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "TplAccount Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away from "/config/accounts"
      if (toLocation.pathname !== "/config/accounts") {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { dataTplAccounts, createTplAccountMutation, updateTplAccountMutation, deleteTplAccountMutation } =
    useTplAccountData();

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
        <TplAccountsTable
          data={dataTplAccounts.data}
          createTplAccountMutation={createTplAccountMutation}
          updateTplAccountMutation={updateTplAccountMutation}
          deleteTplAccountMutation={deleteTplAccountMutation}
        />
      </div>
    </Suspense>
  );
}
