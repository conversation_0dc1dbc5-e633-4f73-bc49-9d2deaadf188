import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createTplAccountsV1MoneyTplAccountsPostMutation,
  deleteTplAccountsV1MoneyTplAccountsItemIdDeleteMutation,
  readTplAccountsAllV1MoneyTplAccountsGetOptions,
  updateTplAccountsV1MoneyTplAccountsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { TplAccountCreateTypes, TplAccountUpdateTypes } from "@/client";

// type Props = {
//   updateData: TplAccountUpdateTypes
//   createData: TplAccountCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useTplAccountData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataTplAccounts, error: errorTplAccounts } = useSuspenseQuery(
    readTplAccountsAllV1MoneyTplAccountsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorTplAccounts) {
    toast.error(errorTplAccounts.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataTplAccounts);

  // CREATE
  const createTplAccountMutation = useMutation({
    ...createTplAccountsV1MoneyTplAccountsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplAccountsAllV1MoneyTplAccountsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createTplAccountMutation", createTplAccountMutation);

  // UPDATE

  const updateTplAccountMutation = useMutation({
    ...updateTplAccountsV1MoneyTplAccountsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplAccountsAllV1MoneyTplAccountsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateTplAccountMutation", updateTplAccountMutation);

  // DELETE
  const deleteTplAccountMutation = useMutation({
    ...deleteTplAccountsV1MoneyTplAccountsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readTplAccountsAllV1MoneyTplAccountsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteTplAccount.mutateAsync({
  //       path: { item_id: (data as TplAccountDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataTplAccounts,
    createTplAccountMutation,
    updateTplAccountMutation,
    deleteTplAccountMutation,
  };
}
