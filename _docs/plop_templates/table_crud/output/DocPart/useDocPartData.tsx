import { useMutation, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { t } from "i18next";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  createDocPartsV1CoreDocPartsPostMutation,
  deleteDocPartsV1CoreDocPartsItemIdDeleteMutation,
  readDocPartsAllV1CoreDocPartsGetOptions,
  updateDocPartsV1CoreDocPartsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import type { RootStateTypes } from "@/utils/redux/store";
// import { DocPartCreateTypes, DocPartUpdateTypes } from "@/client";

// type Props = {
//   updateData: DocPartUpdateTypes
//   createData: DocPartCreateTypes
//   setChangedFields: unknown
//   context: "table" | "form"
//   itemId: number
// }
// {updateData, createData, setChangedFields, context, itemId}: Props

export function useDocPartData() {
  const queryClient = useQueryClient();
  const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);

  // GET DATA
  const { data: dataDocParts, error: errorDocParts } = useSuspenseQuery(
    readDocPartsAllV1CoreDocPartsGetOptions({
      query: {
        org_id: curr_org_id || 0,
      },
    }),
  );

  if (errorDocParts) {
    toast.error(errorDocParts.message);
  }
  console.log("### DATA HOOK #### get user profiles", dataDocParts);

  // CREATE
  const createDocPartMutation = useMutation({
    ...createDocPartsV1CoreDocPartsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocPartsAllV1CoreDocPartsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    // //client side optimistic update
    // onMutate: (newUserInfo: User) => {
    //   queryClient.setQueryData(
    //     ["users"],
    //     (prevUsers: any) =>
    //       [
    //         ...prevUsers,
    //         {
    //           ...newUserInfo,
    //           id: (Math.random() + 1).toString(36).substring(7),
    //         },
    //       ] as User[]
    //   );
    // },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("createDocPartMutation", createDocPartMutation);

  // UPDATE

  const updateDocPartMutation = useMutation({
    ...updateDocPartsV1CoreDocPartsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocPartsAllV1CoreDocPartsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  console.log("updateDocPartMutation", updateDocPartMutation);

  // DELETE
  const deleteDocPartMutation = useMutation({
    ...deleteDocPartsV1CoreDocPartsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readDocPartsAllV1CoreDocPartsGet"],
      });
      toast.success(t("common.success.label"));
      // form.reset();
      queryClient.invalidateQueries();
      // setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  // ##### temp code snipet #####

  // const handleDelete = () => {
  //   if (data?.id == user.curr_org_id) {
  //     toast.error("You can't delete your active org");
  //     return;
  //   }
  //   // console.log("Delete org:", user?.curr_org_id);
  //   if (window.confirm("Delete?")) {
  //     deleteDocPart.mutateAsync({
  //       path: { item_id: (data as DocPartDisplayTypes)?.id },
  //     });
  //   }
  // };

  return {
    dataDocParts,
    createDocPartMutation,
    updateDocPartMutation,
    deleteDocPartMutation,
  };
}
