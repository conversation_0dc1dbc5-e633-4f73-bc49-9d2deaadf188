import { useState, useEffect } from "react";
import styles from "@/styles/Form.module.css";
import { useForm, useStore } from "@tanstack/react-form";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import {
  TextInput,
  Button,
  Textarea,
  Switch,
  NumberInput,
  InputBase,
  Select,
  Checkbox,
  Radio,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import { IMaskInput } from "react-imask";
import {
  {{modelName}}UpdateTypes,
  {{modelName}}DisplayTypes,
  {{modelName}}CreateTypes,
} from "@/api/_client/types.gen";
import {
  update{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPutMutation,
  create{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPostMutation,
  delete{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sItemIdDeleteMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import type { RootStateTypes } from "@/redux/store";
import { normalizeString } from "@/utils/formHelpers";

const default{{modelName}} = {

  {{#each fields}}
    {{name}}: null,
  {{/each}}
};
// number imput type: z.union([z.number(), z.string()])

const formSchema = z
  .object({
  {{#each fields}}
    {{name}}: {{zodType}},
  {{/each}}
 })
  .passthrough();

interface PropsTypes {
  data?: {{modelName}}DisplayTypes;
  variant: "new" | "edit";
  setEditingRow?: (row: any) => void;
  setCreatingRow?: (row: any) => void;
  create{{modelName}}Mutation: any;
  update{{modelName}}Mutation: any;
}

type FormData = z.infer<typeof formSchema>;

function {{modelName}}Form({ 
  data = default{{modelName}},
  variant = "edit",
  setEditingRow,
  setCreatingRow,
  create{{modelName}}Mutation,
  update{{modelName}}Mutation }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(
    new Set()
  );
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as {{modelName}}CreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as {{modelName}}UpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);



  async function handleCreate(formData: {{modelName}}CreateTypes) {
    // console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await create{{modelName}}Mutation.mutateAsync({
      body: [
        {
          ...formData,
          created_by: user?.id,
         
        },
      ],
    },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setCreatingRow?.(null);
        },
      });
  }



  async function handleEdit(formData: {{modelName}}UpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]])
    ) as {{modelName}}UpdateTypes;
    // console.log(
    //   " <<< UPDATING ORG >>> updatedFields",
    //   updatedFields,
    //   user?.curr_org_id,
    //   user?.id
    // );
    await update{{modelName}}Mutation.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
         
        },
      ],
    },
      {
        onSuccess: () => {
          form.reset();
          setChangedFields(new Set());
          setEditingRow?.(null);
        },
      });
  }

  const delete{{modelName}} = useMutation({
    ...delete{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your active org");
      return;
    }
    // console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      delete{{modelName}}.mutateAsync({
        path: { item_id: (data as {{modelName}}DisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);


   return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit"
              ? t("forms.{{modelName}}Form.titleEdit.label")
              : t("forms.{{modelName}}Form.titleNew.label")}{" "}
            {data?.name}
          </h2>

          <div className={styles.formGrid}>
            {{#each fields}}
              {{> (partialName inputType)  modelName=../modelName}}
            {{/each}}
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={!canSubmit || changedFields.size === 0}
                  loading={isSubmitting}
                >
                  {variant === "edit"
                    ? t("common.save.label")
                    : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" &&
              (data as {{modelName}}DisplayTypes)?.id !== user.curr_org_id && (
                <Button
                  color="red"
                  loading={update{{modelName}}Mutation.isPending || create{{modelName}}Mutation.isPending}
                  // disabled
                  onClick={handleDelete}
                >
                  {t("common.delete.label")}
                </Button>
              )}
          </div>
        </form>
      </div>
    </>
  );
};

export default {{modelName}}Form;

