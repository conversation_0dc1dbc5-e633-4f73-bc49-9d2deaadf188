import { createFile<PERSON>out<PERSON>, useRouter } from "@tanstack/react-router";
import { useDispatch } from "react-redux";
import { Suspense, useEffect } from "react";
import {{modelName}}sTable from "./-components/{{modelName}}sTable";
import {
  create{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPostMutation,
  read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGetOptions,
} from "@/api/_client/@tanstack/react-query.gen";
import { use{{modelName}}Data } from "./-data_hooks/use{{modelName}}Data";
import { updateSystemField } from "@/redux/systemSlice";
import Loading from "@/components/system/Loading";


export const Route = createFileRoute("/config/users")({
  loader: async ({
    context: { queryClient, curr_org_id, curr_profile_id },
  }) => {
    try {
      await queryClient.prefetchQuery(
        read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGetOptions({
          query: {
            org_id: curr_org_id || 0,
          },
        })
      );
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "{{modelName}} Page" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe(
      "onBeforeNavigate",
      ({ toLocation }) => {
        // Check if navigating away from "/config/users"
        if (toLocation.pathname !== "/config/users") {
          dispatch(updateSystemField({ pageTitle: "" }));
        }
      }
    );

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);
 
  const {
    data{{modelName}}s,
    create{{modelName}}Mutation,
    update{{modelName}}Mutation,
    delete{{modelName}}Mutation,
  } = use{{modelName}}Data();



  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <div className="table-wrapper">
       
        <{{modelName}}sTable
          data={data{{modelName}}s.data}
          create{{modelName}}Mutation={create{{modelName}}Mutation}
          update{{modelName}}Mutation={update{{modelName}}Mutation}
          delete{{modelName}}Mutation={delete{{modelName}}Mutation}
        />
      </div>
    </Suspense>
  );
}
