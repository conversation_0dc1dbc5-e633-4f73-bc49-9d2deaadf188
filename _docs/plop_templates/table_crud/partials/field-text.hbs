{{log ">>>>>> modelName <<<<<<<" modelName}}
{{log ">>>>>> name <<<<<<<" name}}

{{log ">>>>>> this <<<<<<<" this}}

{/* {{constantCase name}} */}
<div className={styles.span4}>
    <form.Field
    name="{{snakeCase name}}"
    children={({ state, handleChange, handleBlur }) => {
        return (
        <TextInput
            label={t("forms.{{titleCase modelName}}Form.{{camelCase name}}.label")}
            value={state.value || ""}
            onChange={(e) => {
            handleChange(e.target.value);
            onFieldChange("{{snakeCase name}}");
            }}
            onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
            placeholder="Enter your name"
            //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
        />
        );
    }}
    />
</div>