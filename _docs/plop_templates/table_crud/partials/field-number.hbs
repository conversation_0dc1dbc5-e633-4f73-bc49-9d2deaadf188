{/* {{constantCase name}} */}
<div className={styles.span4}>
  <form.Field
    name="{{snakeCase name}}"
    children={({ state, handleChange, handleBlur }) => {
      return (
        <NumberInput
                    allowedDecimalSeparators={[",", "."]}
          hideControls
          label={t("forms.{{titleCase modelName}}Form.{{camelCase name}}.label")} 
          value={state.value || ""}
          onChange={(value: number | string | "") => {
            handleChange(typeof value === "number" ? value : null);
            onFieldChange("{{snakeCase name}}");
          }}
          onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
          //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
        />
      );
    }}
  />
</div>