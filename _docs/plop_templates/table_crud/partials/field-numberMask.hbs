{/* {{constantCase name}} */}
<div className={styles.span4}>
<form.Field
  name="{{snakeCase name}}"
  children={({ state, handleChange, handleBlur }) => {
    return (
      <InputBase
        component={IMaskInput}
        mask="000 000 000"
        label={t("forms.{{titleCase modelName}}Form.{{camelCase name}}.label")}
        value={state.value?.toString() || ""}
        onChange={(e: any) => {
          const value = e.target?.value?.replace(/\s/g, "") || "";
          handleChange(value);
          onFieldChange("{{snakeCase name}}");
        }}
        onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
       
        //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
      />
    );
  }}
/>

