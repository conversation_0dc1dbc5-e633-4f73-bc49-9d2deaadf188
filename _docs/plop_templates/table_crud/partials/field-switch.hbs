{/* {{constantCase name}} */}
<div className={styles.span4}>
    <form.Field
      name="{{snakeCase name}}"
      children={({ state, handleChange, handleBlur }) => {
        return (
          <Switch
            label={t("forms.{{titleCase modelName}}Form.{{camelCase name}}.label")}
            checked={state.value || false}
            onChange={(e) => {
              handleChange(e.target.checked);
              onFieldChange("{{snakeCase name}}");
            }}
            onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
            //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
          />
        );
      }}
    />
  </div>
