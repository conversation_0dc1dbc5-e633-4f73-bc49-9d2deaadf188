{/* {{constantCase name}} */}
<div className={styles.span4}>
    <form.Field
    name="{{snakeCase name}}"
    children={({ state, handleChange, handleBlur }) => {
        return (
        <Checkbox
        label={t("forms.{{titleCase modelName}}Form.{{snakeCase name}}.label")}
        type="checkbox"
        value="length"
        checked={checkedItems.includes("{{snakeCase name}}")}
        onChange={() => {
            handleSurfaceCalc;
            onFieldChange("{{snakeCase name}}");
            }}
        />
        </div>
        );
    }}
    />
</div>