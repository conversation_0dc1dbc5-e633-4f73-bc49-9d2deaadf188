{/* {{constantCase name}} */}
<div className={styles.span4}>
    <form.Field
    name="{{snakeCase name}}"
    children={({ state, handleChange, handleBlur }) => {
        return (
        <DatePickerInput
            type="range"
            label={t("forms.{{titleCase modelName}}Form.{{camelCase name}}.label")}
           value={state.value ? new Date(state.value) : null}
            onChange={(value) => {
            handleChange(value?.toISOString() || "" );
            onFieldChange("{{snakeCase name}}");
            }}
            onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
            //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
        />
        
        );
    }}
    />
</div>