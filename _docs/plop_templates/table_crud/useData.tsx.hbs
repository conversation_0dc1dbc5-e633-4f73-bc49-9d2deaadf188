import {
    useMutation,
    useSuspenseQuery,
    useQueryClient,
  } from "@tanstack/react-query";
  import {
    read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGetOptions,
    create{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPostMutation,
    update{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPutMutation,
    delete{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sItemIdDeleteMutation,
  } from "@/api/_client/@tanstack/react-query.gen";
  import { useSelector } from "react-redux";
  import type { RootStateTypes } from "@/redux/store";
  import toast from "react-hot-toast";
  import { t } from "i18next";
  // import { {{modelName}}CreateTypes, {{modelName}}UpdateTypes } from "@/client";
  
  // type Props = {
  //   updateData: {{modelName}}UpdateTypes
  //   createData: {{modelName}}CreateTypes
  //   setChangedFields: unknown
  //   context: "table" | "form"
  //   itemId: number
  // }
  // {updateData, createData, setChangedFields, context, itemId}: Props
  
  export function use{{modelName}}Data() {
    const queryClient = useQueryClient();
    const { curr_org_id } = useSelector((state: RootStateTypes) => state.user);
  
    // GET DATA
    const { data: data{{modelName}}s, error: error{{modelName}}s } = useSuspenseQuery(
      read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGetOptions({
        query: {
          org_id: curr_org_id || 0,
        },
      })
    );
  
    if (error{{modelName}}s) {
      toast.error(error{{modelName}}s.message);
    }
    console.log("### DATA HOOK #### get user profiles", data{{modelName}}s);
  
    // CREATE
    const create{{modelName}}Mutation = useMutation({
      ...create{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPostMutation(),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGet"],
        });
        toast.success(t("common.success.label"));
  
        queryClient.invalidateQueries();
      },
      // //client side optimistic update
      // onMutate: (newUserInfo: User) => {
      //   queryClient.setQueryData(
      //     ["users"],
      //     (prevUsers: any) =>
      //       [
      //         ...prevUsers,
      //         {
      //           ...newUserInfo,
      //           id: (Math.random() + 1).toString(36).substring(7),
      //         },
      //       ] as User[]
      //   );
      // },
      onError: (error) => {
        toast.error(`${t("common.failed.label")} ${error}`);
      },
    });
  
    console.log("create{{modelName}}Mutation", create{{modelName}}Mutation);
  
    // UPDATE
  
    const update{{modelName}}Mutation = useMutation({
      ...update{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sPutMutation(),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGet"],
        });
        toast.success(t("common.success.label"));
        queryClient.invalidateQueries();
      },
      onError: (error) => {
        toast.error(`${t("common.failed.label")} ${error}`);
      },
    });
  
    console.log("update{{modelName}}Mutation", update{{modelName}}Mutation);
  
    // DELETE
    const delete{{modelName}}Mutation = useMutation({
      ...delete{{modelName}}sV1{{titleCase schemaName}}{{modelName}}sItemIdDeleteMutation(),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["read{{modelName}}sAllV1{{titleCase schemaName}}{{modelName}}sGet"],
        });
        toast.success(t("common.success.label"));
        // form.reset();
        queryClient.invalidateQueries();
        // setChangedFields(new Set());
      },
      onError: (error) => {
        toast.error(`${t("common.failed.label")} ${error}`);
      },
    });
  
    // ##### temp code snipet #####
  
    // const handleDelete = () => {
    //   if (data?.id == user.curr_org_id) {
    //     toast.error("You can't delete your active org");
    //     return;
    //   }
    //   // console.log("Delete org:", user?.curr_org_id);
    //   if (window.confirm("Delete?")) {
    //     delete{{modelName}}.mutateAsync({
    //       path: { item_id: (data as {{modelName}}DisplayTypes)?.id },
    //     });
    //   }
    // };
  
    return {
      data{{modelName}}s,
      create{{modelName}}Mutation,
      update{{modelName}}Mutation,
      delete{{modelName}}Mutation,
    };
  }
  