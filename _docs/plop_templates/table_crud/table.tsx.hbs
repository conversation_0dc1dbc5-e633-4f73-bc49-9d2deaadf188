"use no memo";
import "@mantine/core/styles.css";
import "@mantine/dates/styles.css"; //if using mantine date picker features
import "mantine-react-table/styles.css"; //make sure MRT styles were imported in your app root (once)
import { useMemo, useState } from "react";
import {
  MantineReactTable,
  useMantineReactTable,
  type MRT_ColumnDef,
  type MRT_TableOptions,
  type MRT_Row,
  MRT_EditActionButtons,
} from "mantine-react-table";
import { MRT_Localization_PL } from "@/i18n/MRT_pl.js";
import { MRT_Localization_EN } from "@/i18n/MRT_en.js";
import i18n from "i18next";
import { useSelector } from "react-redux";
import type { RootStateTypes } from "@/redux/store";
import {
  {{modelName}}UpdateTypes,
  {{modelName}}DisplayTypes,
} from "@/api/_client/types.gen";
import {
  ActionIcon,
  Button,
  Flex,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { IconEdit, IconTrash } from "@tabler/icons-react";
import { modals } from "@mantine/modals";
import {{modelName}}Form from "../../-forms/{{modelName}}Form";

type PropsTypes = {
  data: {{modelName}}DisplayTypes[];
  create{{modelName}}Mutation: any;
  update{{modelName}}Mutation: any;
  delete{{modelName}}Mutation: any;
};

const {{modelName}}sTable = ({
  data,
  create{{modelName}}Mutation,
  update{{modelName}}Mutation,
  delete{{modelName}}Mutation,
}: PropsTypes) => {
  console.log(">>>>>>>>>>>>   data", data);
  const currLanguage = i18n.language;
  const user = useSelector((state: RootStateTypes) => state.user);

 

  //should be memoized or stable
  const columns = useMemo<MRT_ColumnDef<{{modelName}}DisplayTypes>[]>(
    () => [
    {{#each fields}}
      {
      accessorKey: "{{name}}",
      header: "{{titleCase name}}",
      },
    {{/each}}
      
    ],
    []
  );

  //CREATE action
  const handleCreate{{modelName}}: MRT_TableOptions<{{modelName}}UpdateTypes>["onCreatingRowSave"] =
    async ({ values, exitCreatingMode }) => {
      // await create{{modelName}}Mutation.mutateAsync({
      //   body: [
      //     {
      //       ...values,
      //       created_by: user?.id,
      //       org_id: user?.curr_org_id,
      //     },
      //   ],
      // });
      exitCreatingMode();
    };

  //UPDATE action
  const handleUpdate{{modelName}}: MRT_TableOptions<{{modelName}}UpdateTypes>["onEditingRowSave"] =
    async ({ values, table }) => {
      console.log("values >>>>>>>> Editing >>>>>>>>>> values", values);
      console.log("table >>>>>>>> Editing >>>>>>>>>> table", table);

      modals.open({
        title: "Edit user profile",
        children: (
          <>
            <{{modelName}}Form
              data={values}
              variant="edit"
              setEditingRow={table.setEditingRow}
              create{{modelName}}Mutation={create{{modelName}}Mutation}
              update{{modelName}}Mutation={update{{modelName}}Mutation}
            />
          </>
        ),
      });
    };

  //DELETE action
  const openDeleteConfirmModal = (row: MRT_Row<{{modelName}}UpdateTypes>) =>
    modals.openConfirmModal({
      title: "Sure?",
      children: (
        <Text>
          Are you sure you want to delete {row.original.display_name}{" "}
        </Text>
      ),
      labels: { confirm: "Delete", cancel: "Cancel" },
      confirmProps: { color: "red" },
      onConfirm: () => {
        console.log("deleting user id:", row.original.id);
        delete{{modelName}}Mutation.mutateAsync({
          path: { item_id: row.original.id },
        });
      },
    });

  const table = useMantineReactTable({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    localization:
      currLanguage === "en" ? MRT_Localization_EN : MRT_Localization_PL, //change to your localization
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,
    mantineTableProps: {
      //   className: clsx(classes.table),
      highlightOnHover: false,
      striped: "odd",
      withColumnBorders: true,
      withRowBorders: true,
      withTableBorder: true,
    },
    createDisplayMode: "modal", //default ('row', and 'custom' are also available)
    editDisplayMode: "modal", //default ('row', 'cell', 'table', and 'custom' are also available)
    enableEditing: true,
    // getRowId: (row) => {
    //   console.log("row", row);
    //   return row.id;
    // },
    // onCreatingRowCancel: () => setValidationErrors({}),
    onCreatingRowSave: handleCreate{{modelName}},
    // onEditingRowCancel: () => setValidationErrors({}),
    onEditingRowSave: handleUpdate{{modelName}},
    renderCreateRowModalContent: ({ table, row }) => (
      <{{modelName}}Form
        data={undefined}
        variant="new"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        create{{modelName}}Mutation={create{{modelName}}Mutation}
        update{{modelName}}Mutation={update{{modelName}}Mutation}
      />
    ),
    renderEditRowModalContent: ({ table, row }) => (
      <{{modelName}}Form
        data={row.original}
        variant="edit"
        setEditingRow={table.setEditingRow}
        setCreatingRow={table.setCreatingRow}
        create{{modelName}}Mutation={create{{modelName}}Mutation}
        update{{modelName}}Mutation={update{{modelName}}Mutation}
      />
    ),
    renderRowActions: ({ row, table }) => (
      <Flex gap="md">
        <Tooltip label="Edit">
          <ActionIcon 
            variant="subtle" 
            onClick={() => {
              console.log("row", row);
              table.setEditingRow(row);
            }}
          >
            <IconEdit />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Delete">
          <ActionIcon  
            variant="subtle" 
            color="red" 
            onClick={() => openDeleteConfirmModal(row)}>
            <IconTrash />
          </ActionIcon>
        </Tooltip>
      </Flex>
    ),
    renderTopToolbarCustomActions: ({ table }) => (
      <Button
        onClick={() => {
          table.setCreatingRow(true); //simplest way to open the create row modal with no default values
          //or you can pass in a row object to set default values with the `createRow` helper function
          // table.setCreatingRow(
          //   createRow(table, {
          //     //optionally pass in default values for the new row, useful for nested data or other complex scenarios
          //   }),
          // );
        }}
      >
        Create {{modelName}}
      </Button>
    ),
  });
  console.log("table", table);
  console.log("table >>>> state", table.getState());

  return <MantineReactTable table={table} />;
};

export default {{modelName}}sTable;
