// plop-templates/form.tsx.hbs
import React from 'react';

interface {{modelName}}FormProps {
  onSubmit: (data: any) => void;
}

{{json fields}} 

const default{{modelName}} = {
  {{#each fields}} 
  '{{field.name}}': '{{field.defaultValue}}'
  {{/each}}
};

const {{modelName}}Form: React.FC<{{modelName}}FormProps> = ({ onSubmit }) => {
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    const formData = new FormData(event.target as HTMLFormElement);
    const data: any = {};
    {{#each fields}}
    data['{{name}}'] = formData.get('{{name}}');
    {{/each}}
    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit}>
         
      {{#each fields}}
          {{#with (fieldTemplate inputType) as |templateName|}}
           
           {{> (templateName) field=this modelName=../modelName }}
          {{/with}}
      
      {{/each}}
      <button type="submit">Submit</button>
    </form>
  );
};

export default {{modelName}}Form;