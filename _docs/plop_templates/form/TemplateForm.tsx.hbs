import { useState, useEffect } from "react";
import styles from "@/styles/Form.module.css";
import { useForm, useStore } from "@tanstack/react-form";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import {
  TextInput,
  Button,
  Textarea,
  Switch,
  NumberInput,
  InputBase,
} from "@mantine/core";
import { IMaskInput } from "react-imask";
import { DatePickerInput } from '@mantine/dates';
import {
  {{pascalCase model_name}}UpdateTypes,
  {{pascalCase model_name}}DisplayTypes,
  {{pascalCase model_name}}CreateTypes,
} from "@/api/_client/types.gen";
import {
  update{{pascalCase model_name}}sV1Core{{pascalCase model_name}}sPutMutation,
  create{{pascalCase model_name}}sV1Core{{pascalCase model_name}}sPostMutation,
  delete{{pascalCase model_name}}sV1Core{{pascalCase model_name}}sItemIdDeleteMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import type { RootStateTypes } from "@/redux/store";
import { normalizeString } from "@/utils/formHelpers";

const defalult{{pascalCase model_name}} = {
  id: 0,
  created_at: "",
  updated_at: "",
  created_by: 0,
  updated_by: 0,
  lang: "pl",
  name: "",
  description: "",
  is_public: false,
  address_id: 0,
  is_formal: true,
  nip: "",
  regon: "",
  max_img_width: 1500,
  total_shares: 1,
  accounts_set_id: 0,
  voting_days: 7,
  members_by_admin: true,
  json_metadata: {
    storage: {
      email: "",
      storage_type: "",
      tokens: {
        access_token: "",
        expiry_date: 0,
        id_token: "",
        refresh_token: "",
        scope: "",
        token_type: "",
      },
      s3: {
        bucket_name: "",
        region: "",
        access_key_id: "",
        secret_access_key: "",
        account_id: "",
        endpoint: "",
      },
    },
  },
};

const formSchema = z
  .object({
    id: z.number(),
    created_at: z.string(),
    updated_at: z.string(),
    created_by: z.number(),
    updated_by: z.number().nullish(),
    lang: z.string(),
    name: z.string().min(3, "Minimum 3 characters required"),
    description: z.string().nullish(),
    is_public: z.boolean().nullish(),
    address_id: z.number().nullish(),
    is_formal: z.boolean().nullish(),
    nip: z.union([z.string(), z.number()]).nullish(),
    regon: z.union([z.string(), z.number()]).nullish(),
    max_img_width: z.number().nullish(),
    total_shares: z.number().nullish(),
    accounts_set_id: z.number().nullish(),
    voting_days: z.number().nullish(),
    members_by_admin: z.boolean().nullish(),
    json_metadata: z
      .object({
        storage: z
          .object({
            email: z.string().nullish(),
            storage_type: z.string().nullish(),
            tokens: z
              .object({
                access_token: z.string().nullish(),
                expiry_date: z.number().nullish(),
                id_token: z.string().nullish(),
                refresh_token: z.string().nullish(),
                scope: z.string().nullish(),
                token_type: z.string().nullish(),
              })
              .nullish(),
            s3: z
              .object({
                bucket_name: z.string().nullish(),
                region: z.string().nullish(),
                access_key_id: z.string().nullish(),
                secret_access_key: z.string().nullish(),
                account_id: z.string().nullish(),
                endpoint: z.string().nullish(),
              })
              .nullish(),
          })
          .nullish(),
      })
      .nullish(),
  })
  .passthrough();

interface PropsTypes {
  data?: {{pascalCase model_name}}DisplayTypes;
  variant: "new" | "edit";
}

type FormData = z.infer<typeof formSchema>;

function {{pascalCase model_name}}Form({ data = defalult{{pascalCase model_name}}, variant = "edit" }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(
    new Set()
  );
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as {{pascalCase model_name}}CreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as {{pascalCase model_name}}UpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  const create{{pascalCase model_name}} = useMutation({
    ...create{{pascalCase model_name}}sV1Core{{pascalCase model_name}}sPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["read{{pascalCase model_name}}sAllV1Core{{pascalCase model_name}}sGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleCreate(formData: {{pascalCase model_name}}CreateTypes) {
    // console.log(" <<< CREATING {{pascalCase model_name}} >>> normalizedFormData", formData, user?.id);
    await create{{pascalCase model_name}}.mutateAsync({
      body: [
        {
          ...formData,
          created_by: user?.id,
          nip: formData?.nip?.toString(),
          regon: formData?.regon?.toString(),
        },
      ],
    });
  }

  const update{{pascalCase model_name}} = useMutation({
    ...update{{pascalCase model_name}}sV1Core{{pascalCase model_name}}sPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["read{{pascalCase model_name}}sAllV1Core{{pascalCase model_name}}sGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleEdit(formData: {{pascalCase model_name}}UpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]])
    ) as {{pascalCase model_name}}UpdateTypes;
    // console.log(
    //   " <<< UPDATING {{pascalCase model_name}} >>> updatedFields",
    //   updatedFields,
    //   user?.curr_{{pascalCase model_name}}_id,
    //   user?.id
    // );
    await update{{pascalCase model_name}}.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
          nip: formData?.nip?.toString(),
          regon: formData?.regon?.toString(),
        },
      ],
    });
  }

  const delete{{pascalCase model_name}} = useMutation({
    ...delete{{pascalCase model_name}}sV1Core{{pascalCase model_name}}sItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["read{{pascalCase model_name}}sAllV1Core{{pascalCase model_name}}sGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_{{pascalCase model_name}}_id) {
      toast.error("You can't delete your active {{pascalCase model_name}}");
      return;
    }
    // console.log("Delete {{pascalCase model_name}}:", user?.curr_{{pascalCase model_name}}_id);
    if (window.confirm("Delete?")) {
      delete{{pascalCase model_name}}.mutateAsync({
        path: { item_id: (data as {{pascalCase model_name}}DisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit"
              ? t("forms.{{pascalCase model_name}}Form.titleEdit.label")
              : t("forms.{{pascalCase model_name}}Form.titleNew.label")}{" "}
            {data?.name}
          </h2>
          <div className={styles.formGrid}>
            <div className={styles.span4}>
              <form.Field
                name="name"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.{{pascalCase model_name}}Form.name.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.{{pascalCase model_name}}Form.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="is_formal"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.{{pascalCase model_name}}Form.isFormal.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        console.log("e.target.checked", e.target.checked);
                        handleChange(e.target.checked);
                        onFieldChange("is_formal");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}></div>

            <div className={styles.span2}>
              <form.Field
                name="nip"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 0000"
                      label={t("forms.{{pascalCase model_name}}Form.nip.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("nip");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="NIP"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="regon"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 000"
                      label={t("forms.{{pascalCase model_name}}Form.regon.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("regon");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="regon"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="total_shares"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.{{pascalCase model_name}}Form.totalShares.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("total_shares");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="voting_days"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.{{pascalCase model_name}}Form.votingDays.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("voting_days");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="max_img_width"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.{{pascalCase model_name}}Form.maxImgWidth.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("max_img_width");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span4}>
              <form.Field
                name="is_public"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.{{pascalCase model_name}}Form.isPublic.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_public");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span4}>
              <form.Field
                name="members_by_admin"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.{{pascalCase model_name}}Form.membersByAdmin.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("members_by_admin");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={!canSubmit || changedFields.size === 0}
                  loading={isSubmitting}
                >
                  {variant === "edit"
                    ? t("common.save.label")
                    : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" &&
              (data as {{pascalCase model_name}}DisplayTypes)?.id !== user.curr_{{pascalCase model_name}}_id && (
                <Button
                  color="red"
                  loading={update{{pascalCase model_name}}.isPending || create{{pascalCase model_name}}.isPending}
                  // disabled
                  onClick={handleDelete}
                >
                  {t("common.delete.label")}
                </Button>
              )}
          </div>
        </form>
      </div>
    </>
  );
}

export default {{pascalCase model_name}}Form;
