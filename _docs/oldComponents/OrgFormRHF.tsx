import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { Button, TextInput } from "@mantine/core";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createOrgsV1CoreOrgsPostMutation,
  updateOrgsV1CoreOrgsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { OrgCreateTypes, OrgUpdateTypes } from "@/api/_client/types.gen";
import { Switch, Textarea } from "@/components/form";
import styles from "@/styles/Form.module.css";
import { normalizeDataForDB, normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defalultOrg: OrgUpdateTypes = {
  id: 0,
  name: "",
  description: "",
  updated_by: undefined,
  is_public: true,
  address_id: 0,
  is_formal: true,
  nip: "",
  regon: "",
  max_img_width: 1500,
  total_shares: 1,
  accounts_set_id: 0,
  voting_days: 7,
  members_by_admin: true,
  json_metadata: {
    storage: {
      email: "",
      storage_type: "",
      tokens: {
        access_token: "",
        expiry_date: 0,
        id_token: "",
        refresh_token: "",
        scope: "",
        token_type: "",
      },
      s3: {
        bucket_name: "",
        region: "",
        access_key_id: "",
        secret_access_key: "",
        account_id: "",
        endpoint: "",
      },
    },
  },
};

const formSchema = z.object({
  id: z.string().optional(),
  updated_by: z.string().optional(),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  is_public: z.boolean().optional(),
  address_id: z.string().optional(),
  is_formal: z.boolean().optional(),
  nip: z.string().optional(),
  regon: z.string().optional(),
  max_img_width: z.number().optional(),
  total_shares: z.number().optional(),
  accounts_set_id: z.string().optional(),
  voting_days: z.number().optional(),
  members_by_admin: z.boolean().optional(),
});

type FormData = OrgUpdateTypes;

interface OrgFormProps {
  data?: FormData;
  variant: "edit" | "new";
}

function OrgForm({ data = defalultOrg, variant = "edit" }: OrgFormProps) {
  //
  console.log("variant", variant);
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = React.useState<Set<keyof FormData>>(new Set());
  console.log("changedFields", changedFields);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    // defaultValues: denormalizeDataFromDB(
    //   variant === "edit" ? data : DEFAULT_ORG
    // ),
  });

  React.useEffect(() => {
    const initialData = variant === "edit" ? data : defalultOrg;
    form.reset(initialData);
  }, [data]);

  const allFields = form.watch();

  const updateOrg = useMutation({
    ...updateOrgsV1CoreOrgsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const createOrg = useMutation({
    ...createOrgsV1CoreOrgsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      form.reset(defalultOrg);
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const onSubmit = async () => {
    const formData = form.getValues();
    console.log("onSubmit", formData);
    if (variant === "edit") {
      const normalizedFormData = normalizeDataForDB(formData) as OrgUpdateTypes;
      // send only changed fields
      const updatedFields = Object.fromEntries(
        Array.from(changedFields).map((field) => [field, normalizedFormData[field]]),
      ) as OrgUpdateTypes;
      console.log(" <<< UPDATING ORG >>> updatedFields", updatedFields, data.id, user?.id);
      await updateOrg.mutateAsync({
        body: [
          {
            ...updatedFields,
            id: data.id,
            updated_by: user?.id,
          },
        ],
      });
    } else if (variant === "new") {
      // if (formData.id) {
      //   delete formData.id;
      // }
      const normalizedFormData = normalizeDataForDB(formData) as OrgCreateTypes;

      // convert nip to string
      if (normalizedFormData?.nip) {
        normalizedFormData.nip = normalizedFormData.nip.toString();
      }
      // convert regon to string
      if (normalizedFormData?.regon) {
        normalizedFormData.regon = normalizedFormData.regon.toString();
      }
      // convert max_img_width to number
      if (normalizedFormData?.max_img_width != null) {
        normalizedFormData.max_img_width = Number(normalizedFormData.max_img_width);
      }
      // convert total_shares to number
      if (normalizedFormData?.total_shares != null) {
        normalizedFormData.total_shares = Number(normalizedFormData.total_shares);
      }
      // convert voting_days to number
      if (normalizedFormData?.voting_days != null) {
        normalizedFormData.voting_days = Number(normalizedFormData.voting_days);
      }
      console.log(" <<< CREATING ORG >>> normalizedFormData", normalizedFormData, user?.id);
      await createOrg.mutateAsync({
        body: [
          {
            ...normalizedFormData,
            created_by: user?.id,
            json_metadata: data.json_metadata,
            name: normalizedFormData.name || "",
            description: normalizedFormData.description || "",
            is_public: normalizedFormData.is_public || false,
            address_id: normalizedFormData.address_id || null,
            is_formal: normalizedFormData.is_formal || false,
            nip: normalizedFormData.nip || null,
            regon: normalizedFormData.regon || null,
            max_img_width: normalizedFormData.max_img_width ?? undefined,
            total_shares: normalizedFormData.total_shares ?? undefined,
            accounts_set_id: normalizedFormData.accounts_set_id || null,
            voting_days: normalizedFormData.voting_days ?? undefined,
            members_by_admin: normalizedFormData.members_by_admin || false,
            lang: normalizedFormData.lang || undefined,
          },
        ],
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.getValues(fieldName);
    const originalValue = data[fieldName];

    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);

    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };
  console.log("formState", form.formState);
  return (
    <>
      <pre>{JSON.stringify(allFields, null, 2)}</pre>
      <div className={styles.container}>
        <form
          // onSubmit={(e) => {
          //   e.preventDefault();
          //   form.handleSubmit(onSubmit);
          // }}
          className={styles.form}
        >
          <h2>
            {variant === "edit" ? t("forms.org.titleEdit.label") : t("forms.org.titleNew.label")} {data.name}
          </h2>
          <div className={styles.formGrid}>
            <div className={styles.span4}>
              <TextInput
                label={t("org.name.label")}
                error={form.formState.errors.name?.message}
                {...form.register("name", {
                  onChange: (value) => {
                    console.log("value", value);
                    onFieldChange("name");
                  },
                })}
              />
              {/* <TextInput
                name="textInput"
                control={form.control}
                label="TextBox"
                onChange={() => onFieldChange("name")}
              /> */}
            </div>
            <div className={styles.span4}>
              <Textarea
                label={t("forms.org.description.label")}
                error={form.formState.errors.description?.message}
                {...form.register("description", {
                  onChange: () => onFieldChange("description"),
                })}
              />
            </div>

            <div className={styles.span4}>
              <Controller
                name="is_public"
                control={form.control}
                defaultValue={false}
                render={({ field }) => (
                  <Switch
                    label="Is Public"
                    checked={field.value || false}
                    onChange={(event) => {
                      const newValue = event.currentTarget.checked;
                      field.onChange(newValue);
                      onFieldChange("is_public");
                    }}
                  />
                )}
              />
            </div>

            {/* <div className={styles.span4}>
              <Switch
                name="is_public"
                label={t("forms.org.isPublic.label")}
                {...form.register("is_public", {
                  onChange: () => onFieldChange("is_public"),
                })}
              />
            </div> */}

            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.addressId.label")}
                error={form.formState.errors.address_id?.message}
                {...form.register("address_id", {
                  onChange: () => onFieldChange("address_id"),
                })}
              />
            </div>
            {/* <div className={styles.span4}>
              <Switch
                name="is_formal"
                label={t("forms.org.isFormal.label")}
                {...form.register("is_formal", {
                  onChange: () => onFieldChange("is_formal"),
                })}
              />
            </div> */}
            <div className={styles.span4}>
              <Controller
                name="is_formal"
                control={form.control}
                defaultValue={false}
                render={({ field }) => (
                  <Switch
                    label={t("forms.org.isFormal.label")}
                    checked={field.value || false}
                    onChange={(event) => {
                      const newValue = event.currentTarget.checked;
                      field.onChange(newValue);
                      onFieldChange("is_formal");
                    }}
                  />
                )}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.nip.label")}
                error={form.formState.errors.nip?.message}
                {...form.register("nip", {
                  onChange: () => onFieldChange("nip"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.regon.label")}
                error={form.formState.errors.regon?.message}
                {...form.register("regon", {
                  onChange: () => onFieldChange("regon"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.maxImgWidth.label")}
                error={form.formState.errors.max_img_width?.message}
                {...form.register("max_img_width", {
                  onChange: () => onFieldChange("max_img_width"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.totalShares.label")}
                error={form.formState.errors.total_shares?.message}
                {...form.register("total_shares", {
                  onChange: () => onFieldChange("total_shares"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.accountsSetId.label")}
                error={form.formState.errors.accounts_set_id?.message}
                {...form.register("accounts_set_id", {
                  onChange: () => onFieldChange("accounts_set_id"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.org.votingDays.label")}
                error={form.formState.errors.voting_days?.message}
                {...form.register("voting_days", {
                  onChange: () => onFieldChange("voting_days"),
                })}
              />
            </div>
            {/* <div className={styles.span4}>
              <Switch
                name="members_by_admin"
                label={t("forms.org.membersByAdmin.label")}
                {...form.register("members_by_admin", {
                  onChange: () => onFieldChange("members_by_admin"),
                })}
              />
            </div> */}
            <div className={styles.span4}>
              <Controller
                name="members_by_admin"
                control={form.control}
                defaultValue={false}
                render={({ field }) => (
                  <Switch
                    label={t("forms.org.membersByAdmin.label")}
                    checked={field.value || false}
                    onChange={(event) => {
                      const newValue = event.currentTarget.checked;
                      field.onChange(newValue);
                      onFieldChange("members_by_admin");
                    }}
                  />
                )}
              />
            </div>
          </div>
        </form>
        <div className={styles.formActions}>
          <Button
            // type="submit"
            loading={updateOrg.isPending || createOrg.isPending}
            disabled={changedFields.size === 0}
            onClick={onSubmit}
          >
            {variant === "edit" ? t("common.save.label") : t("common.create.label")}
          </Button>
        </div>
      </div>
    </>
  );
}

export default OrgForm;
