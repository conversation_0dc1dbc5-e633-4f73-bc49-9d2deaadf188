import Keycloak from "keycloak-js";

const keycloak = new Keycloak({
  url: `${import.meta.env.PUBLIC_KC_URL}`,
  realm: `${import.meta.env.PUBLIC_KC_REALM}`,
  clientId: `${import.meta.env.PUBLIC_KC_CLIENT_ID}`,
});

let isInitialized = false;

// Initialize Keycloak
export const initKeycloak = async (loginRequired = true) => {
  // console.log("[Auth] Starting initKeycloak");

  if (isInitialized) {
    console.log("[Auth] Already initialized, returning keycloak instance");
    return keycloak;
  }

  try {
    const initialized = await keycloak.init({
      onLoad: loginRequired ? "login-required" : "check-sso",
      // onLoad: "login-required",
      silentCheckSsoRedirectUri: window.location.origin + "/silent-check-sso.html",
      redirectUri: window.location.href,
      pkceMethod: "S256",
      checkLoginIframe: false,
      enableLogging: true,
    });

    // console.log("[Auth] Keycloak initialized:", { initialized, authenticated: keycloak.authenticated });

    if (initialized && keycloak.authenticated) {
      startTokenRefresh();
    }

    // Set up event listeners for login/logout
    keycloak.onAuthSuccess = () => {
      // console.log("[Auth] onAuthSuccess triggered, authenticated:", keycloak.authenticated);
      startTokenRefresh();
    };
    keycloak.onAuthLogout = () => {
      // console.log("[Auth] onAuthLogout triggered, authenticated:", keycloak.authenticated);
      stopTokenRefresh();
    };

    isInitialized = true;
    // console.log("[Auth] Initialization complete, authenticated:", keycloak.authenticated);
    return keycloak;
  } catch (error) {
    // console.error("[Auth] Failed to initialize Keycloak:", error);
    throw error;
  }
};

let tokenRefreshInterval = null;

function startTokenRefresh() {
  // console.log("[Auth] Starting token refresh");
  if (tokenRefreshInterval) return;

  tokenRefreshInterval = setInterval(() => {
    if (keycloak.authenticated) {
      keycloak.updateToken(70).catch(() => {
        console.error("[Auth] Failed to refresh token");
      });
    }
  }, 60000); // Check token every minute
}

function stopTokenRefresh() {
  // console.log("[Auth] Stopping token refresh");
  if (tokenRefreshInterval) {
    clearInterval(tokenRefreshInterval);
    tokenRefreshInterval = null;
  }
}

export async function login(options = {}) {
  // console.log("[Auth] Login called with options:", options);
  const defaultRedirectUri = window.location.origin + "/dashboard";
  try {
    await keycloak.login({
      redirectUri: options.redirectUri || defaultRedirectUri,
      ...options, // Spread other potential options
    });
    // Token refresh will be handled by onAuthSuccess
  } catch (error) {
    console.error("[Auth] Login failed:", error);
    throw error;
  }
}

export async function logout(options = {}) {
  // console.log("[Auth] Logout called with options:", options);
  try {
    // Token refresh will be stopped by onAuthLogout
    const defaultRedirectUri = window.location.origin;

    // Ensure we're completely logging out by specifying options
    await keycloak.logout({
      redirectUri: options.redirectUri || defaultRedirectUri,
      ...options,
    });

    // For extra certainty, clear any Keycloak-related items from localStorage
    const keycloakItems = Object.keys(localStorage).filter((key) => key.startsWith("kc-") || key.includes("keycloak"));

    keycloakItems.forEach((key) => localStorage.removeItem(key));
  } catch (error) {
    console.error("[Auth] Logout failed:", error);
    throw error;
  }
}

async function getToken() {
  // console.log("[Auth] GetToken called, current state:", {
  //   authenticated: keycloak.authenticated,
  // });
  try {
    await keycloak.updateToken(5);
    return keycloak.token;
  } catch (error) {
    console.error("[Auth] GetToken failed:", error);
    throw error;
  }
}

// Export isLoggedIn as a function
export function isLoggedIn() {
  // console.log(
  //   "[Auth] isLoggedIn called, authenticated:",
  //   keycloak.authenticated
  // );
  return keycloak.authenticated ?? false;
}

function getUser() {
  return keycloak;
}

function getUserInfo() {
  if (!keycloak.authenticated) {
    // console.log("[Auth] getUserInfo: User not authenticated");
    return null;
  }

  const tokenParsed = keycloak.tokenParsed;
  // console.log("[Auth] Token data:", {
  //   tokenParsed,
  //   email: tokenParsed.email,
  //   name: tokenParsed.name,
  //   preferredUsername: tokenParsed.preferred_username,
  //   givenName: tokenParsed.given_name,
  //   familyName: tokenParsed.family_name,
  // });

  return {
    tokenParsed: tokenParsed,
    email: tokenParsed.email,
    name: tokenParsed.name,
    preferredUsername: tokenParsed.preferred_username,
    givenName: tokenParsed.given_name,
    familyName: tokenParsed.family_name,
    // Get all custom attributes from the token
  };
}

function getCustomAttribute(attributeName) {
  if (!keycloak.authenticated) {
    return null;
  }
  return keycloak.tokenParsed[attributeName] || null;
}

export function getKeycloakAttributes() {
  const user = getUser();
  // console.log("getUserKeycloakId - user:", user);
  // console.log("getUserKeycloakId - tokenParsed:", user?.tokenParsed);
  // console.log("getUserKeycloakId - idTokenParsed:", user?.idTokenParsed);
  const id = user?.tokenParsed?.sub || user?.idTokenParsed?.sub;
  const orgs_limit = user?.tokenParsed?.orgs_limit || 0;
  const orgs_created = user?.tokenParsed?.orgs_created || 0;
  // console.log("getUserKeycloakId - final id:", id);
  return { id, orgs_limit, orgs_created };
}

export function getUserRoles() {
  const user = getUser();
  return user?.tokenParsed?.roles || [];
}

export { startTokenRefresh, stopTokenRefresh, getToken, getUser, getUserInfo, getCustomAttribute };
