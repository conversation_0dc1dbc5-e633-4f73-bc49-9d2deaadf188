import {
  getRouteApi,
  type NavigateOptions,
  type RegisteredRouter,
  type RouteIds,
  useNavigate,
} from "@tanstack/react-router";
import type {
  ColumnFiltersState,
  ColumnOrderState,
  PaginationState,
  RowSelectionState,
  SortingState,
  Updater,
} from "@tanstack/react-table";
import { cleanEmptyParams } from "./clean-empty-params";

// export type ColumnFilter = {
//   column_id: string;
//   value: string;
// };

// export type ColumnSorting = {
//   column_id: string;
//   direction: "asc" | "desc";
// };

export type TableFilters = {
  sorting?: SortingState;
  pagination: PaginationState;
  columnFilters?: ColumnFiltersState;
  columnOrder?: ColumnOrderState;
  columnVisibility?: Record<string, boolean>;
  globalFilter?: string;
  startDate?: string;
  endDate?: string;
};

export const DEFAULT_PAGE_INDEX = 0;
export const DEFAULT_PAGE_SIZE = 20;

export function useFilters<T extends RouteIds<RegisteredRouter["routeTree"]>>(routeId: T) {
  const routeApi = getRouteApi(routeId);
  const navigate = useNavigate();
  const searchParams = routeApi.useSearch() as TableFilters;
  // console.log("searchParams", searchParams);

  // // Provide default values to prevent undefined states
  // const filters: TableFilters = {
  //   columnFilters: searchParams.columnFilters ?? [],
  //   globalFilter: searchParams.globalFilter ?? "",
  //   sorting: searchParams.sorting ?? [],
  //   pageIndex: searchParams.pageIndex ?? DEFAULT_PAGE_INDEX,
  //   pageSize: searchParams.pageSize ?? DEFAULT_PAGE_SIZE,
  // };

  const setFilters = (partialFilters: Partial<TableFilters>) => {
    // console.log("partialFilters", partialFilters);
    const navigateOptions: NavigateOptions = {
      search: (prev: TableFilters) => {
        const newSearch = cleanEmptyParams({
          ...prev,
          ...partialFilters,
        });

        return newSearch;
      },

      replace: true,
    };
    // console.log("NAVIGATING TO", navigateOptions);
    navigate(navigateOptions);
  };

  const resetFilters = () => {
    const navigateOptions: NavigateOptions = {
      search: {
        pagination: { pageIndex: DEFAULT_PAGE_INDEX, pageSize: DEFAULT_PAGE_SIZE },
      },
      replace: true,
    };
    navigate(navigateOptions);
  };

  return {
    filters: searchParams,
    setFilters,
    resetFilters,
  };
}
