import { Button, NumberInput, Switch, Textarea, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  createOrgsV1CoreOrgsPostMutation,
  deleteOrgsV1CoreOrgsItemIdDeleteMutation,
  updateOrgsV1CoreOrgsPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { OrgCreateTypes, OrgUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeDataForDB, normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const getDefalultOrg = (variant) => ({
  ...(variant === "edit"
    ? {
        id: 0,
        updated_by: 0,
      }
    : {
        cerated_by: 0,
      }),
  name: "",
  description: "",
  is_public: false,
  address_id: 0,
  is_formal: true,
  nip: "",
  regon: "",
  max_img_width: 1500,
  total_shares: 1,
  accounts_set_id: 0,
  voting_days: 7,
  members_by_admin: true,
  json_metadata: {
    storage: {
      email: "",
      storage_type: "",
      tokens: {
        access_token: "",
        expiry_date: 0,
        id_token: "",
        refresh_token: "",
        scope: "",
        token_type: "",
      },
      s3: {
        bucket_name: "",
        region: "",
        access_key_id: "",
        secret_access_key: "",
        account_id: "",
        endpoint: "",
      },
    },
  },
});

type FormData = OrgFormProps["variant"] extends "edit" ? OrgUpdateTypes : OrgCreateTypes;

interface OrgFormProps {
  data?: FormData;
  variant: "edit" | "new";
}

function OrgForm({ data, variant = "edit" }) {
  //

  console.log("variant", variant);
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // for dev use only - disable in production;
  const [formState, setFormState] = useState(data);

  const form = useForm({
    mode: "uncontrolled",
    initialValues: getDefalultOrg(variant),
    validateInputOnBlur: true,
    validate: {
      name: (value) => (value.length < 3 ? "Przynajmniej 3 znaki" : null),
    },
  });

  // update form values on data change
  useEffect(() => {
    const initialData = variant === "edit" ? data : defalultOrg;
    form.setValues(initialData);
  }, [data]);

  const updateOrg = useMutation({
    ...updateOrgsV1CoreOrgsPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const createOrg = useMutation({
    ...createOrgsV1CoreOrgsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const deleteOrg = useMutation({
    ...deleteOrgsV1CoreOrgsItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readOrgsAllV1CoreOrgsGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const transformData = (data = {}) => {
    const transformed = {};

    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        let value = data[key];

        // Handle json_metadata field
        if (key === "json_metadata") {
          console.log("value", value);
          if (value === null || (typeof value === "object" && Object.keys(value).length === 0)) {
            value = null; // Set to null if it's null or an empty object
          }
          // Otherwise, keep the object as is
        }
        // Convert boolean strings to booleans
        else if (value === "true") {
          value = true;
        } else if (value === "false") {
          value = false;
        }
        // // Convert strings containing only digits to numbers
        // else if (typeof value === "string" && /^[0-9]+$/.test(value)) {
        //   value = Number(value);
        // }
        else if (key === "nip" || key === "regon" || key === "name") {
          value = value.toString();
        } else if (typeof value === "string" && value === "") {
          value = null;
        }
        // Convert all other values to strings
        // else {
        //   value = value.toString();
        // }

        transformed[key] = value;
      }
    }

    return transformed;
  };

  const handleCreate = async () => {
    if (!form.isValid()) {
      form.validate();
      console.log("form errors", form.errors);
      toast.error(t("common.failed.label"));
      return;
    }
    const formData = form.getValues() as OrgCreateTypes;
    console.log("onSubmit", formData);
    const transformedFormData = transformData(formData) as OrgCreateTypes;
    console.log(" <<< CREATING ORG >>> normalizedFormData", formData, user?.id);
    await createOrg.mutateAsync({
      body: [
        {
          ...transformedFormData,
          created_by: user?.id,
        },
      ],
    });
  };

  const handleEdit = async () => {
    form.validate();

    // if (!form.isValid()) {
    //   console.log("form errors", form.errors);
    //   toast.error(t("common.failed.label"));
    //   return;
    // }
    const formData = form.getValues();
    console.log("onSubmit", formData);
    if (variant === "edit") {
      // const normalizedFormData = normalizeDataForDB(formData) as OrgUpdateTypes;
      // send only changed fields
      const updatedFields = Object.fromEntries(
        Array.from(changedFields).map((field) => [field, formData[field]]),
      ) as OrgUpdateTypes;
      console.log(" <<< UPDATING ORG >>> updatedFields", updatedFields, user?.curr_org_id, user?.id);
      await updateOrg.mutateAsync({
        body: [
          {
            ...updatedFields,
            id: user?.curr_org_id || 0,
            updated_by: user?.id,
          },
        ],
      });
    } else if (variant === "new") {
      // if (formuser?.curr_org_id) {
      //   delete formuser?.curr_org_id;
      // }
      const normalizedFormData = normalizeDataForDB(formData) as OrgCreateTypes;

      console.log(" <<< CREATING ORG >>> normalizedFormData", normalizedFormData, user?.id);
      await createOrg.mutateAsync({
        body: [
          {
            ...normalizedFormData,
            created_by: user?.id,
          },
        ],
      });
    }
  };

  const handleDelete = () => {
    if (data?.id == user.curr_org_id) {
      toast.error("You can't delete your activeorg");
      return;
    }
    console.log("Delete org:", user?.curr_org_id);
    if (window.confirm("Delete?")) {
      deleteOrg.mutateAsync({
        path: { item_id: data?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const formValues = form.getValues();
    // for dev use only - disable in production
    setFormState({
      ...formValues,
      id: data?.id || 0, // Add the id field to match OrgUpdateTypes
    } as OrgUpdateTypes);
    const currentValue = formValues[fieldName];
    const originalValue = data[fieldName];
    console.log("onFieldChange", {
      fieldName,
      currentValue,
      originalValue: data[fieldName],
    });

    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);

    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  return (
    <>
      <pre>{JSON.stringify(formState, null, 2)}</pre>
      <div className={styles.container}>
        <form className={styles.form}>
          <h2>
            {variant === "edit" ? t("forms.OrgForm.titleEdit.label") : t("forms.OrgForm.titleNew.label")} {data.name}
          </h2>
          <div className={styles.formGrid}>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.OrgForm.name.label")}
                key={form.key("name")}
                {...form.getInputProps("name")}
                onChange={(e) => {
                  form.getInputProps("name").onChange(e);
                  onFieldChange("name");
                }}
              />
            </div>
            <div className={styles.span4}>
              <Textarea
                label={t("forms.OrgForm.description.label")}
                key={form.key("description")}
                {...form.getInputProps("description")}
                onChange={(e) => {
                  form.getInputProps("description").onChange(e);
                  onFieldChange("description");
                }}
              />
            </div>
            <div className={styles.span4}>
              <Switch
                label={t("forms.OrgForm.isFormal.label")}
                key={form.key("is_formal")}
                {...form.getInputProps("is_formal", { type: "checkbox" })}
                onChange={(e) => {
                  form.getInputProps("is_formal", { type: "checkbox" }).onChange(e);
                  onFieldChange("is_formal");
                }}
              />
            </div>
            {formState.is_formal && (
              <>
                <div className={styles.span2}>
                  <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                    hideControls
                    label={t("forms.OrgForm.nip.label")}
                    key={form.key("nip")}
                    {...form.getInputProps("nip")}
                    onChange={(e) => {
                      form.getInputProps("nip").onChange(e);
                      onFieldChange("nip");
                    }}
                  />
                </div>
                <div className={styles.span2}>
                  <NumberInput
                    allowedDecimalSeparators={[",", "."]}
                    hideControls
                    label={t("forms.OrgForm.regon.label")}
                    key={form.key("regon")}
                    {...form.getInputProps("regon")}
                    onChange={(e) => {
                      form.getInputProps("regon").onChange(e);
                      onFieldChange("regon");
                    }}
                  />
                </div>
              </>
            )}

            <div className={styles.span1}>
              <NumberInput
                allowedDecimalSeparators={[",", "."]}
                hideControls
                label={t("forms.OrgForm.totalShares.label")}
                key={form.key("total_shares")}
                min={1}
                {...form.getInputProps("total_shares")}
                onChange={(value) => {
                  form.getInputProps("total_shares").onChange(value);
                  onFieldChange("total_shares");
                }}
              />
            </div>
            <div className={styles.span1}></div>
            <div className={styles.span1}>
              <NumberInput
                allowedDecimalSeparators={[",", "."]}
                hideControls
                label={t("forms.OrgForm.votingDays.label")}
                key={form.key("voting_days")}
                min={1}
                {...form.getInputProps("voting_days")}
                onChange={(value) => {
                  form.getInputProps("voting_days").onChange(value);
                  onFieldChange("voting_days");
                }}
              />
            </div>
            <div className={styles.span1}></div>
            <div className={styles.span1}>
              <NumberInput
                allowedDecimalSeparators={[",", "."]}
                hideControls
                label={t("forms.OrgForm.maxImgWidth.label")}
                key={form.key("max_img_width")}
                {...form.getInputProps("max_img_width")}
                onChange={(value) => {
                  form.getInputProps("max_img_width").onChange(value);
                  onFieldChange("max_img_width");
                }}
              />
            </div>
            <div className={styles.span3}></div>
            <div className={styles.span2}>
              <Switch
                label={t("forms.OrgForm.isPublic.label")}
                key={form.key("is_public")}
                {...form.getInputProps("is_public", { type: "checkbox" })}
                onChange={(e) => {
                  form.getInputProps("is_public", { type: "checkbox" }).onChange(e);
                  onFieldChange("is_public");
                }}
              />
            </div>
            <div className={styles.span2}></div>

            <div className={styles.span2}>
              <Switch
                label={t("forms.OrgForm.membersByAdmin.label")}
                key={form.key("members_by_admin")}
                {...form.getInputProps("members_by_admin", {
                  type: "checkbox",
                })}
                onChange={(e) => {
                  form.getInputProps("members_by_admin", { type: "checkbox" }).onChange(e);
                  onFieldChange("members_by_admin");
                }}
              />
            </div>
          </div>
        </form>
        <div className={styles.formActions}>
          {/* SAVE - creating */}
          {variant === "new" && (
            <Button type="submit" loading={updateOrg.isPending || createOrg.isPending} onClick={handleCreate}>
              {t("common.create.label")}
            </Button>
          )}
          {/* SAVE - editing */}
          {variant === "edit" && (
            <Button
              type="submit"
              loading={updateOrg.isPending || createOrg.isPending}
              disabled={changedFields.size === 0}
              onClick={handleEdit}
            >
              {t("common.save.label")}
            </Button>
          )}
          {/* DELETE - editing not current org */}
          {variant === "edit" && data.id !== user.curr_org_id && (
            <Button color="red" loading={updateOrg.isPending || createOrg.isPending} onClick={handleDelete}>
              {t("common.delete.label")}
            </Button>
          )}
          {/* DELETE - editing current org */}
          {variant === "edit" && data.id == user.curr_org_id && (
            <Button color="red" loading={updateOrg.isPending || createOrg.isPending} disabled onClick={handleDelete}>
              {t("forms.OrgForm.cannotDeleteCurrOrg.label")}
            </Button>
          )}
        </div>
      </div>
    </>
  );
}

export default OrgForm;
