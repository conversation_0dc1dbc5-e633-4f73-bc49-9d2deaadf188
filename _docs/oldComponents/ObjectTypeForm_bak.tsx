import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import { createObjectTypesV1CoreObjectTypesPostMutation } from "@/api/_client/@tanstack/react-query.gen";
import { ObjectTypeCreateTypes } from "@/api/_client/types.gen";
import { Button, TextInput, TextInputNumber } from "@/components/form";
import styles from "@/styles/Form.module.css";
import type { RootStateTypes } from "@/utils/redux/store";

const DEFAULT_OBJECT_TYPE = {
  name: "",
  label: "",
  description: "",
  org_id: null,
  created_by: undefined,
};

const formSchema = z.object({
  name: z.string().min(2, {
    message: "forms.objectType.nameError.label",
  }),
  label: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  org_id: z.number().optional().nullable(),
  created_by: z.number().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface ObjectTypeFormProps {
  data: ObjectTypeCreateTypes;
}

function ObjectTypeForm({ data = DEFAULT_OBJECT_TYPE }: ObjectTypeFormProps) {
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = React.useState<Set<keyof FormData>>(new Set());

  const createObjectType = useMutation(createObjectTypesV1CoreObjectTypesPostMutation());

  // Helper function to normalize string values
  const normalizeValue = (value: any): any => {
    if (typeof value === "string") {
      // Trim leading/trailing spaces and reduce multiple spaces to single space
      return value.trim().replace(/\s+/g, " ") || null;
    }
    return value === "" ? null : value;
  };

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: normalizeValue(data.name) || "",
      label: normalizeValue(data.label) || "",
      description: normalizeValue(data.description) || "",
      org_id: normalizeValue(data.org_id) || null,
      created_by: normalizeValue(data.created_by),
    },
  });

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.getValues(fieldName);
    const originalValue = data[fieldName];

    // Normalize both values for comparison
    const normalizedCurrentValue = normalizeValue(currentValue);
    const normalizedOriginalValue = normalizeValue(originalValue);

    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  const onSubmit = async (formData: FormData) => {
    // Normalize all fields in the form data
    const normalizedFormData = Object.fromEntries(
      Object.entries(formData).map(([key, value]) => [key, normalizeValue(value)]),
    ) as FormData;

    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, normalizedFormData[field]]),
    ) as ObjectTypeCreateTypes;

    await createObjectType.mutateAsync(
      {
        body: [
          {
            ...updatedFields,
            label: formData.name,
            created_by: user?.id,
          },
        ],
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
          });
          toast.success(t("common.success.label"));
          form.reset(DEFAULT_OBJECT_TYPE);
          queryClient.invalidateQueries();
          setChangedFields(new Set());
        },
        onError: (error: Error) => {
          toast.error(error.message);
        },
      },
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.paper}>
        <h2>{t("forms.objectType.title.label")}</h2>
        <form onSubmit={form.handleSubmit(onSubmit)} className={styles.form}>
          <div className={styles.stack}>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.objectType.name.label")}
                error={form.formState.errors.name?.message}
                disabled
                className={styles.span4}
                {...form.register("name", {
                  onChange: () => onFieldChange("name"),
                })}
              />
            </div>

            {/* <div className={styles.span4}>
              <TextInput
                label={t("forms.objectType.label.label")}
                error={form.formState.errors.label?.message}
                className={styles.span4}
                {...form.register("label", {
                  onChange: () => onFieldChange("label"),
                })}
              />
            </div> */}

            <div className={styles.span4}>
              <TextInput
                label={t("forms.objectType.description.label")}
                error={form.formState.errors.description?.message}
                className={styles.span4}
                {...form.register("description", {
                  onChange: () => onFieldChange("description"),
                })}
              />
            </div>

            <div className={styles.span4}>
              <TextInputNumber
                label={t("forms.objectType.organization.label")}
                error={form.formState.errors.org_id?.message}
                className={styles.span4}
                {...form.register("org_id", {
                  onChange: () => onFieldChange("org_id"),
                })}
              />
            </div>

            <div className={styles.span4}>
              <Button type="submit" disabled={changedFields.size === 0}>
                {t("common.save.label")}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ObjectTypeForm;
