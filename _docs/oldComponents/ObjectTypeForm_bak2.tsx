import { Button, InputBase, NumberInput, Switch, Textarea, TextInput } from "@mantine/core";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { IMaskInput } from "react-imask";
import { useSelector } from "react-redux";
import { z } from "zod";
import {
  createObjectTypesV1CoreObjectTypesPostMutation,
  deleteObjectTypesV1CoreObjectTypesItemIdDeleteMutation,
  updateObjectTypesV1CoreObjectTypesPutMutation,
} from "@/api/_client/@tanstack/react-query.gen";
import { ObjectTypeCreateTypes, ObjectTypeDisplayTypes, ObjectTypeUpdateTypes } from "@/api/_client/types.gen";
import styles from "@/styles/Form.module.css";
import { normalizeString } from "@/utils/formHelpers";
import type { RootStateTypes } from "@/utils/redux/store";

const defalultObjectType = {
  id: 0,
  created_at: "",
  updated_at: "",
  created_by: 0,
  updated_by: 0,
  lang: "pl",
  name: "",
  description: "",
  is_public: false,
  address_id: 0,
  is_formal: true,
  nip: "",
  regon: "",
  max_img_width: 1500,
  total_shares: 1,
  accounts_set_id: 0,
  voting_days: 7,
  members_by_admin: true,
  json_metadata: {
    storage: {
      email: "",
      storage_type: "",
      tokens: {
        access_token: "",
        expiry_date: 0,
        id_token: "",
        refresh_token: "",
        scope: "",
        token_type: "",
      },
      s3: {
        bucket_name: "",
        region: "",
        access_key_id: "",
        secret_access_key: "",
        account_id: "",
        endpoint: "",
      },
    },
  },
};

const formSchema = z
  .object({
    id: z.number(),
    created_at: z.string(),
    updated_at: z.string(),
    created_by: z.number(),
    updated_by: z.number().nullish(),
    lang: z.string(),
    name: z.string().min(3, "Minimum 3 characters required"),
    description: z.string().nullish(),
    is_public: z.boolean().nullish(),
    address_id: z.number().nullish(),
    is_formal: z.boolean().nullish(),
    nip: z.union([z.string(), z.number()]).nullish(),
    regon: z.union([z.string(), z.number()]).nullish(),
    max_img_width: z.number().nullish(),
    total_shares: z.number().nullish(),
    accounts_set_id: z.number().nullish(),
    voting_days: z.number().nullish(),
    members_by_admin: z.boolean().nullish(),
    json_metadata: z
      .object({
        storage: z
          .object({
            email: z.string().nullish(),
            storage_type: z.string().nullish(),
            tokens: z
              .object({
                access_token: z.string().nullish(),
                expiry_date: z.number().nullish(),
                id_token: z.string().nullish(),
                refresh_token: z.string().nullish(),
                scope: z.string().nullish(),
                token_type: z.string().nullish(),
              })
              .nullish(),
            s3: z
              .object({
                bucket_name: z.string().nullish(),
                region: z.string().nullish(),
                access_key_id: z.string().nullish(),
                secret_access_key: z.string().nullish(),
                account_id: z.string().nullish(),
                endpoint: z.string().nullish(),
              })
              .nullish(),
          })
          .nullish(),
      })
      .nullish(),
  })
  .passthrough();

interface PropsTypes {
  data?: ObjectTypeDisplayTypes;
  variant: "new" | "edit";
}

type FormData = z.infer<typeof formSchema>;

function ObjectTypeForm({ data = defalultObjectType, variant = "edit" }: PropsTypes) {
  //
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof FormData>>(new Set());
  // console.log("changedFields", changedFields);

  const form = useForm({
    defaultValues: data,
    validators: {
      onBlur: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (variant === "new") {
        handleCreate(value as ObjectTypeCreateTypes);
      } else if (variant === "edit") {
        handleEdit(value as ObjectTypeUpdateTypes);
      }
    },
    onSubmitInvalid: (props) => {
      console.log("on invalid", props);
    },
  });

  // update form values on data change
  useEffect(() => {
    if (data) {
      console.log("data >>>>data", data.description);
      form.reset(data);
    }
  }, [data]);

  const createObjectType = useMutation({
    ...createObjectTypesV1CoreObjectTypesPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleCreate(formData: ObjectTypeCreateTypes) {
    // console.log(" <<< CREATING ObjectType >>> normalizedFormData", formData, user?.id);
    await createObjectType.mutateAsync({
      body: [
        {
          ...formData,
          created_by: user?.id,
          nip: formData?.nip?.toString(),
          regon: formData?.regon?.toString(),
        },
      ],
    });
  }

  const updateObjectType = useMutation({
    ...updateObjectTypesV1CoreObjectTypesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  async function handleEdit(formData: ObjectTypeUpdateTypes) {
    // send only changed fields
    const updatedFields = Object.fromEntries(
      Array.from(changedFields).map((field) => [field, formData[field]]),
    ) as ObjectTypeUpdateTypes;
    // console.log(
    //   " <<< UPDATING ObjectType >>> updatedFields",
    //   updatedFields,
    //   user?.curr_ObjectType_id,
    //   user?.id
    // );
    await updateObjectType.mutateAsync({
      body: [
        {
          ...updatedFields,
          id: data?.id,
          updated_by: user?.id,
          nip: formData?.nip?.toString(),
          regon: formData?.regon?.toString(),
        },
      ],
    });
  }

  const deleteObjectType = useMutation({
    ...deleteObjectTypesV1CoreObjectTypesItemIdDeleteMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      form.reset();
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const handleDelete = () => {
    if (data?.id == user.curr_ObjectType_id) {
      toast.error("You can't delete your active ObjectType");
      return;
    }
    // console.log("Delete ObjectType:", user?.curr_ObjectType_id);
    if (window.confirm("Delete?")) {
      deleteObjectType.mutateAsync({
        path: { item_id: (data as ObjectTypeDisplayTypes)?.id },
      });
    }
  };

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.state.values[fieldName];
    const originalValue = data && data[fieldName];
    const normalizedCurrentValue = normalizeString(currentValue);
    const normalizedOriginalValue = normalizeString(originalValue);
    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  // const formErrorMap = useStore(form.store, (state) => state.errorMap);
  // console.log("errorMap", formErrorMap);

  return (
    <>
      {/* <pre>{JSON.stringify(form.state.values, null, 2)}</pre> */}
      <div className={styles.container}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("<< from form declaration >>", form);
            form.handleSubmit();
          }}
        >
          <h2>
            {variant === "edit" ? t("forms.ObjectTypeForm.titleEdit.label") : t("forms.ObjectTypeForm.titleNew.label")}{" "}
            {data?.name}
          </h2>
          <div className={styles.formGrid}>
            <div className={styles.span4}>
              <form.Field
                name="name"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <TextInput
                      label={t("forms.ObjectTypeForm.name.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("name");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Enter your name"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span4}>
              <form.Field
                name="description"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Textarea
                      label={t("forms.ObjectTypeForm.description.label")}
                      value={state.value || ""}
                      onChange={(e) => {
                        handleChange(e.target.value);
                        onFieldChange("description");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="Description"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="is_formal"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.ObjectTypeForm.isFormal.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        console.log("e.target.checked", e.target.checked);
                        handleChange(e.target.checked);
                        onFieldChange("is_formal");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}></div>

            <div className={styles.span2}>
              <form.Field
                name="nip"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 0000"
                      label={t("forms.ObjectTypeForm.nip.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("nip");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="NIP"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="regon"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <InputBase
                      component={IMaskInput}
                      mask="000 000 000"
                      label={t("forms.ObjectTypeForm.regon.label")}
                      value={state.value?.toString() || ""}
                      onChange={(e: any) => {
                        const value = e.target?.value?.replace(/\s/g, "") || "";
                        handleChange(value);
                        onFieldChange("regon");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      placeholder="regon"
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="total_shares"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectTypeForm.totalShares.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("total_shares");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="voting_days"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectTypeForm.votingDays.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("voting_days");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span2}>
              <form.Field
                name="max_img_width"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <NumberInput
                      allowedDecimalSeparators={[",", "."]}
                      hideControls
                      label={t("forms.ObjectTypeForm.maxImgWidth.label")}
                      value={state.value || ""}
                      onChange={(value: number | string | "") => {
                        handleChange(typeof value === "number" ? value : null);
                        onFieldChange("max_img_width");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span4}>
              <form.Field
                name="is_public"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.ObjectTypeForm.isPublic.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("is_public");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>

            <div className={styles.span4}>
              <form.Field
                name="members_by_admin"
                children={({ state, handleChange, handleBlur }) => {
                  return (
                    <Switch
                      label={t("forms.ObjectTypeForm.membersByAdmin.label")}
                      checked={state.value || false}
                      onChange={(e) => {
                        handleChange(e.target.checked);
                        onFieldChange("members_by_admin");
                      }}
                      onFocus={(event) => event.target.select()}
                      onBlur={handleBlur}
                      //error={state.meta.errors.join(", ")}
                      error={(state?.meta?.errors?.[0] as { message: string } | undefined)?.message}
                      // error={JSON.stringify(state.meta.errors, null, 2)}
                    />
                  );
                }}
              />
            </div>
          </div>

          <div className={styles.formActions}>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0} loading={isSubmitting}>
                  {variant === "edit" ? t("common.save.label") : t("common.create.label")}
                </Button>
              )}
            />
            {variant === "edit" && (data as ObjectTypeDisplayTypes)?.id !== user.curr_ObjectType_id && (
              <Button
                color="red"
                loading={updateObjectType.isPending || createObjectType.isPending}
                // disabled
                onClick={handleDelete}
              >
                {t("common.delete.label")}
              </Button>
            )}
          </div>
        </form>
      </div>
    </>
  );
}

export default ObjectTypeForm;
