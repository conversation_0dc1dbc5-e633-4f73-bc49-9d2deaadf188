// src/components/system/AuthDataLoader.tsx
import { createSelector } from "@reduxjs/toolkit";
import { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import type { UserDisplayTypes } from "@/api/_client/types.gen";
import { bak_useUserData } from "@/hooks/bak_useUserData.ts";
import { getUserRoles } from "@/utils/auth.js";
import { updateOrg } from "@/utils/redux/orgSlice";
import { updateProfile } from "@/utils/redux/profileSlice";
import type { RootStateTypes } from "@/utils/redux/store";
import { updateUser } from "@/utils/redux/userSlice";
import Loading from "./Loading";
import { Spinner } from "./Spinner";

// create type for userData with roles
interface UserDisplayTypesWithRoles extends UserDisplayTypes {
  roles: string[];
}

// Create memoized selector
const selectUserFields = createSelector(
  (state: RootStateTypes) => state.user,
  (user) => ({
    id: user.id,
    lang: user.lang,
    curr_org_id: user.curr_org_id,
    curr_profile_id: user.curr_profile_id,
    roles: user.roles,
  }),
);

// In component

interface AuthDataLoaderProps {
  children: React.ReactNode;
}

export const AuthDataLoader: React.FC<AuthDataLoaderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  const { data: userData, isLoading, error } = bak_useUserData();
  const currentUser = useSelector(selectUserFields);
  const prevUserData = useRef<UserDisplayTypes | null>(null);
  // console.log("prevUserData", prevUserData.current);
  // console.log("userData", userData);

  useEffect(() => {
    // console.log("AuthDataLoader userData", userData);
    // Wait for userData to be available and ensure it has changed
    if (userData && userData !== prevUserData.current) {
      // console.log(" **** DATA CHANGED *******  userData !== prevUserData.current", userData, prevUserData.current);
      // Only dispatch if user data has actually changed
      // deep compare userData and prevUserData
      // const isChanged = !_.isEqual(userData, prevUserData.current);
      if (!currentUser || currentUser.id !== userData.id) {
        // console.log(
        //   " **** DATA CHANGE DETECTED *******  userData !== prevUserData.current",
        //   userData,
        //   prevUserData.current,
        // );
        const allRoles = getUserRoles();
        const orgId = userData.curr_org_id;
        const orgRoles = allRoles
          .filter((role) => role.startsWith(`o${orgId}:`))
          .map((role) => role.replace(`o${orgId}:`, ""));

        // Create a new profile object with roles to avoid mutation
        const userWithRoles = {
          ...userData,
          roles: orgRoles,
        };
        dispatch(updateUser(userWithRoles));
        void i18n.changeLanguage(userData.lang);

        const currOrg = userData.orgs?.find((org) => org.id === userData.curr_org_id);
        if (currOrg) {
          dispatch(updateOrg(currOrg));
        }

        const currProfile = userData.profiles?.find((profile) => profile.id === userData.curr_profile_id);
        // console.log("currProfile", currProfile);

        if (currProfile) {
          dispatch(updateProfile(currProfile));
        }
      }
      prevUserData.current = userData;
    }
  }, [userData, currentUser, dispatch, i18n]);

  if (isLoading) {
    return <Spinner size={96} />;
  }

  if (error) {
    console.error("Failed to fetch user data:", error);
    return <div>Error loading user data</div>;
  }

  if (!userData) {
    return <Spinner size={96} />;
  }

  return <>{children}</>;
};
