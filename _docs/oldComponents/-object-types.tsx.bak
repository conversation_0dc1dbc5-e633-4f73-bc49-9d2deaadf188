import { readObjectTypesAllV1CoreObjectTypesGetOptions } from "@/api/_client/@tanstack/react-query.gen";
import { updateSystemField } from "@/redux/systemSlice.ts";
import { Container } from "@mantine/core";
import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { Suspense, useEffect } from "react";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import ObjectTypeForm from "./-forms/ObjectTypeForm/ObjectTypeForm.tsx";
import Loading from "@/components/system/Loading";

export const Route = createFileRoute("/superadmin/object-types")({
  loader: async ({ context: { queryClient, curr_org_id, curr_profile_id } }) => {
    try {
      await queryClient.prefetchQuery(readObjectTypesAllV1CoreObjectTypesGetOptions());
    } catch (error) {
      console.error("Loader error:", error);
    }
  },
  component: PageComponent,
});

function PageComponent() {
  const router = useRouter();
  const dispatch = useDispatch();
  dispatch(updateSystemField({ module: "config" }));
  dispatch(updateSystemField({ pageTitle: "Object types" }));

  useEffect(() => {
    // Subscribe to the onBeforeNavigate event
    const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
      // Check if navigating away
      // console.log(" &&&&&&&  toLocation.pathname", toLocation.pathname);
      if (toLocation.pathname !== `/config/object-types`) {
        dispatch(updateSystemField({ pageTitle: "" }));
      }
    });

    return () => {
      // Clean up the subscription
      unsubscribe();
    };
  }, [router, dispatch]);

  const { data, error } = useSuspenseQuery(readObjectTypesAllV1CoreObjectTypesGetOptions());

  if (error) {
    toast.error(error.message);
  }

  // console.log("data", data);

  return (
    <Suspense fallback={<Loading color="red" message="Data loading..." />}>
      <Container>
        <h3>Typy obiektów</h3>
        {data.data.map((item) => (
          <div key={item.id}>{item.label}</div>
        ))}
        <ObjectTypeForm data={data.data} />
      </Container>
    </Suspense>
  );
}
