import React, { useState } from "react";
import styles from "./DocumentNew.module.css";
import EditorBare from "@/components/EditorBare";
import { ActionIcon, Button, Drawer, Select } from "@mantine/core";
import { IconPlus, IconTrash } from "@tabler/icons-react";

interface Column {
  id: number;
  html: string;
}

interface CustomerAddress {
  id: number;
  country: string;
  area1?: string;
  area2?: string;
  street?: string;
  city: string;
  postal_code?: string;
  street_no?: string;
  local_no?: string;
  location?: string;
}

// Mock customer data - replace with actual API call
const mockCustomers: CustomerAddress[] = [
  {
    id: 1,
    country: "Poland",
    city: "Warsaw",
    street: "Marszałkowska",
    street_no: "1",
    local_no: "2",
    postal_code: "00-001",
  },
  {
    id: 2,
    country: "Poland",
    city: "Krakow",
    street: "Floriańska",
    street_no: "15",
    postal_code: "31-001",
  },
];

interface Page {
  id: number;
  title: string;
  content: string;
  headerParts: Column[];
  footerParts: Column[];
  preParts: Column[];
  postParts: Column[];
}

interface Document {
  id: number;
  title: string;
  pages: Page[];
  headerColumns: Column[];
  footerColumns: Column[];
}

// Helper function to extract first line from HTML content
const getFirstLine = (html: string) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  const firstP = div.querySelector("p");
  return firstP?.textContent || "Empty template";
};

// Templates that would normally come from the database
const columnTemplates = [
  {
    id: 1,
    name: "customerAddress",
    html: "<p>Customer Address</p><p>Company Name</p><p>Street Address</p><p>City, Postal Code</p><p>Country</p>",
  },
  {
    id: 2,
    name: "ourAddress",
    html: "<p>Our Company Details</p><p>Company Name</p><p>Street Address</p><p>City, Postal Code</p><p>Country</p>",
  },
  {
    id: 3,
    name: "offerDetails",
    html: "<p>Offer Details</p><p>Offer Number: [Number]</p><p>Date: [Date]</p><p>Valid Until: [Date]</p>",
  },
  {
    id: 4,
    name: "companyDetails",
    html: "<p>Company Information</p><p>Legal Name</p><p>Tax ID</p><p>Registration Number</p>",
  },
  {
    id: 5,
    name: "bankAccounts",
    html: "<p>Bank Account Details</p><p>Bank Name</p><p>Account Number</p><p>SWIFT/BIC</p><p>IBAN</p>",
  },
  {
    id: 6,
    name: "registrationNumbers",
    html: "<p>Registration Information</p><p>VAT ID</p><p>Company Registry Number</p><p>Statistical Number</p>",
  },
];

const preTemplates = [
  {
    id: 1,
    name: "Basic Introduction",
    html: "<p>Basic Introduction to Document Contents</p><p>This section provides a comprehensive overview of the document's contents and structure. It helps readers understand what to expect in the following pages and how to navigate through the information effectively.</p>",
  },
  {
    id: 2,
    name: "Legal Notice",
    html: "<p>Legal Notice and Copyright Information</p><p>This document is protected by copyright laws and international treaties. Unauthorized reproduction or distribution of this document, or any portion of it, may result in severe civil and criminal penalties.</p>",
  },
  {
    id: 3,
    name: "Document Purpose",
    html: "<p>Document Purpose and Objectives</p><p>This document has been prepared to outline specific goals, methodologies, and expected outcomes. It serves as a formal record of our intentions and planned approach to achieve the stated objectives.</p>",
  },
];

const postTemplates = [
  {
    id: 1,
    name: "Standard Closure",
    html: "<p>Concluding Remarks and Next Steps</p><p>We appreciate your attention to the contents of this document. For any questions or clarifications, please don't hesitate to reach out to the appropriate department or representative.</p>",
  },
  {
    id: 2,
    name: "Terms & Conditions",
    html: "<p>Terms, Conditions and Legal Disclaimers</p><p>By proceeding with the actions outlined in this document, you acknowledge and agree to all terms and conditions stated herein. This agreement is binding and subject to applicable laws and regulations.</p>",
  },
  {
    id: 3,
    name: "Contact Information",
    html: "<p>Contact Details and Support Information</p><p>For further assistance or inquiries, please contact our support team during business hours. Emergency support is available 24/7 through our dedicated hotline.</p>",
  },
];

const defaultPage: Page = {
  id: 0,
  title: "Uchwala z 2025-03-17, nr 1/2025",
  content: "<p>no value provided</p>",
  headerParts: [
    {
      id: 11,
      html: "<p>column title</p> <p>column line 1</p> <p>column line 2</p> <p>column line 3</p>",
    },
    {
      id: 12,
      html: "<p>column title2</p> <p>column line 1</p> <p>column line 2</p> <p>column line 3</p>",
    },
  ],
  preParts: [],
  postParts: [],
  footerParts: [
    {
      id: 41,
      html: "<p>footer 1</p>",
    },
    {
      id: 42,
      html: "<p>footer 2</p>",
    },
  ],
};

const initialDocument: Document = {
  id: 1,
  title: "New Document",
  pages: [defaultPage],
  headerColumns: defaultPage.headerParts,
  footerColumns: defaultPage.footerParts,
};

function DocumentNew() {
  const [activeEditor, setActiveEditor] = useState<string | null>(null);
  const [configDrawerOpen, setConfigDrawerOpen] = useState(false);
  const [document, setDocument] = useState<Document>(initialDocument);
  const [preSelectValue, setPreSelectValue] = useState<string | null>(null);
  const [postSelectValue, setPostSelectValue] = useState<string | null>(null);
  const [headerTemplateValues, setHeaderTemplateValues] = useState<
    (string | null)[]
  >(new Array(document.headerColumns.length).fill(null));
  const [footerTemplateValues, setFooterTemplateValues] = useState<
    (string | null)[]
  >(new Array(document.footerColumns.length).fill(null));
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    null,
  );

  const generateCustomerAddressHtml = (customer: CustomerAddress) => {
    const parts = [
      customer.street && customer.street_no
        ? `${customer.street} ${customer.street_no}${customer.local_no ? `/${customer.local_no}` : ""}`
        : null,
      customer.postal_code && customer.city
        ? `${customer.postal_code} ${customer.city}`
        : customer.city,
      customer.area1,
      customer.area2,
      customer.country,
    ].filter(Boolean);

    return `<p>Customer Address</p>${parts.map((part) => `<p>${part}</p>`).join("")}`;
  };

  const handleCustomerSelect = (customerId: string | null) => {
    setSelectedCustomerId(customerId);
    if (!customerId) return;

    const customer = mockCustomers.find((c) => c.id.toString() === customerId);
    if (!customer) return;

    const customerTemplate = {
      id: 1,
      name: "customerAddress",
      html: generateCustomerAddressHtml(customer),
    };

    // Update the columnTemplates array with the new customer address
    const templateIndex = columnTemplates.findIndex(
      (t) => t.name === "customerAddress",
    );
    if (templateIndex !== -1) {
      columnTemplates[templateIndex] = customerTemplate;
    }

    // If any column is using the customerAddress template, update it
    headerTemplateValues.forEach((value, index) => {
      if (value === "1") {
        // ID of customerAddress template
        updateColumnWithTemplate(index, 1, "header");
      }
    });

    footerTemplateValues.forEach((value, index) => {
      if (value === "1") {
        // ID of customerAddress template
        updateColumnWithTemplate(index, 1, "footer");
      }
    });
  };

  const handleAddPage = () => {
    const newPage: Page = {
      ...defaultPage,
      id: document.pages.length,
      headerParts: document.headerColumns.map((col) => ({ ...col })),
      footerParts: document.footerColumns.map((col) => ({ ...col })),
      content: "<p>no value provided</p>",
      preParts: document.pages[0].preParts.map((col) => ({ ...col })),
      postParts: document.pages[0].postParts.map((col) => ({ ...col })),
    };
    setDocument({
      ...document,
      pages: [...document.pages, newPage],
    });
  };

  const handleRemoveLastPage = () => {
    if (document.pages.length > 1) {
      setDocument({
        ...document,
        pages: document.pages.slice(0, -1),
      });
    }
  };

  const handleSave = () => {
    console.log("Saving document:", document);
    // TODO: Implement save logic
  };

  // const addPreElement = ({ templateId }: { templateId: number }) => {
  //   console.log("add pre element");
  //   const template = templateId
  //     ? preTemplates.find((t) => t.id === templateId)
  //     : null;

  //   const newPart = {
  //     id: 21 + document.pages[0].preParts.length,
  //     html: template?.html || "<p>New pre content</p>",
  //   };

  //   setDocument({
  //     ...document,
  //     pages: document.pages.map((page, idx) =>
  //       idx === 0 ? { ...page, preParts: [...page.preParts, newPart] } : page,
  //     ),
  //   });
  // };

  const addPostElement = (templateId?: number) => {
    const template = templateId
      ? postTemplates.find((t) => t.id === templateId)
      : null;

    const newPart = {
      id: 31 + document.pages[0].postParts.length,
      html: template?.html || "<p>New post content</p>",
    };

    setDocument({
      ...document,
      pages: document.pages.map((page, idx) =>
        idx === document.pages.length - 1
          ? { ...page, postParts: [...page.postParts, newPart] }
          : page,
      ),
    });
  };

  const removePreElement = (id: number) => {
    setDocument({
      ...document,
      pages: document.pages.map((page, idx) =>
        idx === 0
          ? {
              ...page,
              preParts: page.preParts.filter((part) => part.id !== id),
            }
          : page,
      ),
    });
  };

  const removePostElement = (id: number) => {
    setDocument({
      ...document,
      pages: document.pages.map((page, idx) =>
        idx === document.pages.length - 1
          ? {
              ...page,
              postParts: page.postParts.filter((part) => part.id !== id),
            }
          : page,
      ),
    });
  };

  const updateHeaderColumns = (value: string) => {
    const newColumns = Array.from(
      { length: value ? parseInt(value) : 1 },
      (_, i) => ({
        id: 11 + i,
        html: `<p>header col ${i + 1}</p>`,
      }),
    );
    setDocument({
      ...document,
      headerColumns: newColumns,
      pages: document.pages.map((page) => ({
        ...page,
        headerParts: newColumns.map((col) => ({ ...col })),
      })),
    });
  };

  const updateFooterColumns = (value: string) => {
    const newColumns = Array.from(
      { length: value ? parseInt(value) : 1 },
      (_, i) => ({
        id: 31 + i,
        html: `<p>footer col ${i + 1}</p>`,
      }),
    );
    setDocument({
      ...document,
      footerColumns: newColumns,
      pages: document.pages.map((page) => ({
        ...page,
        footerParts: newColumns.map((col) => ({ ...col })),
      })),
    });
  };

  const updatePageContent = (pageIndex: number, content: string) => {
    setDocument({
      ...document,
      pages: document.pages.map((page, idx) =>
        idx === pageIndex ? { ...page, content } : page,
      ),
    });
  };

  const updateColumnContent = (
    pageIndex: number,
    columnId: number,
    html: string,
    type: "header" | "footer" | "pre" | "post",
  ) => {
    const typeMap = {
      header: "headerParts",
      footer: "footerParts",
      pre: "preParts",
      post: "postParts",
    };

    const partKey = typeMap[type];

    setDocument({
      ...document,
      pages: document.pages.map((page, idx) => {
        if (idx !== pageIndex) return page;
        return {
          ...page,
          [partKey]: page[partKey].map((col) =>
            col.id === columnId ? { ...col, html } : col,
          ),
        };
      }),
    });

    // Update global header/footer columns if needed
    if (type === "header" || type === "footer") {
      const globalKey = type === "header" ? "headerColumns" : "footerColumns";
      setDocument((prev) => ({
        ...prev,
        [globalKey]: prev[globalKey].map((col) =>
          col.id === columnId ? { ...col, html } : col,
        ),
      }));
    }
  };

  const updateColumnWithTemplate = (
    columnIndex: number,
    templateId: number | null,
    type: "header" | "footer",
  ) => {
    if (!templateId) return;

    const template = columnTemplates.find((t) => t.id === templateId);
    if (!template) return;

    const targetColumns =
      type === "header" ? document.headerColumns : document.footerColumns;
    const newColumns = [...targetColumns];
    newColumns[columnIndex] = {
      ...newColumns[columnIndex],
      html: template.html,
    };

    setDocument({
      ...document,
      [type === "header" ? "headerColumns" : "footerColumns"]: newColumns,
      pages: document.pages.map((page) => ({
        ...page,
        [type === "header" ? "headerParts" : "footerParts"]: newColumns,
      })),
    });
  };

  return (
    <>
      <Drawer
        opened={configDrawerOpen}
        onClose={() => setConfigDrawerOpen(false)}
        position="right"
        title="Document Settings"
        padding="xl"
        size="xl"
      >
        <div className={styles.settingsSection}>
          <h3>Customer Selection</h3>
          <Select
            label="Select Customer"
            placeholder="Choose a customer"
            value={selectedCustomerId}
            onChange={handleCustomerSelect}
            data={[
              { value: "", label: "No customer" },
              ...mockCustomers.map((customer) => ({
                value: customer.id.toString(),
                label:
                  `${customer.city}, ${customer.street || ""} ${customer.street_no || ""}`.trim(),
              })),
            ]}
            className={styles.customerSelect}
          />
        </div>

        <div className={styles.settingsSection}>
          <h3>Header Configuration</h3>
          <Select
            label="Number of header columns"
            data={["1", "2", "3"]}
            value={document.headerColumns.length.toString()}
            onChange={updateHeaderColumns}
            className={styles.columnCountSelector}
          />
          {document.headerColumns.map((_, index) => (
            <div
              key={`header-template-${index}`}
              className={styles.templateSelector}
            >
              <Select
                label={`Header Column ${index + 1}`}
                placeholder="Choose a template"
                value={headerTemplateValues[index]}
                onChange={(value) => {
                  const newValues = [...headerTemplateValues];
                  newValues[index] = value;
                  setHeaderTemplateValues(newValues);
                  updateColumnWithTemplate(
                    index,
                    value ? parseInt(value) : null,
                    "header",
                  );
                }}
                data={[
                  { value: "", label: "Empty element" },
                  ...columnTemplates.map((t) => ({
                    value: t.id.toString(),
                    label: t.name,
                  })),
                ]}
              />
            </div>
          ))}
        </div>

        <div className={styles.settingsSection}>
          <h3>Footer Configuration</h3>
          <Select
            label="Number of footer columns"
            data={["1", "2", "3"]}
            value={document.footerColumns.length.toString()}
            onChange={updateFooterColumns}
            className={styles.columnCountSelector}
          />
          {document.footerColumns.map((_, index) => (
            <div
              key={`footer-template-${index}`}
              className={styles.templateSelector}
            >
              <Select
                label={`Footer Column ${index + 1}`}
                placeholder="Choose a template"
                value={footerTemplateValues[index]}
                onChange={(value) => {
                  const newValues = [...footerTemplateValues];
                  newValues[index] = value;
                  setFooterTemplateValues(newValues);
                  updateColumnWithTemplate(
                    index,
                    value ? parseInt(value) : null,
                    "footer",
                  );
                }}
                data={[
                  { value: "", label: "Empty element" },
                  ...columnTemplates.map((t) => ({
                    value: t.id.toString(),
                    label: t.name,
                  })),
                ]}
              />
            </div>
          ))}
        </div>

        <div className={styles.settingsSection}>
          <h3>Pre-content Templates</h3>
          <div className={styles.templateButtons}>
            <Select
              label="Add pre-content element"
              placeholder="Select template or empty"
              data={[
                { value: "0", label: "Empty element" },
                ...preTemplates.map((template) => ({
                  value: template.id.toString(),
                  label: getFirstLine(template.html),
                })),
              ]}
              value={preSelectValue}
              onChange={(value) => {
                if (value) {
                  addPreElement(value === "0" ? undefined : parseInt(value));
                  setPreSelectValue(null);
                }
              }}
              clearable
            />
          </div>
          {document.pages[0].preParts.map((part) => (
            <div key={part.id} className={styles.elementRow}>
              <div className={styles.elementPreview}>
                <div dangerouslySetInnerHTML={{ __html: part.html }} />
              </div>
              <ActionIcon
                color="red"
                onClick={() => removePreElement(part.id)}
                variant="subtle"
              >
                <IconTrash size={16} />
              </ActionIcon>
            </div>
          ))}
        </div>

        <div className={styles.settingsSection}>
          <h3>Post-content Templates</h3>
          <div className={styles.templateButtons}>
            <Select
              label="Add post-content element"
              placeholder="Select template or empty"
              data={[
                { value: "0", label: "Empty element" },
                ...postTemplates.map((template) => ({
                  value: template.id.toString(),
                  label: getFirstLine(template.html),
                })),
              ]}
              value={postSelectValue}
              onChange={(value) => {
                if (value) {
                  addPostElement(value === "0" ? undefined : parseInt(value));
                  setPostSelectValue(null);
                }
              }}
              clearable
            />
          </div>
          {document.pages[document.pages.length - 1].postParts.map((part) => (
            <div key={part.id} className={styles.elementRow}>
              <div className={styles.elementPreview}>
                <div dangerouslySetInnerHTML={{ __html: part.html }} />
              </div>
              <ActionIcon
                color="red"
                onClick={() => removePostElement(part.id)}
                variant="subtle"
              >
                <IconTrash size={16} />
              </ActionIcon>
            </div>
          ))}
        </div>
      </Drawer>
      <div className={styles.master_wrapper}>
        <div className={styles.buttons}>
          <Button onClick={() => setConfigDrawerOpen(true)}>
            Open settings drawer
          </Button>{" "}
          <Button onClick={handleAddPage}>Add page</Button>
          <Button onClick={handleRemoveLastPage}>Remove last page</Button>
          <Button onClick={handleSave}>Save</Button>
        </div>
        {document.pages.map((page, index) => (
          <Page
            key={page.id}
            index={index}
            isFirstPage={index === 0}
            isLastPage={index === document.pages.length - 1}
            page={page}
            activeEditor={activeEditor}
            setActiveEditor={setActiveEditor}
            onUpdateContent={(content) => updatePageContent(index, content)}
            onUpdateColumn={(columnId, html, type) =>
              updateColumnContent(index, columnId, html, type)
            }
          />
        ))}
      </div>
    </>
  );
}

export default DocumentNew;

interface PageProps {
  page: Page;
  index: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  activeEditor: string | null;
  setActiveEditor: (id: string | null) => void;
  onUpdateContent: (content: string) => void;
  onUpdateColumn: (
    columnId: number,
    html: string,
    type: "header" | "footer" | "pre" | "post",
  ) => void;
}

function Page({
  page,
  index,
  isFirstPage,
  isLastPage,
  activeEditor,
  setActiveEditor,
  onUpdateContent,
  onUpdateColumn,
}: PageProps) {
  return (
    <div className={styles.page_wrapper}>
      <div className={styles.title}>{page.title}</div>

      {/* header */}
      <div className={styles.header_columns}>
        {page.headerParts.map((column) => (
          <div key={column.id} className={styles.column}>
            <div className={styles.editorWrapper}>
              <EditorBare
                value={column.html}
                onChange={(html) => onUpdateColumn(column.id, html, "header")}
                isActive={activeEditor === column.id.toString()}
                onFocus={() => setActiveEditor(column.id.toString())}
                onBlur={() => setActiveEditor(null)}
              />
            </div>
          </div>
        ))}
      </div>

      {/* pre content - only on first page */}
      {isFirstPage && (
        <div className={styles.pre_content}>
          {page.preParts.map((part) => (
            <div key={part.id} className={styles.column}>
              <div className={styles.editorWrapper}>
                <EditorBare
                  value={part.html}
                  onChange={(html) => onUpdateColumn(part.id, html, "pre")}
                  isActive={activeEditor === part.id.toString()}
                  onFocus={() => setActiveEditor(part.id.toString())}
                  onBlur={() => setActiveEditor(null)}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* main content */}
      <div className={styles.content}>
        <EditorBare
          value={page.content}
          onChange={onUpdateContent}
          isActive={activeEditor === "content"}
          onFocus={() => setActiveEditor("content")}
          onBlur={() => setActiveEditor(null)}
        />
      </div>

      {/* post content - only on last page */}
      {isLastPage && (
        <div className={styles.post_content}>
          {page.postParts.map((part) => (
            <div key={part.id} className={styles.column}>
              <div className={styles.editorWrapper}>
                <EditorBare
                  value={part.html}
                  onChange={(html) => onUpdateColumn(part.id, html, "post")}
                  isActive={activeEditor === part.id.toString()}
                  onFocus={() => setActiveEditor(part.id.toString())}
                  onBlur={() => setActiveEditor(null)}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* footer */}
      <div className={styles.footer_columns}>
        {page.footerParts.map((column) => (
          <div key={column.id} className={styles.column}>
            <div className={styles.editorWrapper}>
              <EditorBare
                value={column.html}
                onChange={(html) => onUpdateColumn(column.id, html, "footer")}
                isActive={activeEditor === column.id.toString()}
                onFocus={() => setActiveEditor(column.id.toString())}
                onBlur={() => setActiveEditor(null)}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
