import {
  getRouteApi,
  type NavigateOptions,
  type RegisteredRouter,
  type RouteIds,
  useNavigate,
} from "@tanstack/react-router";
import type {
  ColumnFiltersState,
  ColumnOrderState,
  PaginationState,
  RowSelectionState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  cleanEmptyParams,
  // DEFAULT_PAGE_INDEX,
  // DEFAULT_PAGE_SIZE,
} from "@/utils/cleanEmptyParams";

export type TableFilters = {
  sorting?: SortingState;
  pagination?: PaginationState;
  globalFilter?: string;
  columnFilters?: ColumnFiltersState;
  columnOrder?: ColumnOrderState;
  rowSelection?: RowSelectionState;
  columnVisibility?: VisibilityState;
  startDate?: string;
  endDate?: string;
};

export function useRouteFilters<T extends RouteIds<RegisteredRouter["routeTree"]>>(routeId: T) {
  const routeApi = getRouteApi(routeId);
  const navigate = useNavigate();
  const searchParams = routeApi.useSearch() as TableFilters;
  // console.log("searchParams", searchParams);

  // Provide default values to prevent undefined states
  // const filters: TableFilters = {
  //   columnFilters: searchParams.columnFilters ?? [],
  //   globalFilter: searchParams.globalFilter ?? "",
  //   sorting: searchParams.sorting ?? [],
  //   pageIndex: searchParams.pageIndex ?? DEFAULT_PAGE_INDEX,
  //   pageSize: searchParams.pageSize ?? DEFAULT_PAGE_SIZE,
  // };

  const setFilters = (partialFilters: Partial<TableFilters>) => {
    console.log("partialFilters", partialFilters);
    const navigateOptions: NavigateOptions = {
      search: (prev) => {
        const newPartial: Record<string, any> = {};
        for (const [key, value] of Object.entries(partialFilters)) {
          if (value === undefined || value === null) continue;

          // Handle objects (e.g. columnVisibility) separately so we don't inadvertently strip them
          if (typeof value === "object") {
            const isEmptyObject = !Array.isArray(value) && Object.keys(value).length === 0;
            const isEmptyArray = Array.isArray(value) && value.length === 0;

            if (isEmptyObject || isEmptyArray) {
              newPartial[key] = undefined; // Remove when empty
            } else {
              newPartial[key] = value; // Preserve non-empty objects/arrays
            }
          } else {
            // Primitive values – keep non-empty strings, numbers, booleans, etc.
            newPartial[key] = value === "" ? undefined : value;
          }
        }

        const newSearch = cleanEmptyParams({
          ...prev,
          ...newPartial,
        });

        return newSearch;
      },
      replace: true,
    };
    console.log("NAVIGATING!!!!!!!  >>>>> navigateOptions", navigateOptions);
    navigate(navigateOptions);
  };

  const resetFilters = () => {
    navigate({ search: {}, replace: true });
  };

  const toggleColumnVisibility = (columnId: string) => {
    const currentVisibilityParam = searchParams.columnVisibility;
    let columnVisibility: VisibilityState = {};

    if (typeof currentVisibilityParam === "string") {
      try {
        const parsed = JSON.parse(currentVisibilityParam);
        if (typeof parsed === "object" && parsed !== null) {
          columnVisibility = parsed;
        }
      } catch {
        // Not a valid JSON string, start fresh.
      }
    } else if (typeof currentVisibilityParam === "object" && currentVisibilityParam !== null) {
      columnVisibility = currentVisibilityParam as VisibilityState;
    }

    const newVisibility = { ...columnVisibility };

    if (newVisibility[columnId] === false) {
      delete newVisibility[columnId];
    } else {
      newVisibility[columnId] = false;
    }
    console.log("newVisibility", newVisibility);

    setFilters({ columnVisibility: newVisibility });
  };

  return {
    filters: searchParams,
    setFilters,
    resetFilters,
    toggleColumnVisibility,
  };
}
