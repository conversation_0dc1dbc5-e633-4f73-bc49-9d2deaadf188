import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@mantine/core";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import { createOrgsV1CoreOrgsPostMutation } from "@/api/_client/@tanstack/react-query.gen";
import { OrgCreateTypes } from "@/api/_client/types.gen";
import { Switch, TextInput, TextInputNumber } from "@/components/form";
import type { RootStateTypes } from "@/utils/redux/store";
import styles from "./OrgNewForm.module.css";

const DEFAULT_ORG: OrgCreateTypes = {
  name: "",
  description: "",
  created_by: undefined,
  is_public: false,
  address_id: null,
  is_formal: false,
  nip: null,
  regon: null,
  max_img_width: 1500,
  total_shares: 1,
  accounts_set_id: null,
  voting_days: 7,
  members_by_admin: false,
};

const formSchema = z.object({
  name: z.string().min(2, {
    message: "forms.OrgType.nameError.label",
  }),
  description: z.string().optional().nullable(),
  created_by: z.number().optional(),
  is_public: z.boolean(),
  address_id: z.number().optional().nullable(),
  is_formal: z.boolean(),
  nip: z.string().optional().nullable(),
  regon: z.string().optional().nullable(),
  max_img_width: z.number().optional().nullable(),
  total_shares: z.number().optional().nullable(),
  accounts_set_id: z.number().optional().nullable(),
  voting_days: z.number().optional().nullable(),
  members_by_admin: z.boolean(),
});

type FormData = OrgCreateTypes;

function OrgNewForm() {
  const data = { ...DEFAULT_ORG };
  const user = useSelector((state: RootStateTypes) => state.user);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = React.useState<Set<keyof FormData>>(new Set());

  // Helper function to normalize string values
  const normalizeValue = (value: any): any => {
    if (typeof value === "string") {
      // Trim leading/trailing spaces and reduce multiple spaces to single space
      return value.trim().replace(/\s+/g, " ") || null;
    }
    return value === "" ? null : value;
  };

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: normalizeValue(data.name) || "",
      description: normalizeValue(data.description) || "",
      is_public: normalizeValue(data.is_public) || false,
      address_id: normalizeValue(data.address_id) || null,
      is_formal: normalizeValue(data.is_formal) || false,
      nip: normalizeValue(data.nip) || null,
      regon: normalizeValue(data.regon) || null,
      max_img_width: normalizeValue(data.max_img_width) || 1500,
      total_shares: normalizeValue(data.total_shares) || 1,
      accounts_set_id: normalizeValue(data.accounts_set_id) || null,
      voting_days: normalizeValue(data.voting_days) || 7,
      members_by_admin: normalizeValue(data.members_by_admin) || false,
    },
  });

  const onFieldChange = (fieldName: keyof FormData) => {
    const currentValue = form.getValues(fieldName);
    const originalValue = data[fieldName];

    // Normalize both values for comparison
    const normalizedCurrentValue = normalizeValue(currentValue);
    const normalizedOriginalValue = normalizeValue(originalValue);

    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  const createOrg = useMutation({
    ...createOrgsV1CoreOrgsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readObjectTypesAllV1CoreObjectTypesGet"],
      });
      toast.success(t("common.success.label"));
      form.reset(DEFAULT_ORG);
      queryClient.invalidateQueries();
      setChangedFields(new Set());
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  const onSubmit = async (formData: FormData) => {
    // Normalize all fields in the form data
    const normalizedFormData = Object.fromEntries(
      Object.entries(formData).map(([key, value]) => [key, normalizeValue(value)]),
    ) as OrgCreateTypes;

    // const updatedFields = Object.fromEntries(
    //   Array.from(changedFields).map((field) => [
    //     field,
    //     normalizedFormData[field],
    //   ])
    // );

    await createOrg.mutateAsync(
      {
        body: [
          {
            ...normalizedFormData,
            created_by: user?.id,
          },
        ],
      },
      {},
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.paper}>
        <h2>{t("forms.newOrg.titleNew.label")}</h2>
        <form onSubmit={form.handleSubmit(onSubmit)} className={styles.form}>
          <div className={styles.stack}>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.objectType.name.label")}
                error={form.formState.errors.name?.message}
                className={styles.span4}
                {...form.register("name", {
                  onChange: () => onFieldChange("name"),
                })}
              />
            </div>

            <div className={styles.span4}>
              <TextInput
                label={t("forms.newOrg.description.label")}
                error={form.formState.errors.description?.message}
                className={styles.span4}
                {...form.register("description", {
                  onChange: () => onFieldChange("description"),
                })}
              />
            </div>

            <div className={styles.span4}>
              <Switch
                label={t("forms.newOrg.isPublic.label")}
                {...form.register("is_public", {
                  onChange: () => onFieldChange("is_public"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInputNumber
                label={t("forms.newOrg.addressId.label")}
                error={form.formState.errors.address_id?.message}
                {...form.register("address_id", {
                  onChange: () => onFieldChange("address_id"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <Switch
                label={t("forms.newOrg.isFormal.label")}
                {...form.register("is_formal", {
                  onChange: () => onFieldChange("is_formal"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.newOrg.nip.label")}
                error={form.formState.errors.nip?.message}
                {...form.register("nip", {
                  onChange: () => onFieldChange("nip"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInput
                label={t("forms.newOrg.regon.label")}
                error={form.formState.errors.regon?.message}
                {...form.register("regon", {
                  onChange: () => onFieldChange("regon"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInputNumber
                label={t("forms.newOrg.maxImgWidth.label")}
                error={form.formState.errors.max_img_width?.message}
                {...form.register("max_img_width", {
                  onChange: () => onFieldChange("max_img_width"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInputNumber
                label={t("forms.newOrg.totalShares.label")}
                error={form.formState.errors.total_shares?.message}
                {...form.register("total_shares", {
                  onChange: () => onFieldChange("total_shares"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInputNumber
                label={t("forms.newOrg.accountsSetId.label")}
                error={form.formState.errors.accounts_set_id?.message}
                {...form.register("accounts_set_id", {
                  onChange: () => onFieldChange("accounts_set_id"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <TextInputNumber
                label={t("forms.newOrg.votingDays.label")}
                error={form.formState.errors.voting_days?.message}
                {...form.register("voting_days", {
                  onChange: () => onFieldChange("voting_days"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <Switch
                label={t("forms.newOrg.membersByAdmin.label")}
                {...form.register("members_by_admin", {
                  onChange: () => onFieldChange("members_by_admin"),
                })}
              />
            </div>

            <div className={styles.span4}>
              <Button type="submit" disabled={changedFields.size === 0}>
                {t("common.create.label")}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default OrgNewForm;
