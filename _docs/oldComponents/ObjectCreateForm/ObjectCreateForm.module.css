.container {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .paper {
    padding: 1rem;
    margin: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .stack {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    align-items: center;
  }
  
  @media (min-width: 768px) {
    .stack {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  .span1 {
    grid-column: span 1;
  }
  
  .span2 {
    grid-column: span 2;
  }
  
  .span3 {
    grid-column: span 3;
  }
  
  .span4 {
    grid-column: span 4;
  }
  
  @media (max-width: 767px) {
    .span1,
    .span2,
    .span3,
    .span4 {
      grid-column: 1 / -1;
    }
  }
  
  .group {
    display: flex;
    gap: 1rem;
  }
  
  .groupGrow > * {
    flex: 1;
  }
  