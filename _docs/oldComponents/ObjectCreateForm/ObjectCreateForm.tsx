import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Checkbox, Select, Textarea } from "@mantine/core";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { z } from "zod";
import { createObjectsV1CoreObjectsPostMutation } from "@/api/_client/@tanstack/react-query.gen";
import { ObjectCreateTypes, ObjectTypeDisplayTypes } from "@/api/_client/types.gen";
import { Button, Switch, TextInput, TextInputNumber } from "@/components/form";
import type { RootStateTypes } from "@/utils/redux/store";
import styles from "./ObjectCreateForm.module.css";

const DEFAULT_OBJECT: ObjectCreateTypes = {
  name: "",
  description: "",
  object_type: "",
  width: "",
  length: "",
  height: "",
  parent_id: null,
  tree_level: 0,
  surface: "",
  volume: "",
  circumference: "",
  is_branch: false,
  org_id: 0,
  created_by: 0,
  json_metadata: {},
};

const formSchema = z.object({
  name: z.string().min(1),
  description: z.string().nullable(),
  object_type: z.string().min(1),
  width: z.union([z.string(), z.number()]).nullable(),
  length: z.union([z.string(), z.number()]).nullable(),
  height: z.union([z.string(), z.number()]).nullable(),
  parent_id: z.number().nullable(),
  tree_level: z.number(),
  org_id: z.number(),
  surface: z.union([z.string(), z.number()]).nullable(),
  volume: z.union([z.string(), z.number()]).nullable(),
  circumference: z.union([z.string(), z.number()]).nullable(),
  is_branch: z.boolean(),
  created_by: z.number(),
});

// type FormData = ObjectCreateTypes;

interface ObjectCreateFormProps {
  objectTypes: ObjectTypeDisplayTypes[];
}

function ObjectCreateForm({ objectTypes }: ObjectCreateFormProps) {
  const data = DEFAULT_OBJECT;
  const parent = useSelector((state: RootStateTypes) => state.system.selectedNodes[0]) || null;
  const user = useSelector((state: RootStateTypes) => state.user);
  const org = useSelector((state: RootStateTypes) => state.org);
  // console.log("parent", parent);
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  // Helper function to normalize string values
  const normalizeValue = (value: any): any => {
    if (typeof value === "string") {
      // Trim leading/trailing spaces and reduce multiple spaces to single space
      return value.trim().replace(/\s+/g, " ") || null;
    }
    return value === "" ? null : value;
  };

  const form = useForm<ObjectCreateTypes>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: normalizeValue(data?.name) || "",
      description: normalizeValue(data?.description) || "",
      object_type: normalizeValue(data?.object_type) || "",
      width: normalizeValue(data?.width) || "",
      length: normalizeValue(data?.length) || "",
      height: normalizeValue(data?.height) || "",
      parent_id: normalizeValue(data?.parent_id) || null,
      tree_level: normalizeValue(data?.tree_level) || 0,
      surface: normalizeValue(data?.surface) || "",
      volume: normalizeValue(data?.volume) || "",
      circumference: normalizeValue(data?.circumference) || "",
      is_branch: data?.is_branch || false,
      org_id: org.id,
      created_by: user.id,
      json_metadata: data?.json_metadata,
    },
  });

  const [changedFields, setChangedFields] = React.useState<Set<keyof ObjectCreateTypes>>(new Set());
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [checkedItemsCirc, setCheckedItemsCirc] = useState<string[]>([]);
  const [calcVolume, setCalcVolume] = useState(false);

  // CALCULATING SURFACE
  useEffect(() => {
    if (checkedItems.length < 2) {
      form.setValue("surface", 0);
    } else {
      const newSurface = checkedItems.reduce(
        (acc, cur) => acc * (Number(form.getValues(cur as keyof ObjectCreateTypes)) || 0),
        1,
      );
      form.setValue("surface", newSurface);
    }
  }, [checkedItems, form]);

  // CALCULATING CIRCUMFERENCE
  useEffect(() => {
    if (checkedItemsCirc.length < 2) {
      form.setValue("circumference", 0);
    } else {
      const value1 = Number(form.getValues(checkedItemsCirc[0] as keyof ObjectCreateTypes)) || 0;
      const value2 = Number(form.getValues(checkedItemsCirc[1] as keyof ObjectCreateTypes)) || 0;
      const newCircumference = 2 * value1 + 2 * value2;
      form.setValue("circumference", newCircumference);
    }
  }, [checkedItemsCirc, form]);

  const createObject = useMutation(createObjectsV1CoreObjectsPostMutation());

  const onFieldChange = (fieldName: keyof ObjectCreateTypes) => {
    const currentValue = form.getValues(fieldName);
    const originalValue = data?.[fieldName] ?? "";

    // Normalize both values for comparison
    const normalizedCurrentValue = normalizeValue(currentValue);
    const normalizedOriginalValue = normalizeValue(originalValue);

    setChangedFields((prev) => {
      const newSet = new Set(prev);
      if (normalizedCurrentValue !== normalizedOriginalValue) {
        newSet.add(fieldName);
      } else {
        newSet.delete(fieldName);
      }
      return newSet;
    });
  };

  const onSubmit = async (formData: ObjectCreateTypes) => {
    // console.log("form data", formData);
    // Normalize all fields in the form data
    const normalizedFormData = Object.fromEntries(
      Object.entries(formData).map(([key, value]) => [key, normalizeValue(value)]),
    );

    const treeLevel = parent ? parent.tree_level + 1 : 0;
    const parentId = parent ? parent.id : null;

    const data = {
      ...normalizedFormData,
      tree_level: treeLevel,
      parent_id: parentId,
      org_id: org.id,
      object_type: normalizedFormData.object_type,
      name: normalizedFormData.name,
    };
    // console.log("form data", data);
    try {
      await createObject.mutate(
        {
          body: [
            {
              ...normalizedFormData,
              tree_level: treeLevel,
              parent_id: parentId,
              org_id: org.id,
              object_type: normalizedFormData.object_type,
              name: normalizedFormData.name,
            },
          ],
        },
        {
          onSuccess: () => {
            toast.success(t("modules.objects.form.createSuccess.label"));
            queryClient.invalidateQueries();
            form.reset();
            form.setValue("object_type", "");
            setChangedFields(new Set());
          },
          onError: (error) => {
            console.error("Failed to create object:", error);
            toast.error(t("modules.objects.form.error.label"));
          },
        },
      );
      setChangedFields(new Set());
    } catch (error) {
      console.error("Failed to create object:", error);
    }
  };

  const handleSurfaceCalc = (event) => {
    const { value, checked } = event.currentTarget;

    // Create a copy of the current state
    let newCheckedItems = [...checkedItems];

    if (checked) {
      // Add the new value
      newCheckedItems.push(value);

      // If there are more than 2 items, remove the first one
      if (newCheckedItems.length > 2) {
        newCheckedItems.shift();
      }
    } else {
      // Remove the unchecked value
      newCheckedItems = newCheckedItems.filter((item) => item !== value);
    }

    // Update the state
    setCheckedItems(newCheckedItems);
  };

  const handleVolumeCalc = (event) => {
    const { checked } = event.currentTarget;
    // console.log("form values", form.getValues());

    if (!checked) {
      setCalcVolume(false);
    } else if (
      checked &&
      (form.getValues("width") as unknown as number) > 0 &&
      (form.getValues("height") as unknown as number) > 0 &&
      (form.getValues("length") as unknown as number) > 0
    ) {
      setCalcVolume(true);
      form.setValue(
        "volume",
        (form.getValues("width") as unknown as number) *
          (form.getValues("height") as unknown as number) *
          (form.getValues("length") as unknown as number),
      );
    }
  };

  type ValidDimensionField = "length" | "width" | "height";

  const handleCircumferenceCalc = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.currentTarget.value as ValidDimensionField;
    const { checked } = event.currentTarget;

    // Create a copy of the current state
    let newCheckedItems = [...checkedItemsCirc];

    if (checked) {
      // Add the new value
      newCheckedItems.push(value);

      // If there are more than 2 items, remove the first one
      if (newCheckedItems.length > 2) {
        newCheckedItems.shift();
      }
    } else {
      // Remove the unchecked value
      newCheckedItems = newCheckedItems.filter((item) => item !== value);
    }

    // Update the state
    setCheckedItemsCirc(newCheckedItems);
  };

  return (
    <div className={styles.container}>
      <div className={styles.paper}>
        <h2>
          {t("modules.objects.form.title")} {parent?.name || "root"}
        </h2>

        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className={styles.stack}>
            <div className={styles.span4}>
              <TextInput
                label={t("modules.objects.form.name.label")}
                placeholder={t("modules.objects.form.name.placeholder.label")}
                {...form.register("name", {
                  onChange: () => onFieldChange("name"),
                })}
                error={t(form.formState.errors.name?.message || "")}
              />
            </div>
            <div className={styles.span4}>
              <Textarea
                label={t("modules.objects.form.description.label")}
                placeholder={t("modules.objects.form.description.placeholder.label")}
                {...form.register("description", {
                  onChange: () => onFieldChange("description"),
                })}
                error={t(form.formState.errors.description?.message || "")}
              />
            </div>
            sdgsdfgsdf sdg sdfg sdf
            <div className={styles.span4}>
              <Controller
                name="object_type"
                control={form.control}
                render={({ field }) => (
                  <Select
                    label={t("modules.objects.form.objectType.label")}
                    placeholder={t("modules.objects.form.objectType.placeholder.label")}
                    data={objectTypes.map((type) => ({
                      value: type.name,
                      label: type.label,
                    }))}
                    error={t(form.formState.errors.object_type?.message || "")}
                    onChange={(value) => {
                      field.onChange(value);
                      onFieldChange("object_type");
                    }}
                    value={field.value}
                  />
                )}
              />
            </div>
            <div className={styles.span1}></div>
            <div className={styles.span1}>
              <TextInputNumber
                label={t("modules.objects.form.dimensions.length.label")}
                placeholder={t("modules.objects.form.dimensions.length.placeholder.label")}
                {...form.register("length", {
                  onChange: () => onFieldChange("length"),
                })}
                error={t(form.formState.errors.length?.message || "")}
              />
            </div>
            <div className={styles.span1}>
              <TextInputNumber
                label={t("modules.objects.form.dimensions.width.label")}
                placeholder={t("modules.objects.form.dimensions.width.placeholder.label")}
                {...form.register("width", {
                  onChange: () => onFieldChange("width"),
                })}
                error={t(form.formState.errors.width?.message || "")}
              />
            </div>
            <div className={styles.span1}>
              <TextInputNumber
                label={t("modules.objects.form.dimensions.height.label")}
                placeholder={t("modules.objects.form.dimensions.height.placeholder.label")}
                {...form.register("height", {
                  onChange: () => onFieldChange("height"),
                })}
                error={t(form.formState.errors.height?.message || "")}
              />
            </div>
            <div className={styles.span1}>
              <TextInputNumber
                label={t("modules.objects.form.calculations.surface.label")}
                placeholder={t("modules.objects.form.calculations.surface.placeholder.label")}
                {...form.register("surface", {
                  onChange: () => onFieldChange("surface"),
                })}
                error={t(form.formState.errors.surface?.message || "")}
              />
            </div>
            <div className={styles.span1}>
              {" "}
              <Checkbox
                label={t("modules.objects.form.dimensions.length.label")}
                type="checkbox"
                value="length"
                checked={checkedItems.includes("length")}
                onChange={handleSurfaceCalc}
              />
            </div>
            <div className={styles.span1}>
              {" "}
              <Checkbox
                label={t("modules.objects.form.dimensions.width.label")}
                type="checkbox"
                value="width"
                checked={checkedItems.includes("width")}
                onChange={handleSurfaceCalc}
              />
            </div>
            <div className={styles.span1}>
              {" "}
              <Checkbox
                label={t("modules.objects.form.dimensions.height.label")}
                type="checkbox"
                value="height"
                checked={checkedItems.includes("height")}
                onChange={handleSurfaceCalc}
              />
            </div>
            <div className={styles.span1}>
              <TextInputNumber
                label={t("modules.objects.form.calculations.volume.label")}
                placeholder={t("modules.objects.form.calculations.volume.placeholder.label")}
                {...form.register("volume", {
                  onChange: () => onFieldChange("volume"),
                })}
                error={t(form.formState.errors.volume?.message || "")}
              />
            </div>
            <div className={styles.span1}>
              <Checkbox
                label={t("modules.objects.form.calculations.volume.calculate.label")}
                checked={calcVolume}
                onChange={handleVolumeCalc}
              />
            </div>
            <div className={styles.span1}>
              <TextInputNumber
                label={t("modules.objects.form.calculations.circumference.label")}
                placeholder={t("modules.objects.form.calculations.circumference.placeholder.label")}
                {...form.register("circumference", {
                  onChange: () => onFieldChange("circumference"),
                })}
                error={t(form.formState.errors.circumference?.message || "")}
              />
            </div>
            <div className={styles.span1}>
              <Checkbox
                label={t("modules.objects.form.dimensions.length.label")}
                type="checkbox"
                value="length"
                checked={checkedItemsCirc.includes("length")}
                onChange={handleCircumferenceCalc}
              />
            </div>
            <div className={styles.span1}>
              <Checkbox
                label={t("modules.objects.form.dimensions.width.label")}
                type="checkbox"
                value="width"
                checked={checkedItemsCirc.includes("width")}
                onChange={handleCircumferenceCalc}
              />
            </div>
            <div className={styles.span4}>
              <Switch
                label={t("modules.objects.form.isBranch.label")}
                {...form.register("is_branch", {
                  onChange: () => onFieldChange("is_branch"),
                })}
              />
            </div>
            <div className={styles.span4}>
              <Button type="submit" disabled={changedFields.size === 0} loading={createObject.isPending}>
                {t("common.create.label")}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ObjectCreateForm;
