http://localhost:4321/app/main/jobs?grouping=%5B%22status%22%5D&expanded=%7B%22status%3ACONFIRMED%22%3Atrue%7D

if i pass url with search params for some reason the const search = Route.useSearch(); is empty object and all search prams are reset

if i pass this url again in the same tab i get all search values correct...

why  is this, how to fix it?

The search object from Route.useSearch() is empty on the first render, but works correctly on subsequent navigations to the same URL.

noooo it is not about first render, it is about firest load in new browser tab

it does not work when you open application in empty tab, if you place url in tab with app loaded - even differetn url it works correct on all renders


import { createFileRoute, useLocation, useNavigate, useRouteContext, useRouter, useSearch } from "@tanstack/react-router";
import type {
  GetAllParams,
  JobColumns,
  JobDataTypes,
  JobDisplayColumnsTypes,
  VariantDisplayColumnsTypes,
} from "@types";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { getVariantsAllApiV1CoreVariantsGetAllPostOptions } from "@/api/_client/@tanstack/react-query.gen";
import { getColumns } from "@/api/core/jobs/columns";
import { useJobsData } from "@/api/core/jobs/hooks_data/useJobsData";
import { useVariantsData } from "@/api/crm/variants/hooks_data/useVariantsData";
import { useUpdateVariantMutation } from "@/api/crm/variants/hooks_mutations/useUpdateVariantMutation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import { DataTable } from "@/components/data-table/data-table";
import type { ExportConfigType, PathStateTypes } from "@/components/data-table/defaults.ts";
import { searchSchema } from "@/dosiero/searchSchema";
import type { RouteContext } from "@/types/router";
import {
  cleanEmptyParams,

} from "@/utils/cleanEmptyParams";
// import { useInternalNavigate } from "@/utils/navigation";
import type { RootStateTypes } from "@/utils/redux/store";
import RenderToolbarContent from "./-components/render-toolbar-content";
import { tableConfig, tableDefaultVariant } from "./-components/table-config";

export const Route = createFileRoute("/main/jobs/")({
  validateSearch: searchSchema,
  component: RouteComponent,
});

function RouteComponent() {
  // Use our custom hook for internal navigation instead of regular useNavigate
  // biome-ignore lint/correctness/noUnusedVariables: kept for future use

  // biome-ignore lint/correctness/noUnusedVariables: used in commented out code
  const navigate = useNavigate();
  const router = useRouter();
  const search = Route.useSearch();

  const isInternal = search._internal === "1";
  console.log("%c <<<< INDEX isInternal  >>>>", "color: green; font-weight: bold;", isInternal);

  const { id: path } = Route.useMatch();
  const { org_id, profile_id, user_id, locale = "pl" } = useRouteContext({ from: path });
  const [variant, setVariant] = useState<VariantDisplayColumnsTypes>(
    {
      ...tableDefaultVariant,
      path: path,
      server_ops: cleanEmptyParams(tableDefaultVariant.server_ops),
      client_ops: cleanEmptyParams(tableDefaultVariant.client_ops)
    });
  console.log(">>> INDEX VARIANT STATE", variant);

  const { dataJobs, errorJobs, isLoadingJobs } = useJobsData(variant.server_ops);
  const { dataVariants, errorVariants, isLoadingVariants } = useVariantsData({
    filters: { profile_id: profile_id, path: path },
  });
  const updateVariantMutation = useUpdateVariantMutation();

  // // on nvigete away actions
  // useEffect(() => {
  //   // Subscribe to the onBeforeNavigate event
  //   const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
  //     // Check if navigating away from "/objects/new"
  //     if (toLocation.pathname !== `/main/jobs/`) {
  //       console.log("Navigating away from /main/jobs/");
  //     }
  //   });

  //   return () => {
  //     // Clean up the subscription
  //     unsubscribe();
  //   };
  // }, [router]);



  // This ref helps us track if we've already processed the variant initialization
  const initialVariantProcessed = useRef(false);

  useEffect(() => {
    console.log("%c <<<< INDEX USEEFFECT STARTED  >>>> dataVariants.length", "color: blue; font-weight: bold;", dataVariants?.data.length);
    if (!dataVariants) return;

    const activeVariant = dataVariants.data.find((variant) => variant.is_active);

    console.log("%c <<<< INDEX initialVariantProcessed  >>>>", "color: blue; font-weight: bold;", initialVariantProcessed.current);

    if (!activeVariant) {
      console.log("DOINT NOTHING - no active variant, default table variant applied")
      return
    } else {

      if (!isInternal) {
        if (!initialVariantProcessed.current) {
          console.log('%cEXTERNAL REQUEST - SETTING PROCESSED TRUE', 'color: blue; font-weight: bold;');

          initialVariantProcessed.current = true;
        } else {
          console.log('%cEXTERNAL REQUEST - DOING NOTHING', 'color: blue; font-weight: bold;');
        }
      } else if (isInternal) {
        if (!initialVariantProcessed.current) {
          console.log('%cIIIIII    Internal navigation   IIIIIII  Processing active variant:', 'color: blue; font-weight: bold;', cleanEmptyParams(activeVariant.client_ops));

          setVariant({
            ...activeVariant,
            server_ops: cleanEmptyParams(activeVariant.server_ops),
            client_ops: cleanEmptyParams(activeVariant.client_ops)

          });
          navigate({ search: cleanEmptyParams(activeVariant.client_ops), replace: true });

          initialVariantProcessed.current = true;
        } else {
          console.log('%cIIIIII    Internal navigation   IIIIIII - active variant already processed', 'color: blue; font-weight: bold;');
        }
      }

    }


  }, [dataVariants, isInternal]);




  // console.log("%cqueryArgs <<<< INDEX  >>>>", "color: blue; font-weight: bold;", queryArgs);

  const { t } = useTranslation();

  // Export configuration for CSV/Excel export
  const exportConfig: ExportConfigType = {
    entityName: "jobs",
    locale: locale,
    columnMapping: {
      start_date: t("forms.JobForm.startDate.label", "start_date"),
      name: t("forms.JobForm.name.label", "name"),
      status: t("forms.JobForm.status.label", "status"),
      type: t("forms.JobForm.type.label", "type"),
      budget: t("forms.JobForm.budget.label", "budget"),
    },
    // Corresponds to the order in columnMapping
    columnWidths: [{ wch: 25 }, { wch: 30 }, { wch: 20 }],
    headers: ["Start date", "Job name", "Status", "Type"],
  };


  if (errorJobs || errorVariants) {
    return <div>Error: {errorJobs?.message || errorVariants?.message}</div>;
  }

  const handleVariantChange = (value: string) => {
    const newActiveVariant = dataVariants?.data.find((v) => v.name === value);
    if (!newActiveVariant) return;

    console.log('%cChanging variant to:', 'color: green; font-weight: bold;', value);

    // update variants data in db: current.is_active = false, selected.is_active = true
    updateVariantMutation.mutate({
      body: [
        {
          id: variant.id,
          is_active: false,
        },
        {
          id: newActiveVariant.id,
          is_active: true,
        },
      ],
    }, {
      onSuccess: () => {
        console.log('Variant updated successfully');
        // Navigate with the internal flag automatically set by our custom hook
        // navigate({
        //   search: searchParams,
        //   replace: true
        // });
      }
    });
  };

  const dataLoading = isLoadingJobs || isLoadingVariants;

  console.log('%csearch', 'color: orange; font-weight: bold;', search);
  // Check if search params have been properly parsed
  // On first load with complex JSON params, search might be empty initially
  const hasSearchParams = Object.keys(search).length > 0 || !window.location.search;

  // If we have URL search params but search object is empty, wait for parsing
  if (window.location.search && !hasSearchParams) {
    return <div>Loading search parameters...</div>;
  }
  const isRouterReady = !router.state.isLoading && router.state.location

  

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold mb-4">Jobs</h1>
        <Select value={variant.name || ""} onValueChange={handleVariantChange}>
          <SelectTrigger className="w-[440px]">
            <SelectValue>{variant.name || "Select variant"}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {dataVariants?.data.map((v) => (
              <SelectItem key={v.id} value={v.name || ""}>
                {v.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <DataTable<JobDisplayColumnsTypes, VariantDisplayColumnsTypes>
        data={dataJobs?.data as JobDisplayColumnsTypes[]}
        serverPagination={dataJobs?.pagination}
        isLoading={dataLoading}
        error={errorJobs || undefined}
        tableConfig={{ ...tableConfig, path: path }}
        // getColumns={(handleRowDeselection) =>
        //   getColumns(handleRowDeselection, variant?.client_ops?.columns as string[] | [])
        // }
        getColumns={(handleRowDeselection) =>
          getColumns(
            handleRowDeselection,
            locale
          )
        }
        exportConfig={exportConfig}
        renderToolbarContent={RenderToolbarContent}
        // pathState={variant.client_ops}
        pathState={search}

        variant={variant}
        setVariant={setVariant}
      />
    </div>
  );
}


AND THIS IS EMPTY TOO
console.log(
    "%cwindow.location.search:",
    "color: green; font-weight: bold;",
    window.location.search
  );




