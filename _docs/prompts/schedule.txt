we have data from this backend function:
async def org_transactions_get_scheduled(
    db: AsyncSession,
    org_id: int,
    period_start: datetime.date,
    period_end: datetime.date,
) -> OrgTransactionScheduledTypes:
    """
    Get scheduled transactions for an organization within a date range.
    
    Args:
        db: Database session
        org_id: Organization ID
        period_start: Start date of the period
        period_end: End date of the period
        
    Returns:
        OrgTransactionScheduledTypes containing categorized scheduled transactions
    """
    # Base query for all scheduled transactions
    base_query = (
        select(OrgTransactionModel)
        .where(
            OrgTransactionModel.org_id == org_id,
            OrgTransactionModel.is_schedule == True,
        )
        .options(selectinload(OrgTransactionModel.org_splits))
    )
    
    # Get transactions with fixed dates
    fixed_dates_query = base_query.where(
        OrgTransactionModel.is_regular == False,
        text("EXISTS (SELECT 1 FROM unnest(sch_dates) date WHERE date BETWEEN :start AND :end)")
    ).params(start=period_start, end=period_end)
    fixed_dates_result = await db.execute(fixed_dates_query)
    fixed_dates = fixed_dates_result.scalars().all()
    
    # Get transactions with fixed end date
    fixed_end_date_query = base_query.where(
        OrgTransactionModel.is_regular == True,
        OrgTransactionModel.sch_end_date.isnot(None),
        OrgTransactionModel.sch_start_date <= period_end,
        OrgTransactionModel.sch_end_date >= period_start
    )
    fixed_end_date_result = await db.execute(fixed_end_date_query)
    fixed_end_date = fixed_end_date_result.scalars().all()
    
    # Get transactions with open end date
    open_end_date_query = base_query.where(
        OrgTransactionModel.is_regular == True,
        OrgTransactionModel.sch_end_date.is_(None),
        OrgTransactionModel.sch_start_date <= period_end
    )
    open_end_date_result = await db.execute(open_end_date_query)
    open_end_date = open_end_date_result.scalars().all()
    
    return OrgTransactionScheduledTypes(
        fixed_dates=[OrgTransactionDisplayTypes.from_orm(item) for item in fixed_dates],
        fixed_end_date=[OrgTransactionDisplayTypes.from_orm(item) for item in fixed_end_date],
        open_end_date=[OrgTransactionDisplayTypes.from_orm(item) for item in open_end_date]
    )


the transaction model:
 id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(True), server_default=text("now()")
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(True), server_default=text("now()")
    )
    created_by: Mapped[int] = mapped_column(Integer)
    updated_by: Mapped[Optional[int]] = mapped_column(Integer)
    lang: Mapped[str] = mapped_column(String(255), server_default=text("'pl'::text"))
    json_metadata: Mapped[Optional[dict]] = mapped_column(JSONB)
    name: Mapped[str] = mapped_column(String(255))
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    date: Mapped[datetime.date] = mapped_column(Date, nullable=False)
    type: Mapped[Optional[str]] = mapped_column(VARCHAR(255), nullable=True)
    due_date: Mapped[Optional[datetime.date]] = mapped_column(Date, nullable=True)
    amount: Mapped[int] = mapped_column(Integer, nullable=False)
    job_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    comment: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    object_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    is_saved: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    posted_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, server_default=text("NOW()"), nullable=False
    )
    memo: Mapped[Optional[str]] = mapped_column(VARCHAR(255), nullable=True)
    org_id: Mapped[int] = mapped_column(Integer, nullable=False)
    our_ref: Mapped[Optional[str]] = mapped_column(VARCHAR(255), nullable=True)
    contrahent_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    cust_ref: Mapped[Optional[str]] = mapped_column(VARCHAR(255), nullable=True)
    set_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    set_item_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    template_used: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )
    # schedule
    is_schedule: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_regular: Mapped[bool] = mapped_column(
        Boolean, server_default=text("'false'::boolean"), nullable=False
    )
    sch_dates: Mapped[list[datetime.date]] = mapped_column(
        ARRAY(Date), server_default=text("'{}'::date[]"), nullable=False
    )
    sch_start_date: Mapped[Optional[datetime.date]] = mapped_column(Date, nullable=True)
    sch_end_date: Mapped[Optional[datetime.date]] = mapped_column(Date, nullable=True)
    sch_interval: Mapped[Optional[str]] = mapped_column(
        VARCHAR(255), nullable=True
    )  # daily, weekly,monthly , yearly

    the data is retuned for props: period_start, period_end

    we have 3 kinds of scheduled transactions:

    fixed_dates
    fixed_end_date
    open_end_date

- with fixed dates - we return rows uder key "fixed_dates"

we return all rows where
is_shedule=True
and
is_regular=False
and 
any date in sch_dates is between period_start and period_end

- repeated transactions with fixed end date - we return rows under key "fixed_end_date"

we return all rows where
is_shedule=True
and
is_regular=True
and 
period_start to period_end overlaps start_date to end_date period

- repeated transactions with open end date (end_date is None) - we return rows under key "open_end_date"

we return all rows where
is_shedule=True
and
is_regular=True
and 
start_date > period_start
and
start_date < period_end

for given period_start and period_end we need to generate array: periodScheduledTransactions
for each group we calculate the transaction dates in given period
- fixed dates - we filter and return only objects with sch_dates in given period
if  transaction has more than one date we duplicate all object data for each date

for next two groups we use the sch_interval field which can be daily, weekly, monthly, yearly
and according to this interval we generate arrray of transactions with generated scheduled_date field

- fixed end date - we generate transactio until sch_end_date or period_end wchichever is smaller
- open end date - we generate transactions until period_end

to each transaction object we need to add field: shceduled_date and sort by this date ascending

finally we return all data for display



