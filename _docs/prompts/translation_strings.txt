create translation strings for languages:
pl in /home/<USER>/apps_data/vscodium/code/dosiero_app/src/i18n/pl.json
en in /home/<USER>/apps_data/vscodium/code/dosiero_app/src/i18n/en.json
for all strings in file 
/home/<USER>/apps_data/vscodium/code/dosiero_app/src/routes/proposals/-forms/ProposalForm.tsx
some strings have assigned object references, eg. 
"forms.JobForm.id.label": "Id",
create new references and values for all not yet translated strings
keep nested format like:
 "forms": {
    "ProposalForm": {
      "titleError": { "label": "Title must be at least 2 characters" },
all strings should be under key: "label" so we can nest other values