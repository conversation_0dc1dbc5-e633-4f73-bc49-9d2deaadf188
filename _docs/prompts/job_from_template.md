@jobReducer.ts 
the template is of shape
{
    "id": 28,
    "created_by": 1,
    "updated_by": null,
    "lang": "pl",
    "json_metadata": null,
    "name": "change me",
    "description": null,
    "type": "MEDIA",
    "org_id": 60,
    "duration_hours": 20,
    "job_details": {
        "id": 1,
        "created_by": 1,
        "name": "change me",
        "tag": null,
        "description": "",
        "proposal_id": null,
        "status": "DRAFT",
        "type": "MEDIA",
        "is_public": false,
        "start_date": null,
        "end_date": null,
        "budget": 0,
        "org_id": 60,
        "accepted_offer_id": null,
        "objects": [],
        "lang": "en",
        "is_schedule": false,
        "is_regular": false,
        "sch_dates": [],
        "sch_start_date": null,
        "sch_end_date": null,
        "sch_interval": null,
        "interval_day": null
    },
    "assigned_objects": [],
    "tasks": [
        {
            "created_by": 1,
            "name": "change me",
            "tag": null,
            "description": null,
            "lang": "en",
            "json_metadata": null,
            "job_id": 0,
            "object_id": null,
            "with_photo": false,
            "order": 1
        }
    ],
    "contrahent": {
        "id": null
    },
    "transactions": [],
    "related_jobs_templates_ids": []
}

and out of that we need to make job object of shape 
{
    "job": {
        "id": 0,
        "name": "change me",
        "type": "",
        "org_id": 60,
        "created_by": 1,
        "status": "CONFIRMED",
        "description": "",
        "is_public": false,
        "start_date": "2025-06-15 15:00",
        "end_date": null,
        "budget": 0,
        "accepted_offer_id": null,
        "lang": "pl",
        "is_schedule": false,
        "is_regular": false,
        "sch_dates": [],
        "sch_interval": null,
        "sch_start_date": null,
        "sch_end_date": null
    },
    "objects": [],
    "tasks": [
        {
            "id": 0,
            "name": "change me",
            "job_id": 0,
            "order": 1,
            "created_by": 1,
            "description": null,
            "lang": "en",
            "json_metadata": null,
            "object_id": null,
            "with_photo": false
        }
    ],
    "contrahent": {
        "id": 0,
        "name": "250615--BuVb",
        "tag": "",
        "created_by": 1,
        "org_id": 60,
        "lang": "pl"
    },
    "transactions": [],
    "related_jobs_templates_ids": []
}

add reducer SET_JOB_FROM_TEMPLATE

for the start date use current job.start_date and set the time from payload
end_date you calculate from start datetime and add duratioin_hours field value, if that is not int set start_date + 1 day and time from checOutTimed (payload)

for all org_id, created_by, lang values use payload data

copy all tasks and replace the created_by and generate local id with nanoid

for each assigned_object id, find object data from payload objects and add to job.objects array as 
class ObjectInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: str | None = None
    tag: str | None = None

if there is numeric id in contrahent object, add it 

copy all transactions and replace the created_by and generate local id with nanoid

for each related_jobs_templates_ids, find job template data from payload allTemplates and add to job.related_jobs_templates_ids array
as 
class JobTemplateInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: str | None = None
    tag: str | None = None
    type: str




