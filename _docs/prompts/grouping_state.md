

# when grouping column "type" we got:

grouping value (old) => {
        if (old != null && old.includes(column.id)) {
          return old.filter((d) => d !== column.id);
        }
        return [...old != null ? old : [], column.id];
      }
data-table.tsx:430 grouping value function result ['type']

we hold the state in url path and state is

pathState 


{
    date_column:"start_date"
    grouping: ['type']
    pagination: {pageIndex: 0, pageSize: 100}
}



# when ungruping column "type" we got:

grouping value (old) => {
        if (old != null && old.includes(column.id)) {
          return old.filter((d) => d !== column.id);
        }
        return [...old != null ? old : [], column.id];
      }
data-table.tsx:430 grouping value function result ['type']

and path state is correct:
pathState 

date_column:"start_date"
pagination: {pageIndex: 0, pageSize: 100}


# when we add second groupin level, here we group by "type" and "status"

grouping value (old) => {
        if (old != null && old.includes(column.id)) {
          return old.filter((d) => d !== column.id);
        }
        return [...old != null ? old : [], column.id];
      }
data-table.tsx:431 grouping value function result ['status']

and pathStatus is
{
    "pagination": {
        "pageIndex": 0,
        "pageSize": 100
    },
    "date_column": "start_date",
    "grouping": [
        "type",
        "status"
    ]
}


# when we expand group "type:PART_TIME" and sub-group "status:DRAFT"

the path status is

{
    "pagination": {
        "pageIndex": 0,
        "pageSize": 100
    },
    "date_column": "start_date",
    "grouping": [
        "type",
        "status"
    ],
    "expanded": {
        "type:PART_TIME": true,
        "type:PART_TIME>status:DRAFT": true
    }
}

when we click on the second level aggregation row we got the function which returns
{
    "type:PART_TIME>status:DRAFT": true
}

so we need to remove this value from the expanded state, it is "toggle" type operation: we add if does not exist and remove if exists

please refactor the expanding state function setExpandedState in src/components/data-table/hooks/use-route-filters.ts

======================================================================================

# when 2 levels grouped


## when 1 leel expanding:
{
    "status:DRAFT": true
}

expanded state in URL: undefined

_________________________________________________

## when 1 level folding:
{
    "status:DRAFT": true
}

expanded state in URL: {
    "status:DRAFT": true
}
ALL ABOVE WORK FINE
_________________________________________________

# when 2 level expanding:

{
    "status:DRAFT>type:INTERNSHIP": true
}

expanded state in URL: {
    "status:DRAFT": true,
    "status:DRAFT>type:PART_TIME": true
}

STILL WORKS OK

but when you try to fold the 2 level or 1 level with 2 level expanded:

NOT WOKING

console.logs:

{
    "status:DRAFT>type:INTERNSHIP": true
}

expanded state in URL: {
    "status:DRAFT": true,
    "status:DRAFT>type:PART_TIME": true
}

when trying to fold 1 level with second level expanded:

NOT WORKING
{
    "status:DRAFT": true
}
    
expanded state in URL: {
    "status:DRAFT": true,
    "status:DRAFT>type:PART_TIME": true,
    "status:DRAFT>type:INTERNSHIP": true
}
    
what you do:
1. get the action value by executing the updaterOrValue
2. establish its level
3. compare value with content of current state
if it exists you remove it and all its parents (if level > 1)
if does not exist you add the value



