class ProblemDisplayTypes(BaseModel):
    model_config = ConfigDict(from_attributes=True) 

    id: int
    created_at: datetime.datetime
    updated_at: datetime.datetime
    created_by: int
    updated_by: int | None = None
    
    description: str | None = None
    lang: str
    json_metadata: Optional[Dict[str, Any]] = None
    status: ProblemStatus
    closed_date: datetime.datetime | None = None
    org_id: int
    objects: list[ObjectDisplayTypes] | None = None


const alloweInputTypes = [
  "text",
  "textArea",
  "switch",
  "select",
  "numberMask",
  "number",
  "date",
  "dateRange",
  "checkbox",
  "radio",
];

const alloweValueTypes = [
  "string",
  "number",
  "boolean",
];

defaultValues - set all default values to null


THE ARRAY TO BE COMPLETED:
[
  {
    "name": "id",
    "inputType": "number",
    "defaultValue": "0",
    "valueType": "number"
    "zodType": "z.number()"
  },
  ...

]

gnerate remamining fileds objects in the array...
the "zodType" is a zod schema type (will be used in auto generated schema from fields array)
for zodType: number we need to use z.number()
and add nullable and optional to all fields
return valid json array, no comments, use ""
