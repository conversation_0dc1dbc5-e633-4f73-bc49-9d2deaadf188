in gneral we load the page either from provided url (or page reload) or as a reslut of our internal intent (navigatint with router or variant reload)
we need to be able to load table configuration from passed url  and we need to be able to store the config in variant options

we use tanstack router
the problem is that on each load of route component we should know if we are suppoused to load config from active variant or from url

to accomplish this we mark all internal navigation requests with isInternal=true tanstack router context
the context by default is set as false so when component is loading first time, from url, we always load table config from url

but if we navigate to the page from within our app, or if we request change of saved variant (table configuration) then we need to override the url search params
with values from active variant

as a result we have 2 navigate functions:
- navigate - standard tanstack router navigation (isInternal is false)
- internalNavigate - our custom navigate function (isInternal is true)

the cases we need to handle:

1. navigating within app - we use internalNavigate and load active variant config
2. navigating to url prowided in browser - we use navigate and load table config from url
3. changing variant - we use internalNavigate and load active variant config
4. we update table options in UI - we use standard navigate in useRouteFilters so table state is in sync with url

so basically by default we just use url config 
but when we use our internalNavigate we override the url params with active variant params

now after applyig grouping the url is updated correctly but table state is not updating
component is in loop and crashes

check and fix the logic in src/dosiero/routes/main/jobs/index.tsx

