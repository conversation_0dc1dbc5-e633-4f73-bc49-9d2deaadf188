mjml documentation
@web https://documentation.mjml.io/

src/routes/documents/-components/mockData.tsx

contains the data structure:

const defaultContent: DocPart = {
id: 15,
name: "default content",
html: "<p>no value provided</p>",
mjml: {
tagName: "mj-section",
attributes: {},
children: [
{
tagName: "mj-column",
attributes: {},
children: [
{
tagName: "mj-text",
attributes: {
"font-size": "20px",
color: "#F45E43",
"font-family": "Helvetica",
},
content: ourAddressHtml,
},
],
},
],
},
};

const pageMjml: any = {
tagName: "mj-wrapper",
attributes: {},
children: [
{
tagName: "mj-section",
attributes: {},
children: [...defaultHeaderParts.map((part) => part.mjml)],
},
...defaultPreParts.map((part) => part.mjml),
defaultContent.mjml,
...defaultPostParts.map((part) => part.mjml),
{
tagName: "mj-section",
attributes: {},
children: [...defaultFooterParts.map((part) => part.mjml)],
},
],
};

export const defaultPage: PageTypes = {
id: 0,
title: "Page title",
html: "<p>no value provided</p>",
parts_top: [],
parts_header: defaultHeaderParts,
parts_pre: defaultPreParts,
content: [defaultContent],
parts_post: defaultPostParts,
parts_footer: defaultFooterParts,
parts_bottom: [],
};

export const initialDocument: Document = {
id: 1,
title: "New Document",
pages: [defaultPage],
html: "",
mjml: {
tagName: "mjml",
attributes: {},
children: [
{
tagName: "mj-body",
attributes: {},
children: [pageMjml],
},
],
},
parts_top: [],
parts_header: defaultHeaderParts,
parts_pre: defaultPreParts,

parts_post: defaultPostParts,
parts_footer: defaultFooterParts,
parts_bottom: [],
};

the types are in src/routes/documents/-components/types.ts

WE NEED TO REFACTOR HOW THE EDITORS CONTENT IS UPDATED IN THE DOCUMENT

we update the content in html field AND in the mjml object of each DocPart AND main document.mjml field in each case
, we update the "content" field of the mj-text element

there are two kinds of updates:

- parts_header, parts_footer, parts_pre, parts_post: content is in mj-section>mj-column>mj-text>content
  here the path to content is mjml.children.children.content

- content: content is in mj-section>mj-text>content
  here the path to content is mjml.children.content

double check all paths

WE ALWAYS UPDTE THE END OF THE PATH MJ-TEXT WHERE THERES IS NO CHILDREN FIELD OR IS EMPTY

remove all existing updaters and create the TWO NEW ONES

in each case we need to update as well the main document.mjml object

remember mantine select components acceept only string values, all ids needs to be converted to strings

##############################################################################################################################

in the drawer we need to add section for selecting content templates (similar to sections pre and post )
same like the pre and post we need to be able to delete the inserted templates

the content template may be of two types: text or table

at the top of the drawer we need to add select component for selecting the table content (optionally - if content is not selected and user is adding table to content section we use the default data from template)
we store the value of the select component in the state: tableContent

in the content selection section (between pre and post sections):

- we place switch component for selecting text or table with value: isTemplateTable (default false)
- for false value of isTemplateTable we display select component with contentTextTemplates (src/routes/documents/-components/mockData.tsx) to choose from
- for true value of isTemplateTable we display select component with contentTableTemplates (src/routes/documents/-components/mockData.tsx) to choose from
