 create json array of accounts objects with childrenjust tree like structure of the accounts with each account fields:
    [
      {
      "name",
      "acc_number",
      "description: <same as name>",
      "type" : "ASSET", || "LIABILITY", || "EQUITY", || "INCOME", || "EXPENSE",
      "commodity": "GBP",
      "is_placeholder": false,
      "created_by":1,
      "lang": "en",
      "is_syntetyczne": true,
      "group_id": 1,
      "level": 0,
      "is_branch": true,
      "is_active": true,
      "enabled": true,
      "balance": 0,
      "set_id": 11,
      "children": [],
    
  }, 
  ... ]

the account numbers should be of pattern:
  000-00-00 (3digits - 2digits - 2digits)
  first 2 levels use first 3 digits:
  the top level instead 1000 is 100
  second level uses second and third digit eg: 110, 120 and increses by 10
  the next levels are separate by dashes and increase by 1 
  third level is 110-01 
  fourth level is 110-01-01
  so 1st level is 100
  2nd level is 110
  3rd level is 110-01
  4th level is 110-01-01