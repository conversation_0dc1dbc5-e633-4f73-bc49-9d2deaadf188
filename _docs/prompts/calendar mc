the job is not displayed correctly, it spans over 2 days and is followed by second job
each row reprezents 24 hours of the day, the height of the job component should be proportional to the part of day the job

we have 2 jobs in sample data:
[
    {
        "id": 80,
        "name": "Wynajem A1 + sprzatanie",
        "start_date": "2025-06-01T13:00:00Z",
        "end_date": "2025-06-02T09:00:00Z",
        "status": "CONFIRMED",
        "objects": [
            {
                "id": 55,
                "name": "Apartament 1"
            }
        ]
    },
    {
        "id": 81,
        "name": "Sprzatanie A1",
        "start_date": "2025-06-02T09:00:00Z",
        "end_date": "2025-06-02T10:00:00Z",
        "status": "CONFIRMED",
        "objects": [
            {
                "id": 55,
                "name": "Apartament 1"
            }
        ]
    }
]

wa cannot use the JobCard component - we need to create JobCalendarCard component
the component height needs to be relative to how much of current day tha job takes
for example our first sample job starts on 1st at 15:00 (+2 local time) so we shouid display the job component on row 1st June and vertically should take 11/24 of row height aligned to bottom
and the job ends on 2dn June at 11:00 loacal time so it should be displaed as well on 2nd Juen row
and it should be followed be the second job which is just for 1 hour but lets make the minimum job display div 25% of row height
in JobCalendarCard component we need a function to calculate component height relative to how many hours of that day the job covers