{"data": [{"id": 14, "created_at": "2025-06-11T09:05:46.413443Z", "updated_at": "2025-06-11T09:05:46.413443Z", "created_by": 1, "updated_by": null, "lang": "pl", "json_metadata": null, "name": "wynajem A1-01 solo", "description": "", "template_type": "CLIENT_SERVICES", "org_id": 1, "is_public": false, "duration_hours": 20, "job_details": {"id": 1, "name": "wynajem A1-01 solo", "description": "", "proposal_id": null, "status": "DRAFT", "type": "CLIENT_SERVICES", "is_public": false, "start_date": null, "end_date": null, "budget": 0, "org_id": 0, "accepted_offer_id": null, "objects": [45], "created_by": 0, "lang": "en", "is_schedule": false, "is_regular": false, "sch_dates": [], "sch_start_date": null, "sch_end_date": null, "sch_interval": null}, "assigned_objects": [45], "tasks": [{"created_by": 0, "name": "wynajem A1-01 solo", "description": null, "lang": "en", "json_metadata": null, "job_id": 0, "object_id": null, "with_photo": false, "order": 1, "id": "default"}, {"created_by": 1, "name": "Wynajem A1", "description": "", "lang": "pl", "json_metadata": null, "job_id": 1, "object_id": 45, "with_photo": false, "order": 1, "id": "Ms1t6WVM0JO3WCTgVb9Xn"}], "contrahent": {"created_by": 0, "name": "250611-client_services-XdAW", "description": null, "lang": "en", "json_metadata": null, "region": "", "nip": null, "is_regular": false, "org_id": 0, "user_profile_id": null, "keywords": [], "contrahent_type_ids": [6], "accounts_ids": [], "id": 1, "is_existing": false}, "transactions": [{"created_by": 1, "lang": "pl", "json_metadata": {}, "name": "Wynajem", "description": null, "date": null, "type": null, "org_id": 1, "due_date": null, "amount": 25000, "job_id": null, "comment": null, "object_id": null, "is_saved": true, "is_schedule": false, "is_regular": false, "sch_dates": null, "sch_interval": null, "sch_start_date": null, "sch_end_date": null, "posted_at": null, "cust_ref": null, "our_ref": null, "memo": null, "contrahent_id": null, "contrahent_type_id": 6, "set_id": null, "set_item_id": null, "template_used": null, "org_splits": [{"created_by": 1, "lang": "pl", "json_metadata": {}, "name": "Wynajem", "description": null, "account_id": 1950, "transaction_id": null, "debit": null, "credit": 25000, "due_date": null, "memo": null, "date": null, "is_debit_minus": false, "is_schedule": false, "is_saved": false, "org_id": 1, "set_id": null, "set_item_id": null}, {"created_by": 1, "lang": "pl", "json_metadata": {}, "name": "Wynajem", "description": null, "account_id": 1958, "transaction_id": null, "debit": 25000, "credit": null, "due_date": null, "memo": null, "date": null, "is_debit_minus": false, "is_schedule": false, "is_saved": false, "org_id": 1, "set_id": null, "set_item_id": null}], "id": "POX38U"}], "related_jobs_templates_ids": [], "is_schedule": false, "is_regular": false, "sch_dates": [], "sch_start_date": null, "sch_end_date": null, "sch_interval": null}], "count": 1}