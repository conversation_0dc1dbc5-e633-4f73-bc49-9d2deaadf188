[{"name": "Podatek dochodowy", "acc_number": "870", "description": "Podatek dochodowy", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Rozliczenie wyniku finansowego", "acc_number": "820", "description": "Rozliczenie wyniku finansowego", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Środki trwałe", "acc_number": "010", "description": "Grunty, budynki, urządznia itp", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Wartości niematerialne i prawne", "acc_number": "020", "description": "Wartości niematerialne i prawne nabyte przez zakup lub nieodpłatne otrzymanie", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Aktywa finansowe", "acc_number": "030", "description": "Na ponad 12 miesięcy,  (zapadalność kończy się w roku następnym po roku od dnia bilansowego)", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Umorzenie środków trwałych", "acc_number": "071", "description": "Zapisy zgodne z przepisami ustawy o podatku dochodowym od osób prawnych. Odpisy umorzeniowe są rów­noznaczne z odpisami amortyzacyjnymi.", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Umorzenie wartości niematerialnych i prawnych", "acc_number": "075", "description": "Odpisy umorzeniowe wartości niematerialnych i prawnych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Środki trwałe w budowie", "acc_number": "080", "description": "Ewidencja poniesionych nakładów na budowę i zakup środ­ków trwałych oraz zakup wartości niematerialnych i prawnych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Nieruchomości dzierżawione, najmowane – obce", "acc_number": "091", "description": "<PERSON><PERSON><PERSON><PERSON>, w tym <PERSON>ów będących w posiadaniu zależnym", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Służebności gruntowe", "acc_number": "092", "description": "<PERSON><PERSON><PERSON><PERSON> sł<PERSON>", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Grunty otrzymane w wieczyste użytkowanie od SP lub jednostek samorządu terytorialnego", "acc_number": "093", "description": "Grunty otrzymane w wieczyste użytkowanie od SP lub jednostek samorządu terytorialnego", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Nieruchomości wydzierżawione", "acc_number": "094", "description": "Nieruchomości  oddane w posiadanie zależne - wydzierżawione odpłatnie bądź nieodpłatnie - na rzecz podmiotów zewnętrznych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Służebności gruntowe na rzecz podmiotów zewnętrznych", "acc_number": "095", "description": "Ustanowienie służebności grun­towej (w tym drogowej) - odpłatnie bądź nieodpłatnie - na rzecz podmiotów zewnętrznych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Inne środki pieniężne", "acc_number": "109", "description": "Środki pieniężne nie ujęte na kontach rachun­ków bankowych.", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Konta bankowe", "acc_number": "130", "description": "Konta bankowe", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Lokaty do 3 miesięcy", "acc_number": "131", "description": "do ewidencji i kontroli operacji pieniężnych związanych z zało­żonymi lokatami o terminie zapadalności do 3 miesięcy", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Należności developera", "acc_number": "201", "description": "Należności przysługujące od dewelopera z tyt. dostaw i usług", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Lokaty o terminie wymagalności od 3 do 12 miesięcy", "acc_number": "132", "description": "do ewidencji i kontroli operacji pieniężnych związanych z zało­żonymi lokatami o terminie zapadalności od 3 do 12 miesięcy, przypadającej na bieżący lub następny rok obrotowy", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Zobowiązania dostawy usługi", "acc_number": "210", "description": "Zobowiązania z tyt. dostaw i usług", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Kredyty", "acc_number": "133", "description": "Do ewidencji zaciągniętych kredytów i pożyczek  oraz nabycie środków trwałych oraz wartości niematerialnych i prawnych, powodujących otwar­cie rachunków kredytowych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "<PERSON><PERSON>ze celowe", "acc_number": "189", "description": "do ewidencji i kontroli operacji pieniężnych związanych z fun­duszami celowymi, w tym ewidencji zaciągniętych kredytów i pożyczek na koszty zadań realizowanych z tych funduszy, udostępnionych w rachunku bieżącym", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Należności z tytułu dostaw i usług", "acc_number": "200", "description": "Należności z tytułu dostaw i usług", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Zobowiązania wobez zarządcy", "acc_number": "211", "description": "Zobowiązania wobec zarządcy nieruchomości wspólnej", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Zobowiązania deweloper", "acc_number": "212", "description": "Zobowiązania wobec dewelopera z tyt. dostaw i usług", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Zaliczki dostawy usługi", "acc_number": "215", "description": "Zaliczki na poczet dostaw i usług", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Rozrachunki VAT", "acc_number": "220", "description": "Rozrachunki z tyt. podatku od towarów i usług (VAT)", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Podatek od nieruchomości", "acc_number": "222", "description": "Rozrachunki z tyt. podatku od nieruchomości", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "<PERSON><PERSON><PERSON><PERSON> komuanlne", "acc_number": "223", "description": "Rozrachunki z tyt. opłat za gospodarowanie odpadami komunalnymi", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Podatek dochodowy", "acc_number": "224", "description": "Rozrachunki z tyt. podatku dochodowego od osób prawnych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Inne zobowiązania", "acc_number": "225", "description": "Do opłat skarbowych, podatku od czynności cywilnoprawnych oraz odsetek od nie­terminowych zapłat podatków i innych zobowiązań publicznoprawnych...", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Zaliczki dewelopera", "acc_number": "234", "description": "Rozrachunki z deweloperem z tyt. zaliczek na utrzymanie nieruchomości wspólnej, dostawy mediów do jego lokali i fundusze celowe przysługujących od dewelopera (lub jego następcy prawnego), którego staraniem została wzniesiona i zagospodarowana nieruchomość wspólna", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Koszty utrzymania", "acc_number": "235", "description": "Rozrachunki z właścicielami lokali z tyt. utrzymania nieruchomości wspólnej", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Media", "acc_number": "236", "description": "Rozrachunki z tyt. dostaw mediów do lokali", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Fundusz remontowy", "acc_number": "237", "description": "Rozrachunki z właścicielami lokali z tyt. wpłat na fundusz remontowy", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Inne fundusze", "acc_number": "238", "description": "Na przykład rozrachunki z właścicielami lokali z tyt. wpłat na fundusz nabycia środków trwałych oraz wartości niematerialnych i prawnych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Pożyczki", "acc_number": "240", "description": "Pożyczki wg udzielajacego", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Rozliczenie niedoborów, szkód i nadwyżek", "acc_number": "246", "description": "Rozliczenie niedoborów, szkód i nadwyżek", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Pozostałe", "acc_number": "249", "description": "Pozostałe rozrachunki", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Należności warunkowe", "acc_number": "291", "description": "pozabilansowa ewidencja wszelkich należności warunkowych", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": false}, {"name": "Zobowiązania warunkowe", "acc_number": "292", "description": "Zobowiązania warunkowe", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "620", "description": "Rozliczenie międzyokresowe kosztów", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Koszty utrzymania", "acc_number": "700", "description": "Zaliczki i inne przychody na koszty utrzymania nieruchomości wspólnej", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Przychody finansowe", "acc_number": "750", "description": "Przychody finansowe", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Zaliczki na ustanowienie służebności na rzecz każdoczesnego właściciela i/lub najmu", "acc_number": "702", "description": "Zaliczki na koszty ustanowienia służebności na rzecz każdoczesnego właściciela i/lub najmu", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Przychody z pożytków", "acc_number": "703", "description": "Przychody z pożytków uzyskanych z części wspólnych nieruchomości", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Przychody z dostaw mediów", "acc_number": "704", "description": "Przychody z dostaw mediów do lokali", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Koszty finansowe", "acc_number": "751", "description": "", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Pozostałe przychody operacyjne", "acc_number": "760", "description": "Pozostałe przychody operacyjne", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Pozostałe koszty operacyjne", "acc_number": "761", "description": "Pozostałe koszty operacyjne", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Rozliczenia międzyokresowe przychodów", "acc_number": "840", "description": "Rozliczenia międzyokresowe przychodów", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "<PERSON><PERSON>ze celowe", "acc_number": "850", "description": "<PERSON><PERSON>ze celowe", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}, {"name": "Wynik finansowy", "acc_number": "860", "description": "Wynik finansowy", "enabled": true, "created_by": 1, "lang": "pl", "group_id": 1, "level": 0, "type": "assets", "is_debit_minus": true, "set_id": 1, "currency": "pln", "parent_id": null, "is_branch": true, "is_placeholder": true, "is_active": true, "balance": 0, "is_syntetyczne": true, "set_item_id": null, "is_bilansowe": true}]