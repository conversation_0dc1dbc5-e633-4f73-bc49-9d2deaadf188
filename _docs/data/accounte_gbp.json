[{"name": "Assets", "acc_number": "100", "description": "Assets", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Current Assets", "acc_number": "110", "description": "Current Assets", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Bank CD", "acc_number": "110-01", "description": "Bank CD", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Checking Account", "acc_number": "110-02", "description": "Checking Account", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Money Market", "acc_number": "110-03", "description": "Money Market", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Savings Account", "acc_number": "110-04", "description": "Savings Account", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Cash in Wallet", "acc_number": "110-05", "description": "Cash in Wallet", "type": "CASH", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Fixed Assets", "acc_number": "120", "description": "Fixed Assets", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "House", "acc_number": "120-01", "description": "House", "type": "ASSET", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Other Asset", "acc_number": "120-02", "description": "Other Asset", "type": "ASSET", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Vehicle", "acc_number": "120-03", "description": "Vehicle", "type": "ASSET", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Investments", "acc_number": "130", "description": "Investments", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Brokerage Account", "acc_number": "130-01", "description": "Brokerage Account", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Bond", "acc_number": "130-01-01", "description": "Bond", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Stock", "acc_number": "130-01-02", "description": "Stock", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Market Index", "acc_number": "130-01-03", "description": "Market Index", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Mutual Fund", "acc_number": "130-01-04", "description": "Mutual Fund", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Retirement", "acc_number": "130-02", "description": "Retirement", "type": "BANK", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Bond", "acc_number": "130-02-01", "description": "Bond", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Stock", "acc_number": "130-02-02", "description": "Stock", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Market Index", "acc_number": "130-02-03", "description": "Market Index", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Mutual Fund", "acc_number": "130-02-04", "description": "Mutual Fund", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Spouse Retirement", "acc_number": "130-03", "description": "Spouse Retirement", "type": "BANK", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Bond", "acc_number": "130-03-01", "description": "Bond", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Stock", "acc_number": "130-03-02", "description": "Stock", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Market Index", "acc_number": "130-03-03", "description": "Market Index", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Mutual Fund", "acc_number": "130-03-04", "description": "Mutual Fund", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Currency Trading", "acc_number": "130-04", "description": "Currency Trading", "type": "CURRENCY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}]}, {"name": "Liabilities", "acc_number": "200", "description": "Liabilities", "type": "LIABILITY", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Credit Card", "acc_number": "210", "description": "Credit Card", "type": "CREDIT", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Line of Credit", "acc_number": "220", "description": "Line of Credit", "type": "CREDIT", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Loans", "acc_number": "230", "description": "Loans", "type": "LIABILITY", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Education Loan", "acc_number": "230-01", "description": "Education Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Mortgage Loan", "acc_number": "230-02", "description": "Mortgage Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Other Loan", "acc_number": "230-03", "description": "Other Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Vehicle Loan", "acc_number": "230-04", "description": "Vehicle Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}]}, {"name": "Income", "acc_number": "300", "description": "Income", "type": "INCOME", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Bonus", "acc_number": "310", "description": "Bonus", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Dividend Income", "acc_number": "320", "description": "Dividend Income", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Gifts Received", "acc_number": "330", "description": "Gifts Received", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Interest Income", "acc_number": "340", "description": "Interest Income", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Bond Interest", "acc_number": "340-01", "description": "Bond Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "CD Interest", "acc_number": "340-02", "description": "CD Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Checking Interest", "acc_number": "340-03", "description": "Checking Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Money Market Interest", "acc_number": "340-04", "description": "Money Market Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Other Interest", "acc_number": "340-05", "description": "Other Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Savings Interest", "acc_number": "340-06", "description": "Savings Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Other Income", "acc_number": "350", "description": "Other Income", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Salary", "acc_number": "360", "description": "Salary", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Salary (Spouse)", "acc_number": "370", "description": "Salary (Spouse)", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Expenses", "acc_number": "400", "description": "Expenses", "type": "EXPENSE", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Adjustment", "acc_number": "400-01", "description": "Adjustment", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Auto", "acc_number": "410", "description": "Auto", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Fees", "acc_number": "410-01", "description": "Fees", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Fuel", "acc_number": "410-02", "description": "Petrol, diesel, LPG …", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Parking", "acc_number": "410-03", "description": "Parking", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Repair and Maintenance", "acc_number": "410-04", "description": "Repair and Maintenance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Bank Service Charge", "acc_number": "420", "description": "Bank Service Charge", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Books", "acc_number": "430", "description": "Books", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Cable", "acc_number": "440", "description": "Cable", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Charity", "acc_number": "450", "description": "Charity", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Childcare", "acc_number": "460", "description": "Childcare", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "470", "description": "<PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Commissions", "acc_number": "480", "description": "Commissions", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Computer", "acc_number": "490", "description": "Computer", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Dining", "acc_number": "490-01", "description": "Dining", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Education", "acc_number": "490-02", "description": "Education", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Entertainment", "acc_number": "490-03", "description": "Entertainment", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Music/Movies", "acc_number": "490-03-01", "description": "Music/Movies", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Recreation", "acc_number": "490-03-02", "description": "Recreation", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Travel", "acc_number": "490-03-03", "description": "Travel", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Gifts", "acc_number": "490-04", "description": "Gifts", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Groceries", "acc_number": "490-05", "description": "Groceries", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Hobbies", "acc_number": "490-06", "description": "Hobbies", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Home Repair", "acc_number": "490-07", "description": "Home Repair", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Insurance", "acc_number": "490-08", "description": "Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Auto Insurance", "acc_number": "490-08-01", "description": "Auto Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Health Insurance", "acc_number": "490-08-02", "description": "Health Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Home Insurance", "acc_number": "490-08-03", "description": "Home Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Life Insurance", "acc_number": "490-08-04", "description": "Life Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Rental Insurance", "acc_number": "490-08-05", "description": "Rental Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Interest", "acc_number": "490-09", "description": "Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Education Loan Interest", "acc_number": "490-09-01", "description": "Education Loan Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Mortgage Interest", "acc_number": "490-09-02", "description": "Mortgage Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Other Interest", "acc_number": "490-09-03", "description": "Other Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Vehicle Loan Interest", "acc_number": "490-09-04", "description": "Vehicle Loan Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Laundry/Dry Cleaning", "acc_number": "490-10", "description": "Laundry/Dry Cleaning", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Medical Expenses", "acc_number": "490-11", "description": "Medical Expenses", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Miscellaneous", "acc_number": "490-12", "description": "Miscellaneous", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Online Services", "acc_number": "490-13", "description": "Online Services", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Phone", "acc_number": "490-14", "description": "Phone", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Public Transportation", "acc_number": "490-15", "description": "Public Transportation", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Rent", "acc_number": "490-16", "description": "Rent", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Subscriptions", "acc_number": "490-17", "description": "Subscriptions", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Supplies", "acc_number": "490-18", "description": "Supplies", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Taxes", "acc_number": "490-19", "description": "Taxes", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Federal", "acc_number": "490-19-01", "description": "Federal", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Medicare", "acc_number": "490-19-02", "description": "Medicare", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Other Tax", "acc_number": "490-19-03", "description": "Other Tax", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Property Tax", "acc_number": "490-19-04", "description": "Property Tax", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Social Security", "acc_number": "490-19-05", "description": "Social Security", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "State/Province", "acc_number": "490-19-06", "description": "State/Province", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Taxes (Spouse)", "acc_number": "490-20", "description": "Taxes (Spouse)", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Federal", "acc_number": "490-20-01", "description": "Federal", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Medicare", "acc_number": "490-20-02", "description": "Medicare", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Other Tax", "acc_number": "490-20-03", "description": "Other Tax", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Social Security", "acc_number": "490-20-04", "description": "Social Security", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "State/Province", "acc_number": "490-20-05", "description": "State/Province", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}, {"name": "Utilities", "acc_number": "490-21", "description": "Utilities", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Electric", "acc_number": "490-21-01", "description": "Electric", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Garbage collection", "acc_number": "490-21-02", "description": "Garbage collection", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Gas", "acc_number": "490-21-03", "description": "Gas", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}, {"name": "Water", "acc_number": "490-21-04", "description": "Water", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}]}, {"name": "Equity", "acc_number": "500", "description": "Equity", "type": "EQUITY", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": [{"name": "Opening Balances", "acc_number": "510", "description": "Opening Balances", "type": "EQUITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 13, "children": []}]}]