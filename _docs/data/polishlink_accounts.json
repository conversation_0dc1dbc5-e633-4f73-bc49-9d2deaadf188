[{"name": "Fixed Assets", "acc_number": "100", "description": "Fixed Assets", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Capital Equipment", "acc_number": "110", "description": "Capital Equipment", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Computer Equipment", "acc_number": "110-01", "description": "Computer Equipment", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": 2, "level": 1, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Tools", "acc_number": "110-02", "description": "Tools", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "EU Reverse VAT Purchase", "acc_number": "130", "description": "EU Reverse VAT Purchase", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Property", "acc_number": "120", "description": "Property", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Land", "acc_number": "130", "description": "Land", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Other", "acc_number": "140", "description": "Other", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Current Assets", "acc_number": "200", "description": "Current Assets", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Accounts Receivable", "acc_number": "201", "description": "Accounts Receivable", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Bank Accounts", "acc_number": "210", "description": "Bank Accounts", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Main account", "acc_number": "210-01", "description": "Main account", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Savings account", "acc_number": "210-02", "description": "Savings account", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Cash", "acc_number": "220", "description": "Cash", "type": "ASSET", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Liabilities", "acc_number": "300", "description": "Liabilities", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Accounts Payable", "acc_number": "301", "description": "Accounts Payable", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Credit card", "acc_number": "310", "description": "Credit card", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Loans from director", "acc_number": "320", "description": "Loans from director", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Other", "acc_number": "330", "description": "Other", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Owed Taxes", "acc_number": "340", "description": "Owed Taxes", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Corporation Tax", "acc_number": "340-01", "description": "Corporation Tax", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Income Tax", "acc_number": "340-02", "description": "Income Tax", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "NI Contributions", "acc_number": "340-03", "description": "NI Contributions", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "<PERSON><PERSON>", "acc_number": "350", "description": "<PERSON><PERSON>", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "VAT", "acc_number": "360", "description": "VAT", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Input", "acc_number": "360-01", "description": "Input", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Liabilities", "acc_number": "360-02", "description": "Liabilities", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Output", "acc_number": "360-03", "description": "Output", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "EU", "acc_number": "360-04", "description": "EU", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Sales", "acc_number": "360-05", "description": "Sales", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}]}, {"name": "Equity", "acc_number": "400", "description": "Equity", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Opening Balances", "acc_number": "410", "description": "Opening Balances", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Director's Loan", "acc_number": "420", "description": "Director's Loan", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Dividends", "acc_number": "430", "description": "Dividends", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Director's Dividends 1", "acc_number": "430-01", "description": "Director's Dividends 1", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Director's Dividends 2", "acc_number": "430-02", "description": "Director's Dividends 2", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Shareholder Dividends 1", "acc_number": "430-03", "description": "Shareholder Dividends 1", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "<PERSON>s", "acc_number": "440", "description": "<PERSON>s", "type": "EQUITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Expenses", "acc_number": "500", "description": "Expenses", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Cost of sales", "acc_number": "510", "description": "Cost of sales", "type": "COST_OF_GOODS_SOLD", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Depreciation", "acc_number": "520", "description": "Depreciation", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Payroll", "acc_number": "530", "description": "Payroll", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Director's Fees", "acc_number": "530-01", "description": "Director's Fees", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Employees", "acc_number": "530-02", "description": "Employees", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Income Tax", "acc_number": "530-02-01", "description": "Income Tax", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "<PERSON><PERSON>", "acc_number": "530-02-01", "description": "<PERSON><PERSON>", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Net Salaries", "acc_number": "550-02-03", "description": "Net Salaries", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "NICs", "acc_number": "550-02-04", "description": "NICs", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Stakeholder Contributions", "acc_number": "550-02-05", "description": "Stakeholder Contributions", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Employer's NICs", "acc_number": "510", "description": "Employer's NICs", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Other expenses", "acc_number": "540", "description": "Other expenses", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Accountant", "acc_number": "540-01", "description": "Accountant", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Advertising Promotion", "acc_number": "540-02", "description": "Advertising Promotion", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Bank Charges", "acc_number": "540-03", "description": "Bank Charges", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Consultancy", "acc_number": "540-04", "description": "Consultancy", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Depreciation", "acc_number": "540-05", "description": "Depreciation", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "EU Reverse VAT", "acc_number": "540-06", "description": "EU Reverse VAT", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Insurance", "acc_number": "540-07", "description": "Insurance", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Office", "acc_number": "540-08", "description": "Office", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Professional", "acc_number": "540-09", "description": "Professional", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Property", "acc_number": "540-10", "description": "Property", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Light Heat Power", "acc_number": "540-11", "description": "Light Heat Power", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Rent", "acc_number": "540-12", "description": "Rent", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Repairs Maintenance", "acc_number": "540-13", "description": "Repairs Maintenance", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Subscriptions", "acc_number": "540-14", "description": "Subscriptions", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "<PERSON><PERSON>", "acc_number": "540-15", "description": "<PERSON><PERSON>", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Telecoms", "acc_number": "540-16", "description": "Telecoms", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Travel/Accom", "acc_number": "540-17", "description": "Travel/Accom", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": false, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}]}, {"name": "Income", "acc_number": "600", "description": "Income", "type": "INCOME", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Sales", "acc_number": "610", "description": "Sales", "type": "INCOME", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Goods", "acc_number": "610-01", "description": "Goods", "type": "INCOME", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Services", "acc_number": "610-02", "description": "Services", "type": "INCOME", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Interest", "acc_number": "620", "description": "Interest", "type": "INCOME", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Misc", "acc_number": "630", "description": "Misc", "type": "INCOME", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}, {"name": "Other", "acc_number": "700", "description": "Other", "type": "OTHER", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "VAT", "acc_number": "710", "description": "VAT", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "Input", "acc_number": "180-01", "description": "Input", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Liabilities", "acc_number": "180-02", "description": "Liabilities", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Output", "acc_number": "180-03", "description": "Output", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": [{"name": "EU", "acc_number": "180-03-01", "description": "EU", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}, {"name": "Sales", "acc_number": "180-03-02", "description": "Sales", "type": "LIABILITY", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 2, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}]}, {"name": "Imbalance", "acc_number": "720", "description": "Imbalance", "type": "EXPENSES", "commodity": "GBP", "is_placeholder": false, "is_debit_minus": false, "created_by": 1, "lang": "en", "is_syntetyczne": true, "group_id": null, "level": 1, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 14, "children": []}]}]