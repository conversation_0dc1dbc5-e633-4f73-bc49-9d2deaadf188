[{"acc_name": "Root Account", "acc_number": "1972cce2e2364f95b2b0bc014502661d", "description": null, "type": "ROOT", "commodity": null, "is_placeholder": null, "children": [{"acc_name": "Bank Accounts", "acc_number": "48a242bf9a2d8c947cb41d96493d91da", "description": "Liquids", "type": "BANK", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Current Account", "acc_number": "bf2ecb42d45a96c78639e10232a420c9", "description": "Main bank account", "type": "BANK", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Reserve Account", "acc_number": "1e9e67d49dfef2572e0f31fa933a7a22", "description": "Reserves held e.g. for paying tax", "type": "BANK", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "Assets", "acc_number": "2c6c804d4a7946eb7fb1641ef9086433", "description": "Fixed and semi-fixed assets", "type": "ASSET", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Capital Equipment", "acc_number": "9c566ece97799eda4e900b003ce48e48", "description": "Additions only, Box 7", "type": "ASSET", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Computer Equipment", "acc_number": "25a9a71594adb4ece13605fab43e4bc8", "description": null, "type": "ASSET", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "EU Reverse VAT Purchase", "acc_number": "6708e3ff1292c2b5defd07da9f858b60", "description": null, "type": "ASSET", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "Other", "acc_number": "906665a36a7a51e6935c82d0774c3587", "description": null, "type": "ASSET", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "Liabilities", "acc_number": "831468eeee94fe029230db0c68e16fd9", "description": "Corporation tax and other fees", "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Owed Corporation Tax", "acc_number": "6c5a4c8accd399946328ed4c651ea9e4", "description": null, "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "<PERSON><PERSON>", "acc_number": "ccd33922eef1dfe0501c75e02ff2fb8d", "description": null, "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Owed Tax/NI", "acc_number": "a3391321a3fd3e1cb1662095c7fc6fa2", "description": null, "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Other", "acc_number": "39b14b7dfa94471537026be477f3b843", "description": null, "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "VAT", "acc_number": "3352145930e40b21fee20532ad07501b", "description": "HMRC input/output accounts", "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Input", "acc_number": "d3fda498135dbfca02febf0fbe379069", "description": "Purchases, box 4", "type": "ASSET", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Output", "acc_number": "a46d9e9624070fcd2427973a4c725ed6", "description": "Box 3", "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "EU", "acc_number": "37d726ec68d451d098496b7f5513f6f8", "description": "On reverse VAT purchases (Box 2)", "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Sales", "acc_number": "61bdfc571cdbf2259d99078fe75527a4", "description": "All, including zero rate UK/EU and World  (Box 1)", "type": "LIABILITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}]}, {"acc_name": "Equity", "acc_number": "c0b1160d2dd6b3059acc5083348b282f", "description": "Capital and other interests", "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Corporation Tax", "acc_number": "372df3daa7816b42ad158a169d734cf3", "description": "Payable to HMRC", "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Director's Loan", "acc_number": "8fb3a7b6ce53849cc89304f6758cbe77", "description": "Loan by company to director", "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Dividends", "acc_number": "c43c647131b41329d9b931486518d75f", "description": null, "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Director's Dividends 1", "acc_number": "a14da40765e4782bdf2aabe3060bde56", "description": null, "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Director's Dividends 2", "acc_number": "cda1c8d0665ad3be4f5429964912920f", "description": null, "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Shareholder Dividends 1", "acc_number": "6a58e6f9f495885efe65182acf63c5c8", "description": null, "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "Opening Balances", "acc_number": "409c82cc76a1dc46eefe49988983a0f9", "description": null, "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "<PERSON>s", "acc_number": "7e4ed68ddfce93cd7844d68222f5b0a0", "description": null, "type": "EQUITY", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "Income", "acc_number": "6dbad2c3df0d9948e7017b85ab9a25b7", "description": "Money coming in", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Interest", "acc_number": "1bf0dd5eb0abd0ab217dc8c0282dd806", "description": "Bank and loan interest", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Misc", "acc_number": "88a2dc9e588ab764de91ec21ced94989", "description": "Other sources of income", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Sales", "acc_number": "86ef7451027dcb6223bb01204ac09a5e", "description": "Income from customers", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "UK", "acc_number": "93539ecc86fdad827ebbe84b2f5d2e40", "description": "Sales in UK", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "EU", "acc_number": "af74692df15b1de7665d5dd7a197cdfb", "description": "Sales in EU", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Goods", "acc_number": "18abb3a5b55a8600cb659e002e2fb1e6", "description": "Sale of goods within EU", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Services", "acc_number": "27079bb92d78c85b0ca9acbafcec0e91", "description": "Sale of services within EU", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}, {"acc_name": "World", "acc_number": "f8c798c7e2e51fe5f7e00b9745e27834", "description": "Sales in rest of world", "type": "INCOME", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}]}, {"acc_name": "Expenses", "acc_number": "99b6260a8d4a093d96a6364781e4b19a", "description": "Money going out", "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Depreciation", "acc_number": "1c98cc846060b99d7548ca6064529143", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Emoluments", "acc_number": "0a7d53c61b7e4f033de30fa466a659c9", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Director's Fees", "acc_number": "b843ed80892a72f769ab3c7b21bd2dd8", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Employer's NICs", "acc_number": "a57741189d33bd3929348714b402f48e", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Employees", "acc_number": "d647bd218618d5091fb5731f36ace35a", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Net Salaries", "acc_number": "e514f3f81e518dc3d6b99788f3f0953b", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Stakeholder Contributions", "acc_number": "4cd3c1e54781507e2b753477189fdc8b", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "NICs", "acc_number": "8d98cc9b1f28734ed38bf9b96ea51e17", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Income Tax", "acc_number": "605b15debe595dc138c98d2125ba7a90", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}]}, {"acc_name": "Other non-VAT expenses", "acc_number": "cfa3e60aafed83de57e4be732815df97", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "VAT Purchases", "acc_number": "d77071fafc0de8455dd566b805bfcc40", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": [{"acc_name": "Accountant", "acc_number": "5ee752c87746e0f368eaf82d531d0189", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Bank Charges", "acc_number": "6db88661f0c6b11b121d9cb6c66fcfb7", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "EU Reverse VAT", "acc_number": "af9b5ef4814015a83053a4c991ca0c1a", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Office", "acc_number": "921ebceb9d81d627d3396b8e44ddd6cb", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Telecoms", "acc_number": "396652462ef503449e4d3dfba1932e72", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Software", "acc_number": "5bf6de7b870ff608d8f942a261383251", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Subscriptions", "acc_number": "63407e5d67414459caac7df095782759", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Sundries", "acc_number": "e2902798fe6b9e3721a13d96b3baaec5", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}, {"acc_name": "Travel/Accom", "acc_number": "b40da1c32982229fc507b7531e6bed53", "description": null, "type": "EXPENSE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}]}, {"acc_name": "Accounts Payable", "acc_number": "9ed585a7122ec73651aba6ab1b333c7e", "description": "Entities we are in credit to", "type": "PAYABLE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": []}, {"acc_name": "Accounts Receivable", "acc_number": "89b200584355ddb86957b95acea890c9", "description": "Entities in debit to us", "type": "RECEIVABLE", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": true, "children": []}, {"acc_name": "Cash", "acc_number": "a21914fb3309df6d54d849affb8b01a0", "description": null, "type": "CASH", "commodity": {"space": "ISO4217", "id": "GBP"}, "is_placeholder": null, "children": []}]}]