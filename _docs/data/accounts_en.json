[{"name": "Assets", "acc_number": "1000", "description": "Assets", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Current Assets", "acc_number": "1000", "description": "Current Assets", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Bank CD", "acc_number": "1000", "description": "Bank CD", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Checking Account", "acc_number": "1000", "description": "Checking Account", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Money Market", "acc_number": "1000", "description": "Money Market", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Savings Account", "acc_number": "1000", "description": "Savings Account", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Cash in Wallet", "acc_number": "1000", "description": "Cash in Wallet", "type": "CASH", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Fixed Assets", "acc_number": "1000", "description": "Fixed Assets", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "House", "acc_number": "1000", "description": "House", "type": "ASSET", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Other Asset", "acc_number": "1000", "description": "Other Asset", "type": "ASSET", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Vehicle", "acc_number": "1000", "description": "Vehicle", "type": "ASSET", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Investments", "acc_number": "1000", "description": "Investments", "type": "ASSET", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Brokerage Account", "acc_number": "1000", "description": "Brokerage Account", "type": "BANK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Bond", "acc_number": "1000", "description": "Bond", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Stock", "acc_number": "1000", "description": "Stock", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Market Index", "acc_number": "1000", "description": "Market Index", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Mutual Fund", "acc_number": "1000", "description": "Mutual Fund", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Retirement", "acc_number": "1000", "description": "Retirement", "type": "BANK", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Bond", "acc_number": "1000", "description": "Bond", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Stock", "acc_number": "1000", "description": "Stock", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Market Index", "acc_number": "1000", "description": "Market Index", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Mutual Fund", "acc_number": "1000", "description": "Mutual Fund", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Spouse Retirement", "acc_number": "1000", "description": "Spouse Retirement", "type": "BANK", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Bond", "acc_number": "1000", "description": "Bond", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Stock", "acc_number": "1000", "description": "Stock", "type": "STOCK", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Market Index", "acc_number": "1000", "description": "Market Index", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Mutual Fund", "acc_number": "1000", "description": "Mutual Fund", "type": "MUTUAL", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Currency Trading", "acc_number": "1000", "description": "Currency Trading", "type": "CURRENCY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}]}, {"name": "Liabilities", "acc_number": "1000", "description": "Liabilities", "type": "LIABILITY", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Credit Card", "acc_number": "1000", "description": "Credit Card", "type": "CREDIT", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Line of Credit", "acc_number": "1000", "description": "Line of Credit", "type": "CREDIT", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Loans", "acc_number": "1000", "description": "Loans", "type": "LIABILITY", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Education Loan", "acc_number": "1000", "description": "Education Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Mortgage Loan", "acc_number": "1000", "description": "Mortgage Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Other Loan", "acc_number": "1000", "description": "Other Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Vehicle Loan", "acc_number": "1000", "description": "Vehicle Loan", "type": "LIABILITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}]}, {"name": "Income", "acc_number": "1000", "description": "Income", "type": "INCOME", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Bonus", "acc_number": "1000", "description": "Bonus", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Dividend Income", "acc_number": "1000", "description": "Dividend Income", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Gifts Received", "acc_number": "1000", "description": "Gifts Received", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Interest Income", "acc_number": "1000", "description": "Interest Income", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Bond Interest", "acc_number": "1000", "description": "Bond Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "CD Interest", "acc_number": "1000", "description": "CD Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Checking Interest", "acc_number": "1000", "description": "Checking Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Money Market Interest", "acc_number": "1000", "description": "Money Market Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Other Interest", "acc_number": "1000", "description": "Other Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Savings Interest", "acc_number": "1000", "description": "Savings Interest", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Other Income", "acc_number": "1000", "description": "Other Income", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Salary", "acc_number": "1000", "description": "Salary", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Salary (Spouse)", "acc_number": "1000", "description": "Salary (Spouse)", "type": "INCOME", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Expenses", "acc_number": "1000", "description": "Expenses", "type": "EXPENSE", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Adjustment", "acc_number": "1000", "description": "Adjustment", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Auto", "acc_number": "1000", "description": "Auto", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Fees", "acc_number": "1000", "description": "Fees", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Fuel", "acc_number": "1000", "description": "Petrol, diesel, LPG …", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Parking", "acc_number": "1000", "description": "Parking", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Repair and Maintenance", "acc_number": "1000", "description": "Repair and Maintenance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Bank Service Charge", "acc_number": "1000", "description": "Bank Service Charge", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Books", "acc_number": "1000", "description": "Books", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Cable", "acc_number": "1000", "description": "Cable", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Charity", "acc_number": "1000", "description": "Charity", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Childcare", "acc_number": "1000", "description": "Childcare", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "1000", "description": "<PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Commissions", "acc_number": "1000", "description": "Commissions", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Computer", "acc_number": "1000", "description": "Computer", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Dining", "acc_number": "1000", "description": "Dining", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Education", "acc_number": "1000", "description": "Education", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Entertainment", "acc_number": "1000", "description": "Entertainment", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Music/Movies", "acc_number": "1000", "description": "Music/Movies", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Recreation", "acc_number": "1000", "description": "Recreation", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Travel", "acc_number": "1000", "description": "Travel", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Gifts", "acc_number": "1000", "description": "Gifts", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Groceries", "acc_number": "1000", "description": "Groceries", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Hobbies", "acc_number": "1000", "description": "Hobbies", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Home Repair", "acc_number": "1000", "description": "Home Repair", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Insurance", "acc_number": "1000", "description": "Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Auto Insurance", "acc_number": "1000", "description": "Auto Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Health Insurance", "acc_number": "1000", "description": "Health Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Home Insurance", "acc_number": "1000", "description": "Home Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Life Insurance", "acc_number": "1000", "description": "Life Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Rental Insurance", "acc_number": "1000", "description": "Rental Insurance", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Interest", "acc_number": "1000", "description": "Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Education Loan Interest", "acc_number": "1000", "description": "Education Loan Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Mortgage Interest", "acc_number": "1000", "description": "Mortgage Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Other Interest", "acc_number": "1000", "description": "Other Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Vehicle Loan Interest", "acc_number": "1000", "description": "Vehicle Loan Interest", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Laundry/Dry Cleaning", "acc_number": "1000", "description": "Laundry/Dry Cleaning", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Medical Expenses", "acc_number": "1000", "description": "Medical Expenses", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Miscellaneous", "acc_number": "1000", "description": "Miscellaneous", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Online Services", "acc_number": "1000", "description": "Online Services", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Phone", "acc_number": "1000", "description": "Phone", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Public Transportation", "acc_number": "1000", "description": "Public Transportation", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Rent", "acc_number": "1000", "description": "Rent", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Subscriptions", "acc_number": "1000", "description": "Subscriptions", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Supplies", "acc_number": "1000", "description": "Supplies", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Taxes", "acc_number": "1000", "description": "Taxes", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Federal", "acc_number": "1000", "description": "Federal", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Medicare", "acc_number": "1000", "description": "Medicare", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Other Tax", "acc_number": "1000", "description": "Other Tax", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Property Tax", "acc_number": "1000", "description": "Property Tax", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Social Security", "acc_number": "1000", "description": "Social Security", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "State/Province", "acc_number": "1000", "description": "State/Province", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Taxes (Spouse)", "acc_number": "1000", "description": "Taxes (Spouse)", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Federal", "acc_number": "1000", "description": "Federal", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Medicare", "acc_number": "1000", "description": "Medicare", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Other Tax", "acc_number": "1000", "description": "Other Tax", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Social Security", "acc_number": "1000", "description": "Social Security", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "State/Province", "acc_number": "1000", "description": "State/Province", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}, {"name": "Utilities", "acc_number": "1000", "description": "Utilities", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Electric", "acc_number": "1000", "description": "Electric", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Garbage collection", "acc_number": "1000", "description": "Garbage collection", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Gas", "acc_number": "1000", "description": "Gas", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}, {"name": "Water", "acc_number": "1000", "description": "Water", "type": "EXPENSE", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}]}, {"name": "Equity", "acc_number": "1000", "description": "Equity", "type": "EQUITY", "currency": "GBP", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": [{"name": "Opening Balances", "acc_number": "1000", "description": "Opening Balances", "type": "EQUITY", "currency": "GBP", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 11, "children": []}]}]