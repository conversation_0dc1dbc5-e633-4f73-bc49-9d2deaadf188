# uv run --with faker python jobs.py

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from pydantic import BaseModel, ConfigDict
from enum import Enum
from faker import Faker
import json
import random
import re

class JobStatus(Enum):
    DRAFT = "DRAFT"
    CONFIRMED = "CONFIRMED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"

class JobCreateTypes(BaseModel):
    model_config = ConfigDict(from_attributes=True, use_enum_values=True)
    
    created_by: int
    name: str
    tag: str
    description: str
    lang: str = "pl"
    status: JobStatus = JobStatus.DRAFT
    type: str
    is_public: bool = False
    start_date: datetime
    end_date: datetime
    budget: float = 0
    org_id: int

class JobFactory:
    def __init__(self):
        self.faker = Faker()

    def sanitize_string(self, text: str) -> str:
        """Remove special characters and ensure valid JSON string."""
        # Replace quotes, backslashes, and control characters
        text = re.sub(r'[\x00-\x1F\x7F-\x9F"\\]', '', text)
        # Replace multiple spaces with single space and trim
        text = re.sub(r'\s+', ' ', text).strip()
        # Ensure non-empty string
        return text if text else "default_value"

    def generate_name(self):
        return self.sanitize_string(self.faker.job())

    def generate_tag(self):
        # Use simple words without special characters
        return self.sanitize_string(self.faker.word())

    def generate_description(self):
        # Limit to ASCII characters and sanitize
        return self.sanitize_string(self.faker.text(max_nb_chars=200))

    def generate_type(self):
        return random.choice(["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"])

    def generate_start_date(self):
        start = datetime(2025, 1, 1)
        end = datetime(2025, 12, 31)
        return self.faker.date_time_between(start_date=start, end_date=end)

    def generate_end_date(self, start_date):
        return start_date + timedelta(days=random.randint(1, 180))

    def generate_budget(self):
        return round(random.uniform(0, 100000), 2)

    def build(self):
        start_date = self.generate_start_date()
        return JobCreateTypes(
            created_by=1,
            name=self.generate_name(),
            tag=self.generate_tag(),
            description=self.generate_description(),
            lang="pl",
            status=random.choice(list(JobStatus)).value,
            type=self.generate_type(),
            is_public=self.faker.boolean(),
            start_date=start_date,
            end_date=self.generate_end_date(start_date),
            budget=self.generate_budget(),
            org_id=1
        )

def generate_jobs(n: int) -> List[JobCreateTypes]:
    factory = JobFactory()
    return [factory.build() for _ in range(n)]

if __name__ == "__main__":
    jobs = generate_jobs(1000)
    jobs_dict = [job.dict() for job in jobs]
    
    # Use ensure_ascii=False to handle Unicode properly, but our sanitization should prevent issues
    with open("jobs_data.json", "w", encoding="utf-8") as f:
        json.dump(jobs_dict, f, indent=2, default=str, ensure_ascii=False)