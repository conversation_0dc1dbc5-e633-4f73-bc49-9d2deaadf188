[{"name": "Aktywa", "acc_number": "1000", "description": "Aktywa", "type": "ASSET", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Aktywa bieżące", "acc_number": "1100", "description": "Aktywa bieżące", "type": "ASSET", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Bankowe certyfikaty depozytowe", "acc_number": "1110", "description": "Bankowe certyfikaty depozytowe", "type": "BANK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Konto bankowe", "acc_number": "1120", "description": "Konto bankowe", "type": "BANK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Rynek pieniężny", "acc_number": "1130", "description": "Rynek pieniężny", "type": "BANK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Oszcz<PERSON><PERSON>", "acc_number": "1140", "description": "Oszcz<PERSON><PERSON>", "type": "BANK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Gotówka w portfelu", "acc_number": "1150", "description": "Gotówka w portfelu", "type": "CASH", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Aktywa trwałe", "acc_number": "1200", "description": "Aktywa trwałe", "type": "ASSET", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Dom", "acc_number": "1210", "description": "Dom", "type": "ASSET", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Aktywa inne", "acc_number": "1220", "description": "Aktywa inne", "type": "ASSET", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Pojazd", "acc_number": "1230", "description": "Pojazd", "type": "ASSET", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Inwestycje", "acc_number": "1300", "description": "Inwestycje", "type": "ASSET", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "<PERSON><PERSON>", "acc_number": "1310", "description": "<PERSON><PERSON>", "type": "BANK", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Obligacje", "acc_number": "1311", "description": "Obligacje", "type": "STOCK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Giełda", "acc_number": "1312", "description": "Giełda", "type": "STOCK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "1313", "description": "<PERSON><PERSON><PERSON>", "type": "MUTUAL", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Fundusz wzajemny", "acc_number": "1314", "description": "Fundusz wzajemny", "type": "MUTUAL", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Emerytura", "acc_number": "1320", "description": "Emerytura", "type": "BANK", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Obligacje", "acc_number": "1321", "description": "Obligacje", "type": "STOCK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Giełda", "acc_number": "1322", "description": "Giełda", "type": "STOCK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "1323", "description": "<PERSON><PERSON><PERSON>", "type": "MUTUAL", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Fundusz wzajemny", "acc_number": "1324", "description": "Fundusz wzajemny", "type": "MUTUAL", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Emerytura małżeńska", "acc_number": "1330", "description": "Emerytura małżeńska", "type": "BANK", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Obligacje", "acc_number": "1331", "description": "Obligacje", "type": "STOCK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Giełda", "acc_number": "1332", "description": "Giełda", "type": "STOCK", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "1333", "description": "<PERSON><PERSON><PERSON>", "type": "MUTUAL", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Fundusz wzajemny", "acc_number": "1334", "description": "Fundusz wzajemny", "type": "MUTUAL", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>", "acc_number": "1340", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>", "type": "CURRENCY", "currency": "DEM", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}]}, {"name": "Pasywa", "acc_number": "2000", "description": "Pasywa", "type": "LIABILITY", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Ka<PERSON> kred<PERSON>", "acc_number": "2100", "description": "Ka<PERSON> kred<PERSON>", "type": "CREDIT", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON> k<PERSON>", "acc_number": "2200", "description": "<PERSON><PERSON> k<PERSON>", "type": "CREDIT", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Kredyty", "acc_number": "2300", "description": "Kredyty", "type": "LIABILITY", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Pożyczka na edukację", "acc_number": "2310", "description": "Pożyczka na edukację", "type": "LIABILITY", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "2320", "description": "<PERSON><PERSON><PERSON>", "type": "LIABILITY", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "K<PERSON><PERSON> inny", "acc_number": "2330", "description": "K<PERSON><PERSON> inny", "type": "LIABILITY", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Kredyt na samochód", "acc_number": "2340", "description": "Kredyt na samochód", "type": "LIABILITY", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}]}, {"name": "Prz<PERSON><PERSON><PERSON>", "acc_number": "3000", "description": "Prz<PERSON><PERSON><PERSON>", "type": "INCOME", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Premia", "acc_number": "3100", "description": "Premia", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Przychód z dywidendy", "acc_number": "3200", "description": "Przychód z dywidendy", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc_number": "3300", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Przychód z odsetek", "acc_number": "3400", "description": "Przychód z odsetek", "type": "INCOME", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Odsetki z obligacji", "acc_number": "3410", "description": "Odsetki z obligacji", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki od certyfikatów", "acc_number": "3420", "description": "Odsetki od certyfikatów", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki na ROR", "acc_number": "3430", "description": "Odsetki na ROR", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki z rynku pieniężnego", "acc_number": "3440", "description": "Odsetki z rynku pieniężnego", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki inne", "acc_number": "3450", "description": "Odsetki inne", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki z oszczędności", "acc_number": "3460", "description": "Odsetki z oszczędności", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Przychód inny", "acc_number": "3500", "description": "Przychód inny", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Pensja", "acc_number": "3600", "description": "Pensja", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Pensja (małżonka)", "acc_number": "3700", "description": "Pensja (małżonka)", "type": "INCOME", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "acc_number": "4000", "description": "<PERSON><PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Ko<PERSON><PERSON>", "acc_number": "4100", "description": "Ko<PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "acc_number": "4200", "description": "<PERSON><PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "<PERSON><PERSON><PERSON>", "acc_number": "4210", "description": "<PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Paliwo", "acc_number": "4220", "description": "Paliwo", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Parking", "acc_number": "4230", "description": "Parking", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Naprawy i utrzymanie", "acc_number": "4240", "description": "Naprawy i utrzymanie", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Opłaty bankowe", "acc_number": "4300", "description": "Opłaty bankowe", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Książki", "acc_number": "4400", "description": "Książki", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Kablówka", "acc_number": "4500", "description": "Kablówka", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON> ch<PERSON>", "acc_number": "4600", "description": "<PERSON><PERSON><PERSON> ch<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Wydatki na dzieci", "acc_number": "4700", "description": "Wydatki na dzieci", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Ubrania", "acc_number": "4800", "description": "Ubrania", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Prowizje", "acc_number": "4900", "description": "Prowizje", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Komputer", "acc_number": "5000", "description": "Komputer", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>", "acc_number": "5100", "description": "<PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Restauracje", "acc_number": "5110", "description": "Restauracje", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Artykuły spożywcze", "acc_number": "5120", "description": "Artykuły spożywcze", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "acc_number": "5200", "description": "<PERSON><PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Rozrywka", "acc_number": "5300", "description": "Rozrywka", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Muzyka/Film", "acc_number": "5310", "description": "Muzyka/Film", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc_number": "5320", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Podróże", "acc_number": "5330", "description": "Podróże", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Prezenty", "acc_number": "5400", "description": "Prezenty", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Artykuły spożywcze", "acc_number": "5500", "description": "Artykuły spożywcze", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON>bby", "acc_number": "5600", "description": "<PERSON>bby", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Naprawy w domu", "acc_number": "5700", "description": "Naprawy w domu", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Ubezpieczenie", "acc_number": "5800", "description": "Ubezpieczenie", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Ubezpieczenie samochodu", "acc_number": "5810", "description": "Ubezpieczenie samochodu", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Ubezpieczenie zdrowotne", "acc_number": "5820", "description": "Ubezpieczenie zdrowotne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Ubezpieczenie domu", "acc_number": "5830", "description": "Ubezpieczenie domu", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Ubezpieczenie na życie", "acc_number": "5840", "description": "Ubezpieczenie na życie", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Ubezpieczenie wynajmu", "acc_number": "5850", "description": "Ubezpieczenie wynajmu", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "O<PERSON>et<PERSON>", "acc_number": "5900", "description": "O<PERSON>et<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Odsetki od pożyczki na edukację", "acc_number": "5910", "description": "Odsetki od pożyczki na edukację", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki od kredytu hipotecznego", "acc_number": "5920", "description": "Odsetki od kredytu hipotecznego", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki inne", "acc_number": "5930", "description": "Odsetki inne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Odsetki kredytu na samochód", "acc_number": "5940", "description": "Odsetki kredytu na samochód", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Pralnia", "acc_number": "6000", "description": "Pralnia", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Wydatki medyczne", "acc_number": "6100", "description": "Wydatki medyczne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON>", "acc_number": "6200", "description": "<PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Internet", "acc_number": "6300", "description": "Internet", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Telefon", "acc_number": "6400", "description": "Telefon", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Transport publiczny", "acc_number": "6500", "description": "Transport publiczny", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Czynsz", "acc_number": "6600", "description": "Czynsz", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Prenumeraty", "acc_number": "6700", "description": "Prenumeraty", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Dostawy", "acc_number": "6800", "description": "Dostawy", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "acc_number": "6900", "description": "<PERSON><PERSON><PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Krajow<PERSON>", "acc_number": "6910", "description": "Krajow<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Medyczne", "acc_number": "6920", "description": "Medyczne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON>", "acc_number": "6930", "description": "<PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Podatek od nieruchomości", "acc_number": "6940", "description": "Podatek od nieruchomości", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>ln<PERSON>", "acc_number": "6950", "description": "<PERSON><PERSON><PERSON>ln<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Wojewódzkie/gminne", "acc_number": "6960", "description": "Wojewódzkie/gminne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Pensja (małżonka)", "acc_number": "7000", "description": "Pensja (małżonka)", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Krajow<PERSON>", "acc_number": "7010", "description": "Krajow<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Medyczne", "acc_number": "7020", "description": "Medyczne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON>", "acc_number": "7030", "description": "<PERSON><PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "<PERSON><PERSON><PERSON>ln<PERSON>", "acc_number": "7040", "description": "<PERSON><PERSON><PERSON>ln<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Wojewódzkie/gminne", "acc_number": "7050", "description": "Wojewódzkie/gminne", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}, {"name": "Usługi", "acc_number": "7100", "description": "Usługi", "type": "EXPENSE", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Prąd", "acc_number": "7110", "description": "Prąd", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Wywó<PERSON>", "acc_number": "7120", "description": "Wywó<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "Gaz", "acc_number": "7130", "description": "Gaz", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}, {"name": "W<PERSON>", "acc_number": "7140", "description": "W<PERSON>", "type": "EXPENSE", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}]}, {"name": "Kapitał własny", "acc_number": "8000", "description": "Kapitał własny", "type": "EQUITY", "currency": "PLN", "is_placeholder": true, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": [{"name": "Bilanse otwarcia", "acc_number": "8100", "description": "Bilanse otwarcia", "type": "EQUITY", "currency": "PLN", "is_placeholder": false, "created_by": 1, "lang": "pl", "is_syntetyczne": true, "group_id": 1, "level": 0, "is_branch": true, "is_active": true, "enabled": true, "balance": 0, "set_id": 10, "children": []}]}]