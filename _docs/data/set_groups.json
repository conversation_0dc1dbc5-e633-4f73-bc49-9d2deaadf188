[{"id": 672, "set_id": 20, "acc_name": "Aktywa trwałe", "acc_type": null, "acc_number": "0", "parent_id": null, "is_branch": true, "label": "Aktywa trwałe", "description": "Grupa 0 - do ewidencji zasobów z okresem wykorzystywania ponad rok.\nZapisujemy: środ<PERSON> trwałe, wartości niematerialne i prawne oraz długotrwałe aktywa finansowe", "creator_id": null, "acc_path": "Aktywa trwałe", "acc_group": "", "acc_labels_path": "Aktywa trwałe", "acc_nr_group": "0", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 695, "set_id": 20, "acc_name": "Aktywa bieżące", "acc_type": null, "acc_number": "1", "parent_id": null, "is_branch": true, "label": "Banki", "description": "Grupa 1 - odzwierciedlenie obrotów i stanów środków na rachunkach bankowych, odzwierciedlenie stanów rachunków bankowych wyrażają­cych należności z tytułu lokat, a także odzwierciedlenie obrotów i stanów środków pieniężnych związanych z przemieszczeniem pieniędzy pomiędzy rachunkami bankowymi, w tym rachunkami bankowymi lokat i odwrotnie.\nEwidencję środków pieniężnych prowadzi się według banków oraz źródła pocho­dzenia środków pieniężnych.", "creator_id": null, "acc_path": "Aktywa bieżące", "acc_group": "", "acc_labels_path": "Banki", "acc_nr_group": "1", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 709, "set_id": 20, "acc_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc_type": null, "acc_number": "2", "parent_id": null, "is_branch": true, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Należności i zobowiązania", "creator_id": null, "acc_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc_group": "", "acc_labels_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc_nr_group": "2", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 749, "set_id": 20, "acc_name": "Materiały i towary", "acc_type": "debit-plus", "acc_number": "3", "parent_id": null, "is_branch": true, "label": "Materiały i towary", "description": "", "creator_id": null, "acc_path": "Materiały i towary", "acc_group": "", "acc_labels_path": "Materiały i towary", "acc_nr_group": "3", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 750, "set_id": 20, "acc_name": "Koszty wg typów działalności", "acc_type": "debit-plus", "acc_number": "5", "parent_id": null, "is_branch": false, "label": "Koszty wg typów działalności", "description": "", "creator_id": null, "acc_path": "Koszty wg typów działalności", "acc_group": "", "acc_labels_path": "Koszty wg typów działalności", "acc_nr_group": "5", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 751, "set_id": 20, "acc_name": "Rozliczenia międzyokresowe", "acc_type": "debit-plus", "acc_number": "6", "parent_id": null, "is_branch": true, "label": "Rozliczenia międzyokresowe", "description": "Rozliczenia międzyokresowe kosztów", "creator_id": null, "acc_path": "Rozliczenia międzyokresowe", "acc_group": "", "acc_labels_path": "Rozliczenia międzyokresowe", "acc_nr_group": "6", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 756, "set_id": 20, "acc_name": "Przychody i ich koszty", "acc_type": "debit-minus", "acc_number": "7", "parent_id": null, "is_branch": true, "label": "Przychody", "description": "Przychody i koszty związane z ich osiągnięciem", "creator_id": null, "acc_path": "Przychody i ich koszty", "acc_group": "", "acc_labels_path": "Przychody", "acc_nr_group": "7", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}, {"id": 817, "set_id": 20, "acc_name": "Ka<PERSON><PERSON>ł<PERSON>, fundusze i wynik finansowy", "acc_type": "debit-minus", "acc_number": "8", "parent_id": null, "is_branch": true, "label": "<PERSON>", "description": "Ka<PERSON><PERSON>ł<PERSON>, fundusze i wynik finansowy", "creator_id": null, "acc_path": "Ka<PERSON><PERSON>ł<PERSON>, fundusze i wynik finansowy", "acc_group": "", "acc_labels_path": "<PERSON>", "acc_nr_group": "8", "acc_nr_syn": "", "acc_nr_a1": "", "acc_nr_a2": "", "acc_nr_a3": "", "is_bilansowe": true, "enabled": true}]