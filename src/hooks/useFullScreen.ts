// // useFullscreen.ts
// import { useRef, useEffect, useCallback } from "react";
// import { useSelector, useDispatch } from "react-redux";
// import { RootState } from "../store"; // adjust import to your store setup
// import {
//   ENTER_FULLSCREEN,
//   EXIT_FULLSCREEN,
//   enterFullscreenAction,
//   exitFullscreenAction,
// } from "./fullscreenActions";

// export function useFullscreen() {
//   const containerRef = useRef<HTMLDivElement>(null);

//   // Read current fullscreen state from Redux
//   const isFullscreen = useSelector(
//     (state: RootState) => state.fullscreen.isFullscreen
//   );

//   const dispatch = useDispatch();

//   // Toggle function
//   const toggleFullscreen = useCallback(async () => {
//     try {
//       if (!document.fullscreenElement) {
//         // Enter fullscreen
//         if (containerRef.current) {
//           await containerRef.current.requestFullscreen();
//           dispatch(enterFullscreenAction());
//         }
//       } else {
//         // Exit fullscreen
//         await document.exitFullscreen();
//         dispatch(exitFullscreenAction());
//       }
//     } catch (error) {
//       console.error("Error toggling fullscreen:", error);
//     }
//   }, [dispatch]);

//   // Keep Redux in sync if the user manually exits fullscreen (e.g., ESC key)
//   useEffect(() => {
//     const handleFullscreenChange = () => {
//       if (document.fullscreenElement) {
//         dispatch(enterFullscreenAction());
//       } else {
//         dispatch(exitFullscreenAction());
//       }
//     };

//     document.addEventListener("fullscreenchange", handleFullscreenChange);
//     return () => {
//       document.removeEventListener("fullscreenchange", handleFullscreenChange);
//     };
//   }, [dispatch]);

//   return {
//     containerRef,
//     isFullscreen,
//     toggleFullscreen,
//   };
// }
