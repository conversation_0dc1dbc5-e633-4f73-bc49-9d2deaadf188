// //useEmailData.tsx

// import { useQuery, useMutation, useQueries } from "@tanstack/react-query";
// import { notifications } from "@mantine/notifications";
// // import { GET_OBJECT} from "@/utils/data/queries.js";
// import { INSERT_EMAILS } from "@/utils/data/mutations.ts";
// import useTQuery from "@/hooks/useTQuery";
// import { useSelector } from "react-redux";
// import { bak_useUserData } from "@nhost/react";
// import { RootStateTypes } from "@/redux/store.ts";
// import { InsertEmailsMutationVariables } from "@/gql/graphql.ts";

// function useData(id) {
//   const user = bak_useUserData();
//   // console.log("user", user);
//   const { tanstackClient, qFn, mFn } = useTQuery();
//   const wspolnotaId = useSelector(
//     (state: RootStateTypes) => state.userProfile.wspolnota_id,
//   );

//   // // GET_ZLECENIA_ID
//   // const getZleceniaId = useQuery({
//   //   queryKey: ["zlecenia-id", id],
//   //   queryFn: async (): Promise<Admin_Zlecenia> => {
//   //     const data = (await qFn(GET_ZLECENIA_ID, { id: id })) as ZleceniaTypes;
//   //     return data?.admin_zlecenia[0];
//   //   },
//   //   enabled: !!id,
//   // });

//   // INSERT_EMAILS
//   const insertEmails = useMutation({
//     mutationFn: (variables: InsertEmailsMutationVariables) =>
//       mFn(INSERT_EMAILS, {
//         ...variables,
//       }),
//     onSuccess: () => {
//       tanstackClient.invalidateQueries(["user-emails"]);
//       // .then(() => console.log("ok, vars: "));
//       // reset();
//     },
//     onError: (error) => {
//       console.log("mutation error", error);
//       notifications.show({
//         id: "hello-there",
//         withCloseButton: true,
//         // onClose: () => console.log("unmounted"),
//         // onOpen: () => console.log("mounted"),
//         autoClose: false,
//         title: "Błąd zapisu",
//         message: ` Przekaż administratorowi: ${error}`,
//         color: "red",
//         //   icon: <IconX />,
//         //   className: 'my-notification-class',
//         //   style: { backgroundColor: 'red' },
//         //   loading: false,
//       });
//     },
//   });

//   return {
//     insertEmails,
//   };
// }

// export default useData;
