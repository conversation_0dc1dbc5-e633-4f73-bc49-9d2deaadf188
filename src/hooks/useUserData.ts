import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useAuth } from "react-oidc-context";
import { toast } from "sonner";
import { getUsersOneApiV1AuthUsersKcKcIdGetOptions } from "@/api/_client/@tanstack/react-query.gen";
import { client } from "@/api/_client/client.gen";
import { useAppDispatch, useAppSelector } from "@/utils/redux/hooks";
import type { RootState } from "@/utils/redux/store";
import { setUserProfile, setUserProfileError, setUserProfileLoading } from "@/utils/redux/userProfileSlice";
import { setUser } from "@/utils/redux/userSlice";

export const useUserData = () => {
  const auth = useAuth();
  const dispatch = useAppDispatch();
  const queryClient = useQueryClient();

  // Get user profile from Redux store
  const { data, isLoading, error } = useAppSelector((state: RootState) => state.user);

  // Fetch user profile data
  const {
    data: dataUser,
    isLoading: isLoadingUser,
    error: errorUser,
  } = useQuery({
    ...getUsersOneApiV1AuthUsersKcKcIdGetOptions({
      path: {
        kc_id: auth.user?.profile.sub!,
      },
    }),
    enabled: !!auth.user,
  });

  // Update Redux store when user profile data changes
  useEffect(() => {
    if (dataUser) {
      // Update user data in Redux
      dispatch(setUser(dataUser));
    }
  }, [dataUser, dispatch]);

  // Update loading state in Redux
  useEffect(() => {
    dispatch(setUserProfileLoading(isLoadingUser));
  }, [isLoadingUser, dispatch]);

  // Update error state in Redux and show error toast
  useEffect(() => {
    if (errorUser) {
      const error = errorUser as Error;
      dispatch(setUserProfileError(error));
      toast.error(error.message);
    }
  }, [errorUser, dispatch]);

  // Function to handle logout
  const logout = async () => {
    try {
      // Clear the user profile from the query cache
      queryClient.removeQueries({ queryKey: ["userProfile"] });
      // Clear the user profile from Redux
      dispatch(setUserProfile(null));
      // Sign out from the OIDC provider
      await auth.signoutRedirect();
    } catch (error) {
      console.error("Error during logout:", error);
      toast.error("An error occurred during logout");
    }
  };

  return {
    data: dataUser,
    isLoading: isLoadingUser,
    error: errorUser || null,
    logout,
  };
};
