// import { useQuery } from "@tanstack/react-query";
// import {
//   GET_MAILING_EMAILS,
//   GET_MAILING_EMAILS_WITH_LIMIT,
// } from "@/utils/data/queries.ts";
// import { useEffect, useState } from "react";
// import { GetMailingEmailsQuery } from "@/gql/graphql.ts";
// import useTQuery from "@/hooks/useTQuery.tsx";
// import { useSelector } from "react-redux";
// import { RootStateTypes } from "@/redux/store.ts";

// type GetMailingEmails = GetMailingEmailsQuery["admin_email_types_users"];
// interface GetMailingEmailsData {
//   admin_email_types_users: GetMailingEmails;
// }

// function useGetBCC(type_id, kwota = null) {
//   const wspolnotaId = useSelector(
//     (state: RootStateTypes) => state.userProfile.wspolnota_id,
//   );
//   const { qFn } = useTQuery();
//   const [bcc, setBcc] = useState(null);
//   // console.log("bcc", bcc);
//   const [type, setType] = useState(null);
//   // console.log("type", type);

//   // GET_MAILING_EMAILS
//   const getMailingEmails = useQuery({
//     queryKey: ["mailing-emails", wspolnotaId, type_id],
//     queryFn: async (): Promise<GetMailingEmails> => {
//       const data = (await qFn(GET_MAILING_EMAILS, {
//         org_id: wspolnotaId,
//         email_type_id: type_id,
//       })) as GetMailingEmailsData;

//       return data.admin_email_types_users;
//     },
//     enabled: !!kwota,
//   });

//   // GET_MAILING_EMAILS_WITH_LIMIT
//   const getMailingEmailsWithLimit = useQuery({
//     queryKey: ["mailing-emails-operacja", wspolnotaId, type_id, kwota],
//     queryFn: async (): Promise<GetMailingEmails> => {
//       const data = (await qFn(GET_MAILING_EMAILS_WITH_LIMIT, {
//         org_id: wspolnotaId,
//         email_type_id: type_id,
//         kwota: kwota,
//       })) as GetMailingEmailsData;

//       return data.admin_email_types_users;
//     },
//     enabled: !!kwota,
//   });

//   const {
//     data: dataMailingEmails,
//     isLoading: isLoadingMailingEmails,
//     error: errorMailingEmails,
//   } = getMailingEmails;

//   const {
//     data: dataMailingEmailsWithLimit,
//     isLoading: isLoadingMailingEmailsWithLimit,
//     error: errorMailingEmailsWithLimit,
//   } = getMailingEmailsWithLimit;

//   useEffect(() => {
//     if (dataMailingEmails && !kwota) {
//       const emailsArray = dataMailingEmails.map((item) => {
//         return item.user.email;
//       });
//       const type = dataMailingEmails[0]?.email_type.type;
//       const bcc = emailsArray.join(",");
//       setBcc(bcc);
//       setType(type);
//     } else if (dataMailingEmailsWithLimit && kwota) {
//       const emailsArray = dataMailingEmailsWithLimit.map((item) => {
//         return item.user.email;
//       });
//       const type = dataMailingEmailsWithLimit[0]?.email_type.type;
//       const bcc = emailsArray.join(",");
//       setBcc(bcc);
//       setType(type);
//     }
//   }, [dataMailingEmails, dataMailingEmailsWithLimit, kwota]);

//   // console.log(
//     "returning: ",
//     bcc,
//     type,
//     isLoadingMailingEmails,
//     errorMailingEmails,
//   );

//   return { bcc, type, isLoadingMailingEmails, errorMailingEmails };
// }

// export default useGetBCC;
