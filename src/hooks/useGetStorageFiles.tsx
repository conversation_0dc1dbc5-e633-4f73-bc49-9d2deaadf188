// import { getKeycloakAttributes } from "@/utils/auth";
import { useMutation, useQuery, useQueryClient, useSuspenseQuery } from "@tanstack/react-query";

const encoreUrl = import.meta.env.PUBLIC_ENCORE_URL;

type Props = {
  userId: number;
  orgId: number;
  fileIds: string[];
  enabled?: boolean;
};

export const useGetStorageFiles = ({ userId, orgId, fileIds, enabled = false }: Props) => {
  // console.log("hook props    userId", userId);
  // console.log("hook props   orgId", orgId);

  // const {id: userDbId} = getKeycloakAttributes();
  const queryClient = useQueryClient();

  const getStorageFiles = async () => {
    if (!userId) {
      throw new Error("User ID is not available");
    }
    const response = await fetch(`${encoreUrl}/dosiero/files/google`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        state: { org_id: orgId.toString(), user_id: userId.toString() },
        fileIds: fileIds,
      }),
    });

    return response.json();
  };

  const { data, error } = useQuery({
    queryKey: ["images-data", fileIds],
    queryFn: getStorageFiles,
    enabled: enabled && Boolean(userId) && Boolean(orgId) && Boolean(fileIds?.length),
  });

  return {
    data,
    error,
  };
};
