import { useEffect, useState } from "react";

type Quality = "good" | "poor" | "offline";

export function useConnectionQuality(): Quality {
  const [quality, setQuality] = useState<Quality>(navigator.onLine ? "good" : "offline");

  useEffect(() => {
    function computeQuality(): Quality | undefined {
      if (!navigator.onLine) return "offline";

      const conn = (navigator.connection || navigator.mozConnection || navigator.webkitConnection) as
        | NetworkInformation
        | undefined;

      if (conn) {
        const slowTypes = ["slow-2g", "2g"];
        if (
          (conn.effectiveType && slowTypes.includes(conn.effectiveType)) ||
          (conn.downlink !== undefined && conn.downlink < 0.5)
        ) {
          return "poor";
        }
      }

      // Use PerformanceNavigationTiming if available, otherwise fall back to good
      const navEntries = performance.getEntriesByType?.('navigation');
      return navEntries?.length ? undefined : "good";
    }

    async function probeLatency(): Promise<Quality> {
      try {
        const t0 = performance.now();
        // Use a small, cache-busted request to measure latency
        await fetch(`/favicon.ico?t=${Date.now()}`, { 
          cache: "no-store", 
          mode: "no-cors",
          // Add a small timeout to prevent hanging
          signal: AbortSignal.timeout?.(5000)
        }).catch(() => {}); // Ignore errors, we only care about timing
        const dt = performance.now() - t0;
        return dt > 2000 ? "poor" : "good";
      } catch {
        return "offline";
      }
    }

    async function update() {
      const q = computeQuality();
      if (q) {
        setQuality(q);
      } else {
        setQuality(await probeLatency());
      }
    }

    update();

    const conn = (navigator.connection || navigator.mozConnection || navigator.webkitConnection) as
      | NetworkInformation
      | undefined;

    window.addEventListener("online", update);
    window.addEventListener("offline", update);
    conn?.addEventListener?.("change", update);

    return () => {
      window.removeEventListener("online", update);
      window.removeEventListener("offline", update);
      conn?.removeEventListener?.("change", update);
    };
  }, []);

  return quality;
}
