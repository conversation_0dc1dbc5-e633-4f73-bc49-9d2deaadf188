import useEmailData from "./useEmailData.tsx";
import getEmailContent from "@/templates/getEmailContent.tsx";

import React from "react";

function useEmail() {
  const { insertEmails } = useEmailData();
  async function sendEmail(bcc, emailProps, emailTypeId, orgId) {
    // console.log("sendEmail", bcc, emailProps, emailTypeId, orgId);
    const { text, html, subject } = getEmailContent(emailTypeId, emailProps);
    // console.log("text html subject", text, html, subject);
    const response = await insertEmails.mutateAsync({
      org_id: orgId,
      type_id: emailTypeId,
      bcc: bcc,
      html: html,
      subject: subject,
      text: text,
    });
    if (response) {
      send(bcc, subject, text, html);
    }
  }

  function send(bcc, subject, text, html) {
    // console.log("TODO: REMOVE ME.... send", bcc, subject);
    if (bcc.length > 0) {
      fetch("http://localhost:3000/administrator-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "nhost-webhook-secret": "5BVn7I8ue3S8DX0vj2rUWuh3tWaI2r5Zi4SVfzbZ",
        },
        body: JSON.stringify({
          data: {
            email: bcc,
            subject: subject,
            html: html,
            text: text,
          },
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          // console.log("Success:", data);
          // TODO update email as "is_sent === true"
        })
        .catch((error) => {
          console.error("Error:", error);
        });
    }
  }
  return { sendEmail };
}

export default useEmail;
