import type { QueryClient } from '@tanstack/react-query';

/**
 * Route context for Tanstack Router
 * This extends the built-in context with our application-specific values
 */
export interface RouteContext {
  // Existing context values
  queryClient: QueryClient;
  org_id: number;
  profile_id: number;
  user_id: number;
  
  /**
   * Flag to track if navigation is internal (from within the app)
   * - true: Internal navigation (using router or variant change)
   * - false: Direct URL access or page reload
   * 
   * When true, we should load variant defaults
   * When false, we should preserve URL parameters
   */
  isInternal: boolean;
}

// Register our custom context type with Tanstack Router
declare module '@tanstack/react-router' {
  interface Register {
    router: unknown;
    routeContext: RouteContext;
  }
}
