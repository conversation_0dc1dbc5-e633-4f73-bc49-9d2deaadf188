

---

// src/pages/dashboard/[...page].astro
//import Layout from "../layouts/Layout.astro"

import type { Route } from "@tanstack/react-router";
import Layout from "@/layouts/main.astro";
import { ReactApp } from "../../dosiero/main.tsx";

export async function getStaticPaths() {
  try {
    // Dynamically import the route tree to avoid initialization issues
    const { routeTree } = await import("../../dosiero/routeTree.gen.ts");
    
    const mapChildren = (route: Route): any[] => {
      if (!route?.options) {
        return [];
      }
      return [route.options, ...Object.values(route?.children || []).flatMap(mapChildren)];
    };

    const asMapped = mapChildren(routeTree as unknown as Route);

    const paths = asMapped.map((route: { path?: string }) => {
      // The root path in TanStack Router is typically '/', which should map to `undefined` for As<PERSON>'s splat route.
      if (route.path === "/") {
        return { params: { page: undefined } };
      }

      // For other paths, remove any leading/trailing slashes.
      // e.g., '/app/' becomes 'app'.
      const page = route.path?.replace(/^\/|\/$/g, "");

      return {
        params: { page },
      };
    });
    console.log("paths", paths);

    return paths;
  } catch (error) {
    console.error("Error loading route tree:", error);
    // Fallback to basic paths if route tree can't be loaded
    return [
      { params: { page: undefined } }, // root path
      { params: { page: "main/jobs" } }, // jobs path
    ];
  }
}
---

<Layout title="Dosiero360 - Complete Resource Management">
  <main>
    <ReactApp client:only="react" />
     
  </main>
</Layout> 