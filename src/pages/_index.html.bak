<!doctype html>
<html lang="en">
  <head>
   
    <title>Dosiero360 - Complete Resource Management</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />
    <!--     <link rel="stylesheet" href="style.css"> -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      /* --- Global Styles & Variables --- */
      :root {
        --primary-color: #0077cc; /* Professional Blue */
        --secondary-color: #00b3b3; /* Teal */
        --accent-color: #ff8c00; /* Vibrant Orange */
        --dark-color: #333333;
        --light-color: #f4f4f4;
        --white-color: #ffffff;
        --gray-color: #777777;
        --border-radius: 8px;
        --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        --font-family: "Poppins", sans-serif;
      }

      *,
      *::before,
      *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      html {
        scroll-behavior: smooth;
        font-size: 16px; /* Base font size */
      }

      body {
        font-family: var(--font-family);
        line-height: 1.7;
        color: var(--dark-color);
        background-color: var(--white-color);
       
      }

      .container {
        max-width: 1140px;
        margin: 0 auto;
        padding: 0 20px;
      }

      h1,
      h2,
      h3,
      h4 {
        font-weight: 600;
        line-height: 1.3;
        margin-bottom: 0.75rem;
      }

      h1 {
        font-size: 2.8rem;
      }
      h2 {
        font-size: 2.2rem;
      }
      h3 {
        font-size: 1.4rem;
      }

      p {
        margin-bottom: 1rem;
        color: var(--gray-color);
      }

      a {
        text-decoration: none;
        color: var(--primary-color);
        transition: color 0.3s ease;
      }

      a:hover {
        color: var(--secondary-color);
      }

      ul {
        list-style: none;
      }

      img,
      svg {
        max-width: 100%;
        height: auto;
        display: block;
      }

      .section-padding {
        padding: 80px 0;
      }

      .section-title {
        text-align: center;
        margin-bottom: 1rem;
        color: var(--dark-color);
      }

      .section-subtitle {
        text-align: center;
        max-width: 600px;
        margin: 0 auto 4rem auto;
        font-size: 1.1rem;
        color: var(--gray-color);
      }

      .bg-light {
        background-color: var(--light-color);
      }
      .bg-dark {
        background-color: var(--dark-color);
        color: var(--light-color);
      }
      .bg-dark h2,
      .bg-dark h3,
      .bg-dark h4 {
        color: var(--white-color);
      }
      .bg-dark p {
        color: rgba(255, 255, 255, 0.8);
      }
      .bg-dark a {
        color: var(--secondary-color);
      }
      .bg-dark a:hover {
        color: var(--white-color);
      }

      /* --- Buttons --- */
      .btn {
        display: inline-block;
        padding: 12px 28px;
        border-radius: var(--border-radius);
        font-weight: 600;
        font-size: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }

      .btn-primary {
        background-color: var(--accent-color);
        color: var(--white-color);
        border-color: var(--accent-color);
      }
      .btn-primary:hover {
        background-color: #e67e00; /* Darker orange */
        border-color: #e67e00;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
      }

      .btn-secondary {
        background-color: var(--secondary-color);
        color: var(--white-color);
        border-color: var(--secondary-color);
      }
      .btn-secondary:hover {
        background-color: #009999; /* Darker teal */
        border-color: #009999;
      }

      .btn-outline {
        background-color: transparent;
        color: var(--primary-color);
        border-color: var(--primary-color);
      }
      .btn-outline:hover {
        background-color: var(--primary-color);
        color: var(--white-color);
      }

      .btn-large {
        padding: 15px 35px;
        font-size: 1.1rem;
      }

      /* --- Header & Navigation --- */
      .header {
        background-color: var(--white-color);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        position: sticky;
        top: 0;
        z-index: 1000;
        padding: 10px 0;
      }

      .nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60px;
      }

      .logo {
        font-size: 1.8rem;
        font-weight: 300;
        color: var(--dark-color);
      }
      .logo strong {
        font-weight: 700;
        color: var(--primary-color);
      }
      .logo:hover {
        color: var(--dark-color); /* Prevent color change on hover */
      }

      .nav-links {
        display: flex;
        align-items: center;
      }

      .nav-links li {
        margin-left: 25px;
      }

      .nav-links a {
        color: var(--dark-color);
        font-weight: 400;
        padding-bottom: 5px; /* Space for potential border */
        position: relative;
      }

      .nav-links a:hover,
      .nav-links a.active {
        color: var(--primary-color);
      }

      .nav-links a::after {
        /* Underline effect on hover/active */
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: width 0.3s ease;
      }

      .nav-links a:hover::after,
      .nav-links a.active::after {
        width: 100%;
      }

      .nav-links .btn {
        margin-left: 25px;
        padding: 8px 20px;
        font-size: 0.9rem;
      }

      .menu-toggle {
        display: none; /* Hidden on desktop */
        background: none;
        border: none;
        font-size: 2rem;
        cursor: pointer;
        color: var(--dark-color);
      }

      /* --- Language Switcher Styles --- */
      .language-switcher {
        display: flex; /* Keeps it aligned if more items were added later */
        align-items: center;
        margin-left: 15px; /* Adjust spacing */
        /* Optional: Remove border if it looks odd with just one button */
        /* border-left: 1px solid #eee;
        padding-left: 15px; */
      }

      #language-toggle-button {
        background: none;
        border: none;
        padding: 5px; /* Adjust padding around the flag */
        cursor: pointer;
        display: flex; /* To help center SVG */
        align-items: center;
        justify-content: center;
        border-radius: 4px; /* Optional rounded corners */
        transition:
          background-color 0.2s ease-in-out,
          transform 0.1s ease;
        line-height: 0; /* Prevent extra space below SVG */
      }

      #language-toggle-button:hover {
        background-color: rgba(0, 0, 0, 0.05); /* Subtle hover */
      }
      #language-toggle-button:active {
        transform: scale(0.95); /* Click feedback */
      }

      #language-toggle-button svg {
        display: block; /* Remove potential bottom space */
        width: 28px; /* Control flag size */
        height: auto; /* Maintain aspect ratio */
        border: 1px solid #ddd; /* Subtle border around flag */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      /* Adjust switcher position on mobile */
      @media (max-width: 767px) {
        .language-switcher {
          margin-left: auto; /* Push to the right, before hamburger */
          margin-right: 5px; /* Adjust spacing */
          padding-left: 5px;
          border-left: none;
        }
        #language-toggle-button svg {
          width: 24px; /* Slightly smaller on mobile */
        }
      }

      /* --- Hero Section --- */
      .hero {
        background: linear-gradient(
            rgba(255, 255, 255, 0.9),
            rgba(255, 255, 255, 0.9)
          ),
          url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2f7' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); /* Subtle background pattern */
        padding: 100px 0 60px 0;
        min-height: 80vh;
        display: flex;
        align-items: center;
      }

      .hero-content {
        display: flex;
        align-items: center;
        gap: 40px;
      }

      .hero-text {
        flex: 1;
      }

      .hero-text h1 {
        color: var(--dark-color);
        margin-bottom: 1.5rem;
      }

      .hero-text p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        max-width: 550px;
      }

      .hero-text .btn {
        margin-right: 15px;
        margin-bottom: 10px; /* For stacking on mobile */
      }

      .hero-image {
        flex: 1;
        max-width: 450px; /* Control SVG size */
        margin: 0 auto;
      }

      .hero-image svg {
        width: 100%;
        height: auto;
      }

      /* --- Features Section --- */
      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-top: 3rem;
      }

      .feature-card {
        background-color: var(--white-color);
        padding: 30px;
        border-radius: var(--border-radius);
        text-align: center;
        box-shadow: var(--box-shadow);
        transition:
          transform 0.3s ease,
          box-shadow 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
      }

      .feature-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 20px auto;
        /* background-color: rgba(0, 119, 204, 0.1); */ /* Light blue background */
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .feature-icon svg {
        width: 35px; /* Adjust icon size */
        height: 35px;
      }
      .feature-card h3 {
        margin-bottom: 10px;
        color: var(--primary-color);
      }

      /* --- How It Works Section --- */
      .steps-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start; /* Align cards at the top */
        gap: 30px;
        margin-top: 3rem;
        position: relative; /* For connector positioning */
      }

      .step-card {
        flex: 1;
        background-color: var(--white-color);
        padding: 30px;
        border-radius: var(--border-radius);
        text-align: center;
        box-shadow: var(--box-shadow);
        position: relative;
        z-index: 1; /* Keep cards above connector */
      }

      .step-number {
        width: 40px;
        height: 40px;
        background-color: var(--secondary-color);
        color: var(--white-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        margin: -50px auto 20px auto; /* Pulls number up */
        border: 4px solid var(--light-color); /* Creates space */
      }

      .step-card h3 {
        color: var(--primary-color);
        margin-bottom: 10px;
      }

      .step-connector {
        display: none; /* Hidden by default, shown in media query */
        flex-grow: 0.5; /* Takes up space between cards */
        height: 2px;
        background-color: var(--secondary-color);
        align-self: center; /* Center vertically */
        margin-top: 50px; /* Align with middle of cards */
        opacity: 0.5;
      }

      /* Show connector line on wider screens */
      @media (min-width: 768px) {
        .step-connector {
          display: block;
        }
        /* Hide the last connector */
        .step-card:last-child .step-connector-after {
          display: none;
        }
        /* Add pseudo-element line for desktop */
        .steps-container::before {
          content: "";
          position: absolute;
          top: 60px; /* Adjust based on card height/padding */
          left: 5%;
          right: 5%;
          height: 3px;
          background: linear-gradient(
            to right,
            transparent,
            var(--secondary-color),
            transparent
          );
          opacity: 0.3;
          z-index: 0;
        }
      }

      /* --- CTA Section --- */
      .cta {
        background: linear-gradient(
          to right,
          var(--primary-color),
          var(--secondary-color)
        );
        color: var(--white-color);
        text-align: center;
      }

      .cta-content h2 {
        color: var(--white-color);
        font-size: 2.5rem;
        margin-bottom: 1rem;
      }

      .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        color: rgba(255, 255, 255, 0.9);
      }

      .cta .btn-primary {
        background-color: var(--white-color);
        color: var(--primary-color);
        border-color: var(--white-color);
      }
      .cta .btn-primary:hover {
        background-color: var(--light-color);
        color: var(--primary-color);
        border-color: var(--light-color);
      }

      /* --- Footer --- */
      .footer {
        padding: 60px 0 30px 0;
      }

      .footer-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 40px;
        flex-wrap: wrap; /* Allow wrapping on smaller screens */
      }

      .footer-logo {
        flex-basis: 30%;
        min-width: 200px; /* Prevent excessive shrinking */
      }

      .footer-logo .logo {
        color: var(--white-color);
        margin-bottom: 1rem;
        display: block; /* Makes margin work */
      }
      .footer-logo .logo strong {
        color: var(--secondary-color);
      }
      .footer-logo p {
        font-size: 0.9rem;
      }

      .footer-links {
        display: flex;
        gap: 40px;
        flex-grow: 1; /* Takes remaining space */
        justify-content: space-around; /* Distribute link sections */
        flex-wrap: wrap; /* Allow link sections to wrap */
      }

      .footer-links h4 {
        margin-bottom: 1rem;
        color: var(--white-color);
        font-size: 1.1rem;
      }

      .footer-links ul li {
        margin-bottom: 0.5rem;
      }

      .footer-links ul li a {
        font-size: 0.95rem;
        /* color: rgba(255, 255, 255, 0.8); */ /* Inherits from .bg-dark a now */
      }
      .footer-links ul li a:hover {
        color: var(--white-color);
        text-decoration: underline;
      }

      /* --- Responsiveness --- */

      /* Tablets and smaller desktops */
      @media (max-width: 992px) {
        h1 {
          font-size: 2.5rem;
        }
        h2 {
          font-size: 2rem;
        }

        .hero-content {
          flex-direction: column;
          text-align: center;
        }
        .hero-text {
          order: 2; /* Text below image */
          margin-top: 30px;
        }
        .hero-text p {
          margin-left: auto;
          margin-right: auto;
        }
        .hero-image {
          order: 1; /* Image on top */
          max-width: 400px;
        }

        .feature-grid {
          grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        }

        .steps-container {
          flex-direction: column;
          align-items: center;
          gap: 40px;
        }
        .step-card {
          width: 80%;
          max-width: 400px;
        }
        .step-connector {
          display: none; /* Hide line between cards */
        }
        .steps-container::before {
          display: none; /* Hide background line */
        }

        .footer-content {
          flex-direction: column;
          align-items: center;
          text-align: center;
        }
        .footer-logo {
          margin-bottom: 30px;
          flex-basis: auto; /* Reset basis */
        }
        .footer-links {
          justify-content: center;
          gap: 30px;
          width: 100%; /* Ensure links take full width */
        }
      }

      /* Mobile devices */
      @media (max-width: 767px) {
        .section-padding {
          padding: 60px 0;
        }
        h1 {
          font-size: 2rem;
        }
        h2 {
          font-size: 1.8rem;
        }
        .section-subtitle {
          font-size: 1rem;
          margin-bottom: 2.5rem;
        }

        /* Mobile Navigation */
        .nav-links {
          display: none; /* Hide links by default */
          flex-direction: column;
          position: absolute;
          top: 70px; /* Below header */
          left: 0;
          width: 100%;
          background-color: var(--white-color);
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          padding: 20px 0;
          text-align: center;
        }
        .nav-links.active {
          display: flex; /* Show when active */
        }
        .nav-links li {
          margin: 10px 0;
          margin-left: 0; /* Reset margin */
        }
        .nav-links .btn {
          margin-top: 15px;
          display: block; /* Make button full width within container */
          width: fit-content; /* Size to content */
          margin-left: auto;
          margin-right: auto;
        }
        .menu-toggle {
          display: block; /* Show hamburger icon */
        }

        .hero {
          padding: 60px 0 40px 0;
          min-height: auto; /* Adjust height */
        }
        .hero-text p {
          font-size: 1.1rem;
        }
        .hero-image {
          max-width: 300px;
        }

        .feature-grid {
          grid-template-columns: 1fr; /* Stack features */
          gap: 20px;
        }

        .step-card {
          width: 90%;
        }
        .step-number {
          margin: -45px auto 15px auto;
        }

        .cta-content h2 {
          font-size: 2rem;
        }
        .cta-content p {
          font-size: 1.1rem;
        }

        .footer-links {
          flex-direction: column;
          gap: 25px;
          align-items: center;
        }
        .footer-links div {
          width: 100%;
        }
      }

      /* --- Testimonials Section --- */
      .testimonial-grid {
        display: grid;
        grid-template-columns: repeat(
          auto-fit,
          minmax(300px, 1fr)
        ); /* Responsive grid */
        gap: 30px;
        margin-top: 3rem;
      }

      .testimonial-card {
        background-color: var(--white-color);
        padding: 30px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        border-left: 5px solid var(--secondary-color); /* Accent border */
        display: flex;
        flex-direction: column; /* Stack elements vertically */
      }

      .testimonial-text {
        font-style: italic;
        color: var(--dark-color);
        margin-bottom: 1.5rem;
        flex-grow: 1; /* Allow text to take available space */
        position: relative;
        padding-left: 25px; /* Space for quote icon */
      }

      .testimonial-text::before {
        content: "“"; /* Opening quote */
        position: absolute;
        left: 0;
        top: -10px;
        font-size: 3rem;
        color: var(--secondary-color);
        opacity: 0.3;
        font-family: Georgia, serif; /* Serif for quotes */
        line-height: 1;
      }

      .testimonial-author {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.25rem; /* Smaller margin */
        margin-top: auto; /* Pushes author to bottom if card heights vary */
      }

      .testimonial-source {
        font-size: 0.9rem;
        color: var(--gray-color);
        margin-bottom: 0; /* Remove default p margin */
      }

      /* Adjust testimonial responsiveness if needed */
      @media (max-width: 767px) {
        .testimonial-grid {
          grid-template-columns: 1fr; /* Stack testimonials on mobile */
          gap: 20px;
        }
        .testimonial-card {
          padding: 25px;
        }
        .testimonial-text {
          margin-bottom: 1rem;
        }
      }
    </style>
  </head>
  <body >
    <header class="header">
      <nav class="container nav">
        <a href="#" class="logo">Dosiero<strong>360</strong></a>

        <ul class="nav-links">
          <li>
            <a href="#features" data-translate="navFeatures">Features</a>
          </li>
          <li>
            <a href="#how-it-works" data-translate="navHowItWorks"
              >How It Works</a
            >
          </li>
          <li>
            <a href="#testimonials" data-translate="navTestimonials"
              >Testimonials</a
            >
          </li>
          <li>
            <a href="#pricing" data-translate="navPricing">Pricing</a>
          </li>
          <li>
            <div class="language-switcher">
              <button id="language-toggle-button" aria-label="Switch language">
                <!-- SVG will be loaded here by JavaScript -->
              </button>
            </div>
          </li>
          <li>
            <a href="/app" class="btn btn-secondary" data-translate="navLogin"
              >Login</a
            >
          </li>
        </ul>
        <!-- Add aria-label translation key -->
        <!-- Updated Language Switcher -->

        <!-- End Language Switcher -->
        <button
          class="menu-toggle"
          aria-label="Toggle menu"
          data-translate-aria="menuToggleLabel"
        >
          ☰
        </button>
      </nav>
    </header>

    <main>
      <!-- Hero Section -->
      <section class="hero">
        <div class="container hero-content">
          <div class="hero-text">
            <h1 data-translate="heroTitle">
              Your Complete Resource Management Hub
            </h1>
            <p data-translate="heroSubtitle">
              Unify proposals, accounting, assets, and jobs in one seamless,
              360° platform. Gain clarity and control over every aspect of your
              organization.
            </p>
            <a
              href="#signup"
              class="btn btn-primary"
              data-translate="heroBtnStart"
              >Get Started Free</a
            >
            <a href="#demo" class="btn btn-outline" data-translate="heroBtnDemo"
              >Request a Demo</a
            >
          </div>
          <div class="hero-image">
            <!-- Placeholder SVG: Abstract 360 concept -->
            <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop
                    offset="0%"
                    style="stop-color: #0077cc; stop-opacity: 1"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: #00b3b3; stop-opacity: 1"
                  />
                </linearGradient>
              </defs>
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="url(#grad1)"
                stroke-width="5"
                fill="none"
                stroke-linecap="round"
                stroke-dasharray="200, 51.2"
                transform="rotate(-90 50 50)"
              >
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  from="-90 50 50"
                  to="270 50 50"
                  dur="10s"
                  repeatCount="indefinite"
                />
              </circle>
              <path
                d="M 50,30 A 20 20 0 1 1 50,70 A 20 20 0 1 1 50,30 Z"
                fill="rgba(0, 179, 179, 0.1)"
              />
              <circle cx="50" cy="50" r="10" fill="url(#grad1)" />
            </svg>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section id="features" class="features section-padding">
        <div class="container">
          <h2 class="section-title" data-translate="featuresTitle">
            Everything You Need, Integrated
          </h2>
          <p class="section-subtitle" data-translate="featuresSubtitle">
            Dosiero360 brings together essential management tools.
          </p>
          <div class="feature-grid">
            <!-- Feature 1 -->
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17 4H7C5.89543 4 5 4.89543 5 6V18C5 19.1046 5.89543 20 7 20H17C18.1046 20 19 19.1046 19 18V6C19 4.89543 18.1046 4 17 4Z"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9 10L11 12L15 8"
                    stroke="#00b3b3"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9 16H15"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <h3 data-translate="feature1Title">Proposals & Voting</h3>
              <p data-translate="feature1Desc">
                Streamline decision-making with transparent proposals and secure
                voting modules.
              </p>
            </div>
            <!-- Feature 2 -->
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 20H6C4.89543 20 4 19.1046 4 18V6C4 4.89543 4.89543 4 6 4H18C19.1046 4 20 4.89543 20 6V18C20 19.1046 19.1046 20 18 20Z"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8 12H16"
                    stroke="#00b3b3"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12 8V16"
                    stroke="#00b3b3"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8 8H8.01"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M16 8H16.01"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8 16H8.01"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M16 16H16.01"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <h3 data-translate="feature2Title">Double-Entry Accounting</h3>
              <p data-translate="feature2Desc">
                Maintain accurate financial records with a robust, integrated
                accounting system.
              </p>
            </div>
            <!-- Feature 3 -->
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 20V14M12 14H8M12 14H16M8 14V10M8 10H5M8 10H11M16 14V10M16 10H13M16 10H19"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <rect
                    x="4"
                    y="3"
                    width="16"
                    height="4"
                    rx="1"
                    stroke="#00b3b3"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <h3 data-translate="feature3Title">Asset & Object Management</h3>
              <p data-translate="feature3Desc">
                Organize resources, documents, and photos in a flexible tree
                structure for easy access.
              </p>
            </div>
            <!-- Feature 4 -->
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14 6H6C4.89543 6 4 6.89543 4 8V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V12"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M19.9998 4L17.4998 6.5L14.9998 4"
                    stroke="#00b3b3"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M17.5 6.5V10.5"
                    stroke="#00b3b3"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9 13H15"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9 17H12"
                    stroke="#0077cc"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <h3 data-translate="feature4Title">Job Management & Offers</h3>
              <p data-translate="feature4Desc">
                Track jobs from proposal to completion, manage contractor
                offers, and link financial data.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- How It Works Section -->
      <section id="how-it-works" class="how-it-works section-padding bg-light">
        <div class="container ">
          <h2 class="section-title" data-translate="howTitle">
            Get Started in Minutes
          </h2>
          <div class="steps-container">
            <div class="step-card">
              <div class="step-number">1</div>
              <h3 data-translate="step1Title">Sign Up</h3>
              <p data-translate="step1Desc">
                Create your Dosiero360 account quickly and easily.
              </p>
            </div>
            <!-- ... step connectors ... -->
            <div class="step-card">
              <div class="step-number">2</div>
              <h3 data-translate="step2Title">Configure</h3>
              <p data-translate="step2Desc">
                Set up your modules: accounting, assets, users & permissions.
              </p>
            </div>
            <!-- ... step connectors ... -->
            <div class="step-card">
              <div class="step-number">3</div>
              <h3 data-translate="step3Title">Manage</h3>
              <p data-translate="step3Desc">
                Start creating proposals, tracking assets, managing finances,
                and assigning jobs.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Testimonials Section -->
      <section id="testimonials" class="testimonials section-padding bg-light">
        <div class="container">
          <h2 class="section-title" data-translate="testimonialsTitle">
            What Others Say About Us
          </h2>
          <p class="section-subtitle" data-translate="testimonialsSubtitle">
            Don't just take our word for it. Hear from organizations benefiting
            from Dosiero360.
          </p>
          <div class="testimonial-grid">
            <!-- Testimonial 1 -->
            <div class="testimonial-card">
              <blockquote
                class="testimonial-text"
                data-translate="testimonial1Quote"
              >
                "Dosiero360 transformed how we manage projects. Having
                proposals, financials, and assets in one place saved us
                countless hours and improved our decision-making."
              </blockquote>
              <p class="testimonial-author" data-translate="testimonial1Author">
                Jane Doe
              </p>
              <p class="testimonial-source" data-translate="testimonial1Source">
                Project Manager, Innovate Solutions
              </p>
            </div>
            <!-- Testimonial 2 -->
            <div class="testimonial-card">
              <blockquote
                class="testimonial-text"
                data-translate="testimonial2Quote"
              >
                "The integrated accounting system is incredibly robust yet easy
                to use. Tracking job costs against budgets is now seamless.
                Highly recommended!"
              </blockquote>
              <p class="testimonial-author" data-translate="testimonial2Author">
                John Smith
              </p>
              <p class="testimonial-source" data-translate="testimonial2Source">
                CFO, BuildRight Corp
              </p>
            </div>
            <!-- Testimonial 3 -->
            <div class="testimonial-card">
              <blockquote
                class="testimonial-text"
                data-translate="testimonial3Quote"
              >
                "Finally, a system that understands the need for clear asset
                organization alongside financial tracking. The flexibility of
                the object management is fantastic."
              </blockquote>
              <p class="testimonial-author" data-translate="testimonial3Author">
                Alice Green
              </p>
              <p class="testimonial-source" data-translate="testimonial3Source">
                Operations Director, Asset Mgmt Co.
              </p>
            </div>
          </div>
        </div>
      </section>
      <!-- END Testimonials Section -->

      <!-- CTA Section -->
      <section id="signup" class="cta section-padding">
        <div class="container cta-content">
          <h2 data-translate="ctaTitle">Ready to Take Control?</h2>
          <p data-translate="ctaSubtitle">
            Experience the power of integrated resource management with
            Dosiero360.
          </p>
          <a
            href="#"
            class="btn btn-primary btn-large"
            data-translate="ctaButton"
            >Start Your Free Trial Now</a
          >
        </div>
      </section>
    </main>

    <footer class="footer bg-dark">
      <div class="container footer-content">
        <div class="footer-logo">
          <a href="#" class="logo">Dosiero<strong>360</strong></a>
          <p data-translate="footerRights">
            © 2023 Dosiero360. All rights reserved.
          </p>
        </div>
        <div class="footer-links">
          <div>
            <h4 data-translate="footerProduct">Product</h4>
            <ul>
              <li>
                <a href="#features" data-translate="footerFeatures">Features</a>
              </li>
              <li>
                <a href="#pricing" data-translate="footerPricing">Pricing</a>
              </li>
              <li>
                <a href="#demo" data-translate="footerDemo">Request Demo</a>
              </li>
            </ul>
          </div>
          <div>
            <h4 data-translate="footerCompany">Company</h4>
            <ul>
              <li>
                <a href="#" data-translate="footerAbout">About Us</a>
              </li>
              <li>
                <a href="#" data-translate="footerContact">Contact</a>
              </li>
              <li>
                <a href="#" data-translate="footerCareers">Careers</a>
              </li>
            </ul>
          </div>
          <div>
            <h4 data-translate="footerLegal">Legal</h4>
            <ul>
              <li>
                <a href="#" data-translate="footerPrivacy">Privacy Policy</a>
              </li>
              <li>
                <a href="#" data-translate="footerTerms">Terms of Service</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
    
    <script>
      // --- Hamburger Menu Logic ---
      const menuToggle = document.querySelector(".menu-toggle");
      const navLinks = document.querySelector(".nav-links");

      menuToggle.addEventListener("click", () => {
        navLinks.classList.toggle("active");
        const isExpanded = navLinks.classList.contains("active");
        menuToggle.setAttribute("aria-expanded", isExpanded);
        // Update icon based on state (keeps the text content for translation)
        // We'll handle the text content update in the language switcher logic
      });

      navLinks.querySelectorAll("a").forEach((link) => {
        link.addEventListener("click", () => {
          if (navLinks.classList.contains("active")) {
            navLinks.classList.remove("active");
            menuToggle.setAttribute("aria-expanded", "false");
            // Update icon text content in language switcher logic
          }
        });
      });

      // Store SVG flags
      const flagSVG = {
        pl: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 10"><rect width="16" height="10" fill="#fff"/><rect width="16" height="5" fill="#dc143c" y="5"/></svg>`,
        en: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30"><clipPath id="t"><path d="M30,15 h30 v15 z v15 h-30 z h-30 v-15 z v-15 h30 z"/></clipPath><path d="M0,0 v30 h60 v-30 z" fill="#012169"/><path d="M0,0 L60,30 M60,0 L0,30" stroke="#fff" stroke-width="6"/><path d="M0,0 L60,30 M60,0 L0,30" clip-path="url(#t)" stroke="#C8102E" stroke-width="4"/><path d="M30,0 v30 M0,15 h60" stroke="#fff" stroke-width="10"/><path d="M30,0 v30 M0,15 h60" stroke="#C8102E" stroke-width="6"/></svg>`,
      };

      // --- Language Switcher Logic ---
      const translations = {
        en: {
          pageTitle: "Dosiero360 - Complete Resource Management",
          navFeatures: "Features",
          navHowItWorks: "How It Works",
          navTestimonials: "Testimonials",
          navPricing: "Pricing",
          navLogin: "Login",
          menuToggleLabel: "Toggle menu",
          menuToggleIconOpen: "☰",
          menuToggleIconClose: "✕",
          heroTitle: "Your Complete Resource Management Hub",
          heroSubtitle:
            "Unify proposals, accounting, assets, and jobs in one seamless, 360° platform. Gain clarity and control over every aspect of your organization.",
          heroBtnStart: "Get Started Free",
          heroBtnDemo: "Request a Demo",
          featuresTitle: "Everything You Need, Integrated",
          featuresSubtitle:
            "Dosiero360 brings together essential management tools.",
          feature1Title: "Proposals & Voting",
          feature1Desc:
            "Streamline decision-making with transparent proposals and secure voting modules.",
          feature2Title: "Double-Entry Accounting",
          feature2Desc:
            "Maintain accurate financial records with a robust, integrated accounting system.",
          feature3Title: "Asset & Object Management",
          feature3Desc:
            "Organize resources, documents, and photos in a flexible tree structure for easy access.",
          feature4Title: "Job Management & Offers",
          feature4Desc:
            "Track jobs from proposal to completion, manage contractor offers, and link financial data.",
          howTitle: "Get Started in Minutes",
          step1Title: "Sign Up",
          step1Desc: "Create your Dosiero360 account quickly and easily.",
          step2Title: "Configure",
          step2Desc:
            "Set up your modules: accounting, assets, users & permissions.",
          step3Title: "Manage",
          step3Desc:
            "Start creating proposals, tracking assets, managing finances, and assigning jobs.",
          testimonialsTitle: "What Others Say About Us",
          testimonialsSubtitle:
            "Don't just take our word for it. Hear from organizations benefiting from Dosiero360.",
          testimonial1Quote:
            '"Dosiero360 transformed how we manage projects. Having proposals, financials, and assets in one place saved us countless hours and improved our decision-making."',
          testimonial1Author: "Jane Doe",
          testimonial1Source: "Project Manager, Innovate Solutions",
          testimonial2Quote:
            '"The integrated accounting system is incredibly robust yet easy to use. Tracking job costs against budgets is now seamless. Highly recommended!"',
          testimonial2Author: "John Smith",
          testimonial2Source: "CFO, BuildRight Corp",
          testimonial3Quote:
            '"Finally, a system that understands the need for clear asset organization alongside financial tracking. The flexibility of the object management is fantastic."',
          testimonial3Author: "Alice Green",
          testimonial3Source: "Operations Director, Asset Mgmt Co.",
          ctaTitle: "Ready to Take Control?",
          ctaSubtitle:
            "Experience the power of integrated resource management with Dosiero360.",
          ctaButton: "Start Your Free Trial Now",
          footerRights: "© 2023 Dosiero360. All rights reserved.",
          footerProduct: "Product",
          footerFeatures: "Features",
          footerPricing: "Pricing",
          footerDemo: "Request Demo",
          footerCompany: "Company",
          footerAbout: "About Us",
          footerContact: "Contact",
          footerCareers: "Careers",
          footerLegal: "Legal",
          footerPrivacy: "Privacy Policy",
          footerTerms: "Terms of Service",
          switchLangLabel: "Switch to Polish",
        },
        pl: {
          // --- POLISH TRANSLATIONS (Machine Translated - REVIEW RECOMMENDED) ---
          pageTitle: "Dosiero360 - Kompleksowe Zarządzanie Zasobami",
          navFeatures: "Funkcje",
          navHowItWorks: "Jak to działa",
          navTestimonials: "Opinie",
          navPricing: "Cennik",
          navLogin: "Zaloguj się",
          menuToggleLabel: "Przełącz menu",
          menuToggleIconOpen: "☰",
          menuToggleIconClose: "✕",
          heroTitle: "Twoje Kompletne Centrum Zarządzania Zasobami",
          heroSubtitle:
            "Połącz wnioski, księgowość, aktywa i zadania w jednej, płynnej platformie 360°. Zyskaj przejrzystość i kontrolę nad każdym aspektem Twojej organizacji.",
          heroBtnStart: "Zacznij za Darmo",
          heroBtnDemo: "Poproś o Demo",
          featuresTitle: "Wszystko, Czego Potrzebujesz, Zintegrowane",
          featuresSubtitle:
            "Dosiero360 łączy niezbędne narzędzia do zarządzania.",
          feature1Title: "Wnioski i Głosowania",
          feature1Desc:
            "Usprawnij podejmowanie decyzji dzięki przejrzystym wnioskom i bezpiecznym modułom głosowania.",
          feature2Title: "Księgowość Podwójna",
          feature2Desc:
            "Utrzymuj dokładne zapisy finansowe dzięki solidnemu, zintegrowanemu systemowi księgowemu.",
          feature3Title: "Zarządzanie Aktywami i Obiektami",
          feature3Desc:
            "Organizuj zasoby, dokumenty i zdjęcia w elastycznej strukturze drzewa dla łatwego dostępu.",
          feature4Title: "Zarządzanie Zadaniami i Ofertami",
          feature4Desc:
            "Śledź zadania od wniosku do ukończenia, zarządzaj ofertami wykonawców i łącz dane finansowe.",
          howTitle: "Zacznij w Kilka Minut",
          step1Title: "Zarejestruj się",
          step1Desc: "Stwórz swoje konto Dosiero360 szybko i łatwo.",
          step2Title: "Skonfiguruj",
          step2Desc:
            "Skonfiguruj swoje moduły: księgowość, aktywa, użytkowników i uprawnienia.",
          step3Title: "Zarządzaj",
          step3Desc:
            "Zacznij tworzyć wnioski, śledzić aktywa, zarządzać finansami i przydzielać zadania.",
          testimonialsTitle: "Co Mówią o Nas Inni",
          testimonialsSubtitle:
            "Nie wierz nam na słowo. Posłuchaj organizacji korzystających z Dosiero360.",
          testimonial1Quote:
            '"Dosiero360 zmieniło sposób, w jaki zarządzamy projektami. Posiadanie wniosków, finansów i aktywów w jednym miejscu zaoszczędziło nam niezliczone godziny i poprawiło podejmowanie decyzji."',
          testimonial1Author: "Anna Nowak", // Example Polish Name
          testimonial1Source: "Kierownik Projektu, Innovate Solutions",
          testimonial2Quote:
            '"Zintegrowany system księgowy jest niezwykle solidny, a jednocześnie łatwy w użyciu. Śledzenie kosztów zadań w odniesieniu do budżetów jest teraz płynne. Gorąco polecam!"',
          testimonial2Author: "Jan Kowalski", // Example Polish Name
          testimonial2Source: "Dyrektor Finansowy, BuildRight Corp",
          testimonial3Quote:
            '"Wreszcie system, który rozumie potrzebę przejrzystej organizacji aktywów wraz ze śledzeniem finansowym. Elastyczność zarządzania obiektami jest fantastyczna."',
          testimonial3Author: "Alicja Zielińska", // Example Polish Name
          testimonial3Source: "Dyrektor Operacyjny, Asset Mgmt Co.",
          ctaTitle: "Gotowy Przejąć Kontrolę?",
          ctaSubtitle:
            "Doświadcz mocy zintegrowanego zarządzania zasobami z Dosiero360.",
          ctaButton: "Rozpocznij Darmowy Okres Próbny Teraz",
          footerRights: "© 2023 Dosiero360. Wszelkie prawa zastrzeżone.",
          footerProduct: "Produkt",
          footerFeatures: "Funkcje",
          footerPricing: "Cennik",
          footerDemo: "Poproś o Demo",
          footerCompany: "Firma",
          footerAbout: "O nas",
          footerContact: "Kontakt",
          footerCareers: "Kariera",
          footerLegal: "Prawne",
          footerPrivacy: "Polityka Prywatności",
          footerTerms: "Warunki Świadczenia Usług",
          switchLangLabel: "Przełącz na angielski",
        },
      };

      // Select the single button
      const langToggleButton = document.getElementById(
        "language-toggle-button",
      );
      const translatableElements =
        document.querySelectorAll("[data-translate]");
      const translatableAttributes = document.querySelectorAll(
        "[data-translate-aria]",
      );
      const htmlTag = document.documentElement;

      const setLanguage = (lang) => {
        if (!translations[lang] || !flagSVG[lang]) {
          console.error(`Language ${lang} configuration not found.`);
          return;
        }

        // 1. Determine the language to switch TO (the other language)
        const targetLang = lang === "en" ? "pl" : "en";

        // 2. Update the button content and data attribute
        if (langToggleButton && flagSVG[targetLang]) {
          langToggleButton.innerHTML = flagSVG[targetLang]; // Show flag of the *other* language
          langToggleButton.setAttribute("data-lang-target", targetLang); // Store the lang it will switch TO
          // Update Aria Label for the button itself
          if (translations[lang].switchLangLabel) {
            langToggleButton.setAttribute(
              "aria-label",
              translations[lang].switchLangLabel,
            );
          }
        } else {
          console.error("Language toggle button or target flag SVG not found");
        }

        // 3. Update page text content
        translatableElements.forEach((el) => {
          const key = el.getAttribute("data-translate");
          if (translations[lang][key] !== undefined) {
            // Check if key exists
            if (el.tagName === "TITLE") {
              document.title = translations[lang][key];
            } else {
              el.textContent = translations[lang][key];
            }
          } else {
            console.warn(
              `Translation key "${key}" not found for language "${lang}".`,
            );
            // Fallback to English if not found in current language (optional)
            if (lang !== "en" && translations["en"][key] !== undefined) {
              el.textContent = translations["en"][key];
            }
          }
        });

        // 4. Update specified attributes (like aria-label for menu)
        translatableAttributes.forEach((el) => {
          const key = el.getAttribute("data-translate-aria");
          if (translations[lang][key] !== undefined) {
            el.setAttribute("aria-label", translations[lang][key]);
          } else {
            console.warn(
              `Attribute translation key "${key}" not found for language "${lang}".`,
            );
          }
        });

        // 5. Update hamburger icon text content
        const menuToggleIconKey = navLinks.classList.contains("active")
          ? "menuToggleIconClose"
          : "menuToggleIconOpen";
        if (menuToggle && translations[lang][menuToggleIconKey]) {
          menuToggle.textContent = translations[lang][menuToggleIconKey];
        }

        // 6. Update html lang attribute
        htmlTag.setAttribute("lang", lang);

        // 7. Persist selection
        localStorage.setItem("language", lang);
      };

      // Event listener for the single button
      if (langToggleButton) {
        langToggleButton.addEventListener("click", () => {
          const targetLang = langToggleButton.getAttribute("data-lang-target");
          if (targetLang) {
            setLanguage(targetLang);
          } else {
            console.error("Could not determine target language from button.");
          }
        });
      } else {
        console.error("Language toggle button not found in the DOM.");
      }

      // Set initial language on page load
      const preferredLang = localStorage.getItem("language");
      const initialLang =
        preferredLang && translations[preferredLang] ? preferredLang : "en"; // Default to 'en' if stored lang is invalid
      setLanguage(initialLang);
    </script>

    <!--     <script src="script.js"></script> -->
  </body>
</html>
