import { useQuery } from "@tanstack/react-query";
import type { ServerOptions } from "@types";
import { toast } from "sonner";
import { getJobsAllApiV1CoreJobsGetAllPostOptions } from "@/api/_client/@tanstack/react-query.gen";

export function useJobsData(params: ServerOptions) {
  const {
    data: dataJobs,
    error: errorJobs,
    isLoading: isLoadingJobs,
  } = useQuery(
    {
      ...getJobsAllApiV1CoreJobsGetAllPostOptions({
        body: params,
      }),
      enabled: !!params,
    }
  );

  if (errorJobs) {
    toast.error(errorJobs.message);
    console.error("errorJobs", errorJobs);
  }

  return {
    dataJobs,
    errorJobs,
    isLoadingJobs,
  };
}
