import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { getJobsOneApiV1CoreJobsItemIdGetOptions } from "@/api/_client/@tanstack/react-query.gen";

export function useSingleJobData({ itemId }: { itemId: number | null }) {
  // GET JOB BY ID
  const {
    data: dataJobById,
    error: errorJobById,
    isLoading: isLoadingJobById,
  } = useQuery({
    ...getJobsOneApiV1CoreJobsItemIdGetOptions({
      path: {
        item_id: itemId!,
      },
    }),
    enabled: !!itemId,
  });

  if (errorJobById) {
    toast.error(errorJobById.message);
    console.error("errorJobById", errorJobById);
  }

  return {
    dataJobById,
    errorJobById,
    isLoadingJobById,
  };
}
