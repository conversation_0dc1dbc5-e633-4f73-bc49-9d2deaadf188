import { z } from 'zod';

/**
 * Custom schema for job updates to ensure type compatibility with forms
 */
export const zCustomJobUpdateTypes = z.object({
  id: z.number().int(),
  updated_by: z.number().int().optional().default(1),
  org_id: z.union([z.number().int(), z.null()]).optional(),
  created_by: z.union([z.number().int(), z.null()]).optional(),
  status: z.union([
    z.literal('DRAFT'),
    z.literal('CONFIRMED'),
    z.literal('IN_PROGRESS'),
    z.literal('COMPLETED'),
    z.literal('CANCELLED'),
    z.null()
  ]).optional(),
  type: z.union([
    z.literal('CLIENT_SERVICES'),
    z.literal('CLIENT_GOODS'),
    z.literal('VENDOR'),
    z.literal('CONTRACTOR'),
    z.literal('MEMBER'),
    z.literal('MEDIA'),
    z.literal('EMPLOYEE'),
    z.literal('DIRECTOR'),
    z.literal('ADMIN'),
    z.null()
  ]).optional(),
  lang: z.union([z.string(), z.null()]).optional(),
  name: z.union([z.string(), z.null()]).optional(),
  tag: z.union([z.string(), z.null()]).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  start_date: z.union([z.string().datetime(), z.null()]).optional(),
  end_date: z.union([z.string().datetime(), z.null()]).optional(),
  is_recurring: z.union([z.boolean(), z.null()]).optional(),
  is_public: z.union([z.boolean(), z.null()]).optional(),
  budget: z.union([z.number(), z.null()]).optional(),
  interval_type: z.union([z.string(), z.null()]).optional(),
  interval_day: z.union([z.number().int(), z.null()]).optional(),
  interval_value: z.union([z.number().int(), z.null()]).optional(),
  json_metadata: z.union([z.object({}), z.null()]).optional()
});
