import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { createJobsApiV1CoreJobsPostMutation } from "@/api/_client/@tanstack/react-query.gen";

export function useCreateJobMutation() {
  // const queryClient = useQueryClient();
  const { t } = useTranslation();

  // const createJobMutation = useMutation({
  //   ...createJobsApiV1CoreJobsPostMutation(),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({
  //       queryKey: ["readJobsAllV1CoreJobsGet"],
  //     });
  //     toast.success(t("common.success.label"));

  //     queryClient.invalidateQueries();
  //   },
  //   onError: (error) => {
  //     toast.error(`${t("common.failed.label")} ${error}`);
  //   },
  // });

  const createJobMutation = useMutation({
    ...createJobsApiV1CoreJobsPostMutation(),
    meta: {
      invalidatesQuery: ["readJobsAllV1CoreJobsGet"],
      successMessage: t("common.success.label"),
      errorMessage: t("common.failed.label"),
    },
  });

  return createJobMutation;
}
