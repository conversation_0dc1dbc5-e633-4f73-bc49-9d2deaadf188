import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { updateJobsApiV1CoreJobsPutMutation } from "@/api/_client/@tanstack/react-query.gen";

export function useUpdateJobMutation() {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  // const updateJobMutation = useMutation({
  //   ...updateJobsApiV1CoreJobsPutMutation(),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({
  //       queryKey: ["getJobsAllApiV1CoreJobsGetAllPost"],
  //     });
  //     toast.success(t("common.success.label"));

  //     queryClient.invalidateQueries();
  //   },
  //   onError: (error) => {
  //     toast.error(`${t("common.failed.label")} ${error}`);
  //   },
  // });


  const updateJobMutation = useMutation({
    ...updateJobsApiV1CoreJobsPutMutation(),
    onSuccess: () => {
      // console.log("updateJobMutation onSuccess", queryClient)
      // queryClient.invalidateQueries({
      //   predicate: (query: any) =>
      //     ['getJobsAllApiV1CoreJobsGetAllPost'].includes(query.queryKey[0]),
      // })
      // queryClient.invalidateQueries({
      //   predicate: (query) => query.queryKey[0] === 'getJobsAllApiV1CoreJobsGetAllPost'
      // });

      // queryClient.invalidateQueries({
      //   queryKey: ["getJobsAllApiV1CoreJobsGetAllPost"],
      // });
      queryClient.invalidateQueries();
    },
    meta: {
      invalidatesQuery: ["getJobsAllApiV1CoreJobsGetAllPost"],
      successMessage: t("common.success.label"),
      errorMessage: t("common.failed.label"),
    },
  });

  return updateJobMutation;
}
