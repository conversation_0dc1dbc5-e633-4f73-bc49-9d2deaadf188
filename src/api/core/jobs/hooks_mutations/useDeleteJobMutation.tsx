import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { deleteJobsApiV1CoreJobsItemIdDeleteMutation } from "@/api/_client/@tanstack/react-query.gen";

export function useDeleteJobMutation() {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  // const deleteJobMutation = useMutation({
  //   ...deleteJobsApiV1CoreJobsItemIdDeleteMutation(),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({
  //       queryKey: ["readJobsAllV1CoreJobsGet"],
  //     });
  //     toast.success(t("common.success.label"));

  //     queryClient.invalidateQueries();
  //   },
  //   onError: (error) => {
  //     toast.error(`${t("common.failed.label")} ${error}`);
  //   },
  // });

  const deleteJobMutation = useMutation({
    ...deleteJobsApiV1CoreJobsItemIdDeleteMutation(),
    meta: {
      invalidatesQuery: ["readJobsAllV1CoreJobsGet"],
      successMessage: t("common.success.label"),
      errorMessage: t("common.failed.label"),
    },
  });

  return deleteJobMutation;
}
