// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { CreateUsersApiV1AuthUsersPostData, CreateUsersApiV1AuthUsersPostResponses, CreateUsersApiV1AuthUsersPostErrors, UpdateUsersApiV1AuthUsersPutData, UpdateUsersApiV1AuthUsersPutResponses, UpdateUsersApiV1AuthUsersPutErrors, GetUsersOneApiV1AuthUsersKcKcIdGetData, GetUsersOneApiV1AuthUsersKcKcIdGetResponses, GetUsersOneApiV1AuthUsersKcKcIdGetErrors, DeleteUsersApiV1AuthUsersItemIdDeleteData, DeleteUsersApiV1AuthUsersItemIdDeleteResponses, DeleteUsersApiV1AuthUsersItemIdDeleteErrors, GetUsersOneApiV1AuthUsersItemIdGetData, GetUsersOneApiV1AuthUsersItemIdGetResponses, GetUsersOneApiV1AuthUsersItemIdGetErrors, GetUsersAllApiV1AuthUsersGetAllPostData, GetUsersAllApiV1AuthUsersGetAllPostResponses, GetUsersAllApiV1AuthUsersGetAllPostErrors, RegisterApiV1AuthUsersRegisterPostData, RegisterApiV1AuthUsersRegisterPostResponses, RegisterApiV1AuthUsersRegisterPostErrors, LinkContrahentApiV1AuthUsersLinkContrahentPostData, LinkContrahentApiV1AuthUsersLinkContrahentPostResponses, LinkContrahentApiV1AuthUsersLinkContrahentPostErrors, CreateJobsApiV1CoreJobsPostData, CreateJobsApiV1CoreJobsPostResponses, CreateJobsApiV1CoreJobsPostErrors, UpdateJobsApiV1CoreJobsPutData, UpdateJobsApiV1CoreJobsPutResponses, UpdateJobsApiV1CoreJobsPutErrors, DeleteJobsApiV1CoreJobsItemIdDeleteData, DeleteJobsApiV1CoreJobsItemIdDeleteResponses, DeleteJobsApiV1CoreJobsItemIdDeleteErrors, GetJobsOneApiV1CoreJobsItemIdGetData, GetJobsOneApiV1CoreJobsItemIdGetResponses, GetJobsOneApiV1CoreJobsItemIdGetErrors, GetJobsAllApiV1CoreJobsGetAllPostData, GetJobsAllApiV1CoreJobsGetAllPostResponses, GetJobsAllApiV1CoreJobsGetAllPostErrors, GetJobTasksAllApiV1CoreJobTasksGetData, GetJobTasksAllApiV1CoreJobTasksGetResponses, GetJobTasksAllApiV1CoreJobTasksGetErrors, CreateJobTasksApiV1CoreJobTasksPostData, CreateJobTasksApiV1CoreJobTasksPostResponses, CreateJobTasksApiV1CoreJobTasksPostErrors, UpdateJobTasksApiV1CoreJobTasksPutData, UpdateJobTasksApiV1CoreJobTasksPutResponses, UpdateJobTasksApiV1CoreJobTasksPutErrors, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponses, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteErrors, GetJobTasksOneApiV1CoreJobTasksItemIdGetData, GetJobTasksOneApiV1CoreJobTasksItemIdGetResponses, GetJobTasksOneApiV1CoreJobTasksItemIdGetErrors, CreateFilesApiV1CoreFilesPostData, CreateFilesApiV1CoreFilesPostResponses, CreateFilesApiV1CoreFilesPostErrors, UpdateFilesApiV1CoreFilesPutData, UpdateFilesApiV1CoreFilesPutResponses, UpdateFilesApiV1CoreFilesPutErrors, DeleteFilesApiV1CoreFilesItemIdDeleteData, DeleteFilesApiV1CoreFilesItemIdDeleteResponses, DeleteFilesApiV1CoreFilesItemIdDeleteErrors, GetFilesOneApiV1CoreFilesItemIdGetData, GetFilesOneApiV1CoreFilesItemIdGetResponses, GetFilesOneApiV1CoreFilesItemIdGetErrors, GetFilesAllApiV1CoreFilesGetAllPostData, GetFilesAllApiV1CoreFilesGetAllPostResponses, GetFilesAllApiV1CoreFilesGetAllPostErrors, CreateObjectsApiV1CoreObjectsPostData, CreateObjectsApiV1CoreObjectsPostResponses, CreateObjectsApiV1CoreObjectsPostErrors, UpdateObjectsApiV1CoreObjectsPutData, UpdateObjectsApiV1CoreObjectsPutResponses, UpdateObjectsApiV1CoreObjectsPutErrors, DeleteObjectsApiV1CoreObjectsItemIdDeleteData, DeleteObjectsApiV1CoreObjectsItemIdDeleteResponses, DeleteObjectsApiV1CoreObjectsItemIdDeleteErrors, GetObjectsOneApiV1CoreObjectsItemIdGetData, GetObjectsOneApiV1CoreObjectsItemIdGetResponses, GetObjectsOneApiV1CoreObjectsItemIdGetErrors, GetObjectsAllApiV1CoreObjectsGetAllPostData, GetObjectsAllApiV1CoreObjectsGetAllPostResponses, GetObjectsAllApiV1CoreObjectsGetAllPostErrors, CreateObjectSystemsApiV1CoreObjectSystemsPostData, CreateObjectSystemsApiV1CoreObjectSystemsPostResponses, CreateObjectSystemsApiV1CoreObjectSystemsPostErrors, UpdateObjectSystemsApiV1CoreObjectSystemsPutData, UpdateObjectSystemsApiV1CoreObjectSystemsPutResponses, UpdateObjectSystemsApiV1CoreObjectSystemsPutErrors, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteResponses, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteErrors, GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData, GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponses, GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetErrors, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponses, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostErrors, CreateOrgsApiV1CoreOrgsPostData, CreateOrgsApiV1CoreOrgsPostResponses, CreateOrgsApiV1CoreOrgsPostErrors, UpdateOrgsApiV1CoreOrgsPutData, UpdateOrgsApiV1CoreOrgsPutResponses, UpdateOrgsApiV1CoreOrgsPutErrors, DeleteOrgsApiV1CoreOrgsItemIdDeleteData, DeleteOrgsApiV1CoreOrgsItemIdDeleteResponses, DeleteOrgsApiV1CoreOrgsItemIdDeleteErrors, GetOrgsOneApiV1CoreOrgsItemIdGetData, GetOrgsOneApiV1CoreOrgsItemIdGetResponses, GetOrgsOneApiV1CoreOrgsItemIdGetErrors, GetOrgsAllApiV1CoreOrgsGetAllPostData, GetOrgsAllApiV1CoreOrgsGetAllPostResponses, GetOrgsAllApiV1CoreOrgsGetAllPostErrors, CreateEmailTypesApiV1CoreEmailTypesPostData, CreateEmailTypesApiV1CoreEmailTypesPostResponses, CreateEmailTypesApiV1CoreEmailTypesPostErrors, UpdateEmailTypesApiV1CoreEmailTypesPutData, UpdateEmailTypesApiV1CoreEmailTypesPutResponses, UpdateEmailTypesApiV1CoreEmailTypesPutErrors, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteResponses, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteErrors, GetEmailTypesOneApiV1CoreEmailTypesItemIdGetData, GetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponses, GetEmailTypesOneApiV1CoreEmailTypesItemIdGetErrors, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponses, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostErrors, CreateEmailsApiV1CoreEmailsPostData, CreateEmailsApiV1CoreEmailsPostResponses, CreateEmailsApiV1CoreEmailsPostErrors, UpdateEmailsApiV1CoreEmailsPutData, UpdateEmailsApiV1CoreEmailsPutResponses, UpdateEmailsApiV1CoreEmailsPutErrors, DeleteEmailsApiV1CoreEmailsItemIdDeleteData, DeleteEmailsApiV1CoreEmailsItemIdDeleteResponses, DeleteEmailsApiV1CoreEmailsItemIdDeleteErrors, GetEmailsOneApiV1CoreEmailsItemIdGetData, GetEmailsOneApiV1CoreEmailsItemIdGetResponses, GetEmailsOneApiV1CoreEmailsItemIdGetErrors, GetEmailsAllApiV1CoreEmailsGetAllPostData, GetEmailsAllApiV1CoreEmailsGetAllPostResponses, GetEmailsAllApiV1CoreEmailsGetAllPostErrors, CreateCommentsApiV1CoreCommentsPostData, CreateCommentsApiV1CoreCommentsPostResponses, CreateCommentsApiV1CoreCommentsPostErrors, UpdateCommentsApiV1CoreCommentsPutData, UpdateCommentsApiV1CoreCommentsPutResponses, UpdateCommentsApiV1CoreCommentsPutErrors, DeleteCommentsApiV1CoreCommentsItemIdDeleteData, DeleteCommentsApiV1CoreCommentsItemIdDeleteResponses, DeleteCommentsApiV1CoreCommentsItemIdDeleteErrors, GetCommentsOneApiV1CoreCommentsItemIdGetData, GetCommentsOneApiV1CoreCommentsItemIdGetResponses, GetCommentsOneApiV1CoreCommentsItemIdGetErrors, GetCommentsAllApiV1CoreCommentsGetAllPostData, GetCommentsAllApiV1CoreCommentsGetAllPostResponses, GetCommentsAllApiV1CoreCommentsGetAllPostErrors, CreateDocsApiV1CoreDocsPostData, CreateDocsApiV1CoreDocsPostResponses, CreateDocsApiV1CoreDocsPostErrors, UpdateDocsApiV1CoreDocsPutData, UpdateDocsApiV1CoreDocsPutResponses, UpdateDocsApiV1CoreDocsPutErrors, GetDocTemplatesApiV1CoreDocsTemplatesPostData, GetDocTemplatesApiV1CoreDocsTemplatesPostResponses, GetDocTemplatesApiV1CoreDocsTemplatesPostErrors, GetTemplatesTableApiV1CoreDocsTemplatesTablePostData, GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponses, GetTemplatesTableApiV1CoreDocsTemplatesTablePostErrors, DeleteDocsApiV1CoreDocsItemIdDeleteData, DeleteDocsApiV1CoreDocsItemIdDeleteResponses, DeleteDocsApiV1CoreDocsItemIdDeleteErrors, GetDocsOneApiV1CoreDocsItemIdGetData, GetDocsOneApiV1CoreDocsItemIdGetResponses, GetDocsOneApiV1CoreDocsItemIdGetErrors, GetDocsAllApiV1CoreDocsGetAllPostData, GetDocsAllApiV1CoreDocsGetAllPostResponses, GetDocsAllApiV1CoreDocsGetAllPostErrors, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponses, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostErrors, UpdateDocsWithContentApiV1CoreDocsWithContentPutData, UpdateDocsWithContentApiV1CoreDocsWithContentPutResponses, UpdateDocsWithContentApiV1CoreDocsWithContentPutErrors, CreateDocPagesApiV1CoreDocPagesPostData, CreateDocPagesApiV1CoreDocPagesPostResponses, CreateDocPagesApiV1CoreDocPagesPostErrors, UpdateDocPagesWithPartsApiV1CoreDocPagesPutData, UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponses, UpdateDocPagesWithPartsApiV1CoreDocPagesPutErrors, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteResponses, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteErrors, GetDocPagesOneApiV1CoreDocPagesItemIdGetData, GetDocPagesOneApiV1CoreDocPagesItemIdGetResponses, GetDocPagesOneApiV1CoreDocPagesItemIdGetErrors, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponses, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostErrors, GetDocPagesAllApiV1CoreDocPagesGetAllPostData, GetDocPagesAllApiV1CoreDocPagesGetAllPostResponses, GetDocPagesAllApiV1CoreDocPagesGetAllPostErrors, CreateDocPartsApiV1CoreDocPartsPostData, CreateDocPartsApiV1CoreDocPartsPostResponses, CreateDocPartsApiV1CoreDocPartsPostErrors, UpdateDocPartsApiV1CoreDocPartsPutData, UpdateDocPartsApiV1CoreDocPartsPutResponses, UpdateDocPartsApiV1CoreDocPartsPutErrors, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteResponses, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteErrors, GetDocPartsOneApiV1CoreDocPartsItemIdGetData, GetDocPartsOneApiV1CoreDocPartsItemIdGetResponses, GetDocPartsOneApiV1CoreDocPartsItemIdGetErrors, GetDocPartsAllApiV1CoreDocPartsGetAllPostData, GetDocPartsAllApiV1CoreDocPartsGetAllPostResponses, GetDocPartsAllApiV1CoreDocPartsGetAllPostErrors, CreateVariantsApiV1CoreVariantsPostData, CreateVariantsApiV1CoreVariantsPostResponses, CreateVariantsApiV1CoreVariantsPostErrors, UpdateVariantsApiV1CoreVariantsPutData, UpdateVariantsApiV1CoreVariantsPutResponses, UpdateVariantsApiV1CoreVariantsPutErrors, DeleteVariantsApiV1CoreVariantsItemIdDeleteData, DeleteVariantsApiV1CoreVariantsItemIdDeleteResponses, DeleteVariantsApiV1CoreVariantsItemIdDeleteErrors, GetVariantsOneApiV1CoreVariantsItemIdGetData, GetVariantsOneApiV1CoreVariantsItemIdGetResponses, GetVariantsOneApiV1CoreVariantsItemIdGetErrors, GetVariantsAllApiV1CoreVariantsGetAllPostData, GetVariantsAllApiV1CoreVariantsGetAllPostResponses, GetVariantsAllApiV1CoreVariantsGetAllPostErrors, CreateAddressesApiV1CrmAddressesPostData, CreateAddressesApiV1CrmAddressesPostResponses, CreateAddressesApiV1CrmAddressesPostErrors, UpdateAddressesApiV1CrmAddressesPutData, UpdateAddressesApiV1CrmAddressesPutResponses, UpdateAddressesApiV1CrmAddressesPutErrors, DeleteAddressesApiV1CrmAddressesItemIdDeleteData, DeleteAddressesApiV1CrmAddressesItemIdDeleteResponses, DeleteAddressesApiV1CrmAddressesItemIdDeleteErrors, GetAddressesOneApiV1CrmAddressesItemIdGetData, GetAddressesOneApiV1CrmAddressesItemIdGetResponses, GetAddressesOneApiV1CrmAddressesItemIdGetErrors, GetAddressesAllApiV1CrmAddressesGetAllPostData, GetAddressesAllApiV1CrmAddressesGetAllPostResponses, GetAddressesAllApiV1CrmAddressesGetAllPostErrors, CreateContactsApiV1CrmContactsPostData, CreateContactsApiV1CrmContactsPostResponses, CreateContactsApiV1CrmContactsPostErrors, UpdateContactsApiV1CrmContactsPutData, UpdateContactsApiV1CrmContactsPutResponses, UpdateContactsApiV1CrmContactsPutErrors, DeleteContactsApiV1CrmContactsItemIdDeleteData, DeleteContactsApiV1CrmContactsItemIdDeleteResponses, DeleteContactsApiV1CrmContactsItemIdDeleteErrors, GetContactsOneApiV1CrmContactsItemIdGetData, GetContactsOneApiV1CrmContactsItemIdGetResponses, GetContactsOneApiV1CrmContactsItemIdGetErrors, GetContactsAllApiV1CrmContactsGetAllPostData, GetContactsAllApiV1CrmContactsGetAllPostResponses, GetContactsAllApiV1CrmContactsGetAllPostErrors, CreateContrahentsApiV1CrmContrahentsPostData, CreateContrahentsApiV1CrmContrahentsPostResponses, CreateContrahentsApiV1CrmContrahentsPostErrors, UpdateContrahentsApiV1CrmContrahentsPutData, UpdateContrahentsApiV1CrmContrahentsPutResponses, UpdateContrahentsApiV1CrmContrahentsPutErrors, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteResponses, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteErrors, GetContrahentsOneApiV1CrmContrahentsItemIdGetData, GetContrahentsOneApiV1CrmContrahentsItemIdGetResponses, GetContrahentsOneApiV1CrmContrahentsItemIdGetErrors, GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData, GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponses, GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetErrors, GetContrahentsAllApiV1CrmContrahentsGetAllPostData, GetContrahentsAllApiV1CrmContrahentsGetAllPostResponses, GetContrahentsAllApiV1CrmContrahentsGetAllPostErrors, ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData, ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponses, ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetErrors, GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData, GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponses, GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetErrors, CreateProfilesApiV1CrmProfilesPostData, CreateProfilesApiV1CrmProfilesPostResponses, CreateProfilesApiV1CrmProfilesPostErrors, UpdateProfilesApiV1CrmProfilesPutData, UpdateProfilesApiV1CrmProfilesPutResponses, UpdateProfilesApiV1CrmProfilesPutErrors, DeleteProfilesApiV1CrmProfilesItemIdDeleteData, DeleteProfilesApiV1CrmProfilesItemIdDeleteResponses, DeleteProfilesApiV1CrmProfilesItemIdDeleteErrors, GetProfilesOneApiV1CrmProfilesItemIdGetData, GetProfilesOneApiV1CrmProfilesItemIdGetResponses, GetProfilesOneApiV1CrmProfilesItemIdGetErrors, GetProfilesAllApiV1CrmProfilesGetAllPostData, GetProfilesAllApiV1CrmProfilesGetAllPostResponses, GetProfilesAllApiV1CrmProfilesGetAllPostErrors, CreateTypesApiV1CrmTypesPostData, CreateTypesApiV1CrmTypesPostResponses, CreateTypesApiV1CrmTypesPostErrors, UpdateTypesApiV1CrmTypesPutData, UpdateTypesApiV1CrmTypesPutResponses, UpdateTypesApiV1CrmTypesPutErrors, DeleteTypesApiV1CrmTypesItemIdDeleteData, DeleteTypesApiV1CrmTypesItemIdDeleteResponses, DeleteTypesApiV1CrmTypesItemIdDeleteErrors, GetTypesOneApiV1CrmTypesItemIdGetData, GetTypesOneApiV1CrmTypesItemIdGetResponses, GetTypesOneApiV1CrmTypesItemIdGetErrors, GetTypesAllApiV1CrmTypesGetAllPostData, GetTypesAllApiV1CrmTypesGetAllPostResponses, GetTypesAllApiV1CrmTypesGetAllPostErrors, GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData, GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponses, GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetErrors, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponses, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostErrors, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponses, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutErrors, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponses, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostErrors, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteResponses, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteErrors, GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData, GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponses, GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetErrors, ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData, ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponses, ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetErrors, ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData, ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponses, ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetErrors, CreateOrgSplitsApiV1MoneyOrgSplitsPostData, CreateOrgSplitsApiV1MoneyOrgSplitsPostResponses, CreateOrgSplitsApiV1MoneyOrgSplitsPostErrors, UpdateOrgSplitsApiV1MoneyOrgSplitsPutData, UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponses, UpdateOrgSplitsApiV1MoneyOrgSplitsPutErrors, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteResponses, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteErrors, GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData, GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponses, GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetErrors, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponses, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostErrors, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponses, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostErrors, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponses, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostErrors, GetJobJobsDatesJobIdGetData, GetJobJobsDatesJobIdGetResponses, GetJobJobsDatesJobIdGetErrors, HealthCheckHealthGetData, HealthCheckHealthGetResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Create Users
 * Create new users
 */
export const createUsersApiV1AuthUsersPost = <ThrowOnError extends boolean = false>(options: Options<CreateUsersApiV1AuthUsersPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateUsersApiV1AuthUsersPostResponses, CreateUsersApiV1AuthUsersPostErrors, ThrowOnError>({
        url: '/api/v1/auth/users/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Users
 * Update users
 */
export const updateUsersApiV1AuthUsersPut = <ThrowOnError extends boolean = false>(options: Options<UpdateUsersApiV1AuthUsersPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateUsersApiV1AuthUsersPutResponses, UpdateUsersApiV1AuthUsersPutErrors, ThrowOnError>({
        url: '/api/v1/auth/users/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Users One
 * Get a user by their Keycloak ID.
 *
 * Args:
 * keycloak_id: The Keycloak user ID (UUID string)
 *
 * Returns:
 * UserDisplayTypes: User data if found
 *
 * Raises:
 * HTTPException: If user is not found, invalid UUID format, or an error occurs
 */
export const getUsersOneApiV1AuthUsersKcKcIdGet = <ThrowOnError extends boolean = false>(options: Options<GetUsersOneApiV1AuthUsersKcKcIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetUsersOneApiV1AuthUsersKcKcIdGetResponses, GetUsersOneApiV1AuthUsersKcKcIdGetErrors, ThrowOnError>({
        url: '/api/v1/auth/users/kc/{kc_id}',
        ...options
    });
};

/**
 * Delete Users
 * Delete users by ID.
 */
export const deleteUsersApiV1AuthUsersItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteUsersApiV1AuthUsersItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteUsersApiV1AuthUsersItemIdDeleteResponses, DeleteUsersApiV1AuthUsersItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/auth/users/{item_id}',
        ...options
    });
};

/**
 * Get Users One
 * Get users by ID.
 */
export const getUsersOneApiV1AuthUsersItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetUsersOneApiV1AuthUsersItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetUsersOneApiV1AuthUsersItemIdGetResponses, GetUsersOneApiV1AuthUsersItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/auth/users/{item_id}',
        ...options
    });
};

/**
 * Get Users All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getUsersAllApiV1AuthUsersGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetUsersAllApiV1AuthUsersGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetUsersAllApiV1AuthUsersGetAllPostResponses, GetUsersAllApiV1AuthUsersGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/auth/users/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Register
 * Register a new user in both the database and Keycloak.
 *
 * Rate limited to 5 requests per minute to prevent abuse.
 */
export const registerApiV1AuthUsersRegisterPost = <ThrowOnError extends boolean = false>(options: Options<RegisterApiV1AuthUsersRegisterPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RegisterApiV1AuthUsersRegisterPostResponses, RegisterApiV1AuthUsersRegisterPostErrors, ThrowOnError>({
        url: '/api/v1/auth/users/register',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Link Contrahent
 * Links a contrahent to a user, creating a new profile and updating Keycloak roles.
 */
export const linkContrahentApiV1AuthUsersLinkContrahentPost = <ThrowOnError extends boolean = false>(options: Options<LinkContrahentApiV1AuthUsersLinkContrahentPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<LinkContrahentApiV1AuthUsersLinkContrahentPostResponses, LinkContrahentApiV1AuthUsersLinkContrahentPostErrors, ThrowOnError>({
        url: '/api/v1/auth/users/link-contrahent',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Jobs
 * Create new jobs with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createJobsApiV1CoreJobsPost = <ThrowOnError extends boolean = false>(options: Options<CreateJobsApiV1CoreJobsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateJobsApiV1CoreJobsPostResponses, CreateJobsApiV1CoreJobsPostErrors, ThrowOnError>({
        url: '/api/v1/core/jobs/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Jobs
 * Update jobs by ID.
 */
export const updateJobsApiV1CoreJobsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateJobsApiV1CoreJobsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateJobsApiV1CoreJobsPutResponses, UpdateJobsApiV1CoreJobsPutErrors, ThrowOnError>({
        url: '/api/v1/core/jobs/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Jobs
 * Delete jobs by ID.
 */
export const deleteJobsApiV1CoreJobsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteJobsApiV1CoreJobsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteJobsApiV1CoreJobsItemIdDeleteResponses, DeleteJobsApiV1CoreJobsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/jobs/{item_id}',
        ...options
    });
};

/**
 * Get Jobs One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getJobsOneApiV1CoreJobsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetJobsOneApiV1CoreJobsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetJobsOneApiV1CoreJobsItemIdGetResponses, GetJobsOneApiV1CoreJobsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/jobs/{item_id}',
        ...options
    });
};

/**
 * Get Jobs All
 * Get all Jobs paginated list with advanced filtering and sorting options.
 */
export const getJobsAllApiV1CoreJobsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetJobsAllApiV1CoreJobsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetJobsAllApiV1CoreJobsGetAllPostResponses, GetJobsAllApiV1CoreJobsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/jobs/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Job Tasks All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getJobTasksAllApiV1CoreJobTasksGet = <ThrowOnError extends boolean = false>(options: Options<GetJobTasksAllApiV1CoreJobTasksGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetJobTasksAllApiV1CoreJobTasksGetResponses, GetJobTasksAllApiV1CoreJobTasksGetErrors, ThrowOnError>({
        url: '/api/v1/core/job-tasks/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Job Tasks
 * Create new job_tasks with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createJobTasksApiV1CoreJobTasksPost = <ThrowOnError extends boolean = false>(options: Options<CreateJobTasksApiV1CoreJobTasksPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateJobTasksApiV1CoreJobTasksPostResponses, CreateJobTasksApiV1CoreJobTasksPostErrors, ThrowOnError>({
        url: '/api/v1/core/job-tasks/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Job Tasks
 * Update job_tasks by ID.
 */
export const updateJobTasksApiV1CoreJobTasksPut = <ThrowOnError extends boolean = false>(options: Options<UpdateJobTasksApiV1CoreJobTasksPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateJobTasksApiV1CoreJobTasksPutResponses, UpdateJobTasksApiV1CoreJobTasksPutErrors, ThrowOnError>({
        url: '/api/v1/core/job-tasks/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Job Tasks
 * Delete job_tasks by ID.
 */
export const deleteJobTasksApiV1CoreJobTasksItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponses, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/job-tasks/{item_id}',
        ...options
    });
};

/**
 * Get Job Tasks One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getJobTasksOneApiV1CoreJobTasksItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetJobTasksOneApiV1CoreJobTasksItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetJobTasksOneApiV1CoreJobTasksItemIdGetResponses, GetJobTasksOneApiV1CoreJobTasksItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/job-tasks/{item_id}',
        ...options
    });
};

/**
 * Create Files
 * Create new files with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createFilesApiV1CoreFilesPost = <ThrowOnError extends boolean = false>(options: Options<CreateFilesApiV1CoreFilesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateFilesApiV1CoreFilesPostResponses, CreateFilesApiV1CoreFilesPostErrors, ThrowOnError>({
        url: '/api/v1/core/files/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Files
 * Update files by ID.
 */
export const updateFilesApiV1CoreFilesPut = <ThrowOnError extends boolean = false>(options: Options<UpdateFilesApiV1CoreFilesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateFilesApiV1CoreFilesPutResponses, UpdateFilesApiV1CoreFilesPutErrors, ThrowOnError>({
        url: '/api/v1/core/files/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Files
 * Delete files by ID.
 */
export const deleteFilesApiV1CoreFilesItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteFilesApiV1CoreFilesItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteFilesApiV1CoreFilesItemIdDeleteResponses, DeleteFilesApiV1CoreFilesItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/files/{item_id}',
        ...options
    });
};

/**
 * Get Files One
 * Get file.
 * Get a single file with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getFilesOneApiV1CoreFilesItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetFilesOneApiV1CoreFilesItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetFilesOneApiV1CoreFilesItemIdGetResponses, GetFilesOneApiV1CoreFilesItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/files/{item_id}',
        ...options
    });
};

/**
 * Get Files All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getFilesAllApiV1CoreFilesGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetFilesAllApiV1CoreFilesGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetFilesAllApiV1CoreFilesGetAllPostResponses, GetFilesAllApiV1CoreFilesGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/files/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Objects
 * Create new objects
 */
export const createObjectsApiV1CoreObjectsPost = <ThrowOnError extends boolean = false>(options: Options<CreateObjectsApiV1CoreObjectsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateObjectsApiV1CoreObjectsPostResponses, CreateObjectsApiV1CoreObjectsPostErrors, ThrowOnError>({
        url: '/api/v1/core/objects/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Objects
 * Update objects
 */
export const updateObjectsApiV1CoreObjectsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateObjectsApiV1CoreObjectsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateObjectsApiV1CoreObjectsPutResponses, UpdateObjectsApiV1CoreObjectsPutErrors, ThrowOnError>({
        url: '/api/v1/core/objects/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Objects
 * Delete objects by ID.
 */
export const deleteObjectsApiV1CoreObjectsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteObjectsApiV1CoreObjectsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteObjectsApiV1CoreObjectsItemIdDeleteResponses, DeleteObjectsApiV1CoreObjectsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/objects/{item_id}',
        ...options
    });
};

/**
 * Get Objects One
 * Get objects by ID.
 */
export const getObjectsOneApiV1CoreObjectsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetObjectsOneApiV1CoreObjectsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetObjectsOneApiV1CoreObjectsItemIdGetResponses, GetObjectsOneApiV1CoreObjectsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/objects/{item_id}',
        ...options
    });
};

/**
 * Get Objects All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getObjectsAllApiV1CoreObjectsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetObjectsAllApiV1CoreObjectsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetObjectsAllApiV1CoreObjectsGetAllPostResponses, GetObjectsAllApiV1CoreObjectsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/objects/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Object Systems
 * Create new object_systems
 */
export const createObjectSystemsApiV1CoreObjectSystemsPost = <ThrowOnError extends boolean = false>(options: Options<CreateObjectSystemsApiV1CoreObjectSystemsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateObjectSystemsApiV1CoreObjectSystemsPostResponses, CreateObjectSystemsApiV1CoreObjectSystemsPostErrors, ThrowOnError>({
        url: '/api/v1/core/object-systems/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Object Systems
 * Update object_systems
 */
export const updateObjectSystemsApiV1CoreObjectSystemsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateObjectSystemsApiV1CoreObjectSystemsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateObjectSystemsApiV1CoreObjectSystemsPutResponses, UpdateObjectSystemsApiV1CoreObjectSystemsPutErrors, ThrowOnError>({
        url: '/api/v1/core/object-systems/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Object Systems
 * Delete object_systems by ID.
 */
export const deleteObjectSystemsApiV1CoreObjectSystemsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteResponses, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/object-systems/{item_id}',
        ...options
    });
};

/**
 * Get Object Systems One
 * Get object_systems by ID.
 */
export const getObjectSystemsOneApiV1CoreObjectSystemsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponses, GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/object-systems/{item_id}',
        ...options
    });
};

/**
 * Get Object Systems All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getObjectSystemsAllApiV1CoreObjectSystemsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponses, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/object-systems/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Orgs
 * Create new orgs
 */
export const createOrgsApiV1CoreOrgsPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrgsApiV1CoreOrgsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrgsApiV1CoreOrgsPostResponses, CreateOrgsApiV1CoreOrgsPostErrors, ThrowOnError>({
        url: '/api/v1/core/orgs/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Orgs
 * Update orgs
 */
export const updateOrgsApiV1CoreOrgsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrgsApiV1CoreOrgsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrgsApiV1CoreOrgsPutResponses, UpdateOrgsApiV1CoreOrgsPutErrors, ThrowOnError>({
        url: '/api/v1/core/orgs/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Orgs
 * Delete orgs by ID.
 */
export const deleteOrgsApiV1CoreOrgsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteOrgsApiV1CoreOrgsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteOrgsApiV1CoreOrgsItemIdDeleteResponses, DeleteOrgsApiV1CoreOrgsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/orgs/{item_id}',
        ...options
    });
};

/**
 * Get Orgs One
 * Get orgs by ID.
 */
export const getOrgsOneApiV1CoreOrgsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetOrgsOneApiV1CoreOrgsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrgsOneApiV1CoreOrgsItemIdGetResponses, GetOrgsOneApiV1CoreOrgsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/orgs/{item_id}',
        ...options
    });
};

/**
 * Get Orgs All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getOrgsAllApiV1CoreOrgsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetOrgsAllApiV1CoreOrgsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetOrgsAllApiV1CoreOrgsGetAllPostResponses, GetOrgsAllApiV1CoreOrgsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/orgs/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Email Types
 * Create new email_types
 */
export const createEmailTypesApiV1CoreEmailTypesPost = <ThrowOnError extends boolean = false>(options: Options<CreateEmailTypesApiV1CoreEmailTypesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateEmailTypesApiV1CoreEmailTypesPostResponses, CreateEmailTypesApiV1CoreEmailTypesPostErrors, ThrowOnError>({
        url: '/api/v1/core/email-types/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Email Types
 * Update email_types
 */
export const updateEmailTypesApiV1CoreEmailTypesPut = <ThrowOnError extends boolean = false>(options: Options<UpdateEmailTypesApiV1CoreEmailTypesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateEmailTypesApiV1CoreEmailTypesPutResponses, UpdateEmailTypesApiV1CoreEmailTypesPutErrors, ThrowOnError>({
        url: '/api/v1/core/email-types/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Email Types
 * Delete email_types by ID.
 */
export const deleteEmailTypesApiV1CoreEmailTypesItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteResponses, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/email-types/{item_id}',
        ...options
    });
};

/**
 * Get Email Types One
 * Get email_types by ID.
 */
export const getEmailTypesOneApiV1CoreEmailTypesItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetEmailTypesOneApiV1CoreEmailTypesItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponses, GetEmailTypesOneApiV1CoreEmailTypesItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/email-types/{item_id}',
        ...options
    });
};

/**
 * Get Email Types All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getEmailTypesAllApiV1CoreEmailTypesGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponses, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/email-types/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Emails
 * Create new emails
 */
export const createEmailsApiV1CoreEmailsPost = <ThrowOnError extends boolean = false>(options: Options<CreateEmailsApiV1CoreEmailsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateEmailsApiV1CoreEmailsPostResponses, CreateEmailsApiV1CoreEmailsPostErrors, ThrowOnError>({
        url: '/api/v1/core/emails/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Emails
 * Update emails
 */
export const updateEmailsApiV1CoreEmailsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateEmailsApiV1CoreEmailsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateEmailsApiV1CoreEmailsPutResponses, UpdateEmailsApiV1CoreEmailsPutErrors, ThrowOnError>({
        url: '/api/v1/core/emails/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Emails
 * Delete emails by ID.
 */
export const deleteEmailsApiV1CoreEmailsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteEmailsApiV1CoreEmailsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteEmailsApiV1CoreEmailsItemIdDeleteResponses, DeleteEmailsApiV1CoreEmailsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/emails/{item_id}',
        ...options
    });
};

/**
 * Get Emails One
 * Get emails by ID.
 */
export const getEmailsOneApiV1CoreEmailsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetEmailsOneApiV1CoreEmailsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetEmailsOneApiV1CoreEmailsItemIdGetResponses, GetEmailsOneApiV1CoreEmailsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/emails/{item_id}',
        ...options
    });
};

/**
 * Get Emails All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getEmailsAllApiV1CoreEmailsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetEmailsAllApiV1CoreEmailsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetEmailsAllApiV1CoreEmailsGetAllPostResponses, GetEmailsAllApiV1CoreEmailsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/emails/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Comments
 * Create new comments
 */
export const createCommentsApiV1CoreCommentsPost = <ThrowOnError extends boolean = false>(options: Options<CreateCommentsApiV1CoreCommentsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateCommentsApiV1CoreCommentsPostResponses, CreateCommentsApiV1CoreCommentsPostErrors, ThrowOnError>({
        url: '/api/v1/core/comments/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Comments
 * Update comments
 */
export const updateCommentsApiV1CoreCommentsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateCommentsApiV1CoreCommentsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateCommentsApiV1CoreCommentsPutResponses, UpdateCommentsApiV1CoreCommentsPutErrors, ThrowOnError>({
        url: '/api/v1/core/comments/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Comments
 * Delete comments by ID.
 */
export const deleteCommentsApiV1CoreCommentsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteCommentsApiV1CoreCommentsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteCommentsApiV1CoreCommentsItemIdDeleteResponses, DeleteCommentsApiV1CoreCommentsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/comments/{item_id}',
        ...options
    });
};

/**
 * Get Comments One
 * Get comments by ID.
 */
export const getCommentsOneApiV1CoreCommentsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetCommentsOneApiV1CoreCommentsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCommentsOneApiV1CoreCommentsItemIdGetResponses, GetCommentsOneApiV1CoreCommentsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/comments/{item_id}',
        ...options
    });
};

/**
 * Get Comments All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getCommentsAllApiV1CoreCommentsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetCommentsAllApiV1CoreCommentsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetCommentsAllApiV1CoreCommentsGetAllPostResponses, GetCommentsAllApiV1CoreCommentsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/comments/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Docs
 * Create new docs
 */
export const createDocsApiV1CoreDocsPost = <ThrowOnError extends boolean = false>(options: Options<CreateDocsApiV1CoreDocsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateDocsApiV1CoreDocsPostResponses, CreateDocsApiV1CoreDocsPostErrors, ThrowOnError>({
        url: '/api/v1/core/docs/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Docs
 * Update docs
 */
export const updateDocsApiV1CoreDocsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateDocsApiV1CoreDocsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateDocsApiV1CoreDocsPutResponses, UpdateDocsApiV1CoreDocsPutErrors, ThrowOnError>({
        url: '/api/v1/core/docs/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Doc Templates
 * Get document templates and part templates for a specific organization.
 *
 * Args:
 * org_id: Required - The organization ID
 * doc_type: Optional - Filter by document type
 *
 * Returns:
 * DocTemplatesTypes - Object containing doc templates and part templates
 */
export const getDocTemplatesApiV1CoreDocsTemplatesPost = <ThrowOnError extends boolean = false>(options: Options<GetDocTemplatesApiV1CoreDocsTemplatesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetDocTemplatesApiV1CoreDocsTemplatesPostResponses, GetDocTemplatesApiV1CoreDocsTemplatesPostErrors, ThrowOnError>({
        url: '/api/v1/core/docs/templates',
        ...options
    });
};

/**
 * Get Templates Table
 * Get all templates (docs and doc_parts) for a specific organization in a combined
 * table format.
 *
 * Args:
 * org_id: Required - The organization ID
 * doc_type: Optional - Filter by document type
 *
 * Returns:
 * List[Union[DocTableTypes, DocPartDisplayTypes]] - Combined list of document and
 * part templates
 */
export const getTemplatesTableApiV1CoreDocsTemplatesTablePost = <ThrowOnError extends boolean = false>(options: Options<GetTemplatesTableApiV1CoreDocsTemplatesTablePostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponses, GetTemplatesTableApiV1CoreDocsTemplatesTablePostErrors, ThrowOnError>({
        url: '/api/v1/core/docs/templates-table',
        ...options
    });
};

/**
 * Delete Docs
 * Delete docs by ID.
 */
export const deleteDocsApiV1CoreDocsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteDocsApiV1CoreDocsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteDocsApiV1CoreDocsItemIdDeleteResponses, DeleteDocsApiV1CoreDocsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/docs/{item_id}',
        ...options
    });
};

/**
 * Get Docs One
 * Get docs by ID.
 */
export const getDocsOneApiV1CoreDocsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetDocsOneApiV1CoreDocsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetDocsOneApiV1CoreDocsItemIdGetResponses, GetDocsOneApiV1CoreDocsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/docs/{item_id}',
        ...options
    });
};

/**
 * Get Docs All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocsAllApiV1CoreDocsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetDocsAllApiV1CoreDocsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetDocsAllApiV1CoreDocsGetAllPostResponses, GetDocsAllApiV1CoreDocsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/docs/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Docs All With Content
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocsAllWithContentApiV1CoreDocsGetAllWithContentPost = <ThrowOnError extends boolean = false>(options: Options<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponses, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostErrors, ThrowOnError>({
        url: '/api/v1/core/docs/get-all-with-content',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Docs With Content
 * Update docs
 */
export const updateDocsWithContentApiV1CoreDocsWithContentPut = <ThrowOnError extends boolean = false>(options: Options<UpdateDocsWithContentApiV1CoreDocsWithContentPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateDocsWithContentApiV1CoreDocsWithContentPutResponses, UpdateDocsWithContentApiV1CoreDocsWithContentPutErrors, ThrowOnError>({
        url: '/api/v1/core/docs/with-content',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Doc Pages
 * Create new doc_pages
 */
export const createDocPagesApiV1CoreDocPagesPost = <ThrowOnError extends boolean = false>(options: Options<CreateDocPagesApiV1CoreDocPagesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateDocPagesApiV1CoreDocPagesPostResponses, CreateDocPagesApiV1CoreDocPagesPostErrors, ThrowOnError>({
        url: '/api/v1/core/doc-pages/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Doc Pages With Parts
 * Update doc_pages
 */
export const updateDocPagesWithPartsApiV1CoreDocPagesPut = <ThrowOnError extends boolean = false>(options: Options<UpdateDocPagesWithPartsApiV1CoreDocPagesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponses, UpdateDocPagesWithPartsApiV1CoreDocPagesPutErrors, ThrowOnError>({
        url: '/api/v1/core/doc-pages/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Doc Pages With Links
 * Delete doc_pages by ID.
 */
export const deleteDocPagesWithLinksApiV1CoreDocPagesItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteResponses, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/doc-pages/{item_id}',
        ...options
    });
};

/**
 * Get Doc Pages One
 * Get doc_pages by ID.
 */
export const getDocPagesOneApiV1CoreDocPagesItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetDocPagesOneApiV1CoreDocPagesItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetDocPagesOneApiV1CoreDocPagesItemIdGetResponses, GetDocPagesOneApiV1CoreDocPagesItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/doc-pages/{item_id}',
        ...options
    });
};

/**
 * Get Doc Pages All With Parts
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPost = <ThrowOnError extends boolean = false>(options: Options<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponses, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostErrors, ThrowOnError>({
        url: '/api/v1/core/doc-pages/get-all-with-parts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Doc Pages All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPagesAllApiV1CoreDocPagesGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetDocPagesAllApiV1CoreDocPagesGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetDocPagesAllApiV1CoreDocPagesGetAllPostResponses, GetDocPagesAllApiV1CoreDocPagesGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/doc-pages/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Doc Parts
 * Create new doc_parts
 */
export const createDocPartsApiV1CoreDocPartsPost = <ThrowOnError extends boolean = false>(options: Options<CreateDocPartsApiV1CoreDocPartsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateDocPartsApiV1CoreDocPartsPostResponses, CreateDocPartsApiV1CoreDocPartsPostErrors, ThrowOnError>({
        url: '/api/v1/core/doc-parts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Doc Parts
 * Update doc_parts
 */
export const updateDocPartsApiV1CoreDocPartsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateDocPartsApiV1CoreDocPartsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateDocPartsApiV1CoreDocPartsPutResponses, UpdateDocPartsApiV1CoreDocPartsPutErrors, ThrowOnError>({
        url: '/api/v1/core/doc-parts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Doc Parts
 * Delete doc_parts by ID.
 */
export const deleteDocPartsApiV1CoreDocPartsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteDocPartsApiV1CoreDocPartsItemIdDeleteResponses, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/doc-parts/{item_id}',
        ...options
    });
};

/**
 * Get Doc Parts One
 * Get doc_parts by ID.
 */
export const getDocPartsOneApiV1CoreDocPartsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetDocPartsOneApiV1CoreDocPartsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetDocPartsOneApiV1CoreDocPartsItemIdGetResponses, GetDocPartsOneApiV1CoreDocPartsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/doc-parts/{item_id}',
        ...options
    });
};

/**
 * Get Doc Parts All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPartsAllApiV1CoreDocPartsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetDocPartsAllApiV1CoreDocPartsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetDocPartsAllApiV1CoreDocPartsGetAllPostResponses, GetDocPartsAllApiV1CoreDocPartsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/doc-parts/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Variants
 * Create new variants
 */
export const createVariantsApiV1CoreVariantsPost = <ThrowOnError extends boolean = false>(options: Options<CreateVariantsApiV1CoreVariantsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateVariantsApiV1CoreVariantsPostResponses, CreateVariantsApiV1CoreVariantsPostErrors, ThrowOnError>({
        url: '/api/v1/core/variants/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Variants
 * Update variants
 */
export const updateVariantsApiV1CoreVariantsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateVariantsApiV1CoreVariantsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateVariantsApiV1CoreVariantsPutResponses, UpdateVariantsApiV1CoreVariantsPutErrors, ThrowOnError>({
        url: '/api/v1/core/variants/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Variants
 * Delete variants by ID.
 */
export const deleteVariantsApiV1CoreVariantsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteVariantsApiV1CoreVariantsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteVariantsApiV1CoreVariantsItemIdDeleteResponses, DeleteVariantsApiV1CoreVariantsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/core/variants/{item_id}',
        ...options
    });
};

/**
 * Get Variants One
 * Get variants by ID.
 */
export const getVariantsOneApiV1CoreVariantsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetVariantsOneApiV1CoreVariantsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetVariantsOneApiV1CoreVariantsItemIdGetResponses, GetVariantsOneApiV1CoreVariantsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/core/variants/{item_id}',
        ...options
    });
};

/**
 * Get Variants All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getVariantsAllApiV1CoreVariantsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetVariantsAllApiV1CoreVariantsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetVariantsAllApiV1CoreVariantsGetAllPostResponses, GetVariantsAllApiV1CoreVariantsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/core/variants/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Addresses
 * Create new addresses
 */
export const createAddressesApiV1CrmAddressesPost = <ThrowOnError extends boolean = false>(options: Options<CreateAddressesApiV1CrmAddressesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateAddressesApiV1CrmAddressesPostResponses, CreateAddressesApiV1CrmAddressesPostErrors, ThrowOnError>({
        url: '/api/v1/crm/addresses/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Addresses
 * Update addresses
 */
export const updateAddressesApiV1CrmAddressesPut = <ThrowOnError extends boolean = false>(options: Options<UpdateAddressesApiV1CrmAddressesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateAddressesApiV1CrmAddressesPutResponses, UpdateAddressesApiV1CrmAddressesPutErrors, ThrowOnError>({
        url: '/api/v1/crm/addresses/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Addresses
 * Delete addresses by ID.
 */
export const deleteAddressesApiV1CrmAddressesItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteAddressesApiV1CrmAddressesItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteAddressesApiV1CrmAddressesItemIdDeleteResponses, DeleteAddressesApiV1CrmAddressesItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/crm/addresses/{item_id}',
        ...options
    });
};

/**
 * Get Addresses One
 * Get addresses by ID.
 */
export const getAddressesOneApiV1CrmAddressesItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetAddressesOneApiV1CrmAddressesItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetAddressesOneApiV1CrmAddressesItemIdGetResponses, GetAddressesOneApiV1CrmAddressesItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/addresses/{item_id}',
        ...options
    });
};

/**
 * Get Addresses All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getAddressesAllApiV1CrmAddressesGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetAddressesAllApiV1CrmAddressesGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetAddressesAllApiV1CrmAddressesGetAllPostResponses, GetAddressesAllApiV1CrmAddressesGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/crm/addresses/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Contacts
 * Create new contacts
 */
export const createContactsApiV1CrmContactsPost = <ThrowOnError extends boolean = false>(options: Options<CreateContactsApiV1CrmContactsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateContactsApiV1CrmContactsPostResponses, CreateContactsApiV1CrmContactsPostErrors, ThrowOnError>({
        url: '/api/v1/crm/contacts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Contacts
 * Update contacts
 */
export const updateContactsApiV1CrmContactsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateContactsApiV1CrmContactsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateContactsApiV1CrmContactsPutResponses, UpdateContactsApiV1CrmContactsPutErrors, ThrowOnError>({
        url: '/api/v1/crm/contacts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Contacts
 * Delete contacts by ID.
 */
export const deleteContactsApiV1CrmContactsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteContactsApiV1CrmContactsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteContactsApiV1CrmContactsItemIdDeleteResponses, DeleteContactsApiV1CrmContactsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/crm/contacts/{item_id}',
        ...options
    });
};

/**
 * Get Contacts One
 * Get contacts by ID.
 */
export const getContactsOneApiV1CrmContactsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetContactsOneApiV1CrmContactsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetContactsOneApiV1CrmContactsItemIdGetResponses, GetContactsOneApiV1CrmContactsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/contacts/{item_id}',
        ...options
    });
};

/**
 * Get Contacts All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getContactsAllApiV1CrmContactsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetContactsAllApiV1CrmContactsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetContactsAllApiV1CrmContactsGetAllPostResponses, GetContactsAllApiV1CrmContactsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/crm/contacts/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Contrahents
 * Create new contrahents with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createContrahentsApiV1CrmContrahentsPost = <ThrowOnError extends boolean = false>(options: Options<CreateContrahentsApiV1CrmContrahentsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateContrahentsApiV1CrmContrahentsPostResponses, CreateContrahentsApiV1CrmContrahentsPostErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Contrahents
 * Contrahent update
 */
export const updateContrahentsApiV1CrmContrahentsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateContrahentsApiV1CrmContrahentsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateContrahentsApiV1CrmContrahentsPutResponses, UpdateContrahentsApiV1CrmContrahentsPutErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Contrahents
 * Delete contrahents by ID.
 */
export const deleteContrahentsApiV1CrmContrahentsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteContrahentsApiV1CrmContrahentsItemIdDeleteResponses, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/{item_id}',
        ...options
    });
};

/**
 * Get Contrahents One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getContrahentsOneApiV1CrmContrahentsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetContrahentsOneApiV1CrmContrahentsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetContrahentsOneApiV1CrmContrahentsItemIdGetResponses, GetContrahentsOneApiV1CrmContrahentsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/{item_id}',
        ...options
    });
};

/**
 * Get Contrahents Relations One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponses, GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/with-relations/{item_id}',
        ...options
    });
};

/**
 * Get Contrahents All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getContrahentsAllApiV1CrmContrahentsGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetContrahentsAllApiV1CrmContrahentsGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetContrahentsAllApiV1CrmContrahentsGetAllPostResponses, GetContrahentsAllApiV1CrmContrahentsGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get organization contrahents
 * Get all contrahents associated with a specific organization
 */
export const readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGet = <ThrowOnError extends boolean = false>(options: Options<ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponses, ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/org/{org_id}',
        ...options
    });
};

/**
 * Get Car Owners
 * Get all employees who have associated vehicles
 */
export const getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGet = <ThrowOnError extends boolean = false>(options: Options<GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponses, GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/contrahents/car-owners/{org_id}',
        ...options
    });
};

/**
 * Create Profiles
 * Create new profiles
 */
export const createProfilesApiV1CrmProfilesPost = <ThrowOnError extends boolean = false>(options: Options<CreateProfilesApiV1CrmProfilesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateProfilesApiV1CrmProfilesPostResponses, CreateProfilesApiV1CrmProfilesPostErrors, ThrowOnError>({
        url: '/api/v1/crm/profiles/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Profiles
 * Update profiles
 */
export const updateProfilesApiV1CrmProfilesPut = <ThrowOnError extends boolean = false>(options: Options<UpdateProfilesApiV1CrmProfilesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateProfilesApiV1CrmProfilesPutResponses, UpdateProfilesApiV1CrmProfilesPutErrors, ThrowOnError>({
        url: '/api/v1/crm/profiles/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Profiles
 * Delete profiles by ID.
 */
export const deleteProfilesApiV1CrmProfilesItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteProfilesApiV1CrmProfilesItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteProfilesApiV1CrmProfilesItemIdDeleteResponses, DeleteProfilesApiV1CrmProfilesItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/crm/profiles/{item_id}',
        ...options
    });
};

/**
 * Get Profiles One
 * Get profiles by ID.
 */
export const getProfilesOneApiV1CrmProfilesItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetProfilesOneApiV1CrmProfilesItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetProfilesOneApiV1CrmProfilesItemIdGetResponses, GetProfilesOneApiV1CrmProfilesItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/profiles/{item_id}',
        ...options
    });
};

/**
 * Get Profiles All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getProfilesAllApiV1CrmProfilesGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetProfilesAllApiV1CrmProfilesGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetProfilesAllApiV1CrmProfilesGetAllPostResponses, GetProfilesAllApiV1CrmProfilesGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/crm/profiles/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Types
 * Create new types with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createTypesApiV1CrmTypesPost = <ThrowOnError extends boolean = false>(options: Options<CreateTypesApiV1CrmTypesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateTypesApiV1CrmTypesPostResponses, CreateTypesApiV1CrmTypesPostErrors, ThrowOnError>({
        url: '/api/v1/crm/types/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Types
 * type update
 */
export const updateTypesApiV1CrmTypesPut = <ThrowOnError extends boolean = false>(options: Options<UpdateTypesApiV1CrmTypesPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateTypesApiV1CrmTypesPutResponses, UpdateTypesApiV1CrmTypesPutErrors, ThrowOnError>({
        url: '/api/v1/crm/types/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Types
 * Delete types by ID.
 */
export const deleteTypesApiV1CrmTypesItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteTypesApiV1CrmTypesItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteTypesApiV1CrmTypesItemIdDeleteResponses, DeleteTypesApiV1CrmTypesItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/crm/types/{item_id}',
        ...options
    });
};

/**
 * Get Types One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getTypesOneApiV1CrmTypesItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetTypesOneApiV1CrmTypesItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetTypesOneApiV1CrmTypesItemIdGetResponses, GetTypesOneApiV1CrmTypesItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/crm/types/{item_id}',
        ...options
    });
};

/**
 * Get Types All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getTypesAllApiV1CrmTypesGetAllPost = <ThrowOnError extends boolean = false>(options: Options<GetTypesAllApiV1CrmTypesGetAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetTypesAllApiV1CrmTypesGetAllPostResponses, GetTypesAllApiV1CrmTypesGetAllPostErrors, ThrowOnError>({
        url: '/api/v1/crm/types/get-all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Org Transactions All
 * Get all org org_transactions with advanced filtering, sorting, and pagination.
 */
export const getOrgTransactionsAllApiV1MoneyOrgTransactionsGet = <ThrowOnError extends boolean = false>(options: Options<GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponses, GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/',
        ...options
    });
};

/**
 * Create Org Transactions
 * Create new org_transactions with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createOrgTransactionsApiV1MoneyOrgTransactionsPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponses, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Org Transactions
 * Documentation.
 */
export const updateOrgTransactionsApiV1MoneyOrgTransactionsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponses, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Org Transactions Saved
 * Create row in table org_transactions.
 *
 * We create  template or schedule transaction.
 *
 * We DO NOT create current transaction.
 */
export const createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponses, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/saved',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Org Transactions
 * Delete org_transactions by ID.
 */
export const deleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteResponses, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/{item_id}',
        ...options
    });
};

/**
 * Get Org Transactions One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponses, GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/{item_id}',
        ...options
    });
};

/**
 * Read Org Transactions Saved
 * Get all saved transactions for an organization.
 */
export const readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGet = <ThrowOnError extends boolean = false>(options: Options<ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponses, ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/saved/{org_id}',
        ...options
    });
};

/**
 * Get scheduled transactions for an organization
 * Get scheduled transactions for an organization within a date range.
 *
 * Args:
 * org_id: Organization ID to get transactions for
 * period_start: Start date of the period
 * period_end: End date of the period
 *
 * Returns:
 * OrgTransactionScheduledTypes containing categorized scheduled transactions
 */
export const readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGet = <ThrowOnError extends boolean = false>(options: Options<ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponses, ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetErrors, ThrowOnError>({
        url: '/api/v1/money/org-transactions/scheduled/{org_id}',
        ...options
    });
};

/**
 * Create Org Splits
 * Create new org_splits with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createOrgSplitsApiV1MoneyOrgSplitsPost = <ThrowOnError extends boolean = false>(options: Options<CreateOrgSplitsApiV1MoneyOrgSplitsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<CreateOrgSplitsApiV1MoneyOrgSplitsPostResponses, CreateOrgSplitsApiV1MoneyOrgSplitsPostErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Org Splits
 * Documentation.
 */
export const updateOrgSplitsApiV1MoneyOrgSplitsPut = <ThrowOnError extends boolean = false>(options: Options<UpdateOrgSplitsApiV1MoneyOrgSplitsPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponses, UpdateOrgSplitsApiV1MoneyOrgSplitsPutErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Org Splits
 * Delete org_splits by ID.
 */
export const deleteOrgSplitsApiV1MoneyOrgSplitsItemIdDelete = <ThrowOnError extends boolean = false>(options: Options<DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteResponses, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/{item_id}',
        ...options
    });
};

/**
 * Get Org Splits One
 * Get org_split.
 * Get a single org_split with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGet = <ThrowOnError extends boolean = false>(options: Options<GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponses, GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/{item_id}',
        ...options
    });
};

/**
 * Get Org Splits All
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllApiV1MoneyOrgSplitsAllPost = <ThrowOnError extends boolean = false>(options: Options<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponses, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/all',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Org Splits All Raw Sql
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPost = <ThrowOnError extends boolean = false>(options: Options<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponses, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/all-raw-sql',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Org Splits All Core
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePost = <ThrowOnError extends boolean = false>(options: Options<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponses, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostErrors, ThrowOnError>({
        url: '/api/v1/money/org-splits/all-core',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Job
 */
export const getJobJobsDatesJobIdGet = <ThrowOnError extends boolean = false>(options: Options<GetJobJobsDatesJobIdGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetJobJobsDatesJobIdGetResponses, GetJobJobsDatesJobIdGetErrors, ThrowOnError>({
        url: '/jobs-dates/{job_id}',
        ...options
    });
};

/**
 * Health Check
 */
export const healthCheckHealthGet = <ThrowOnError extends boolean = false>(options?: Options<HealthCheckHealthGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<HealthCheckHealthGetResponses, unknown, ThrowOnError>({
        url: '/health',
        ...options
    });
};