// This file is auto-generated by @hey-api/openapi-ts

/**
 * AddressCreateTypes
 */
export type AddressCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * City
     */
    city: string;
    /**
     * Country
     */
    country?: string;
    /**
     * Area1
     */
    area1?: string | null;
    /**
     * Area2
     */
    area2?: string | null;
    /**
     * Street
     */
    street?: string | null;
    /**
     * Postal Code
     */
    postal_code?: string | null;
    /**
     * Lon
     */
    lon?: number | string | null;
    /**
     * Lat
     */
    lat?: number | string | null;
    /**
     * Location
     */
    location?: string | null;
    /**
     * Street No
     */
    street_no?: string | null;
    /**
     * Local No
     */
    local_no?: string | null;
};

/**
 * AddressDataColumnsTypes
 */
export type AddressDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<AddressDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * AddressDataTypes
 */
export type AddressDataTypes = {
    /**
     * Data
     */
    data: Array<AddressDisplayTypes>;
    pagination: Pagination;
};

/**
 * AddressDisplayColumnsTypes
 */
export type AddressDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Country
     */
    country?: string | null;
    /**
     * Area1
     */
    area1?: string | null;
    /**
     * Area2
     */
    area2?: string | null;
    /**
     * Street
     */
    street?: string | null;
    /**
     * City
     */
    city?: string | null;
    /**
     * Postal Code
     */
    postal_code?: string | null;
    /**
     * Lon
     */
    lon?: string | null;
    /**
     * Lat
     */
    lat?: string | null;
    /**
     * Location
     */
    location?: string | null;
    /**
     * Street No
     */
    street_no?: string | null;
    /**
     * Local No
     */
    local_no?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * AddressDisplayTypes
 */
export type AddressDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Country
     */
    country?: string;
    /**
     * Area1
     */
    area1?: string | null;
    /**
     * Area2
     */
    area2?: string | null;
    /**
     * Street
     */
    street?: string | null;
    /**
     * City
     */
    city: string;
    /**
     * Postal Code
     */
    postal_code?: string | null;
    /**
     * Lon
     */
    lon?: string | null;
    /**
     * Lat
     */
    lat?: string | null;
    /**
     * Location
     */
    location?: string | null;
    /**
     * Street No
     */
    street_no?: string | null;
    /**
     * Local No
     */
    local_no?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * AddressUpdateTypes
 */
export type AddressUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Country
     */
    country?: string | null;
    /**
     * Area1
     */
    area1?: string | null;
    /**
     * Area2
     */
    area2?: string | null;
    /**
     * Street
     */
    street?: string | null;
    /**
     * City
     */
    city?: string | null;
    /**
     * Postal Code
     */
    postal_code?: string | null;
    /**
     * Lon
     */
    lon?: number | string | null;
    /**
     * Lat
     */
    lat?: number | string | null;
    /**
     * Location
     */
    location?: string | null;
    /**
     * Street No
     */
    street_no?: string | null;
    /**
     * Local No
     */
    local_no?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * ClientOptionsTypes
 */
export type ClientOptionsTypes = {
    /**
     * Columnvisibility
     */
    columnVisibility?: {
        [key: string]: boolean;
    } | null;
    /**
     * Columnorder
     */
    columnOrder?: Array<string> | null;
    columnPinning?: ColumnPinning | null;
    /**
     * Columnfilters
     */
    columnFilters?: Array<{
        [key: string]: unknown;
    }> | null;
    /**
     * Globalfilter
     */
    globalFilter?: string | null;
    /**
     * Sorting
     */
    sorting?: Array<Sorting> | null;
    /**
     * Expanded
     */
    expanded?: {
        [key: string]: boolean;
    } | null;
    /**
     * Grouping
     */
    grouping?: Array<string> | null;
    /**
     * Columnsizing
     */
    columnSizing?: {
        [key: string]: number;
    } | null;
    columnSizingInfo?: SizingInfo | null;
    pagination?: ClientPagination | null;
    /**
     * Rowselection
     */
    rowSelection?: {
        [key: string]: boolean;
    } | null;
    rowPinning?: RowPinning | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Date Column
     */
    date_column?: string | null;
};

/**
 * ClientPagination
 */
export type ClientPagination = {
    /**
     * Pageindex
     */
    pageIndex: number;
    /**
     * Pagesize
     */
    pageSize: number;
};

/**
 * ColumnPinning
 */
export type ColumnPinning = {
    /**
     * Left
     */
    left?: Array<string>;
    /**
     * Right
     */
    right?: Array<string>;
};

/**
 * Columns
 */
export type Columns = {
    /**
     * Id
     */
    id: string;
    /**
     * Term
     */
    term: string;
};

/**
 * CommentCreateTypes
 */
export type CommentCreateTypes = {
    /**
     * Created By
     */
    created_by: number;
    /**
     * Content
     */
    content: string;
    /**
     * Lang
     */
    lang: string;
    /**
     * Parent Table
     */
    parent_table: string;
    /**
     * Parent Id
     */
    parent_id: number;
};

/**
 * CommentDataColumnsTypes
 */
export type CommentDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<CommentDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * CommentDataTypes
 */
export type CommentDataTypes = {
    /**
     * Data
     */
    data: Array<CommentDisplayTypes>;
    pagination: Pagination;
};

/**
 * CommentDisplayColumnsTypes
 */
export type CommentDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Content
     */
    content?: string | null;
    /**
     * Creator Display Name
     */
    creator_display_name?: string | null;
};

/**
 * CommentDisplayTypes
 */
export type CommentDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Lang
     */
    lang: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Parent Table
     */
    parent_table: string;
    /**
     * Parent Id
     */
    parent_id: number;
    /**
     * Content
     */
    content: string;
    /**
     * Creator Display Name
     */
    creator_display_name?: string | null;
};

/**
 * CommentUpdateTypes
 */
export type CommentUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by: number;
    /**
     * Content
     */
    content?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
};

/**
 * ContactCreateTypes
 */
export type ContactCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * ContactDataColumnsTypes
 */
export type ContactDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<ContactDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * ContactDataTypes
 */
export type ContactDataTypes = {
    /**
     * Data
     */
    data: Array<ContactDisplayTypes>;
    pagination: Pagination;
};

/**
 * ContactDisplayColumnsTypes
 */
export type ContactDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * ContactDisplayTypes
 */
export type ContactDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * ContactUpdateTypes
 */
export type ContactUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * ContrahentBasicTypes
 */
export type ContrahentBasicTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
};

/**
 * ContrahentCreateTypes
 */
export type ContrahentCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Region
     */
    region?: string;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * User Profile Id
     */
    user_profile_id?: number | null;
    /**
     * Keywords
     */
    keywords?: Array<string>;
    /**
     * Types
     */
    types: Array<TypeDisplay>;
    /**
     * Accounts Ids
     */
    accounts_ids?: Array<number> | null;
};

/**
 * ContrahentDataColumnsTypes
 */
export type ContrahentDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<ContrahentDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * ContrahentDataTypes
 */
export type ContrahentDataTypes = {
    /**
     * Data
     */
    data: Array<ContrahentDisplayTypes>;
    pagination: Pagination;
};

/**
 * ContrahentDisplayColumnsTypes
 */
export type ContrahentDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * User Profile Id
     */
    user_profile_id?: number | null;
    /**
     * Keywords
     */
    keywords?: Array<string> | null;
    /**
     * Types
     */
    types?: Array<TypeDisplay> | null;
    /**
     * Accounts Ids
     */
    accounts_ids?: Array<number> | null;
    /**
     * Orgs
     */
    orgs?: Array<OrgDisplayTypes> | null;
    /**
     * Profile
     */
    profile?: unknown | null;
};

/**
 * ContrahentDisplayTypes
 */
export type ContrahentDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Region
     */
    region?: string;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * User Profile Id
     */
    user_profile_id?: number | null;
    /**
     * Keywords
     */
    keywords?: Array<string>;
    /**
     * Accounts Ids
     */
    accounts_ids?: Array<number>;
};

/**
 * ContrahentUpdateTypes
 */
export type ContrahentUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * User Profile Id
     */
    user_profile_id?: number | null;
    /**
     * Keywords
     */
    keywords?: Array<string> | null;
    /**
     * Contrahent Type Ids
     */
    contrahent_type_ids?: Array<number> | null;
    /**
     * Accounts Ids
     */
    accounts_ids?: Array<number> | null;
};

/**
 * ContrahentWithRelationsDisplayTypes
 */
export type ContrahentWithRelationsDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Region
     */
    region?: string;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * User Profile Id
     */
    user_profile_id?: number | null;
    /**
     * Keywords
     */
    keywords?: Array<string>;
    /**
     * Types
     */
    types: Array<TypeDisplay>;
    /**
     * Accounts Ids
     */
    accounts_ids?: Array<number>;
    /**
     * Orgs
     */
    orgs?: Array<OrgDisplayTypes>;
    /**
     * Profile
     */
    profile?: unknown | null;
};

/**
 * ContrahentWithVehiclesDisplayTypes
 */
export type ContrahentWithVehiclesDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Region
     */
    region?: string;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * User Profile Id
     */
    user_profile_id?: number | null;
    /**
     * Keywords
     */
    keywords?: Array<string>;
    /**
     * Accounts Ids
     */
    accounts_ids?: Array<number>;
    /**
     * Vehicles
     */
    vehicles?: Array<ObjectDisplayTypes>;
};

/**
 * DashboardModules
 */
export type DashboardModules = {
    /**
     * Proposals
     */
    proposals?: boolean;
    /**
     * Jobs
     */
    jobs?: boolean;
    /**
     * Trips
     */
    trips?: boolean;
    /**
     * Transactions
     */
    transactions?: boolean;
    /**
     * Problems
     */
    problems?: boolean;
    /**
     * Notices
     */
    notices?: boolean;
    /**
     * Contrahent Jobs
     */
    contrahent_jobs?: boolean;
    /**
     * Contrahent Transactions
     */
    contrahent_transactions?: boolean;
};

/**
 * DocCreateTypes
 */
export type DocCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    /**
     * Text
     */
    text?: string | null;
    doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    /**
     * Is Template
     */
    is_template: boolean;
    /**
     * Is Draft
     */
    is_draft: boolean;
    /**
     * Pdf Id
     */
    pdf_id?: number | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Pages
     */
    pages?: Array<DocPageCreateTypes> | null;
    /**
     * Parts Top
     */
    parts_top?: Array<DocPartCreateTypes> | null;
    /**
     * Parts Header
     */
    parts_header?: Array<DocPartCreateTypes> | null;
    /**
     * Parts Pre
     */
    parts_pre?: Array<DocPartCreateTypes> | null;
    /**
     * Parts Post
     */
    parts_post?: Array<DocPartCreateTypes> | null;
    /**
     * Parts Footer
     */
    parts_footer?: Array<DocPartCreateTypes> | null;
    /**
     * Parts Bottom
     */
    parts_bottom?: Array<DocPartCreateTypes> | null;
};

/**
 * DocDataColumnsTypes
 */
export type DocDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<DocDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * DocDataTypes
 */
export type DocDataTypes = {
    /**
     * Data
     */
    data: Array<DocDisplayTypes>;
    pagination: Pagination;
};

/**
 * DocDisplayColumnsTypes
 */
export type DocDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    /**
     * Is Template
     */
    is_template?: boolean | null;
    /**
     * Is Draft
     */
    is_draft: boolean;
    /**
     * Pages
     */
    pages?: Array<DocPageDisplayTypes> | null;
    /**
     * Parts Top
     */
    parts_top?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Header
     */
    parts_header?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Pre
     */
    parts_pre?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Post
     */
    parts_post?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Footer
     */
    parts_footer?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Bottom
     */
    parts_bottom?: Array<DocPartDisplayTypes> | null;
};

/**
 * DocDisplayTypes
 */
export type DocDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    /**
     * Is Template
     */
    is_template?: boolean | null;
    /**
     * Is Draft
     */
    is_draft: boolean;
    /**
     * Pages
     */
    pages?: Array<DocPageDisplayTypes> | null;
    /**
     * Parts Top
     */
    parts_top?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Header
     */
    parts_header?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Pre
     */
    parts_pre?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Post
     */
    parts_post?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Footer
     */
    parts_footer?: Array<DocPartDisplayTypes> | null;
    /**
     * Parts Bottom
     */
    parts_bottom?: Array<DocPartDisplayTypes> | null;
};

/**
 * DocPageCreateTypes
 */
export type DocPageCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Org Id
     */
    org_id?: number;
    /**
     * Page Number
     */
    page_number?: number;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    /**
     * Content
     */
    content?: Array<DocPartCreateTypes> | null;
    /**
     * Is Template
     */
    is_template: boolean;
};

/**
 * DocPageDataColumnsTypes
 */
export type DocPageDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<DocPageDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * DocPageDataTypes
 */
export type DocPageDataTypes = {
    /**
     * Data
     */
    data: Array<DocPageDisplayTypes>;
    pagination: Pagination;
};

/**
 * DocPageDisplayColumnsTypes
 */
export type DocPageDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    /**
     * Content
     */
    content?: Array<DocPartDisplayTypes> | null;
    /**
     * Page Number
     */
    page_number?: number | null;
    /**
     * Is Template
     */
    is_template?: boolean | null;
};

/**
 * DocPageDisplayTypes
 */
export type DocPageDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    /**
     * Content
     */
    content?: Array<DocPartDisplayTypes> | null;
    /**
     * Page Number
     */
    page_number: number;
    /**
     * Is Template
     */
    is_template?: boolean | null;
};

/**
 * DocPageUpdateTypes
 */
export type DocPageUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Content
     */
    content?: Array<DocPartCreateTypes> | null;
    /**
     * Page Number
     */
    page_number?: number | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
};

/**
 * DocPartCreateTypes
 */
export type DocPartCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocPartsSchemasDocTypeEnum | null;
    type: PartTypeEnum;
    /**
     * Is Template
     */
    is_template?: boolean | null;
};

/**
 * DocPartDataColumnsTypes
 */
export type DocPartDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<DocPartDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * DocPartDataTypes
 */
export type DocPartDataTypes = {
    /**
     * Data
     */
    data: Array<DocPartDisplayTypes>;
    pagination: Pagination;
};

/**
 * DocPartDisplayColumnsTypes
 */
export type DocPartDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocPartsSchemasDocTypeEnum | null;
    type?: PartTypeEnum | null;
    /**
     * Is Template
     */
    is_template?: boolean | null;
};

/**
 * DocPartDisplayTypes
 */
export type DocPartDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocPartsSchemasDocTypeEnum | null;
    type?: PartTypeEnum | null;
    /**
     * Is Template
     */
    is_template?: boolean | null;
};

/**
 * DocPartUpdateTypes
 */
export type DocPartUpdateTypes = {
    /**
     * Id
     */
    id: number | string;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocPartsSchemasDocTypeEnum | null;
    /**
     * Org Id
     */
    org_id: number;
    type: PartTypeEnum;
    /**
     * Is Template
     */
    is_template: boolean;
};

/**
 * DocTableTypes
 */
export type DocTableTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    /**
     * Type
     */
    type?: string;
    /**
     * Is Template
     */
    is_template?: boolean | null;
};

/**
 * DocTemplatesTypes
 */
export type DocTemplatesTypes = {
    /**
     * Doc Templates
     */
    doc_templates: Array<DocDisplayTypes>;
    /**
     * Parts Top
     */
    parts_top: Array<DocPartDisplayTypes>;
    /**
     * Parts Header
     */
    parts_header: Array<DocPartDisplayTypes>;
    /**
     * Parts Pre
     */
    parts_pre: Array<DocPartDisplayTypes>;
    /**
     * Parts Content Text
     */
    parts_content_text: Array<DocPartDisplayTypes>;
    /**
     * Parts Content Table
     */
    parts_content_table: Array<DocPartDisplayTypes>;
    /**
     * Parts Post
     */
    parts_post: Array<DocPartDisplayTypes>;
    /**
     * Parts Footer
     */
    parts_footer: Array<DocPartDisplayTypes>;
    /**
     * Parts Bottom
     */
    parts_bottom: Array<DocPartDisplayTypes>;
};

/**
 * DocUpdateTypes
 */
export type DocUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Mjml
     */
    mjml?: {
        [key: string]: unknown;
    } | null;
    /**
     * Text
     */
    text?: string | null;
    doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    /**
     * Is Draft
     */
    is_draft?: boolean | null;
    /**
     * Pdf Id
     */
    pdf_id?: number | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Pages
     */
    pages?: Array<DocPageUpdateTypes> | null;
    /**
     * Parts Top
     */
    parts_top?: Array<DocPartUpdateTypes> | null;
    /**
     * Parts Header
     */
    parts_header?: Array<DocPartUpdateTypes> | null;
    /**
     * Parts Pre
     */
    parts_pre?: Array<DocPartUpdateTypes> | null;
    /**
     * Parts Post
     */
    parts_post?: Array<DocPartUpdateTypes> | null;
    /**
     * Parts Footer
     */
    parts_footer?: Array<DocPartUpdateTypes> | null;
    /**
     * Parts Bottom
     */
    parts_bottom?: Array<DocPartUpdateTypes> | null;
};

/**
 * DropboxData
 */
export type DropboxData = {
    /**
     * Access Token
     */
    access_token?: string | null;
    /**
     * Account Id
     */
    account_id?: string | null;
    /**
     * Folder
     */
    folder?: string | null;
};

/**
 * EmailCreateTypes
 */
export type EmailCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Bcc
     */
    bcc?: string | null;
    /**
     * Url
     */
    url?: string | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Type Id
     */
    type_id: number;
    /**
     * Subject
     */
    subject: string;
    /**
     * Text
     */
    text: string;
    /**
     * Html
     */
    html: string;
    /**
     * Is Sent
     */
    is_sent?: boolean;
};

/**
 * EmailDataColumnsTypes
 */
export type EmailDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<EmailDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * EmailDataTypes
 */
export type EmailDataTypes = {
    /**
     * Data
     */
    data: Array<EmailDisplayTypes>;
    pagination: Pagination;
};

/**
 * EmailDisplayColumnsTypes
 */
export type EmailDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Bcc
     */
    bcc?: string | null;
    /**
     * Url
     */
    url?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Type Id
     */
    type_id?: number | null;
    /**
     * Subject
     */
    subject?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Is Sent
     */
    is_sent?: boolean | null;
};

/**
 * EmailDisplayTypes
 */
export type EmailDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Bcc
     */
    bcc?: string | null;
    /**
     * Url
     */
    url?: string | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Type Id
     */
    type_id: number;
    /**
     * Subject
     */
    subject: string;
    /**
     * Text
     */
    text: string;
    /**
     * Html
     */
    html: string;
    /**
     * Is Sent
     */
    is_sent?: boolean;
};

/**
 * EmailTypeCreateTypes
 */
export type EmailTypeCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Email Type
     */
    email_type: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Is Optional
     */
    is_optional: boolean;
    /**
     * Org Id
     */
    org_id: number;
};

/**
 * EmailTypeDataColumnsTypes
 */
export type EmailTypeDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<EmailTypeDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * EmailTypeDataTypes
 */
export type EmailTypeDataTypes = {
    /**
     * Data
     */
    data: Array<EmailTypeDisplayTypes>;
    pagination: Pagination;
};

/**
 * EmailTypeDisplayColumnsTypes
 */
export type EmailTypeDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Email Type
     */
    email_type?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Is Optional
     */
    is_optional?: boolean | null;
};

/**
 * EmailTypeDisplayTypes
 */
export type EmailTypeDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Email Type
     */
    email_type: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Is Optional
     */
    is_optional: boolean;
};

/**
 * EmailTypeUpdateTypes
 */
export type EmailTypeUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Email Type
     */
    email_type?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Is Optional
     */
    is_optional?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
};

/**
 * EmailUpdateTypes
 */
export type EmailUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Bcc
     */
    bcc?: string | null;
    /**
     * Url
     */
    url?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Type Id
     */
    type_id?: number | null;
    /**
     * Subject
     */
    subject?: string | null;
    /**
     * Text
     */
    text?: string | null;
    /**
     * Html
     */
    html?: string | null;
    /**
     * Is Sent
     */
    is_sent?: boolean | null;
};

/**
 * FileStatus
 */
export type FileStatus = 'DRAFT' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';

/**
 * FilesCreateTypes
 */
export type FilesCreateTypes = {
    /**
     * Created By
     */
    created_by?: number | string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * File Name
     */
    file_name: string;
    /**
     * Th File Name
     */
    th_file_name?: string | null;
    /**
     * File Id
     */
    file_id: string;
    /**
     * Th File Id
     */
    th_file_id?: string | null;
    /**
     * Url
     */
    url: string;
    /**
     * Th Url
     */
    th_url?: string | null;
    /**
     * Th Data
     */
    th_data?: (Blob | File) | null;
    /**
     * S3 User Id
     */
    s3_user_id?: string | null;
    /**
     * S3 Bucket
     */
    s3_bucket?: string | null;
    /**
     * Is Object Extra
     */
    is_object_extra?: boolean | string;
    /**
     * Is Doc
     */
    is_doc?: boolean | string;
    /**
     * Generated Pdf Type
     */
    generated_pdf_type?: string | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | string | null;
    /**
     * Org Id
     */
    org_id: number | string;
    /**
     * Mimetype
     */
    mimetype?: string | null;
};

/**
 * FilesDataColumnsTypes
 */
export type FilesDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<FilesDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * FilesDataTypes
 */
export type FilesDataTypes = {
    /**
     * Data
     */
    data: Array<FilesDisplayTypes>;
    pagination: Pagination;
};

/**
 * FilesDisplayColumnsTypes
 */
export type FilesDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status?: FileStatus | null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * FilesDisplayTypes
 */
export type FilesDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * File Name
     */
    file_name: string;
    /**
     * Th File Name
     */
    th_file_name?: string | null;
    /**
     * File Id
     */
    file_id?: string | null;
    /**
     * Th File Id
     */
    th_file_id?: string | null;
    /**
     * Url
     */
    url?: string | null;
    /**
     * Th Url
     */
    th_url?: string | null;
    /**
     * Th Data
     */
    th_data?: string | null;
    /**
     * S3 User Id
     */
    s3_user_id?: string | null;
    /**
     * S3 Bucket
     */
    s3_bucket?: string | null;
    /**
     * Is Object Extra
     */
    is_object_extra?: boolean;
    /**
     * Is Doc
     */
    is_doc?: boolean;
    /**
     * Generated Pdf Type
     */
    generated_pdf_type?: string | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Mimetype
     */
    mimetype?: string | null;
};

/**
 * FilesUpdateTypes
 */
export type FilesUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * File Name
     */
    file_name?: string | null;
    /**
     * Th File Name
     */
    th_file_name?: string | null;
    /**
     * File Id
     */
    file_id?: string | null;
    /**
     * Th File Id
     */
    th_file_id?: string | null;
    /**
     * Url
     */
    url?: string | null;
    /**
     * Th Url
     */
    th_url?: string | null;
    /**
     * Th Data
     */
    th_data?: (Blob | File) | null;
    /**
     * S3 User Id
     */
    s3_user_id?: string | null;
    /**
     * S3 Bucket
     */
    s3_bucket?: string | null;
    /**
     * Is Object Extra
     */
    is_object_extra?: boolean | null;
    /**
     * Is Doc
     */
    is_doc?: boolean | null;
    /**
     * Generated Pdf Type
     */
    generated_pdf_type?: string | null;
    /**
     * Parent Table
     */
    parent_table?: string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Mimetype
     */
    mimetype?: string | null;
};

/**
 * GenericDataResponse
 */
export type GenericDataResponse = {
    /**
     * Data
     */
    data: Array<{
        [key: string]: unknown;
    }>;
    pagination: Pagination;
    /**
     * Timing
     */
    timing: {
        [key: string]: number;
    };
};

/**
 * HTTPValidationError
 */
export type HttpValidationError = {
    /**
     * Detail
     */
    detail?: Array<ValidationError>;
};

/**
 * JobCreateTypes
 */
export type JobCreateTypes = {
    /**
     * Created By
     */
    created_by: number;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Proposal Id
     */
    proposal_id?: number | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status: JobStatus;
    /**
     * Type
     */
    type: string;
    /**
     * Is Public
     */
    is_public: boolean;
    /**
     * Start Date
     */
    start_date: string;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Objects
     */
    objects?: Array<number> | null;
    /**
     * Is Schedule
     */
    is_schedule: boolean;
    /**
     * Is Regular
     */
    is_regular: boolean;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobDataColumnsTypes
 */
export type JobDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<JobDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * JobDataTypes
 */
export type JobDataTypes = {
    /**
     * Data
     */
    data: Array<JobDisplayTypes>;
    pagination: Pagination;
};

/**
 * JobDisplayColumnsTypes
 */
export type JobDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status?: JobStatus | null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobDisplayTypes
 */
export type JobDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status: JobStatus;
    /**
     * Type
     */
    type: string;
    /**
     * Is Public
     */
    is_public?: boolean;
    /**
     * Start Date
     */
    start_date: string;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean;
    /**
     * Is Regular
     */
    is_regular?: boolean;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string>;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobOptions
 */
export type JobOptionsInput = {
    /**
     * Calendar Objects
     */
    calendar_objects?: Array<ObjectsList>;
    /**
     * Calendar Period
     */
    calendar_period?: Array<number>;
    /**
     * Job Start
     */
    job_start?: string;
    /**
     * Job End
     */
    job_end?: string;
};

/**
 * JobOptions
 */
export type JobOptionsOutput = {
    /**
     * Calendar Objects
     */
    calendar_objects?: Array<ObjectsList>;
    /**
     * Calendar Period
     */
    calendar_period?: Array<number>;
    /**
     * Job Start
     */
    job_start?: string;
    /**
     * Job End
     */
    job_end?: string;
};

/**
 * JobStatus
 */
export type JobStatus = 'DRAFT' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';

/**
 * JobTaskCreateTypes
 */
export type JobTaskCreateTypes = {
    /**
     * Created By
     */
    created_by: number;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Proposal Id
     */
    proposal_id?: number | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status?: JobTaskStatus;
    /**
     * Type
     */
    type: string;
    /**
     * Is Public
     */
    is_public?: boolean;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Objects
     */
    objects?: Array<number> | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean;
    /**
     * Is Regular
     */
    is_regular?: boolean;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string>;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobTaskDataColumnsTypes
 */
export type JobTaskDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<JobTaskDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * JobTaskDataTypes
 */
export type JobTaskDataTypes = {
    /**
     * Data
     */
    data: Array<JobTaskDisplayTypes>;
    pagination: Pagination;
};

/**
 * JobTaskDisplayColumnsTypes
 */
export type JobTaskDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status?: JobTaskStatus | null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobTaskDisplayTypes
 */
export type JobTaskDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status: JobTaskStatus;
    /**
     * Type
     */
    type: string;
    /**
     * Is Public
     */
    is_public?: boolean;
    /**
     * Start Date
     */
    start_date: string;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean;
    /**
     * Is Regular
     */
    is_regular?: boolean;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string>;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobTaskStatus
 */
export type JobTaskStatus = 'DRAFT' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';

/**
 * JobTaskUpdateTypes
 */
export type JobTaskUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status?: JobTaskStatus | null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JobUpdateTypes
 */
export type JobUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    status?: JobStatus | null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * JsonMetadata
 */
export type JsonMetadataInput = {
    storage?: StorageData | null;
};

/**
 * JsonMetadata
 */
export type JsonMetadataOutput = {
    storage?: StorageData | null;
};

/**
 * KeycloakUserTypes
 */
export type KeycloakUserTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * First Name
     */
    first_name?: string | null;
    /**
     * Last Name
     */
    last_name?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Curr Profile Id
     */
    curr_profile_id?: number | null;
    /**
     * Curr Org Id
     */
    curr_org_id?: number | null;
    /**
     * Keycloak Id
     */
    keycloak_id?: string | null;
    /**
     * Profiles
     */
    profiles?: Array<ProfileDisplayTypes> | null;
};

/**
 * LinkContrahentToUserTypes
 */
export type LinkContrahentToUserTypes = {
    /**
     * User Id
     */
    user_id: number;
    /**
     * Contrahent Id
     */
    contrahent_id: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Lang
     */
    lang: string;
};

/**
 * MemberOptions
 */
export type MemberOptionsInput = {
    /**
     * Is Voting
     */
    is_voting?: boolean;
    /**
     * Shares
     */
    shares?: number | string;
    /**
     * Znaczaca Operacja Limit
     */
    znaczaca_operacja_limit?: number | string;
    /**
     * Member Object Ids
     */
    member_object_ids?: Array<number>;
};

/**
 * MemberOptions
 */
export type MemberOptionsOutput = {
    /**
     * Is Voting
     */
    is_voting?: boolean;
    /**
     * Shares
     */
    shares?: number;
    /**
     * Znaczaca Operacja Limit
     */
    znaczaca_operacja_limit?: number;
    /**
     * Member Object Ids
     */
    member_object_ids?: Array<number>;
};

/**
 * ObjectCreateTypes
 */
export type ObjectCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Object Type
     */
    object_type: string;
    /**
     * Object Type Id
     */
    object_type_id?: number | null;
    /**
     * Object Type Group
     */
    object_type_group?: string | null;
    /**
     * Width
     */
    width?: number | string | null;
    /**
     * Length
     */
    length?: number | string | null;
    /**
     * Height
     */
    height?: number | string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Tree Level
     */
    tree_level: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Surface
     */
    surface?: number | string | null;
    /**
     * Volume
     */
    volume?: number | string | null;
    /**
     * Circumference
     */
    circumference?: number | string | null;
    /**
     * Is Branch
     */
    is_branch?: boolean;
};

/**
 * ObjectDataColumnsTypes
 */
export type ObjectDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<ObjectDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * ObjectDataTypes
 */
export type ObjectDataTypes = {
    /**
     * Data
     */
    data: Array<ObjectDisplayTypes>;
    pagination: Pagination;
};

/**
 * ObjectDisplayColumnsTypes
 */
export type ObjectDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
};

/**
 * ObjectDisplayTypes
 */
export type ObjectDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Object Type
     */
    object_type: string;
    /**
     * Vehicle Owner Id
     */
    vehicle_owner_id?: number | null;
    /**
     * Object Type Id
     */
    object_type_id?: number | null;
    /**
     * Object Type Group
     */
    object_type_group?: string | null;
    /**
     * Width
     */
    width?: number | null;
    /**
     * Length
     */
    length?: number | null;
    /**
     * Height
     */
    height?: number | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Tree Level
     */
    tree_level: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Surface
     */
    surface?: number | null;
    /**
     * Volume
     */
    volume?: number | null;
    /**
     * Circumference
     */
    circumference?: number | null;
    /**
     * Is Branch
     */
    is_branch?: boolean;
    /**
     * Documents
     */
    documents?: Array<FilesDisplayTypes>;
    /**
     * Photos
     */
    photos?: Array<FilesDisplayTypes>;
    /**
     * Other Files
     */
    other_files?: Array<FilesDisplayTypes>;
    /**
     * Systems
     */
    systems?: Array<SystemType>;
    /**
     * Path
     */
    path?: Array<string>;
};

/**
 * ObjectSystemCreateTypes
 */
export type ObjectSystemCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Object Id
     */
    object_id: number;
    /**
     * System Type
     */
    system_type: string;
};

/**
 * ObjectSystemDataColumnsTypes
 */
export type ObjectSystemDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<ObjectSystemDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * ObjectSystemDataTypes
 */
export type ObjectSystemDataTypes = {
    /**
     * Data
     */
    data: Array<ObjectSystemDisplayTypes>;
    pagination: Pagination;
};

/**
 * ObjectSystemDisplayColumnsTypes
 */
export type ObjectSystemDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Object Id
     */
    object_id?: number | null;
    /**
     * System Type
     */
    system_type?: string | null;
};

/**
 * ObjectSystemDisplayTypes
 */
export type ObjectSystemDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Object Id
     */
    object_id: number;
    /**
     * System Type
     */
    system_type: string;
};

/**
 * ObjectSystemUpdateTypes
 */
export type ObjectSystemUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Object Id
     */
    object_id?: number | null;
    /**
     * System Type
     */
    system_type?: string | null;
};

/**
 * ObjectUpdateTypes
 */
export type ObjectUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Object Type
     */
    object_type?: string | null;
    /**
     * Object Type Id
     */
    object_type_id?: number | null;
    /**
     * Object Type Group
     */
    object_type_group?: string | null;
    /**
     * Width
     */
    width?: number | string | null;
    /**
     * Length
     */
    length?: number | string | null;
    /**
     * Height
     */
    height?: number | string | null;
    /**
     * Parent Id
     */
    parent_id?: number | null;
    /**
     * Tree Level
     */
    tree_level?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Surface
     */
    surface?: number | string | null;
    /**
     * Volume
     */
    volume?: number | string | null;
    /**
     * Circumference
     */
    circumference?: number | string | null;
    /**
     * Is Branch
     */
    is_branch?: boolean | null;
};

/**
 * ObjectsList
 */
export type ObjectsList = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
};

/**
 * Options
 */
export type OptionsInput = {
    dashboard_modules?: DashboardModules;
    jobs?: JobOptionsInput;
    member?: MemberOptionsInput;
    /**
     * Variants
     */
    variants?: Array<VariantInput>;
};

/**
 * Options
 */
export type OptionsOutput = {
    dashboard_modules?: DashboardModules;
    jobs?: JobOptionsOutput;
    member?: MemberOptionsOutput;
    /**
     * Variants
     */
    variants?: Array<VariantOutput>;
};

/**
 * OrderColumn
 */
export type OrderColumn = {
    /**
     * Column Name
     */
    column_name: string;
    /**
     * Direction
     */
    direction: string;
};

/**
 * OrgCreateTypes
 */
export type OrgCreateTypes = {
    /**
     * Created By
     */
    created_by: number;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string;
    json_metadata?: JsonMetadataInput | null;
    /**
     * Is Public
     */
    is_public?: boolean;
    /**
     * Address Id
     */
    address_id?: number | null;
    /**
     * Is Formal
     */
    is_formal?: boolean;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
    /**
     * Max Img Width
     */
    max_img_width?: number;
    /**
     * Total Shares
     */
    total_shares?: number;
    /**
     * Accounts Set Id
     */
    accounts_set_id?: number | null;
    /**
     * Voting Days
     */
    voting_days?: number;
    /**
     * Members By Admin
     */
    members_by_admin?: boolean;
    /**
     * Curr Acc Period Id
     */
    curr_acc_period_id?: number | null;
    /**
     * Curr Acc Period Name
     */
    curr_acc_period_name?: string | null;
    /**
     * Curr Acc Period Start
     */
    curr_acc_period_start?: string | null;
    /**
     * Curr Acc Period End
     */
    curr_acc_period_end?: string | null;
};

/**
 * OrgDataColumnsTypes
 */
export type OrgDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<OrgDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * OrgDataTypes
 */
export type OrgDataTypes = {
    /**
     * Data
     */
    data: Array<OrgDisplayTypes>;
    pagination: Pagination;
};

/**
 * OrgDisplayColumnsTypes
 */
export type OrgDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Lang
     */
    lang?: string | null;
    json_metadata?: JsonMetadataOutput | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Address Id
     */
    address_id?: number | null;
    /**
     * Is Formal
     */
    is_formal?: boolean | null;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
    /**
     * Max Img Width
     */
    max_img_width?: number | null;
    /**
     * Total Shares
     */
    total_shares?: number | null;
    /**
     * Accounts Set Id
     */
    accounts_set_id?: number | null;
    /**
     * Voting Days
     */
    voting_days?: number | null;
    /**
     * Members By Admin
     */
    members_by_admin?: boolean | null;
    /**
     * Curr Acc Period Id
     */
    curr_acc_period_id?: number | null;
    /**
     * Curr Acc Period Name
     */
    curr_acc_period_name?: string | null;
    /**
     * Curr Acc Period Start
     */
    curr_acc_period_start?: string | null;
    /**
     * Curr Acc Period End
     */
    curr_acc_period_end?: string | null;
};

/**
 * OrgDisplayTypes
 */
export type OrgDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Lang
     */
    lang: string;
    json_metadata?: JsonMetadataOutput | null;
    /**
     * Name
     */
    name: string;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean;
    /**
     * Address Id
     */
    address_id?: number | null;
    /**
     * Is Formal
     */
    is_formal?: boolean;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
    /**
     * Max Img Width
     */
    max_img_width?: number;
    /**
     * Total Shares
     */
    total_shares?: number;
    /**
     * Accounts Set Id
     */
    accounts_set_id?: number | null;
    /**
     * Voting Days
     */
    voting_days?: number;
    /**
     * Members By Admin
     */
    members_by_admin?: boolean;
    /**
     * Curr Acc Period Id
     */
    curr_acc_period_id?: number | null;
    /**
     * Curr Acc Period Name
     */
    curr_acc_period_name?: string | null;
    /**
     * Curr Acc Period Start
     */
    curr_acc_period_start?: string | null;
    /**
     * Curr Acc Period End
     */
    curr_acc_period_end?: string | null;
};

/**
 * OrgSplitCreateTypes
 */
export type OrgSplitCreateTypes = {
    /**
     * Created By
     */
    created_by: number;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Account Id
     */
    account_id: number;
    /**
     * Transaction Id
     */
    transaction_id?: number | null;
    /**
     * Debit
     */
    debit?: number | null;
    /**
     * Credit
     */
    credit?: number | null;
    /**
     * Due Date
     */
    due_date?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Date
     */
    date?: string | null;
    /**
     * Is Debit Minus
     */
    is_debit_minus?: boolean;
    /**
     * Is Schedule
     */
    is_schedule?: boolean;
    /**
     * Is Saved
     */
    is_saved?: boolean;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Set Id
     */
    set_id?: number | null;
    /**
     * Set Item Id
     */
    set_item_id?: number | null;
};

/**
 * OrgSplitDataColumnsTypes
 */
export type OrgSplitDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<OrgSplitDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * OrgSplitDataTypes
 */
export type OrgSplitDataTypes = {
    /**
     * Data
     */
    data: Array<OrgSplitDisplayTypes>;
    pagination: Pagination;
};

/**
 * OrgSplitDisplayColumnsTypes
 */
export type OrgSplitDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Account Id
     */
    account_id?: number | null;
    /**
     * Transaction Id
     */
    transaction_id?: number | null;
    /**
     * Debit
     */
    debit?: number | null;
    /**
     * Credit
     */
    credit?: number | null;
    /**
     * Due Date
     */
    due_date?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Date
     */
    date?: string | null;
    /**
     * Is Debit Minus
     */
    is_debit_minus?: boolean | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Saved
     */
    is_saved?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Set Id
     */
    set_id?: number | null;
    /**
     * Set Item Id
     */
    set_item_id?: number | null;
};

/**
 * OrgSplitDisplayTypes
 */
export type OrgSplitDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by: number | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description: string | null;
    /**
     * Account Id
     */
    account_id: number;
    /**
     * Transaction Id
     */
    transaction_id: number;
    /**
     * Debit
     */
    debit?: number | null;
    /**
     * Credit
     */
    credit?: number | null;
    /**
     * Due Date
     */
    due_date?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Date
     */
    date?: string | null;
    /**
     * Is Debit Minus
     */
    is_debit_minus: boolean;
    /**
     * Is Schedule
     */
    is_schedule: boolean;
    /**
     * Is Saved
     */
    is_saved: boolean;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Set Id
     */
    set_id: number | null;
    /**
     * Set Item Id
     */
    set_item_id: number | null;
};

/**
 * OrgSplitUpdateTypes
 */
export type OrgSplitUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by: number;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Account Id
     */
    account_id?: number | null;
    /**
     * Transaction Id
     */
    transaction_id?: number | null;
    /**
     * Debit
     */
    debit?: number | null;
    /**
     * Credit
     */
    credit?: number | null;
    /**
     * Due Date
     */
    due_date?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Date
     */
    date?: string | null;
    /**
     * Is Debit Minus
     */
    is_debit_minus?: boolean | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Saved
     */
    is_saved?: boolean | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Set Id
     */
    set_id?: number | null;
    /**
     * Set Item Id
     */
    set_item_id?: number | null;
};

/**
 * OrgTransactionCreateTypes
 */
export type OrgTransactionCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Date
     */
    date?: null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Due Date
     */
    due_date?: null;
    /**
     * Amount
     */
    amount: number;
    /**
     * Job Id
     */
    job_id?: number | null;
    /**
     * Comment
     */
    comment?: string | null;
    /**
     * Object Id
     */
    object_id?: number | null;
    /**
     * Is Saved
     */
    is_saved?: boolean;
    /**
     * Is Schedule
     */
    is_schedule?: boolean;
    /**
     * Is Regular
     */
    is_regular?: boolean;
    /**
     * Sch Dates
     */
    sch_dates?: Array<null> | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: null;
    /**
     * Sch End Date
     */
    sch_end_date?: null;
    /**
     * Posted At
     */
    posted_at?: string | null;
    /**
     * Cust Ref
     */
    cust_ref?: string | null;
    /**
     * Our Ref
     */
    our_ref?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    /**
     * Contrahent Type Id
     */
    contrahent_type_id?: number | null;
    /**
     * Set Id
     */
    set_id?: number | null;
    /**
     * Set Item Id
     */
    set_item_id?: number | null;
    /**
     * Template Used
     */
    template_used?: string | null;
    /**
     * Org Splits
     */
    org_splits: Array<OrgSplitCreateTypes>;
};

/**
 * OrgTransactionDataTypes
 */
export type OrgTransactionDataTypes = {
    /**
     * Data
     */
    data: Array<OrgTransactionDisplayTypes>;
    pagination: Pagination;
};

/**
 * OrgTransactionDisplayColumnsTypes
 */
export type OrgTransactionDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at?: string | null;
    /**
     * Updated At
     */
    updated_at?: string | null;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Budget
     */
    budget?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Accepted Offer Id
     */
    accepted_offer_id?: number | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<string> | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: string | null;
    /**
     * Sch End Date
     */
    sch_end_date?: string | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Interval Day
     */
    interval_day?: number | null;
};

/**
 * OrgTransactionDisplayTypes
 */
export type OrgTransactionDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Created By
     */
    created_by?: number | null;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Date
     */
    date?: null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Due Date
     */
    due_date?: null;
    /**
     * Amount
     */
    amount: number;
    /**
     * Job Id
     */
    job_id?: number | null;
    /**
     * Comment
     */
    comment?: string | null;
    /**
     * Object Id
     */
    object_id?: number | null;
    /**
     * Is Saved
     */
    is_saved?: boolean;
    /**
     * Is Schedule
     */
    is_schedule?: boolean;
    /**
     * Is Regular
     */
    is_regular?: boolean;
    /**
     * Sch Dates
     */
    sch_dates?: Array<null> | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: null;
    /**
     * Sch End Date
     */
    sch_end_date?: null;
    /**
     * Posted At
     */
    posted_at?: string | null;
    /**
     * Cust Ref
     */
    cust_ref?: string | null;
    /**
     * Our Ref
     */
    our_ref?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    /**
     * Contrahent Type Id
     */
    contrahent_type_id?: number | null;
    /**
     * Set Id
     */
    set_id?: number | null;
    /**
     * Set Item Id
     */
    set_item_id?: number | null;
    /**
     * Template Used
     */
    template_used?: string | null;
    /**
     * Org Splits
     */
    org_splits: Array<OrgSplitDisplayTypes>;
    /**
     * Files
     */
    files?: Array<FilesDisplayTypes>;
};

/**
 * OrgTransactionScheduledTypes
 */
export type OrgTransactionScheduledTypes = {
    /**
     * Fixed Dates
     */
    fixed_dates: Array<OrgTransactionDisplayTypes>;
    /**
     * Fixed End Date
     */
    fixed_end_date: Array<OrgTransactionDisplayTypes>;
    /**
     * Open End Date
     */
    open_end_date: Array<OrgTransactionDisplayTypes>;
};

/**
 * OrgTransactionUpdateTypes
 */
export type OrgTransactionUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Date
     */
    date?: null;
    /**
     * Type
     */
    type?: string | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Due Date
     */
    due_date?: null;
    /**
     * Amount
     */
    amount?: number | null;
    /**
     * Job Id
     */
    job_id?: number | null;
    /**
     * Comment
     */
    comment?: string | null;
    /**
     * Object Id
     */
    object_id?: number | null;
    /**
     * Is Saved
     */
    is_saved?: boolean | null;
    /**
     * Is Schedule
     */
    is_schedule?: boolean | null;
    /**
     * Is Regular
     */
    is_regular?: boolean | null;
    /**
     * Sch Dates
     */
    sch_dates?: Array<null> | null;
    /**
     * Sch Interval
     */
    sch_interval?: string | null;
    /**
     * Sch Start Date
     */
    sch_start_date?: null;
    /**
     * Sch End Date
     */
    sch_end_date?: null;
    /**
     * Posted At
     */
    posted_at?: string | null;
    /**
     * Cust Ref
     */
    cust_ref?: string | null;
    /**
     * Our Ref
     */
    our_ref?: string | null;
    /**
     * Memo
     */
    memo?: string | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    /**
     * Contrahent Type Id
     */
    contrahent_type_id?: number | null;
    /**
     * Set Id
     */
    set_id?: number | null;
    /**
     * Set Item Id
     */
    set_item_id?: number | null;
    /**
     * Template Used
     */
    template_used?: string | null;
    /**
     * Org Splits
     */
    org_splits: Array<OrgSplitCreateTypes>;
};

/**
 * OrgUpdateTypes
 */
export type OrgUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Tag
     */
    tag?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    json_metadata?: JsonMetadataInput | null;
    /**
     * Is Public
     */
    is_public?: boolean | null;
    /**
     * Address Id
     */
    address_id?: number | null;
    /**
     * Is Formal
     */
    is_formal?: boolean | null;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
    /**
     * Max Img Width
     */
    max_img_width?: number | null;
    /**
     * Total Shares
     */
    total_shares?: number | null;
    /**
     * Accounts Set Id
     */
    accounts_set_id?: number | null;
    /**
     * Voting Days
     */
    voting_days?: number | null;
    /**
     * Members By Admin
     */
    members_by_admin?: boolean | null;
    /**
     * Curr Acc Period Id
     */
    curr_acc_period_id?: number | null;
    /**
     * Curr Acc Period Name
     */
    curr_acc_period_name?: string | null;
    /**
     * Curr Acc Period Start
     */
    curr_acc_period_start?: string | null;
    /**
     * Curr Acc Period End
     */
    curr_acc_period_end?: string | null;
};

/**
 * Pagination
 */
export type Pagination = {
    /**
     * Pageindex
     */
    pageIndex: number;
    /**
     * Pagesize
     */
    pageSize: number;
    /**
     * Totalitems
     */
    totalItems?: number;
};

/**
 * PartTypeEnum
 */
export type PartTypeEnum = 'document' | 'top' | 'header' | 'pre' | 'content_text' | 'content_table' | 'post' | 'footer' | 'bottom';

/**
 * PathOptions
 */
export type PathOptions = {
    /**
     * Columnsizing
     */
    columnSizing?: {
        [key: string]: number;
    };
    /**
     * Columnsizinginfo
     */
    columnSizingInfo?: {
        [key: string]: unknown;
    };
    /**
     * Rowselection
     */
    rowSelection?: {
        [key: string]: unknown;
    };
    rowPinning?: Pinning;
    /**
     * Expanded
     */
    expanded?: {
        [key: string]: unknown;
    };
    /**
     * Grouping
     */
    grouping?: Array<string>;
    /**
     * Sorting
     */
    sorting?: Array<Sorting>;
    /**
     * Columnfilters
     */
    columnFilters?: Array<Columns>;
    /**
     * Columnpinning
     */
    columnPinning?: {
        [key: string]: unknown;
    };
    /**
     * Columnorder
     */
    columnOrder?: Array<string>;
    /**
     * Columnvisibility
     */
    columnVisibility?: {
        [key: string]: unknown;
    };
    pagination?: ClientPagination;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Date Column
     */
    date_column?: string | null;
    serverPagination?: Pagination;
    /**
     * Serversearch
     */
    serverSearch?: Array<Columns>;
};

/**
 * Period
 */
export type Period = 'TODAY' | 'YESTERDAY' | 'LAST_7' | 'LAST_30' | 'THIS_WEEK' | 'LAST_WEEK' | 'NEXT_WEEK' | 'THIS_MONTH' | 'LAST_MONTH' | 'NEXT_MONTH' | 'THIS_YEAR' | 'LAST_YEAR' | 'NEXT_YEAR' | 'CUSTOM' | 'DATES';

/**
 * Pinning
 */
export type Pinning = {
    /**
     * Top
     */
    top?: Array<string>;
    /**
     * Bottom
     */
    bottom?: Array<string>;
};

/**
 * ProfileCreateTypes
 */
export type ProfileCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    json_metadata?: OptionsInput;
    /**
     * User Id
     */
    user_id: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Type Id
     */
    type_id: number;
    /**
     * Display Name
     */
    display_name?: string | null;
    /**
     * Is Current
     */
    is_current?: boolean;
    /**
     * Is Company
     */
    is_company?: boolean;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
};

/**
 * ProfileDataColumnsTypes
 */
export type ProfileDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<ProfileDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * ProfileDataTypes
 */
export type ProfileDataTypes = {
    /**
     * Data
     */
    data: Array<ProfileDisplayTypes>;
    pagination: Pagination;
};

/**
 * ProfileDisplayColumnsTypes
 */
export type ProfileDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    json_metadata?: OptionsOutput | null;
    /**
     * User Id
     */
    user_id?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Type Id
     */
    type_id?: number | null;
    /**
     * Display Name
     */
    display_name?: string | null;
    /**
     * Is Current
     */
    is_current?: boolean | null;
    /**
     * Is Company
     */
    is_company?: boolean | null;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
    /**
     * Contrahents
     */
    contrahents?: Array<ContrahentBasicTypes> | null;
};

/**
 * ProfileDisplayTypes
 */
export type ProfileDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    json_metadata?: OptionsOutput;
    /**
     * User Id
     */
    user_id: number;
    /**
     * Org Id
     */
    org_id: number;
    /**
     * Type Id
     */
    type_id: number;
    /**
     * Display Name
     */
    display_name?: string | null;
    /**
     * Is Current
     */
    is_current?: boolean;
    /**
     * Is Company
     */
    is_company?: boolean;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
};

/**
 * ProfileUpdateTypes
 */
export type ProfileUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    json_metadata?: OptionsInput;
    /**
     * User Id
     */
    user_id?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Type Id
     */
    type_id?: number | null;
    /**
     * Display Name
     */
    display_name?: string | null;
    /**
     * Is Current
     */
    is_current?: boolean | null;
    /**
     * Is Company
     */
    is_company?: boolean | null;
    /**
     * Nip
     */
    nip?: string | null;
    /**
     * Regon
     */
    regon?: string | null;
};

/**
 * RowPinning
 */
export type RowPinning = {
    /**
     * Top
     */
    top?: Array<string>;
    /**
     * Bottom
     */
    bottom?: Array<string>;
};

/**
 * S3Data
 */
export type S3Data = {
    /**
     * Bucket Name
     */
    bucket_name?: string | null;
    /**
     * Region
     */
    region?: string | null;
    /**
     * Access Key Id
     */
    access_key_id?: string | null;
    /**
     * Secret Access Key
     */
    secret_access_key?: string | null;
    /**
     * Account Id
     */
    account_id?: string | null;
    /**
     * Endpoint
     */
    endpoint?: string | null;
};

/**
 * SearchTerm
 */
export type SearchTerm = {
    /**
     * Column Name
     */
    column_name: string;
    /**
     * Term
     */
    term: string;
};

/**
 * ServerOptions
 */
export type ServerOptions = {
    /**
     * Filters
     */
    filters?: {
        [key: string]: unknown;
    } | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    period?: Period | null;
    /**
     * Date Column
     */
    date_column?: string | null;
    /**
     * Start Date
     */
    start_date?: string | null;
    /**
     * End Date
     */
    end_date?: string | null;
    /**
     * Page Index
     */
    page_index?: number;
    /**
     * Page Size
     */
    page_size?: number | null;
    /**
     * Search
     */
    search?: Array<SearchTerm> | null;
    /**
     * Order
     */
    order?: Array<OrderColumn> | null;
    /**
     * Columns
     */
    columns?: Array<string> | null;
};

/**
 * SizingInfo
 */
export type SizingInfo = {
    /**
     * Startoffset
     */
    startOffset?: number | null;
    /**
     * Startsize
     */
    startSize?: number | null;
    /**
     * Deltaoffset
     */
    deltaOffset?: number | null;
    /**
     * Deltapercentage
     */
    deltaPercentage?: number | null;
    /**
     * Isresizingcolumn
     */
    isResizingColumn?: boolean;
    /**
     * Columnsizingstart
     */
    columnSizingStart?: Array<number>;
};

/**
 * Sorting
 */
export type Sorting = {
    /**
     * Id
     */
    id: string;
    /**
     * Desc
     */
    desc: boolean;
};

/**
 * StorageData
 */
export type StorageData = {
    /**
     * Email
     */
    email?: string | null;
    tokens?: TokenData | null;
    /**
     * Storage Type
     */
    storage_type?: string | null;
    s3?: S3Data | null;
    dropbox?: DropboxData | null;
};

/**
 * SystemType
 */
export type SystemType = {
    /**
     * Id
     */
    id: number;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    /**
     * Created By
     */
    created_by: number;
    /**
     * Updated By
     */
    updated_by?: number | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Object Id
     */
    object_id: number;
    /**
     * System Type
     */
    system_type: string;
    /**
     * Systems Files
     */
    systems_files?: Array<FilesDisplayTypes>;
};

/**
 * TokenData
 */
export type TokenData = {
    /**
     * Access Token
     */
    access_token?: string | null;
    /**
     * Expiry Date
     */
    expiry_date?: number | null;
    /**
     * Id Token
     */
    id_token?: string | null;
    /**
     * Refresh Token
     */
    refresh_token?: string | null;
    /**
     * Scope
     */
    scope?: string | null;
    /**
     * Token Type
     */
    token_type?: string | null;
};

/**
 * TypeCreateTypes
 */
export type TypeCreateTypes = {
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Order
     */
    order: number;
};

/**
 * TypeDataColumnsTypes
 */
export type TypeDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<TypeDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * TypeDataTypes
 */
export type TypeDataTypes = {
    /**
     * Data
     */
    data: Array<TypeDisplayTypes>;
    pagination: Pagination;
};

/**
 * TypeDisplay
 */
export type TypeDisplay = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
};

/**
 * TypeDisplayColumnsTypes
 */
export type TypeDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Order
     */
    order?: number | null;
};

/**
 * TypeDisplayTypes
 */
export type TypeDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Order
     */
    order: number;
};

/**
 * TypeUpdateTypes
 */
export type TypeUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Order
     */
    order?: number | null;
};

/**
 * UserCreateTypes
 */
export type UserCreateTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * First Name
     */
    first_name?: string | null;
    /**
     * Last Name
     */
    last_name?: string | null;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email: string;
    /**
     * Email Verified
     */
    email_verified?: boolean;
    /**
     * Is Superuser
     */
    is_superuser?: boolean;
    /**
     * Is Admin
     */
    is_admin?: boolean;
    /**
     * Is Limited
     */
    is_limited?: boolean;
    /**
     * Is Disabled
     */
    is_disabled?: boolean;
    /**
     * Is In Credit
     */
    is_in_credit?: boolean;
    /**
     * Is Temp
     */
    is_temp?: boolean;
    /**
     * Is Deleted
     */
    is_deleted?: boolean;
    /**
     * Curr Profile Id
     */
    curr_profile_id?: number | null;
    /**
     * Curr Org Id
     */
    curr_org_id?: number | null;
    /**
     * Keycloak Id
     */
    keycloak_id?: string | null;
};

/**
 * UserDataColumnsTypes
 */
export type UserDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<UserDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * UserDataTypes
 */
export type UserDataTypes = {
    /**
     * Data
     */
    data: Array<UserDisplayTypes>;
    pagination: Pagination;
};

/**
 * UserDisplayColumnsTypes
 */
export type UserDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * First Name
     */
    first_name?: string | null;
    /**
     * Last Name
     */
    last_name?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Curr Profile Id
     */
    curr_profile_id?: number | null;
    /**
     * Curr Org Id
     */
    curr_org_id?: number | null;
    /**
     * Keycloak Id
     */
    keycloak_id?: string | null;
};

/**
 * UserDisplayTypes
 */
export type UserDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * First Name
     */
    first_name?: string | null;
    /**
     * Last Name
     */
    last_name?: string | null;
    /**
     * Lang
     */
    lang: string;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Curr Profile Id
     */
    curr_profile_id?: number | null;
    /**
     * Curr Org Id
     */
    curr_org_id?: number | null;
    /**
     * Keycloak Id
     */
    keycloak_id?: string | null;
};

/**
 * UserRegisterTypes
 */
export type UserRegisterTypes = {
    /**
     * Created By
     */
    created_by?: number;
    /**
     * Email
     */
    email: string;
    /**
     * First Name
     */
    first_name?: string | null;
    /**
     * Last Name
     */
    last_name?: string | null;
    /**
     * Lang
     */
    lang?: string;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Contrahent Id
     */
    contrahent_id?: number | null;
    /**
     * Org Id
     */
    org_id?: number | null;
    /**
     * Source Profile Id
     */
    source_profile_id: number;
};

/**
 * UserUpdateTypes
 */
export type UserUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Updated By
     */
    updated_by?: number;
    /**
     * First Name
     */
    first_name?: string | null;
    /**
     * Last Name
     */
    last_name?: string | null;
    /**
     * Lang
     */
    lang?: string | null;
    /**
     * Description
     */
    description?: string | null;
    /**
     * Json Metadata
     */
    json_metadata?: {
        [key: string]: unknown;
    } | null;
    /**
     * Phone
     */
    phone?: string | null;
    /**
     * Email
     */
    email?: string | null;
    /**
     * Email Verified
     */
    email_verified?: boolean | null;
    /**
     * Is Superuser
     */
    is_superuser?: boolean | null;
    /**
     * Is Admin
     */
    is_admin?: boolean | null;
    /**
     * Is Limited
     */
    is_limited?: boolean | null;
    /**
     * Is Disabled
     */
    is_disabled?: boolean | null;
    /**
     * Is In Credit
     */
    is_in_credit?: boolean | null;
    /**
     * Is Temp
     */
    is_temp?: boolean | null;
    /**
     * Is Deleted
     */
    is_deleted?: boolean | null;
    /**
     * Curr Profile Id
     */
    curr_profile_id?: number | null;
    /**
     * Curr Org Id
     */
    curr_org_id?: number | null;
    /**
     * Keycloak Id
     */
    keycloak_id?: string | null;
};

/**
 * ValidationError
 */
export type ValidationError = {
    /**
     * Location
     */
    loc: Array<string | number>;
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
};

/**
 * Variant
 */
export type VariantInput = {
    /**
     * Name
     */
    name: string;
    /**
     * Path
     */
    path: string;
    /**
     * Config
     */
    config: Array<PathOptions>;
};

/**
 * Variant
 */
export type VariantOutput = {
    /**
     * Name
     */
    name: string;
    /**
     * Path
     */
    path: string;
    /**
     * Config
     */
    config: Array<PathOptions>;
};

/**
 * VariantCreateTypes
 */
export type VariantCreateTypes = {
    /**
     * Deactivate Id
     */
    deactivate_id?: number | null;
    /**
     * Profile Id
     */
    profile_id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Path
     */
    path: string;
    client_ops?: ClientOptionsTypes;
    server_ops?: ServerOptions;
    /**
     * Is Active
     */
    is_active?: boolean;
};

/**
 * VariantDataColumnsTypes
 */
export type VariantDataColumnsTypes = {
    /**
     * Data
     */
    data: Array<VariantDisplayColumnsTypes>;
    pagination: Pagination;
};

/**
 * VariantDataTypes
 */
export type VariantDataTypes = {
    /**
     * Data
     */
    data: Array<VariantDisplayTypes>;
    pagination: Pagination;
};

/**
 * VariantDisplayColumnsTypes
 */
export type VariantDisplayColumnsTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Profile Id
     */
    profile_id?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Path
     */
    path?: string | null;
    client_ops?: ClientOptionsTypes | null;
    server_ops?: ServerOptions | null;
    /**
     * Is Active
     */
    is_active?: boolean | null;
};

/**
 * VariantDisplayTypes
 */
export type VariantDisplayTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Profile Id
     */
    profile_id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Path
     */
    path: string;
    /**
     * Is Active
     */
    is_active: boolean;
    client_ops?: ClientOptionsTypes | null;
    server_ops?: ServerOptions | null;
};

/**
 * VariantUpdateTypes
 */
export type VariantUpdateTypes = {
    /**
     * Id
     */
    id: number;
    /**
     * Deactivate Id
     */
    deactivate_id?: number | null;
    /**
     * Profile Id
     */
    profile_id?: number | null;
    /**
     * Name
     */
    name?: string | null;
    /**
     * Path
     */
    path?: string | null;
    client_ops?: ClientOptionsTypes | null;
    server_ops?: ServerOptions | null;
    /**
     * Is Active
     */
    is_active?: boolean | null;
};

/**
 * DocTypeEnum
 */
export type AppSchemasCoreDocPartsSchemasDocTypeEnum = 'proposal' | 'uchwala' | 'email' | 'invoice' | 'prices';

/**
 * DocTypeEnum
 */
export type AppSchemasCoreDocsSchemasDocTypeEnum = 'proposal' | 'uchwala' | 'email';

export type CreateUsersApiV1AuthUsersPostData = {
    /**
     * Items
     */
    body: Array<UserCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/auth/users/';
};

export type CreateUsersApiV1AuthUsersPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateUsersApiV1AuthUsersPostError = CreateUsersApiV1AuthUsersPostErrors[keyof CreateUsersApiV1AuthUsersPostErrors];

export type CreateUsersApiV1AuthUsersPostResponses = {
    /**
     * Response Create Users Api V1 Auth Users  Post
     * Successful Response
     */
    201: Array<UserDisplayTypes>;
};

export type CreateUsersApiV1AuthUsersPostResponse = CreateUsersApiV1AuthUsersPostResponses[keyof CreateUsersApiV1AuthUsersPostResponses];

export type UpdateUsersApiV1AuthUsersPutData = {
    /**
     * Items
     */
    body: Array<UserUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/auth/users/';
};

export type UpdateUsersApiV1AuthUsersPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateUsersApiV1AuthUsersPutError = UpdateUsersApiV1AuthUsersPutErrors[keyof UpdateUsersApiV1AuthUsersPutErrors];

export type UpdateUsersApiV1AuthUsersPutResponses = {
    /**
     * Response Update Users Api V1 Auth Users  Put
     * Successful Response
     */
    200: Array<UserDisplayTypes>;
};

export type UpdateUsersApiV1AuthUsersPutResponse = UpdateUsersApiV1AuthUsersPutResponses[keyof UpdateUsersApiV1AuthUsersPutResponses];

export type GetUsersOneApiV1AuthUsersKcKcIdGetData = {
    body?: never;
    path: {
        /**
         * Kc Id
         */
        kc_id: string;
    };
    query?: never;
    url: '/api/v1/auth/users/kc/{kc_id}';
};

export type GetUsersOneApiV1AuthUsersKcKcIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetUsersOneApiV1AuthUsersKcKcIdGetError = GetUsersOneApiV1AuthUsersKcKcIdGetErrors[keyof GetUsersOneApiV1AuthUsersKcKcIdGetErrors];

export type GetUsersOneApiV1AuthUsersKcKcIdGetResponses = {
    /**
     * Successful Response
     */
    200: KeycloakUserTypes;
};

export type GetUsersOneApiV1AuthUsersKcKcIdGetResponse = GetUsersOneApiV1AuthUsersKcKcIdGetResponses[keyof GetUsersOneApiV1AuthUsersKcKcIdGetResponses];

export type DeleteUsersApiV1AuthUsersItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/auth/users/{item_id}';
};

export type DeleteUsersApiV1AuthUsersItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteUsersApiV1AuthUsersItemIdDeleteError = DeleteUsersApiV1AuthUsersItemIdDeleteErrors[keyof DeleteUsersApiV1AuthUsersItemIdDeleteErrors];

export type DeleteUsersApiV1AuthUsersItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetUsersOneApiV1AuthUsersItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/auth/users/{item_id}';
};

export type GetUsersOneApiV1AuthUsersItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetUsersOneApiV1AuthUsersItemIdGetError = GetUsersOneApiV1AuthUsersItemIdGetErrors[keyof GetUsersOneApiV1AuthUsersItemIdGetErrors];

export type GetUsersOneApiV1AuthUsersItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: UserDisplayTypes;
};

export type GetUsersOneApiV1AuthUsersItemIdGetResponse = GetUsersOneApiV1AuthUsersItemIdGetResponses[keyof GetUsersOneApiV1AuthUsersItemIdGetResponses];

export type GetUsersAllApiV1AuthUsersGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/auth/users/get-all';
};

export type GetUsersAllApiV1AuthUsersGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetUsersAllApiV1AuthUsersGetAllPostError = GetUsersAllApiV1AuthUsersGetAllPostErrors[keyof GetUsersAllApiV1AuthUsersGetAllPostErrors];

export type GetUsersAllApiV1AuthUsersGetAllPostResponses = {
    /**
     * Response Get Users All Api V1 Auth Users Get All Post
     * Successful Response
     */
    200: UserDataTypes | UserDataColumnsTypes;
};

export type GetUsersAllApiV1AuthUsersGetAllPostResponse = GetUsersAllApiV1AuthUsersGetAllPostResponses[keyof GetUsersAllApiV1AuthUsersGetAllPostResponses];

export type RegisterApiV1AuthUsersRegisterPostData = {
    body: UserRegisterTypes;
    path?: never;
    query?: never;
    url: '/api/v1/auth/users/register';
};

export type RegisterApiV1AuthUsersRegisterPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RegisterApiV1AuthUsersRegisterPostError = RegisterApiV1AuthUsersRegisterPostErrors[keyof RegisterApiV1AuthUsersRegisterPostErrors];

export type RegisterApiV1AuthUsersRegisterPostResponses = {
    /**
     * Successful Response
     */
    200: UserDisplayTypes;
};

export type RegisterApiV1AuthUsersRegisterPostResponse = RegisterApiV1AuthUsersRegisterPostResponses[keyof RegisterApiV1AuthUsersRegisterPostResponses];

export type LinkContrahentApiV1AuthUsersLinkContrahentPostData = {
    body: LinkContrahentToUserTypes;
    path?: never;
    query?: never;
    url: '/api/v1/auth/users/link-contrahent';
};

export type LinkContrahentApiV1AuthUsersLinkContrahentPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type LinkContrahentApiV1AuthUsersLinkContrahentPostError = LinkContrahentApiV1AuthUsersLinkContrahentPostErrors[keyof LinkContrahentApiV1AuthUsersLinkContrahentPostErrors];

export type LinkContrahentApiV1AuthUsersLinkContrahentPostResponses = {
    /**
     * Response Link Contrahent Api V1 Auth Users Link Contrahent Post
     * Successful Response
     */
    200: {
        [key: string]: unknown;
    };
};

export type LinkContrahentApiV1AuthUsersLinkContrahentPostResponse = LinkContrahentApiV1AuthUsersLinkContrahentPostResponses[keyof LinkContrahentApiV1AuthUsersLinkContrahentPostResponses];

export type CreateJobsApiV1CoreJobsPostData = {
    /**
     * Items
     */
    body: Array<JobCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/jobs/';
};

export type CreateJobsApiV1CoreJobsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateJobsApiV1CoreJobsPostError = CreateJobsApiV1CoreJobsPostErrors[keyof CreateJobsApiV1CoreJobsPostErrors];

export type CreateJobsApiV1CoreJobsPostResponses = {
    /**
     * Response Create Jobs Api V1 Core Jobs  Post
     * Successful Response
     */
    201: Array<JobDisplayTypes>;
};

export type CreateJobsApiV1CoreJobsPostResponse = CreateJobsApiV1CoreJobsPostResponses[keyof CreateJobsApiV1CoreJobsPostResponses];

export type UpdateJobsApiV1CoreJobsPutData = {
    /**
     * Items
     */
    body: Array<JobUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/jobs/';
};

export type UpdateJobsApiV1CoreJobsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateJobsApiV1CoreJobsPutError = UpdateJobsApiV1CoreJobsPutErrors[keyof UpdateJobsApiV1CoreJobsPutErrors];

export type UpdateJobsApiV1CoreJobsPutResponses = {
    /**
     * Response Update Jobs Api V1 Core Jobs  Put
     * Successful Response
     */
    200: Array<JobDisplayTypes>;
};

export type UpdateJobsApiV1CoreJobsPutResponse = UpdateJobsApiV1CoreJobsPutResponses[keyof UpdateJobsApiV1CoreJobsPutResponses];

export type DeleteJobsApiV1CoreJobsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/jobs/{item_id}';
};

export type DeleteJobsApiV1CoreJobsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteJobsApiV1CoreJobsItemIdDeleteError = DeleteJobsApiV1CoreJobsItemIdDeleteErrors[keyof DeleteJobsApiV1CoreJobsItemIdDeleteErrors];

export type DeleteJobsApiV1CoreJobsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    204: void;
};

export type DeleteJobsApiV1CoreJobsItemIdDeleteResponse = DeleteJobsApiV1CoreJobsItemIdDeleteResponses[keyof DeleteJobsApiV1CoreJobsItemIdDeleteResponses];

export type GetJobsOneApiV1CoreJobsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/jobs/{item_id}';
};

export type GetJobsOneApiV1CoreJobsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetJobsOneApiV1CoreJobsItemIdGetError = GetJobsOneApiV1CoreJobsItemIdGetErrors[keyof GetJobsOneApiV1CoreJobsItemIdGetErrors];

export type GetJobsOneApiV1CoreJobsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: JobDisplayTypes;
};

export type GetJobsOneApiV1CoreJobsItemIdGetResponse = GetJobsOneApiV1CoreJobsItemIdGetResponses[keyof GetJobsOneApiV1CoreJobsItemIdGetResponses];

export type GetJobsAllApiV1CoreJobsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/jobs/get-all';
};

export type GetJobsAllApiV1CoreJobsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetJobsAllApiV1CoreJobsGetAllPostError = GetJobsAllApiV1CoreJobsGetAllPostErrors[keyof GetJobsAllApiV1CoreJobsGetAllPostErrors];

export type GetJobsAllApiV1CoreJobsGetAllPostResponses = {
    /**
     * Response Get Jobs All Api V1 Core Jobs Get All Post
     * Successful Response
     */
    200: JobDataTypes | JobDataColumnsTypes;
};

export type GetJobsAllApiV1CoreJobsGetAllPostResponse = GetJobsAllApiV1CoreJobsGetAllPostResponses[keyof GetJobsAllApiV1CoreJobsGetAllPostResponses];

export type GetJobTasksAllApiV1CoreJobTasksGetData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/job-tasks/';
};

export type GetJobTasksAllApiV1CoreJobTasksGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetJobTasksAllApiV1CoreJobTasksGetError = GetJobTasksAllApiV1CoreJobTasksGetErrors[keyof GetJobTasksAllApiV1CoreJobTasksGetErrors];

export type GetJobTasksAllApiV1CoreJobTasksGetResponses = {
    /**
     * Response Get Job Tasks All Api V1 Core Job Tasks  Get
     * Successful Response
     */
    200: JobTaskDataTypes | JobTaskDataColumnsTypes;
};

export type GetJobTasksAllApiV1CoreJobTasksGetResponse = GetJobTasksAllApiV1CoreJobTasksGetResponses[keyof GetJobTasksAllApiV1CoreJobTasksGetResponses];

export type CreateJobTasksApiV1CoreJobTasksPostData = {
    /**
     * Items
     */
    body: Array<JobTaskCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/job-tasks/';
};

export type CreateJobTasksApiV1CoreJobTasksPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateJobTasksApiV1CoreJobTasksPostError = CreateJobTasksApiV1CoreJobTasksPostErrors[keyof CreateJobTasksApiV1CoreJobTasksPostErrors];

export type CreateJobTasksApiV1CoreJobTasksPostResponses = {
    /**
     * Response Create Job Tasks Api V1 Core Job Tasks  Post
     * Successful Response
     */
    201: Array<JobTaskDisplayTypes>;
};

export type CreateJobTasksApiV1CoreJobTasksPostResponse = CreateJobTasksApiV1CoreJobTasksPostResponses[keyof CreateJobTasksApiV1CoreJobTasksPostResponses];

export type UpdateJobTasksApiV1CoreJobTasksPutData = {
    /**
     * Items
     */
    body: Array<JobTaskUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/job-tasks/';
};

export type UpdateJobTasksApiV1CoreJobTasksPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateJobTasksApiV1CoreJobTasksPutError = UpdateJobTasksApiV1CoreJobTasksPutErrors[keyof UpdateJobTasksApiV1CoreJobTasksPutErrors];

export type UpdateJobTasksApiV1CoreJobTasksPutResponses = {
    /**
     * Response Update Job Tasks Api V1 Core Job Tasks  Put
     * Successful Response
     */
    200: Array<JobTaskDisplayTypes>;
};

export type UpdateJobTasksApiV1CoreJobTasksPutResponse = UpdateJobTasksApiV1CoreJobTasksPutResponses[keyof UpdateJobTasksApiV1CoreJobTasksPutResponses];

export type DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/job-tasks/{item_id}';
};

export type DeleteJobTasksApiV1CoreJobTasksItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteJobTasksApiV1CoreJobTasksItemIdDeleteError = DeleteJobTasksApiV1CoreJobTasksItemIdDeleteErrors[keyof DeleteJobTasksApiV1CoreJobTasksItemIdDeleteErrors];

export type DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    204: void;
};

export type DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponse = DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponses[keyof DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponses];

export type GetJobTasksOneApiV1CoreJobTasksItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/job-tasks/{item_id}';
};

export type GetJobTasksOneApiV1CoreJobTasksItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetJobTasksOneApiV1CoreJobTasksItemIdGetError = GetJobTasksOneApiV1CoreJobTasksItemIdGetErrors[keyof GetJobTasksOneApiV1CoreJobTasksItemIdGetErrors];

export type GetJobTasksOneApiV1CoreJobTasksItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: JobTaskDisplayTypes;
};

export type GetJobTasksOneApiV1CoreJobTasksItemIdGetResponse = GetJobTasksOneApiV1CoreJobTasksItemIdGetResponses[keyof GetJobTasksOneApiV1CoreJobTasksItemIdGetResponses];

export type CreateFilesApiV1CoreFilesPostData = {
    /**
     * Items
     */
    body: Array<FilesCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/files/';
};

export type CreateFilesApiV1CoreFilesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateFilesApiV1CoreFilesPostError = CreateFilesApiV1CoreFilesPostErrors[keyof CreateFilesApiV1CoreFilesPostErrors];

export type CreateFilesApiV1CoreFilesPostResponses = {
    /**
     * Response Create Files Api V1 Core Files  Post
     * Successful Response
     */
    201: Array<FilesDisplayTypes>;
};

export type CreateFilesApiV1CoreFilesPostResponse = CreateFilesApiV1CoreFilesPostResponses[keyof CreateFilesApiV1CoreFilesPostResponses];

export type UpdateFilesApiV1CoreFilesPutData = {
    /**
     * Items
     */
    body: Array<FilesUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/files/';
};

export type UpdateFilesApiV1CoreFilesPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateFilesApiV1CoreFilesPutError = UpdateFilesApiV1CoreFilesPutErrors[keyof UpdateFilesApiV1CoreFilesPutErrors];

export type UpdateFilesApiV1CoreFilesPutResponses = {
    /**
     * Response Update Files Api V1 Core Files  Put
     * Successful Response
     */
    200: Array<FilesDisplayTypes>;
};

export type UpdateFilesApiV1CoreFilesPutResponse = UpdateFilesApiV1CoreFilesPutResponses[keyof UpdateFilesApiV1CoreFilesPutResponses];

export type DeleteFilesApiV1CoreFilesItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/files/{item_id}';
};

export type DeleteFilesApiV1CoreFilesItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteFilesApiV1CoreFilesItemIdDeleteError = DeleteFilesApiV1CoreFilesItemIdDeleteErrors[keyof DeleteFilesApiV1CoreFilesItemIdDeleteErrors];

export type DeleteFilesApiV1CoreFilesItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    204: void;
};

export type DeleteFilesApiV1CoreFilesItemIdDeleteResponse = DeleteFilesApiV1CoreFilesItemIdDeleteResponses[keyof DeleteFilesApiV1CoreFilesItemIdDeleteResponses];

export type GetFilesOneApiV1CoreFilesItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/files/{item_id}';
};

export type GetFilesOneApiV1CoreFilesItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetFilesOneApiV1CoreFilesItemIdGetError = GetFilesOneApiV1CoreFilesItemIdGetErrors[keyof GetFilesOneApiV1CoreFilesItemIdGetErrors];

export type GetFilesOneApiV1CoreFilesItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: FilesDisplayTypes;
};

export type GetFilesOneApiV1CoreFilesItemIdGetResponse = GetFilesOneApiV1CoreFilesItemIdGetResponses[keyof GetFilesOneApiV1CoreFilesItemIdGetResponses];

export type GetFilesAllApiV1CoreFilesGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/files/get-all';
};

export type GetFilesAllApiV1CoreFilesGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetFilesAllApiV1CoreFilesGetAllPostError = GetFilesAllApiV1CoreFilesGetAllPostErrors[keyof GetFilesAllApiV1CoreFilesGetAllPostErrors];

export type GetFilesAllApiV1CoreFilesGetAllPostResponses = {
    /**
     * Response Get Files All Api V1 Core Files Get All Post
     * Successful Response
     */
    200: FilesDataTypes | FilesDataColumnsTypes;
};

export type GetFilesAllApiV1CoreFilesGetAllPostResponse = GetFilesAllApiV1CoreFilesGetAllPostResponses[keyof GetFilesAllApiV1CoreFilesGetAllPostResponses];

export type CreateObjectsApiV1CoreObjectsPostData = {
    /**
     * Items
     */
    body: Array<ObjectCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/objects/';
};

export type CreateObjectsApiV1CoreObjectsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateObjectsApiV1CoreObjectsPostError = CreateObjectsApiV1CoreObjectsPostErrors[keyof CreateObjectsApiV1CoreObjectsPostErrors];

export type CreateObjectsApiV1CoreObjectsPostResponses = {
    /**
     * Response Create Objects Api V1 Core Objects  Post
     * Successful Response
     */
    201: Array<ObjectDisplayTypes>;
};

export type CreateObjectsApiV1CoreObjectsPostResponse = CreateObjectsApiV1CoreObjectsPostResponses[keyof CreateObjectsApiV1CoreObjectsPostResponses];

export type UpdateObjectsApiV1CoreObjectsPutData = {
    /**
     * Items
     */
    body: Array<ObjectUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/objects/';
};

export type UpdateObjectsApiV1CoreObjectsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateObjectsApiV1CoreObjectsPutError = UpdateObjectsApiV1CoreObjectsPutErrors[keyof UpdateObjectsApiV1CoreObjectsPutErrors];

export type UpdateObjectsApiV1CoreObjectsPutResponses = {
    /**
     * Response Update Objects Api V1 Core Objects  Put
     * Successful Response
     */
    200: Array<ObjectDisplayTypes>;
};

export type UpdateObjectsApiV1CoreObjectsPutResponse = UpdateObjectsApiV1CoreObjectsPutResponses[keyof UpdateObjectsApiV1CoreObjectsPutResponses];

export type DeleteObjectsApiV1CoreObjectsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/objects/{item_id}';
};

export type DeleteObjectsApiV1CoreObjectsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteObjectsApiV1CoreObjectsItemIdDeleteError = DeleteObjectsApiV1CoreObjectsItemIdDeleteErrors[keyof DeleteObjectsApiV1CoreObjectsItemIdDeleteErrors];

export type DeleteObjectsApiV1CoreObjectsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetObjectsOneApiV1CoreObjectsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/objects/{item_id}';
};

export type GetObjectsOneApiV1CoreObjectsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetObjectsOneApiV1CoreObjectsItemIdGetError = GetObjectsOneApiV1CoreObjectsItemIdGetErrors[keyof GetObjectsOneApiV1CoreObjectsItemIdGetErrors];

export type GetObjectsOneApiV1CoreObjectsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: ObjectDisplayTypes;
};

export type GetObjectsOneApiV1CoreObjectsItemIdGetResponse = GetObjectsOneApiV1CoreObjectsItemIdGetResponses[keyof GetObjectsOneApiV1CoreObjectsItemIdGetResponses];

export type GetObjectsAllApiV1CoreObjectsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/objects/get-all';
};

export type GetObjectsAllApiV1CoreObjectsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetObjectsAllApiV1CoreObjectsGetAllPostError = GetObjectsAllApiV1CoreObjectsGetAllPostErrors[keyof GetObjectsAllApiV1CoreObjectsGetAllPostErrors];

export type GetObjectsAllApiV1CoreObjectsGetAllPostResponses = {
    /**
     * Response Get Objects All Api V1 Core Objects Get All Post
     * Successful Response
     */
    200: ObjectDataTypes | ObjectDataColumnsTypes;
};

export type GetObjectsAllApiV1CoreObjectsGetAllPostResponse = GetObjectsAllApiV1CoreObjectsGetAllPostResponses[keyof GetObjectsAllApiV1CoreObjectsGetAllPostResponses];

export type CreateObjectSystemsApiV1CoreObjectSystemsPostData = {
    /**
     * Items
     */
    body: Array<ObjectSystemCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/object-systems/';
};

export type CreateObjectSystemsApiV1CoreObjectSystemsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateObjectSystemsApiV1CoreObjectSystemsPostError = CreateObjectSystemsApiV1CoreObjectSystemsPostErrors[keyof CreateObjectSystemsApiV1CoreObjectSystemsPostErrors];

export type CreateObjectSystemsApiV1CoreObjectSystemsPostResponses = {
    /**
     * Response Create Object Systems Api V1 Core Object Systems  Post
     * Successful Response
     */
    201: Array<ObjectSystemDisplayTypes>;
};

export type CreateObjectSystemsApiV1CoreObjectSystemsPostResponse = CreateObjectSystemsApiV1CoreObjectSystemsPostResponses[keyof CreateObjectSystemsApiV1CoreObjectSystemsPostResponses];

export type UpdateObjectSystemsApiV1CoreObjectSystemsPutData = {
    /**
     * Items
     */
    body: Array<ObjectSystemUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/object-systems/';
};

export type UpdateObjectSystemsApiV1CoreObjectSystemsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateObjectSystemsApiV1CoreObjectSystemsPutError = UpdateObjectSystemsApiV1CoreObjectSystemsPutErrors[keyof UpdateObjectSystemsApiV1CoreObjectSystemsPutErrors];

export type UpdateObjectSystemsApiV1CoreObjectSystemsPutResponses = {
    /**
     * Response Update Object Systems Api V1 Core Object Systems  Put
     * Successful Response
     */
    200: Array<ObjectSystemDisplayTypes>;
};

export type UpdateObjectSystemsApiV1CoreObjectSystemsPutResponse = UpdateObjectSystemsApiV1CoreObjectSystemsPutResponses[keyof UpdateObjectSystemsApiV1CoreObjectSystemsPutResponses];

export type DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/object-systems/{item_id}';
};

export type DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteError = DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteErrors[keyof DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteErrors];

export type DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/object-systems/{item_id}';
};

export type GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetError = GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetErrors[keyof GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetErrors];

export type GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: ObjectSystemDisplayTypes;
};

export type GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponse = GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponses[keyof GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponses];

export type GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/object-systems/get-all';
};

export type GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostError = GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostErrors[keyof GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostErrors];

export type GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponses = {
    /**
     * Response Get Object Systems All Api V1 Core Object Systems Get All Post
     * Successful Response
     */
    200: ObjectSystemDataTypes | ObjectSystemDataColumnsTypes;
};

export type GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponse = GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponses[keyof GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponses];

export type CreateOrgsApiV1CoreOrgsPostData = {
    /**
     * Items
     */
    body: Array<OrgCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/orgs/';
};

export type CreateOrgsApiV1CoreOrgsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrgsApiV1CoreOrgsPostError = CreateOrgsApiV1CoreOrgsPostErrors[keyof CreateOrgsApiV1CoreOrgsPostErrors];

export type CreateOrgsApiV1CoreOrgsPostResponses = {
    /**
     * Response Create Orgs Api V1 Core Orgs  Post
     * Successful Response
     */
    201: Array<OrgDisplayTypes>;
};

export type CreateOrgsApiV1CoreOrgsPostResponse = CreateOrgsApiV1CoreOrgsPostResponses[keyof CreateOrgsApiV1CoreOrgsPostResponses];

export type UpdateOrgsApiV1CoreOrgsPutData = {
    /**
     * Items
     */
    body: Array<OrgUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/orgs/';
};

export type UpdateOrgsApiV1CoreOrgsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrgsApiV1CoreOrgsPutError = UpdateOrgsApiV1CoreOrgsPutErrors[keyof UpdateOrgsApiV1CoreOrgsPutErrors];

export type UpdateOrgsApiV1CoreOrgsPutResponses = {
    /**
     * Response Update Orgs Api V1 Core Orgs  Put
     * Successful Response
     */
    200: Array<OrgDisplayTypes>;
};

export type UpdateOrgsApiV1CoreOrgsPutResponse = UpdateOrgsApiV1CoreOrgsPutResponses[keyof UpdateOrgsApiV1CoreOrgsPutResponses];

export type DeleteOrgsApiV1CoreOrgsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/orgs/{item_id}';
};

export type DeleteOrgsApiV1CoreOrgsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteOrgsApiV1CoreOrgsItemIdDeleteError = DeleteOrgsApiV1CoreOrgsItemIdDeleteErrors[keyof DeleteOrgsApiV1CoreOrgsItemIdDeleteErrors];

export type DeleteOrgsApiV1CoreOrgsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetOrgsOneApiV1CoreOrgsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/orgs/{item_id}';
};

export type GetOrgsOneApiV1CoreOrgsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgsOneApiV1CoreOrgsItemIdGetError = GetOrgsOneApiV1CoreOrgsItemIdGetErrors[keyof GetOrgsOneApiV1CoreOrgsItemIdGetErrors];

export type GetOrgsOneApiV1CoreOrgsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrgDisplayTypes;
};

export type GetOrgsOneApiV1CoreOrgsItemIdGetResponse = GetOrgsOneApiV1CoreOrgsItemIdGetResponses[keyof GetOrgsOneApiV1CoreOrgsItemIdGetResponses];

export type GetOrgsAllApiV1CoreOrgsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/orgs/get-all';
};

export type GetOrgsAllApiV1CoreOrgsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgsAllApiV1CoreOrgsGetAllPostError = GetOrgsAllApiV1CoreOrgsGetAllPostErrors[keyof GetOrgsAllApiV1CoreOrgsGetAllPostErrors];

export type GetOrgsAllApiV1CoreOrgsGetAllPostResponses = {
    /**
     * Response Get Orgs All Api V1 Core Orgs Get All Post
     * Successful Response
     */
    200: OrgDataTypes | OrgDataColumnsTypes;
};

export type GetOrgsAllApiV1CoreOrgsGetAllPostResponse = GetOrgsAllApiV1CoreOrgsGetAllPostResponses[keyof GetOrgsAllApiV1CoreOrgsGetAllPostResponses];

export type CreateEmailTypesApiV1CoreEmailTypesPostData = {
    /**
     * Items
     */
    body: Array<EmailTypeCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/email-types/';
};

export type CreateEmailTypesApiV1CoreEmailTypesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateEmailTypesApiV1CoreEmailTypesPostError = CreateEmailTypesApiV1CoreEmailTypesPostErrors[keyof CreateEmailTypesApiV1CoreEmailTypesPostErrors];

export type CreateEmailTypesApiV1CoreEmailTypesPostResponses = {
    /**
     * Response Create Email Types Api V1 Core Email Types  Post
     * Successful Response
     */
    201: Array<EmailTypeDisplayTypes>;
};

export type CreateEmailTypesApiV1CoreEmailTypesPostResponse = CreateEmailTypesApiV1CoreEmailTypesPostResponses[keyof CreateEmailTypesApiV1CoreEmailTypesPostResponses];

export type UpdateEmailTypesApiV1CoreEmailTypesPutData = {
    /**
     * Items
     */
    body: Array<EmailTypeUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/email-types/';
};

export type UpdateEmailTypesApiV1CoreEmailTypesPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateEmailTypesApiV1CoreEmailTypesPutError = UpdateEmailTypesApiV1CoreEmailTypesPutErrors[keyof UpdateEmailTypesApiV1CoreEmailTypesPutErrors];

export type UpdateEmailTypesApiV1CoreEmailTypesPutResponses = {
    /**
     * Response Update Email Types Api V1 Core Email Types  Put
     * Successful Response
     */
    200: Array<EmailTypeDisplayTypes>;
};

export type UpdateEmailTypesApiV1CoreEmailTypesPutResponse = UpdateEmailTypesApiV1CoreEmailTypesPutResponses[keyof UpdateEmailTypesApiV1CoreEmailTypesPutResponses];

export type DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/email-types/{item_id}';
};

export type DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteError = DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteErrors[keyof DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteErrors];

export type DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetEmailTypesOneApiV1CoreEmailTypesItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/email-types/{item_id}';
};

export type GetEmailTypesOneApiV1CoreEmailTypesItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetEmailTypesOneApiV1CoreEmailTypesItemIdGetError = GetEmailTypesOneApiV1CoreEmailTypesItemIdGetErrors[keyof GetEmailTypesOneApiV1CoreEmailTypesItemIdGetErrors];

export type GetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: EmailTypeDisplayTypes;
};

export type GetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponse = GetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponses[keyof GetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponses];

export type GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/email-types/get-all';
};

export type GetEmailTypesAllApiV1CoreEmailTypesGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetEmailTypesAllApiV1CoreEmailTypesGetAllPostError = GetEmailTypesAllApiV1CoreEmailTypesGetAllPostErrors[keyof GetEmailTypesAllApiV1CoreEmailTypesGetAllPostErrors];

export type GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponses = {
    /**
     * Response Get Email Types All Api V1 Core Email Types Get All Post
     * Successful Response
     */
    200: EmailTypeDataTypes | EmailTypeDataColumnsTypes;
};

export type GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponse = GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponses[keyof GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponses];

export type CreateEmailsApiV1CoreEmailsPostData = {
    /**
     * Items
     */
    body: Array<EmailCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/emails/';
};

export type CreateEmailsApiV1CoreEmailsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateEmailsApiV1CoreEmailsPostError = CreateEmailsApiV1CoreEmailsPostErrors[keyof CreateEmailsApiV1CoreEmailsPostErrors];

export type CreateEmailsApiV1CoreEmailsPostResponses = {
    /**
     * Response Create Emails Api V1 Core Emails  Post
     * Successful Response
     */
    201: Array<EmailDisplayTypes>;
};

export type CreateEmailsApiV1CoreEmailsPostResponse = CreateEmailsApiV1CoreEmailsPostResponses[keyof CreateEmailsApiV1CoreEmailsPostResponses];

export type UpdateEmailsApiV1CoreEmailsPutData = {
    /**
     * Items
     */
    body: Array<EmailUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/emails/';
};

export type UpdateEmailsApiV1CoreEmailsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateEmailsApiV1CoreEmailsPutError = UpdateEmailsApiV1CoreEmailsPutErrors[keyof UpdateEmailsApiV1CoreEmailsPutErrors];

export type UpdateEmailsApiV1CoreEmailsPutResponses = {
    /**
     * Response Update Emails Api V1 Core Emails  Put
     * Successful Response
     */
    200: Array<EmailDisplayTypes>;
};

export type UpdateEmailsApiV1CoreEmailsPutResponse = UpdateEmailsApiV1CoreEmailsPutResponses[keyof UpdateEmailsApiV1CoreEmailsPutResponses];

export type DeleteEmailsApiV1CoreEmailsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/emails/{item_id}';
};

export type DeleteEmailsApiV1CoreEmailsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteEmailsApiV1CoreEmailsItemIdDeleteError = DeleteEmailsApiV1CoreEmailsItemIdDeleteErrors[keyof DeleteEmailsApiV1CoreEmailsItemIdDeleteErrors];

export type DeleteEmailsApiV1CoreEmailsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetEmailsOneApiV1CoreEmailsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/emails/{item_id}';
};

export type GetEmailsOneApiV1CoreEmailsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetEmailsOneApiV1CoreEmailsItemIdGetError = GetEmailsOneApiV1CoreEmailsItemIdGetErrors[keyof GetEmailsOneApiV1CoreEmailsItemIdGetErrors];

export type GetEmailsOneApiV1CoreEmailsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: EmailDisplayTypes;
};

export type GetEmailsOneApiV1CoreEmailsItemIdGetResponse = GetEmailsOneApiV1CoreEmailsItemIdGetResponses[keyof GetEmailsOneApiV1CoreEmailsItemIdGetResponses];

export type GetEmailsAllApiV1CoreEmailsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/emails/get-all';
};

export type GetEmailsAllApiV1CoreEmailsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetEmailsAllApiV1CoreEmailsGetAllPostError = GetEmailsAllApiV1CoreEmailsGetAllPostErrors[keyof GetEmailsAllApiV1CoreEmailsGetAllPostErrors];

export type GetEmailsAllApiV1CoreEmailsGetAllPostResponses = {
    /**
     * Response Get Emails All Api V1 Core Emails Get All Post
     * Successful Response
     */
    200: EmailDataTypes | EmailDataColumnsTypes;
};

export type GetEmailsAllApiV1CoreEmailsGetAllPostResponse = GetEmailsAllApiV1CoreEmailsGetAllPostResponses[keyof GetEmailsAllApiV1CoreEmailsGetAllPostResponses];

export type CreateCommentsApiV1CoreCommentsPostData = {
    /**
     * Items
     */
    body: Array<CommentCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/comments/';
};

export type CreateCommentsApiV1CoreCommentsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateCommentsApiV1CoreCommentsPostError = CreateCommentsApiV1CoreCommentsPostErrors[keyof CreateCommentsApiV1CoreCommentsPostErrors];

export type CreateCommentsApiV1CoreCommentsPostResponses = {
    /**
     * Response Create Comments Api V1 Core Comments  Post
     * Successful Response
     */
    201: Array<CommentDisplayTypes>;
};

export type CreateCommentsApiV1CoreCommentsPostResponse = CreateCommentsApiV1CoreCommentsPostResponses[keyof CreateCommentsApiV1CoreCommentsPostResponses];

export type UpdateCommentsApiV1CoreCommentsPutData = {
    /**
     * Items
     */
    body: Array<CommentUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/comments/';
};

export type UpdateCommentsApiV1CoreCommentsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateCommentsApiV1CoreCommentsPutError = UpdateCommentsApiV1CoreCommentsPutErrors[keyof UpdateCommentsApiV1CoreCommentsPutErrors];

export type UpdateCommentsApiV1CoreCommentsPutResponses = {
    /**
     * Response Update Comments Api V1 Core Comments  Put
     * Successful Response
     */
    200: Array<CommentDisplayTypes>;
};

export type UpdateCommentsApiV1CoreCommentsPutResponse = UpdateCommentsApiV1CoreCommentsPutResponses[keyof UpdateCommentsApiV1CoreCommentsPutResponses];

export type DeleteCommentsApiV1CoreCommentsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/comments/{item_id}';
};

export type DeleteCommentsApiV1CoreCommentsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteCommentsApiV1CoreCommentsItemIdDeleteError = DeleteCommentsApiV1CoreCommentsItemIdDeleteErrors[keyof DeleteCommentsApiV1CoreCommentsItemIdDeleteErrors];

export type DeleteCommentsApiV1CoreCommentsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetCommentsOneApiV1CoreCommentsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/comments/{item_id}';
};

export type GetCommentsOneApiV1CoreCommentsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCommentsOneApiV1CoreCommentsItemIdGetError = GetCommentsOneApiV1CoreCommentsItemIdGetErrors[keyof GetCommentsOneApiV1CoreCommentsItemIdGetErrors];

export type GetCommentsOneApiV1CoreCommentsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: CommentDisplayTypes;
};

export type GetCommentsOneApiV1CoreCommentsItemIdGetResponse = GetCommentsOneApiV1CoreCommentsItemIdGetResponses[keyof GetCommentsOneApiV1CoreCommentsItemIdGetResponses];

export type GetCommentsAllApiV1CoreCommentsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/comments/get-all';
};

export type GetCommentsAllApiV1CoreCommentsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCommentsAllApiV1CoreCommentsGetAllPostError = GetCommentsAllApiV1CoreCommentsGetAllPostErrors[keyof GetCommentsAllApiV1CoreCommentsGetAllPostErrors];

export type GetCommentsAllApiV1CoreCommentsGetAllPostResponses = {
    /**
     * Response Get Comments All Api V1 Core Comments Get All Post
     * Successful Response
     */
    200: CommentDataTypes | CommentDataColumnsTypes;
};

export type GetCommentsAllApiV1CoreCommentsGetAllPostResponse = GetCommentsAllApiV1CoreCommentsGetAllPostResponses[keyof GetCommentsAllApiV1CoreCommentsGetAllPostResponses];

export type CreateDocsApiV1CoreDocsPostData = {
    /**
     * Items
     */
    body: Array<DocCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/docs/';
};

export type CreateDocsApiV1CoreDocsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateDocsApiV1CoreDocsPostError = CreateDocsApiV1CoreDocsPostErrors[keyof CreateDocsApiV1CoreDocsPostErrors];

export type CreateDocsApiV1CoreDocsPostResponses = {
    /**
     * Response Create Docs Api V1 Core Docs  Post
     * Successful Response
     */
    201: Array<DocDisplayTypes>;
};

export type CreateDocsApiV1CoreDocsPostResponse = CreateDocsApiV1CoreDocsPostResponses[keyof CreateDocsApiV1CoreDocsPostResponses];

export type UpdateDocsApiV1CoreDocsPutData = {
    /**
     * Items
     */
    body: Array<DocUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/docs/';
};

export type UpdateDocsApiV1CoreDocsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateDocsApiV1CoreDocsPutError = UpdateDocsApiV1CoreDocsPutErrors[keyof UpdateDocsApiV1CoreDocsPutErrors];

export type UpdateDocsApiV1CoreDocsPutResponses = {
    /**
     * Response Update Docs Api V1 Core Docs  Put
     * Successful Response
     */
    200: Array<DocDisplayTypes>;
};

export type UpdateDocsApiV1CoreDocsPutResponse = UpdateDocsApiV1CoreDocsPutResponses[keyof UpdateDocsApiV1CoreDocsPutResponses];

export type GetDocTemplatesApiV1CoreDocsTemplatesPostData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Org Id
         */
        org_id: number;
        /**
         * Doc Type
         */
        doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    };
    url: '/api/v1/core/docs/templates';
};

export type GetDocTemplatesApiV1CoreDocsTemplatesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocTemplatesApiV1CoreDocsTemplatesPostError = GetDocTemplatesApiV1CoreDocsTemplatesPostErrors[keyof GetDocTemplatesApiV1CoreDocsTemplatesPostErrors];

export type GetDocTemplatesApiV1CoreDocsTemplatesPostResponses = {
    /**
     * Successful Response
     */
    200: DocTemplatesTypes;
};

export type GetDocTemplatesApiV1CoreDocsTemplatesPostResponse = GetDocTemplatesApiV1CoreDocsTemplatesPostResponses[keyof GetDocTemplatesApiV1CoreDocsTemplatesPostResponses];

export type GetTemplatesTableApiV1CoreDocsTemplatesTablePostData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Org Id
         */
        org_id: number;
        /**
         * Doc Type
         */
        doc_type?: AppSchemasCoreDocsSchemasDocTypeEnum | null;
    };
    url: '/api/v1/core/docs/templates-table';
};

export type GetTemplatesTableApiV1CoreDocsTemplatesTablePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetTemplatesTableApiV1CoreDocsTemplatesTablePostError = GetTemplatesTableApiV1CoreDocsTemplatesTablePostErrors[keyof GetTemplatesTableApiV1CoreDocsTemplatesTablePostErrors];

export type GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponses = {
    /**
     * Response Get Templates Table Api V1 Core Docs Templates Table Post
     * Successful Response
     */
    200: Array<DocTableTypes | DocPartDisplayTypes>;
};

export type GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponse = GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponses[keyof GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponses];

export type DeleteDocsApiV1CoreDocsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/docs/{item_id}';
};

export type DeleteDocsApiV1CoreDocsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteDocsApiV1CoreDocsItemIdDeleteError = DeleteDocsApiV1CoreDocsItemIdDeleteErrors[keyof DeleteDocsApiV1CoreDocsItemIdDeleteErrors];

export type DeleteDocsApiV1CoreDocsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetDocsOneApiV1CoreDocsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/docs/{item_id}';
};

export type GetDocsOneApiV1CoreDocsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocsOneApiV1CoreDocsItemIdGetError = GetDocsOneApiV1CoreDocsItemIdGetErrors[keyof GetDocsOneApiV1CoreDocsItemIdGetErrors];

export type GetDocsOneApiV1CoreDocsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: DocDisplayTypes;
};

export type GetDocsOneApiV1CoreDocsItemIdGetResponse = GetDocsOneApiV1CoreDocsItemIdGetResponses[keyof GetDocsOneApiV1CoreDocsItemIdGetResponses];

export type GetDocsAllApiV1CoreDocsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/docs/get-all';
};

export type GetDocsAllApiV1CoreDocsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocsAllApiV1CoreDocsGetAllPostError = GetDocsAllApiV1CoreDocsGetAllPostErrors[keyof GetDocsAllApiV1CoreDocsGetAllPostErrors];

export type GetDocsAllApiV1CoreDocsGetAllPostResponses = {
    /**
     * Response Get Docs All Api V1 Core Docs Get All Post
     * Successful Response
     */
    200: DocDataTypes | DocDataColumnsTypes;
};

export type GetDocsAllApiV1CoreDocsGetAllPostResponse = GetDocsAllApiV1CoreDocsGetAllPostResponses[keyof GetDocsAllApiV1CoreDocsGetAllPostResponses];

export type GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/docs/get-all-with-content';
};

export type GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostError = GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostErrors[keyof GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostErrors];

export type GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponses = {
    /**
     * Response Get Docs All With Content Api V1 Core Docs Get All With Content Post
     * Successful Response
     */
    200: DocDataTypes | DocDataColumnsTypes;
};

export type GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponse = GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponses[keyof GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponses];

export type UpdateDocsWithContentApiV1CoreDocsWithContentPutData = {
    /**
     * Items
     */
    body: Array<DocUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/docs/with-content';
};

export type UpdateDocsWithContentApiV1CoreDocsWithContentPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateDocsWithContentApiV1CoreDocsWithContentPutError = UpdateDocsWithContentApiV1CoreDocsWithContentPutErrors[keyof UpdateDocsWithContentApiV1CoreDocsWithContentPutErrors];

export type UpdateDocsWithContentApiV1CoreDocsWithContentPutResponses = {
    /**
     * Response Update Docs With Content Api V1 Core Docs With Content Put
     * Successful Response
     */
    200: Array<DocDisplayTypes>;
};

export type UpdateDocsWithContentApiV1CoreDocsWithContentPutResponse = UpdateDocsWithContentApiV1CoreDocsWithContentPutResponses[keyof UpdateDocsWithContentApiV1CoreDocsWithContentPutResponses];

export type CreateDocPagesApiV1CoreDocPagesPostData = {
    /**
     * Items
     */
    body: Array<DocPageCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/doc-pages/';
};

export type CreateDocPagesApiV1CoreDocPagesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateDocPagesApiV1CoreDocPagesPostError = CreateDocPagesApiV1CoreDocPagesPostErrors[keyof CreateDocPagesApiV1CoreDocPagesPostErrors];

export type CreateDocPagesApiV1CoreDocPagesPostResponses = {
    /**
     * Response Create Doc Pages Api V1 Core Doc Pages  Post
     * Successful Response
     */
    201: Array<DocPageDisplayTypes>;
};

export type CreateDocPagesApiV1CoreDocPagesPostResponse = CreateDocPagesApiV1CoreDocPagesPostResponses[keyof CreateDocPagesApiV1CoreDocPagesPostResponses];

export type UpdateDocPagesWithPartsApiV1CoreDocPagesPutData = {
    /**
     * Items
     */
    body: Array<DocPageUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/doc-pages/';
};

export type UpdateDocPagesWithPartsApiV1CoreDocPagesPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateDocPagesWithPartsApiV1CoreDocPagesPutError = UpdateDocPagesWithPartsApiV1CoreDocPagesPutErrors[keyof UpdateDocPagesWithPartsApiV1CoreDocPagesPutErrors];

export type UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponses = {
    /**
     * Response Update Doc Pages With Parts Api V1 Core Doc Pages  Put
     * Successful Response
     */
    200: Array<DocPageDisplayTypes>;
};

export type UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponse = UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponses[keyof UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponses];

export type DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/doc-pages/{item_id}';
};

export type DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteError = DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteErrors[keyof DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteErrors];

export type DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetDocPagesOneApiV1CoreDocPagesItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/doc-pages/{item_id}';
};

export type GetDocPagesOneApiV1CoreDocPagesItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocPagesOneApiV1CoreDocPagesItemIdGetError = GetDocPagesOneApiV1CoreDocPagesItemIdGetErrors[keyof GetDocPagesOneApiV1CoreDocPagesItemIdGetErrors];

export type GetDocPagesOneApiV1CoreDocPagesItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: DocPageDisplayTypes;
};

export type GetDocPagesOneApiV1CoreDocPagesItemIdGetResponse = GetDocPagesOneApiV1CoreDocPagesItemIdGetResponses[keyof GetDocPagesOneApiV1CoreDocPagesItemIdGetResponses];

export type GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData = {
    body: ServerOptions;
    path?: never;
    query?: never;
    url: '/api/v1/core/doc-pages/get-all-with-parts';
};

export type GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostError = GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostErrors[keyof GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostErrors];

export type GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponses = {
    /**
     * Successful Response
     */
    200: DocPageDataTypes;
};

export type GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponse = GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponses[keyof GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponses];

export type GetDocPagesAllApiV1CoreDocPagesGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/doc-pages/get-all';
};

export type GetDocPagesAllApiV1CoreDocPagesGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocPagesAllApiV1CoreDocPagesGetAllPostError = GetDocPagesAllApiV1CoreDocPagesGetAllPostErrors[keyof GetDocPagesAllApiV1CoreDocPagesGetAllPostErrors];

export type GetDocPagesAllApiV1CoreDocPagesGetAllPostResponses = {
    /**
     * Response Get Doc Pages All Api V1 Core Doc Pages Get All Post
     * Successful Response
     */
    200: DocPageDataTypes | DocPageDataColumnsTypes;
};

export type GetDocPagesAllApiV1CoreDocPagesGetAllPostResponse = GetDocPagesAllApiV1CoreDocPagesGetAllPostResponses[keyof GetDocPagesAllApiV1CoreDocPagesGetAllPostResponses];

export type CreateDocPartsApiV1CoreDocPartsPostData = {
    /**
     * Items
     */
    body: Array<DocPartCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/doc-parts/';
};

export type CreateDocPartsApiV1CoreDocPartsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateDocPartsApiV1CoreDocPartsPostError = CreateDocPartsApiV1CoreDocPartsPostErrors[keyof CreateDocPartsApiV1CoreDocPartsPostErrors];

export type CreateDocPartsApiV1CoreDocPartsPostResponses = {
    /**
     * Response Create Doc Parts Api V1 Core Doc Parts  Post
     * Successful Response
     */
    201: Array<DocPartDisplayTypes>;
};

export type CreateDocPartsApiV1CoreDocPartsPostResponse = CreateDocPartsApiV1CoreDocPartsPostResponses[keyof CreateDocPartsApiV1CoreDocPartsPostResponses];

export type UpdateDocPartsApiV1CoreDocPartsPutData = {
    /**
     * Items
     */
    body: Array<DocPartUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/doc-parts/';
};

export type UpdateDocPartsApiV1CoreDocPartsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateDocPartsApiV1CoreDocPartsPutError = UpdateDocPartsApiV1CoreDocPartsPutErrors[keyof UpdateDocPartsApiV1CoreDocPartsPutErrors];

export type UpdateDocPartsApiV1CoreDocPartsPutResponses = {
    /**
     * Response Update Doc Parts Api V1 Core Doc Parts  Put
     * Successful Response
     */
    200: Array<DocPartDisplayTypes>;
};

export type UpdateDocPartsApiV1CoreDocPartsPutResponse = UpdateDocPartsApiV1CoreDocPartsPutResponses[keyof UpdateDocPartsApiV1CoreDocPartsPutResponses];

export type DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/doc-parts/{item_id}';
};

export type DeleteDocPartsApiV1CoreDocPartsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteDocPartsApiV1CoreDocPartsItemIdDeleteError = DeleteDocPartsApiV1CoreDocPartsItemIdDeleteErrors[keyof DeleteDocPartsApiV1CoreDocPartsItemIdDeleteErrors];

export type DeleteDocPartsApiV1CoreDocPartsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetDocPartsOneApiV1CoreDocPartsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/doc-parts/{item_id}';
};

export type GetDocPartsOneApiV1CoreDocPartsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocPartsOneApiV1CoreDocPartsItemIdGetError = GetDocPartsOneApiV1CoreDocPartsItemIdGetErrors[keyof GetDocPartsOneApiV1CoreDocPartsItemIdGetErrors];

export type GetDocPartsOneApiV1CoreDocPartsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: DocPartDisplayTypes;
};

export type GetDocPartsOneApiV1CoreDocPartsItemIdGetResponse = GetDocPartsOneApiV1CoreDocPartsItemIdGetResponses[keyof GetDocPartsOneApiV1CoreDocPartsItemIdGetResponses];

export type GetDocPartsAllApiV1CoreDocPartsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/doc-parts/get-all';
};

export type GetDocPartsAllApiV1CoreDocPartsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDocPartsAllApiV1CoreDocPartsGetAllPostError = GetDocPartsAllApiV1CoreDocPartsGetAllPostErrors[keyof GetDocPartsAllApiV1CoreDocPartsGetAllPostErrors];

export type GetDocPartsAllApiV1CoreDocPartsGetAllPostResponses = {
    /**
     * Response Get Doc Parts All Api V1 Core Doc Parts Get All Post
     * Successful Response
     */
    200: DocPartDataTypes | DocPartDataColumnsTypes;
};

export type GetDocPartsAllApiV1CoreDocPartsGetAllPostResponse = GetDocPartsAllApiV1CoreDocPartsGetAllPostResponses[keyof GetDocPartsAllApiV1CoreDocPartsGetAllPostResponses];

export type CreateVariantsApiV1CoreVariantsPostData = {
    /**
     * Items
     */
    body: Array<VariantCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/variants/';
};

export type CreateVariantsApiV1CoreVariantsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateVariantsApiV1CoreVariantsPostError = CreateVariantsApiV1CoreVariantsPostErrors[keyof CreateVariantsApiV1CoreVariantsPostErrors];

export type CreateVariantsApiV1CoreVariantsPostResponses = {
    /**
     * Response Create Variants Api V1 Core Variants  Post
     * Successful Response
     */
    201: Array<VariantDisplayTypes>;
};

export type CreateVariantsApiV1CoreVariantsPostResponse = CreateVariantsApiV1CoreVariantsPostResponses[keyof CreateVariantsApiV1CoreVariantsPostResponses];

export type UpdateVariantsApiV1CoreVariantsPutData = {
    /**
     * Items
     */
    body: Array<VariantUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/core/variants/';
};

export type UpdateVariantsApiV1CoreVariantsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateVariantsApiV1CoreVariantsPutError = UpdateVariantsApiV1CoreVariantsPutErrors[keyof UpdateVariantsApiV1CoreVariantsPutErrors];

export type UpdateVariantsApiV1CoreVariantsPutResponses = {
    /**
     * Response Update Variants Api V1 Core Variants  Put
     * Successful Response
     */
    200: Array<VariantDisplayTypes>;
};

export type UpdateVariantsApiV1CoreVariantsPutResponse = UpdateVariantsApiV1CoreVariantsPutResponses[keyof UpdateVariantsApiV1CoreVariantsPutResponses];

export type DeleteVariantsApiV1CoreVariantsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/variants/{item_id}';
};

export type DeleteVariantsApiV1CoreVariantsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteVariantsApiV1CoreVariantsItemIdDeleteError = DeleteVariantsApiV1CoreVariantsItemIdDeleteErrors[keyof DeleteVariantsApiV1CoreVariantsItemIdDeleteErrors];

export type DeleteVariantsApiV1CoreVariantsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetVariantsOneApiV1CoreVariantsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/core/variants/{item_id}';
};

export type GetVariantsOneApiV1CoreVariantsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetVariantsOneApiV1CoreVariantsItemIdGetError = GetVariantsOneApiV1CoreVariantsItemIdGetErrors[keyof GetVariantsOneApiV1CoreVariantsItemIdGetErrors];

export type GetVariantsOneApiV1CoreVariantsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: VariantDisplayTypes;
};

export type GetVariantsOneApiV1CoreVariantsItemIdGetResponse = GetVariantsOneApiV1CoreVariantsItemIdGetResponses[keyof GetVariantsOneApiV1CoreVariantsItemIdGetResponses];

export type GetVariantsAllApiV1CoreVariantsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/core/variants/get-all';
};

export type GetVariantsAllApiV1CoreVariantsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetVariantsAllApiV1CoreVariantsGetAllPostError = GetVariantsAllApiV1CoreVariantsGetAllPostErrors[keyof GetVariantsAllApiV1CoreVariantsGetAllPostErrors];

export type GetVariantsAllApiV1CoreVariantsGetAllPostResponses = {
    /**
     * Response Get Variants All Api V1 Core Variants Get All Post
     * Successful Response
     */
    200: VariantDataTypes | VariantDataColumnsTypes;
};

export type GetVariantsAllApiV1CoreVariantsGetAllPostResponse = GetVariantsAllApiV1CoreVariantsGetAllPostResponses[keyof GetVariantsAllApiV1CoreVariantsGetAllPostResponses];

export type CreateAddressesApiV1CrmAddressesPostData = {
    /**
     * Items
     */
    body: Array<AddressCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/addresses/';
};

export type CreateAddressesApiV1CrmAddressesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateAddressesApiV1CrmAddressesPostError = CreateAddressesApiV1CrmAddressesPostErrors[keyof CreateAddressesApiV1CrmAddressesPostErrors];

export type CreateAddressesApiV1CrmAddressesPostResponses = {
    /**
     * Response Create Addresses Api V1 Crm Addresses  Post
     * Successful Response
     */
    201: Array<AddressDisplayTypes>;
};

export type CreateAddressesApiV1CrmAddressesPostResponse = CreateAddressesApiV1CrmAddressesPostResponses[keyof CreateAddressesApiV1CrmAddressesPostResponses];

export type UpdateAddressesApiV1CrmAddressesPutData = {
    /**
     * Items
     */
    body: Array<AddressUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/addresses/';
};

export type UpdateAddressesApiV1CrmAddressesPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateAddressesApiV1CrmAddressesPutError = UpdateAddressesApiV1CrmAddressesPutErrors[keyof UpdateAddressesApiV1CrmAddressesPutErrors];

export type UpdateAddressesApiV1CrmAddressesPutResponses = {
    /**
     * Response Update Addresses Api V1 Crm Addresses  Put
     * Successful Response
     */
    200: Array<AddressDisplayTypes>;
};

export type UpdateAddressesApiV1CrmAddressesPutResponse = UpdateAddressesApiV1CrmAddressesPutResponses[keyof UpdateAddressesApiV1CrmAddressesPutResponses];

export type DeleteAddressesApiV1CrmAddressesItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/addresses/{item_id}';
};

export type DeleteAddressesApiV1CrmAddressesItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteAddressesApiV1CrmAddressesItemIdDeleteError = DeleteAddressesApiV1CrmAddressesItemIdDeleteErrors[keyof DeleteAddressesApiV1CrmAddressesItemIdDeleteErrors];

export type DeleteAddressesApiV1CrmAddressesItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetAddressesOneApiV1CrmAddressesItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/addresses/{item_id}';
};

export type GetAddressesOneApiV1CrmAddressesItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAddressesOneApiV1CrmAddressesItemIdGetError = GetAddressesOneApiV1CrmAddressesItemIdGetErrors[keyof GetAddressesOneApiV1CrmAddressesItemIdGetErrors];

export type GetAddressesOneApiV1CrmAddressesItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: AddressDisplayTypes;
};

export type GetAddressesOneApiV1CrmAddressesItemIdGetResponse = GetAddressesOneApiV1CrmAddressesItemIdGetResponses[keyof GetAddressesOneApiV1CrmAddressesItemIdGetResponses];

export type GetAddressesAllApiV1CrmAddressesGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/crm/addresses/get-all';
};

export type GetAddressesAllApiV1CrmAddressesGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAddressesAllApiV1CrmAddressesGetAllPostError = GetAddressesAllApiV1CrmAddressesGetAllPostErrors[keyof GetAddressesAllApiV1CrmAddressesGetAllPostErrors];

export type GetAddressesAllApiV1CrmAddressesGetAllPostResponses = {
    /**
     * Response Get Addresses All Api V1 Crm Addresses Get All Post
     * Successful Response
     */
    200: AddressDataTypes | AddressDataColumnsTypes;
};

export type GetAddressesAllApiV1CrmAddressesGetAllPostResponse = GetAddressesAllApiV1CrmAddressesGetAllPostResponses[keyof GetAddressesAllApiV1CrmAddressesGetAllPostResponses];

export type CreateContactsApiV1CrmContactsPostData = {
    /**
     * Items
     */
    body: Array<ContactCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/contacts/';
};

export type CreateContactsApiV1CrmContactsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateContactsApiV1CrmContactsPostError = CreateContactsApiV1CrmContactsPostErrors[keyof CreateContactsApiV1CrmContactsPostErrors];

export type CreateContactsApiV1CrmContactsPostResponses = {
    /**
     * Response Create Contacts Api V1 Crm Contacts  Post
     * Successful Response
     */
    201: Array<ContactDisplayTypes>;
};

export type CreateContactsApiV1CrmContactsPostResponse = CreateContactsApiV1CrmContactsPostResponses[keyof CreateContactsApiV1CrmContactsPostResponses];

export type UpdateContactsApiV1CrmContactsPutData = {
    /**
     * Items
     */
    body: Array<ContactUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/contacts/';
};

export type UpdateContactsApiV1CrmContactsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateContactsApiV1CrmContactsPutError = UpdateContactsApiV1CrmContactsPutErrors[keyof UpdateContactsApiV1CrmContactsPutErrors];

export type UpdateContactsApiV1CrmContactsPutResponses = {
    /**
     * Response Update Contacts Api V1 Crm Contacts  Put
     * Successful Response
     */
    200: Array<ContactDisplayTypes>;
};

export type UpdateContactsApiV1CrmContactsPutResponse = UpdateContactsApiV1CrmContactsPutResponses[keyof UpdateContactsApiV1CrmContactsPutResponses];

export type DeleteContactsApiV1CrmContactsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/contacts/{item_id}';
};

export type DeleteContactsApiV1CrmContactsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteContactsApiV1CrmContactsItemIdDeleteError = DeleteContactsApiV1CrmContactsItemIdDeleteErrors[keyof DeleteContactsApiV1CrmContactsItemIdDeleteErrors];

export type DeleteContactsApiV1CrmContactsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetContactsOneApiV1CrmContactsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/contacts/{item_id}';
};

export type GetContactsOneApiV1CrmContactsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetContactsOneApiV1CrmContactsItemIdGetError = GetContactsOneApiV1CrmContactsItemIdGetErrors[keyof GetContactsOneApiV1CrmContactsItemIdGetErrors];

export type GetContactsOneApiV1CrmContactsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: ContactDisplayTypes;
};

export type GetContactsOneApiV1CrmContactsItemIdGetResponse = GetContactsOneApiV1CrmContactsItemIdGetResponses[keyof GetContactsOneApiV1CrmContactsItemIdGetResponses];

export type GetContactsAllApiV1CrmContactsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/crm/contacts/get-all';
};

export type GetContactsAllApiV1CrmContactsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetContactsAllApiV1CrmContactsGetAllPostError = GetContactsAllApiV1CrmContactsGetAllPostErrors[keyof GetContactsAllApiV1CrmContactsGetAllPostErrors];

export type GetContactsAllApiV1CrmContactsGetAllPostResponses = {
    /**
     * Response Get Contacts All Api V1 Crm Contacts Get All Post
     * Successful Response
     */
    200: ContactDataTypes | ContactDataColumnsTypes;
};

export type GetContactsAllApiV1CrmContactsGetAllPostResponse = GetContactsAllApiV1CrmContactsGetAllPostResponses[keyof GetContactsAllApiV1CrmContactsGetAllPostResponses];

export type CreateContrahentsApiV1CrmContrahentsPostData = {
    /**
     * Items
     */
    body: Array<ContrahentCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/contrahents/';
};

export type CreateContrahentsApiV1CrmContrahentsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateContrahentsApiV1CrmContrahentsPostError = CreateContrahentsApiV1CrmContrahentsPostErrors[keyof CreateContrahentsApiV1CrmContrahentsPostErrors];

export type CreateContrahentsApiV1CrmContrahentsPostResponses = {
    /**
     * Response Create Contrahents Api V1 Crm Contrahents  Post
     * Successful Response
     */
    201: Array<ContrahentDisplayTypes>;
};

export type CreateContrahentsApiV1CrmContrahentsPostResponse = CreateContrahentsApiV1CrmContrahentsPostResponses[keyof CreateContrahentsApiV1CrmContrahentsPostResponses];

export type UpdateContrahentsApiV1CrmContrahentsPutData = {
    /**
     * Items
     */
    body: Array<ContrahentUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/contrahents/';
};

export type UpdateContrahentsApiV1CrmContrahentsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateContrahentsApiV1CrmContrahentsPutError = UpdateContrahentsApiV1CrmContrahentsPutErrors[keyof UpdateContrahentsApiV1CrmContrahentsPutErrors];

export type UpdateContrahentsApiV1CrmContrahentsPutResponses = {
    /**
     * Response Update Contrahents Api V1 Crm Contrahents  Put
     * Successful Response
     */
    200: Array<ContrahentDisplayTypes>;
};

export type UpdateContrahentsApiV1CrmContrahentsPutResponse = UpdateContrahentsApiV1CrmContrahentsPutResponses[keyof UpdateContrahentsApiV1CrmContrahentsPutResponses];

export type DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/contrahents/{item_id}';
};

export type DeleteContrahentsApiV1CrmContrahentsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteContrahentsApiV1CrmContrahentsItemIdDeleteError = DeleteContrahentsApiV1CrmContrahentsItemIdDeleteErrors[keyof DeleteContrahentsApiV1CrmContrahentsItemIdDeleteErrors];

export type DeleteContrahentsApiV1CrmContrahentsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetContrahentsOneApiV1CrmContrahentsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/contrahents/{item_id}';
};

export type GetContrahentsOneApiV1CrmContrahentsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetContrahentsOneApiV1CrmContrahentsItemIdGetError = GetContrahentsOneApiV1CrmContrahentsItemIdGetErrors[keyof GetContrahentsOneApiV1CrmContrahentsItemIdGetErrors];

export type GetContrahentsOneApiV1CrmContrahentsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: ContrahentDisplayTypes;
};

export type GetContrahentsOneApiV1CrmContrahentsItemIdGetResponse = GetContrahentsOneApiV1CrmContrahentsItemIdGetResponses[keyof GetContrahentsOneApiV1CrmContrahentsItemIdGetResponses];

export type GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/contrahents/with-relations/{item_id}';
};

export type GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetError = GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetErrors[keyof GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetErrors];

export type GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: ContrahentWithRelationsDisplayTypes;
};

export type GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponse = GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponses[keyof GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponses];

export type GetContrahentsAllApiV1CrmContrahentsGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/crm/contrahents/get-all';
};

export type GetContrahentsAllApiV1CrmContrahentsGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetContrahentsAllApiV1CrmContrahentsGetAllPostError = GetContrahentsAllApiV1CrmContrahentsGetAllPostErrors[keyof GetContrahentsAllApiV1CrmContrahentsGetAllPostErrors];

export type GetContrahentsAllApiV1CrmContrahentsGetAllPostResponses = {
    /**
     * Response Get Contrahents All Api V1 Crm Contrahents Get All Post
     * Successful Response
     */
    200: ContrahentDataTypes | ContrahentDataColumnsTypes;
};

export type GetContrahentsAllApiV1CrmContrahentsGetAllPostResponse = GetContrahentsAllApiV1CrmContrahentsGetAllPostResponses[keyof GetContrahentsAllApiV1CrmContrahentsGetAllPostResponses];

export type ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData = {
    body?: never;
    path: {
        /**
         * Org Id
         */
        org_id: number;
    };
    query?: {
        /**
         * Skip
         */
        skip?: number;
        /**
         * Limit
         */
        limit?: number;
    };
    url: '/api/v1/crm/contrahents/org/{org_id}';
};

export type ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetError = ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetErrors[keyof ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetErrors];

export type ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponses = {
    /**
     * Successful Response
     */
    200: ContrahentDataTypes;
};

export type ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponse = ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponses[keyof ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponses];

export type GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData = {
    body?: never;
    path: {
        /**
         * Org Id
         */
        org_id: number;
    };
    query?: never;
    url: '/api/v1/crm/contrahents/car-owners/{org_id}';
};

export type GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetError = GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetErrors[keyof GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetErrors];

export type GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponses = {
    /**
     * Response Get Car Owners Api V1 Crm Contrahents Car Owners  Org Id  Get
     * Successful Response
     */
    200: Array<ContrahentWithVehiclesDisplayTypes>;
};

export type GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponse = GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponses[keyof GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponses];

export type CreateProfilesApiV1CrmProfilesPostData = {
    /**
     * Items
     */
    body: Array<ProfileCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/profiles/';
};

export type CreateProfilesApiV1CrmProfilesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateProfilesApiV1CrmProfilesPostError = CreateProfilesApiV1CrmProfilesPostErrors[keyof CreateProfilesApiV1CrmProfilesPostErrors];

export type CreateProfilesApiV1CrmProfilesPostResponses = {
    /**
     * Response Create Profiles Api V1 Crm Profiles  Post
     * Successful Response
     */
    201: Array<ProfileDisplayTypes>;
};

export type CreateProfilesApiV1CrmProfilesPostResponse = CreateProfilesApiV1CrmProfilesPostResponses[keyof CreateProfilesApiV1CrmProfilesPostResponses];

export type UpdateProfilesApiV1CrmProfilesPutData = {
    /**
     * Items
     */
    body: Array<ProfileUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/profiles/';
};

export type UpdateProfilesApiV1CrmProfilesPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateProfilesApiV1CrmProfilesPutError = UpdateProfilesApiV1CrmProfilesPutErrors[keyof UpdateProfilesApiV1CrmProfilesPutErrors];

export type UpdateProfilesApiV1CrmProfilesPutResponses = {
    /**
     * Response Update Profiles Api V1 Crm Profiles  Put
     * Successful Response
     */
    200: Array<ProfileDisplayTypes>;
};

export type UpdateProfilesApiV1CrmProfilesPutResponse = UpdateProfilesApiV1CrmProfilesPutResponses[keyof UpdateProfilesApiV1CrmProfilesPutResponses];

export type DeleteProfilesApiV1CrmProfilesItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/profiles/{item_id}';
};

export type DeleteProfilesApiV1CrmProfilesItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteProfilesApiV1CrmProfilesItemIdDeleteError = DeleteProfilesApiV1CrmProfilesItemIdDeleteErrors[keyof DeleteProfilesApiV1CrmProfilesItemIdDeleteErrors];

export type DeleteProfilesApiV1CrmProfilesItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetProfilesOneApiV1CrmProfilesItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/profiles/{item_id}';
};

export type GetProfilesOneApiV1CrmProfilesItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetProfilesOneApiV1CrmProfilesItemIdGetError = GetProfilesOneApiV1CrmProfilesItemIdGetErrors[keyof GetProfilesOneApiV1CrmProfilesItemIdGetErrors];

export type GetProfilesOneApiV1CrmProfilesItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: ProfileDisplayTypes;
};

export type GetProfilesOneApiV1CrmProfilesItemIdGetResponse = GetProfilesOneApiV1CrmProfilesItemIdGetResponses[keyof GetProfilesOneApiV1CrmProfilesItemIdGetResponses];

export type GetProfilesAllApiV1CrmProfilesGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/crm/profiles/get-all';
};

export type GetProfilesAllApiV1CrmProfilesGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetProfilesAllApiV1CrmProfilesGetAllPostError = GetProfilesAllApiV1CrmProfilesGetAllPostErrors[keyof GetProfilesAllApiV1CrmProfilesGetAllPostErrors];

export type GetProfilesAllApiV1CrmProfilesGetAllPostResponses = {
    /**
     * Response Get Profiles All Api V1 Crm Profiles Get All Post
     * Successful Response
     */
    200: ProfileDataTypes | ProfileDataColumnsTypes;
};

export type GetProfilesAllApiV1CrmProfilesGetAllPostResponse = GetProfilesAllApiV1CrmProfilesGetAllPostResponses[keyof GetProfilesAllApiV1CrmProfilesGetAllPostResponses];

export type CreateTypesApiV1CrmTypesPostData = {
    /**
     * Items
     */
    body: Array<TypeCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/types/';
};

export type CreateTypesApiV1CrmTypesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateTypesApiV1CrmTypesPostError = CreateTypesApiV1CrmTypesPostErrors[keyof CreateTypesApiV1CrmTypesPostErrors];

export type CreateTypesApiV1CrmTypesPostResponses = {
    /**
     * Response Create Types Api V1 Crm Types  Post
     * Successful Response
     */
    201: Array<TypeDisplayTypes>;
};

export type CreateTypesApiV1CrmTypesPostResponse = CreateTypesApiV1CrmTypesPostResponses[keyof CreateTypesApiV1CrmTypesPostResponses];

export type UpdateTypesApiV1CrmTypesPutData = {
    /**
     * Items
     */
    body: Array<TypeUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/crm/types/';
};

export type UpdateTypesApiV1CrmTypesPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateTypesApiV1CrmTypesPutError = UpdateTypesApiV1CrmTypesPutErrors[keyof UpdateTypesApiV1CrmTypesPutErrors];

export type UpdateTypesApiV1CrmTypesPutResponses = {
    /**
     * Response Update Types Api V1 Crm Types  Put
     * Successful Response
     */
    200: Array<TypeDisplayTypes>;
};

export type UpdateTypesApiV1CrmTypesPutResponse = UpdateTypesApiV1CrmTypesPutResponses[keyof UpdateTypesApiV1CrmTypesPutResponses];

export type DeleteTypesApiV1CrmTypesItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/types/{item_id}';
};

export type DeleteTypesApiV1CrmTypesItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteTypesApiV1CrmTypesItemIdDeleteError = DeleteTypesApiV1CrmTypesItemIdDeleteErrors[keyof DeleteTypesApiV1CrmTypesItemIdDeleteErrors];

export type DeleteTypesApiV1CrmTypesItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetTypesOneApiV1CrmTypesItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/crm/types/{item_id}';
};

export type GetTypesOneApiV1CrmTypesItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetTypesOneApiV1CrmTypesItemIdGetError = GetTypesOneApiV1CrmTypesItemIdGetErrors[keyof GetTypesOneApiV1CrmTypesItemIdGetErrors];

export type GetTypesOneApiV1CrmTypesItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: TypeDisplayTypes;
};

export type GetTypesOneApiV1CrmTypesItemIdGetResponse = GetTypesOneApiV1CrmTypesItemIdGetResponses[keyof GetTypesOneApiV1CrmTypesItemIdGetResponses];

export type GetTypesAllApiV1CrmTypesGetAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/crm/types/get-all';
};

export type GetTypesAllApiV1CrmTypesGetAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetTypesAllApiV1CrmTypesGetAllPostError = GetTypesAllApiV1CrmTypesGetAllPostErrors[keyof GetTypesAllApiV1CrmTypesGetAllPostErrors];

export type GetTypesAllApiV1CrmTypesGetAllPostResponses = {
    /**
     * Response Get Types All Api V1 Crm Types Get All Post
     * Successful Response
     */
    200: TypeDataTypes | TypeDataColumnsTypes;
};

export type GetTypesAllApiV1CrmTypesGetAllPostResponse = GetTypesAllApiV1CrmTypesGetAllPostResponses[keyof GetTypesAllApiV1CrmTypesGetAllPostResponses];

export type GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Org Id
         * Organization ID
         */
        org_id: number;
        /**
         * Is Public
         * Filter by public status
         */
        is_public?: boolean | null;
        /**
         * Contrahent Id
         * Filter by contrahent ID
         */
        contrahent_id?: number | null;
        /**
         * Date Column
         * Date column to filter on (e.g., 'start_date', 'end_date')
         */
        date_column?: string | null;
        /**
         * Start Date
         * Start date in YYYY-MM-DD format
         */
        start_date?: string | null;
        /**
         * End Date
         * End date in YYYY-MM-DD format
         */
        end_date?: string | null;
        /**
         * Statuses
         * Comma-separated list of statuses to filter by
         */
        statuses?: string | null;
        /**
         * Page Index
         * Page index (0-based)
         */
        page_index?: number;
        /**
         * Page Size
         * Number of items per page
         */
        page_size?: number | null;
        /**
         * Search
         * JSON string of search terms
         */
        search?: string | null;
        /**
         * Order
         * JSON string of order columns and directions
         */
        order?: string | null;
        /**
         * Columns
         * JSON string of columns to display
         */
        columns?: string | null;
    };
    url: '/api/v1/money/org-transactions/';
};

export type GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetError = GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetErrors[keyof GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetErrors];

export type GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponses = {
    /**
     * Response Get Org Transactions All Api V1 Money Org Transactions  Get
     * Successful Response
     */
    200: OrgTransactionDataTypes | OrgTransactionDisplayColumnsTypes;
};

export type GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponse = GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponses[keyof GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponses];

export type CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData = {
    /**
     * Items
     */
    body: Array<OrgTransactionCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/money/org-transactions/';
};

export type CreateOrgTransactionsApiV1MoneyOrgTransactionsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrgTransactionsApiV1MoneyOrgTransactionsPostError = CreateOrgTransactionsApiV1MoneyOrgTransactionsPostErrors[keyof CreateOrgTransactionsApiV1MoneyOrgTransactionsPostErrors];

export type CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponses = {
    /**
     * Response Create Org Transactions Api V1 Money Org Transactions  Post
     * Successful Response
     */
    201: Array<OrgTransactionDisplayTypes>;
};

export type CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponse = CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponses[keyof CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponses];

export type UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData = {
    /**
     * Items
     */
    body: Array<OrgTransactionUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/money/org-transactions/';
};

export type UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutError = UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutErrors[keyof UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutErrors];

export type UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponses = {
    /**
     * Response Update Org Transactions Api V1 Money Org Transactions  Put
     * Successful Response
     */
    200: Array<OrgTransactionDisplayTypes>;
};

export type UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponse = UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponses[keyof UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponses];

export type CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData = {
    /**
     * Items
     */
    body: Array<OrgTransactionCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/money/org-transactions/saved';
};

export type CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostError = CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostErrors[keyof CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostErrors];

export type CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponses = {
    /**
     * Response Create Org Transactions Saved Api V1 Money Org Transactions Saved Post
     * Successful Response
     */
    200: Array<OrgTransactionDisplayTypes>;
};

export type CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponse = CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponses[keyof CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponses];

export type DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/money/org-transactions/{item_id}';
};

export type DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteError = DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteErrors[keyof DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteErrors];

export type DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/money/org-transactions/{item_id}';
};

export type GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetError = GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetErrors[keyof GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetErrors];

export type GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrgTransactionDisplayTypes;
};

export type GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponse = GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponses[keyof GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponses];

export type ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData = {
    body?: never;
    path: {
        /**
         * Org Id
         */
        org_id: number;
    };
    query?: {
        /**
         * Skip
         */
        skip?: number;
        /**
         * Limit
         */
        limit?: number;
    };
    url: '/api/v1/money/org-transactions/saved/{org_id}';
};

export type ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetError = ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetErrors[keyof ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetErrors];

export type ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrgTransactionDataTypes;
};

export type ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponse = ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponses[keyof ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponses];

export type ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData = {
    body?: never;
    path: {
        /**
         * Org Id
         */
        org_id: number;
    };
    query: {
        /**
         * Period Start
         */
        period_start: string;
        /**
         * Period End
         */
        period_end: string;
    };
    url: '/api/v1/money/org-transactions/scheduled/{org_id}';
};

export type ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetError = ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetErrors[keyof ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetErrors];

export type ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrgTransactionScheduledTypes;
};

export type ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponse = ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponses[keyof ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponses];

export type CreateOrgSplitsApiV1MoneyOrgSplitsPostData = {
    /**
     * Items
     */
    body: Array<OrgSplitCreateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/money/org-splits/';
};

export type CreateOrgSplitsApiV1MoneyOrgSplitsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrgSplitsApiV1MoneyOrgSplitsPostError = CreateOrgSplitsApiV1MoneyOrgSplitsPostErrors[keyof CreateOrgSplitsApiV1MoneyOrgSplitsPostErrors];

export type CreateOrgSplitsApiV1MoneyOrgSplitsPostResponses = {
    /**
     * Response Create Org Splits Api V1 Money Org Splits  Post
     * Successful Response
     */
    201: Array<OrgSplitDisplayTypes>;
};

export type CreateOrgSplitsApiV1MoneyOrgSplitsPostResponse = CreateOrgSplitsApiV1MoneyOrgSplitsPostResponses[keyof CreateOrgSplitsApiV1MoneyOrgSplitsPostResponses];

export type UpdateOrgSplitsApiV1MoneyOrgSplitsPutData = {
    /**
     * Items
     */
    body: Array<OrgSplitUpdateTypes>;
    path?: never;
    query?: never;
    url: '/api/v1/money/org-splits/';
};

export type UpdateOrgSplitsApiV1MoneyOrgSplitsPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrgSplitsApiV1MoneyOrgSplitsPutError = UpdateOrgSplitsApiV1MoneyOrgSplitsPutErrors[keyof UpdateOrgSplitsApiV1MoneyOrgSplitsPutErrors];

export type UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponses = {
    /**
     * Response Update Org Splits Api V1 Money Org Splits  Put
     * Successful Response
     */
    200: Array<OrgSplitDisplayTypes>;
};

export type UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponse = UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponses[keyof UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponses];

export type DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/money/org-splits/{item_id}';
};

export type DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteError = DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteErrors[keyof DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteErrors];

export type DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData = {
    body?: never;
    path: {
        /**
         * Item Id
         */
        item_id: number;
    };
    query?: never;
    url: '/api/v1/money/org-splits/{item_id}';
};

export type GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetError = GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetErrors[keyof GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetErrors];

export type GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrgSplitDisplayTypes;
};

export type GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponse = GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponses[keyof GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponses];

export type GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/money/org-splits/all';
};

export type GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostError = GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostErrors[keyof GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostErrors];

export type GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponses = {
    /**
     * Response Get Org Splits All Api V1 Money Org Splits All Post
     * Successful Response
     */
    200: OrgSplitDataTypes | OrgSplitDataColumnsTypes;
};

export type GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponse = GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponses[keyof GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponses];

export type GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/money/org-splits/all-raw-sql';
};

export type GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostError = GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostErrors[keyof GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostErrors];

export type GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponses = {
    /**
     * Successful Response
     */
    200: GenericDataResponse;
};

export type GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponse = GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponses[keyof GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponses];

export type GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData = {
    body: ServerOptions;
    path?: never;
    query?: {
        /**
         * Skip Validation
         */
        skip_validation?: boolean;
    };
    url: '/api/v1/money/org-splits/all-core';
};

export type GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostError = GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostErrors[keyof GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostErrors];

export type GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponses = {
    /**
     * Successful Response
     */
    200: GenericDataResponse;
};

export type GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponse = GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponses[keyof GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponses];

export type GetJobJobsDatesJobIdGetData = {
    body?: never;
    path: {
        /**
         * Job Id
         */
        job_id: number;
    };
    query?: never;
    url: '/jobs-dates/{job_id}';
};

export type GetJobJobsDatesJobIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetJobJobsDatesJobIdGetError = GetJobJobsDatesJobIdGetErrors[keyof GetJobJobsDatesJobIdGetErrors];

export type GetJobJobsDatesJobIdGetResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type HealthCheckHealthGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/health';
};

export type HealthCheckHealthGetResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type ClientOptions = {
    baseUrl: 'http://127.0.0.1:8005' | (string & {});
};