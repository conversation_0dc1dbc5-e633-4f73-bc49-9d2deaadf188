// This file is auto-generated by @hey-api/openapi-ts

import { z } from 'zod';

/**
 * AddressCreateTypes
 */
export const zAddressCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    city: z.string(),
    country: z.string().optional().default('Poland'),
    area1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    area2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street: z.union([
        z.string(),
        z.null()
    ]).optional(),
    postal_code: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lon: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    lat: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    location: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    local_no: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * AddressDisplayColumnsTypes
 */
export const zAddressDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    country: z.union([
        z.string(),
        z.null()
    ]).optional(),
    area1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    area2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street: z.union([
        z.string(),
        z.null()
    ]).optional(),
    city: z.union([
        z.string(),
        z.null()
    ]).optional(),
    postal_code: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lat: z.union([
        z.string(),
        z.null()
    ]).optional(),
    location: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    local_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * Pagination
 */
export const zPagination = z.object({
    pageIndex: z.number().int(),
    pageSize: z.number().int(),
    totalItems: z.number().int().optional().default(0)
});

/**
 * AddressDataColumnsTypes
 */
export const zAddressDataColumnsTypes = z.object({
    data: z.array(zAddressDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * AddressDisplayTypes
 */
export const zAddressDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    country: z.string().optional().default('Poland'),
    area1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    area2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street: z.union([
        z.string(),
        z.null()
    ]).optional(),
    city: z.string(),
    postal_code: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lat: z.union([
        z.string(),
        z.null()
    ]).optional(),
    location: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    local_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * AddressDataTypes
 */
export const zAddressDataTypes = z.object({
    data: z.array(zAddressDisplayTypes),
    pagination: zPagination
});

/**
 * AddressUpdateTypes
 */
export const zAddressUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    country: z.union([
        z.string(),
        z.null()
    ]).optional(),
    area1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    area2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street: z.union([
        z.string(),
        z.null()
    ]).optional(),
    city: z.union([
        z.string(),
        z.null()
    ]).optional(),
    postal_code: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lon: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    lat: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    location: z.union([
        z.string(),
        z.null()
    ]).optional(),
    street_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    local_no: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * ColumnPinning
 */
export const zColumnPinning = z.object({
    left: z.array(z.string()).optional().default([]),
    right: z.array(z.string()).optional().default([])
});

/**
 * Sorting
 */
export const zSorting = z.object({
    id: z.string(),
    desc: z.boolean()
});

/**
 * SizingInfo
 */
export const zSizingInfo = z.object({
    startOffset: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    startSize: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    deltaOffset: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    deltaPercentage: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    isResizingColumn: z.boolean().optional().default(false),
    columnSizingStart: z.array(z.number().int()).optional().default([])
});

/**
 * ClientPagination
 */
export const zClientPagination = z.object({
    pageIndex: z.number().int(),
    pageSize: z.number().int()
});

/**
 * RowPinning
 */
export const zRowPinning = z.object({
    top: z.array(z.string()).optional().default([]),
    bottom: z.array(z.string()).optional().default([])
});

/**
 * ClientOptionsTypes
 */
export const zClientOptionsTypes = z.object({
    columnVisibility: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    columnOrder: z.union([
        z.array(z.string()),
        z.null()
    ]).optional(),
    columnPinning: z.union([
        zColumnPinning,
        z.null()
    ]).optional(),
    columnFilters: z.union([
        z.array(z.object({})),
        z.null()
    ]).optional(),
    globalFilter: z.union([
        z.string(),
        z.null()
    ]).optional(),
    sorting: z.union([
        z.array(zSorting),
        z.null()
    ]).optional(),
    expanded: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    grouping: z.union([
        z.array(z.string()),
        z.null()
    ]).optional(),
    columnSizing: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    columnSizingInfo: z.union([
        zSizingInfo,
        z.null()
    ]).optional(),
    pagination: z.union([
        zClientPagination,
        z.null()
    ]).optional(),
    rowSelection: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    rowPinning: z.union([
        zRowPinning,
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    date_column: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * Columns
 */
export const zColumns = z.object({
    id: z.string(),
    term: z.string()
});

/**
 * CommentCreateTypes
 */
export const zCommentCreateTypes = z.object({
    created_by: z.number().int(),
    content: z.string(),
    lang: z.string(),
    parent_table: z.string(),
    parent_id: z.number().int()
});

/**
 * CommentDisplayColumnsTypes
 */
export const zCommentDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    content: z.union([
        z.string(),
        z.null()
    ]).optional(),
    creator_display_name: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * CommentDataColumnsTypes
 */
export const zCommentDataColumnsTypes = z.object({
    data: z.array(zCommentDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * CommentDisplayTypes
 */
export const zCommentDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    lang: z.string(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    parent_table: z.string(),
    parent_id: z.number().int(),
    content: z.string(),
    creator_display_name: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * CommentDataTypes
 */
export const zCommentDataTypes = z.object({
    data: z.array(zCommentDisplayTypes),
    pagination: zPagination
});

/**
 * CommentUpdateTypes
 */
export const zCommentUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int(),
    content: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * ContactCreateTypes
 */
export const zContactCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * ContactDisplayColumnsTypes
 */
export const zContactDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * ContactDataColumnsTypes
 */
export const zContactDataColumnsTypes = z.object({
    data: z.array(zContactDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * ContactDisplayTypes
 */
export const zContactDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * ContactDataTypes
 */
export const zContactDataTypes = z.object({
    data: z.array(zContactDisplayTypes),
    pagination: zPagination
});

/**
 * ContactUpdateTypes
 */
export const zContactUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * ContrahentBasicTypes
 */
export const zContrahentBasicTypes = z.object({
    id: z.number().int(),
    name: z.string()
});

/**
 * TypeDisplay
 */
export const zTypeDisplay = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * ContrahentCreateTypes
 */
export const zContrahentCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    region: z.string().optional().default('eu'),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    user_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keywords: z.array(z.string()).optional(),
    types: z.array(zTypeDisplay).min(1),
    accounts_ids: z.union([
        z.array(z.number().int()),
        z.null()
    ]).optional()
});

/**
 * TokenData
 */
export const zTokenData = z.object({
    access_token: z.union([
        z.string(),
        z.null()
    ]).optional(),
    expiry_date: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    id_token: z.union([
        z.string(),
        z.null()
    ]).optional(),
    refresh_token: z.union([
        z.string(),
        z.null()
    ]).optional(),
    scope: z.union([
        z.string(),
        z.null()
    ]).optional(),
    token_type: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * S3Data
 */
export const zS3Data = z.object({
    bucket_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    access_key_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    secret_access_key: z.union([
        z.string(),
        z.null()
    ]).optional(),
    account_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    endpoint: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * DropboxData
 */
export const zDropboxData = z.object({
    access_token: z.union([
        z.string(),
        z.null()
    ]).optional(),
    account_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    folder: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * StorageData
 */
export const zStorageData = z.object({
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tokens: z.union([
        zTokenData,
        z.null()
    ]).optional(),
    storage_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    s3: z.union([
        zS3Data,
        z.null()
    ]).optional(),
    dropbox: z.union([
        zDropboxData,
        z.null()
    ]).optional()
});

/**
 * JsonMetadata
 */
export const zJsonMetadataOutput = z.object({
    storage: z.union([
        zStorageData,
        z.null()
    ]).optional()
});

/**
 * OrgDisplayTypes
 */
export const zOrgDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        zJsonMetadataOutput,
        z.null()
    ]).optional(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.boolean().optional().default(true),
    address_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_formal: z.boolean().optional().default(false),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    max_img_width: z.number().int().optional().default(1500),
    total_shares: z.number().int().optional().default(4),
    accounts_set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    voting_days: z.number().int().optional().default(7),
    members_by_admin: z.boolean().optional().default(true),
    curr_acc_period_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_acc_period_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_acc_period_start: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    curr_acc_period_end: z.union([
        z.string().date(),
        z.null()
    ]).optional()
});

/**
 * ContrahentDisplayColumnsTypes
 */
export const zContrahentDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    user_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keywords: z.union([
        z.array(z.string()),
        z.null()
    ]).optional(),
    types: z.union([
        z.array(zTypeDisplay),
        z.null()
    ]).optional(),
    accounts_ids: z.union([
        z.array(z.number().int()),
        z.null()
    ]).optional(),
    orgs: z.union([
        z.array(zOrgDisplayTypes),
        z.null()
    ]).optional(),
    profile: z.union([
        z.unknown(),
        z.null()
    ]).optional()
});

/**
 * ContrahentDataColumnsTypes
 */
export const zContrahentDataColumnsTypes = z.object({
    data: z.array(zContrahentDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * ContrahentDisplayTypes
 */
export const zContrahentDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    region: z.string().optional().default('eu'),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    user_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keywords: z.array(z.string()).optional(),
    accounts_ids: z.array(z.number().int()).optional()
});

/**
 * ContrahentDataTypes
 */
export const zContrahentDataTypes = z.object({
    data: z.array(zContrahentDisplayTypes),
    pagination: zPagination
});

/**
 * ContrahentUpdateTypes
 */
export const zContrahentUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    region: z.union([
        z.string(),
        z.null()
    ]).optional(),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    user_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keywords: z.union([
        z.array(z.string()),
        z.null()
    ]).optional(),
    contrahent_type_ids: z.union([
        z.array(z.number().int()).min(1),
        z.null()
    ]).optional(),
    accounts_ids: z.union([
        z.array(z.number().int()).min(1),
        z.null()
    ]).optional()
});

/**
 * ContrahentWithRelationsDisplayTypes
 */
export const zContrahentWithRelationsDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    region: z.string().optional().default('eu'),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    user_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keywords: z.array(z.string()).optional(),
    types: z.array(zTypeDisplay).min(1),
    accounts_ids: z.array(z.number().int()).optional(),
    orgs: z.array(zOrgDisplayTypes).optional(),
    profile: z.union([
        z.unknown(),
        z.null()
    ]).optional()
});

/**
 * FilesDisplayTypes
 */
export const zFilesDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    file_name: z.string(),
    th_file_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    file_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_file_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_data: z.union([
        z.string(),
        z.null()
    ]).optional(),
    s3_user_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    s3_bucket: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_object_extra: z.boolean().optional().default(false),
    is_doc: z.boolean().optional().default(false),
    generated_pdf_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    mimetype: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * SystemType
 */
export const zSystemType = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    object_id: z.number().int(),
    system_type: z.string(),
    systems_files: z.array(zFilesDisplayTypes).optional().default([])
});

/**
 * ObjectDisplayTypes
 */
export const zObjectDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_type: z.string(),
    vehicle_owner_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    object_type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    object_type_group: z.union([
        z.string(),
        z.null()
    ]).optional(),
    width: z.union([
        z.number(),
        z.null()
    ]).optional(),
    length: z.union([
        z.number(),
        z.null()
    ]).optional(),
    height: z.union([
        z.number(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    tree_level: z.number().int(),
    org_id: z.number().int(),
    surface: z.union([
        z.number(),
        z.null()
    ]).optional(),
    volume: z.union([
        z.number(),
        z.null()
    ]).optional(),
    circumference: z.union([
        z.number(),
        z.null()
    ]).optional(),
    is_branch: z.boolean().optional().default(false),
    documents: z.array(zFilesDisplayTypes).optional().default([]),
    photos: z.array(zFilesDisplayTypes).optional().default([]),
    other_files: z.array(zFilesDisplayTypes).optional().default([]),
    systems: z.array(zSystemType).optional().default([]),
    path: z.array(z.string()).optional().default([])
});

/**
 * ContrahentWithVehiclesDisplayTypes
 */
export const zContrahentWithVehiclesDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    region: z.string().optional().default('eu'),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    user_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keywords: z.array(z.string()).optional(),
    accounts_ids: z.array(z.number().int()).optional(),
    vehicles: z.array(zObjectDisplayTypes).optional()
});

/**
 * DashboardModules
 */
export const zDashboardModules = z.object({
    proposals: z.boolean().optional().default(true),
    jobs: z.boolean().optional().default(true),
    trips: z.boolean().optional().default(true),
    transactions: z.boolean().optional().default(true),
    problems: z.boolean().optional().default(true),
    notices: z.boolean().optional().default(true),
    contrahent_jobs: z.boolean().optional().default(true),
    contrahent_transactions: z.boolean().optional().default(true)
});

/**
 * DocTypeEnum
 */
export const zAppSchemasCoreDocsSchemasDocTypeEnum = z.enum([
    'proposal',
    'uchwala',
    'email'
]);

/**
 * DocTypeEnum
 */
export const zAppSchemasCoreDocPartsSchemasDocTypeEnum = z.enum([
    'proposal',
    'uchwala',
    'email',
    'invoice',
    'prices'
]);

/**
 * PartTypeEnum
 */
export const zPartTypeEnum = z.enum([
    'document',
    'top',
    'header',
    'pre',
    'content_text',
    'content_table',
    'post',
    'footer',
    'bottom'
]);

/**
 * DocPartCreateTypes
 */
export const zDocPartCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    org_id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocPartsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    type: zPartTypeEnum,
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * DocPageCreateTypes
 */
export const zDocPageCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    org_id: z.number().int().optional().default(1),
    page_number: z.number().int().optional().default(1),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    content: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    is_template: z.boolean()
});

/**
 * DocCreateTypes
 */
export const zDocCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    is_template: z.boolean(),
    is_draft: z.boolean(),
    pdf_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    pages: z.union([
        z.array(zDocPageCreateTypes),
        z.null()
    ]).optional(),
    parts_top: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    parts_header: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    parts_pre: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    parts_post: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    parts_footer: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    parts_bottom: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional()
});

/**
 * DocPartDisplayTypes
 */
export const zDocPartDisplayTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    lang: z.string(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocPartsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    type: z.union([
        zPartTypeEnum,
        z.null()
    ]).optional(),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * DocPageDisplayTypes
 */
export const zDocPageDisplayTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    content: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    page_number: z.number().int(),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * DocDisplayColumnsTypes
 */
export const zDocDisplayColumnsTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    lang: z.string(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_draft: z.boolean(),
    pages: z.union([
        z.array(zDocPageDisplayTypes),
        z.null()
    ]).optional(),
    parts_top: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_header: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_pre: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_post: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_footer: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_bottom: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional()
});

/**
 * DocDataColumnsTypes
 */
export const zDocDataColumnsTypes = z.object({
    data: z.array(zDocDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * DocDisplayTypes
 */
export const zDocDisplayTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    lang: z.string(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_draft: z.boolean(),
    pages: z.union([
        z.array(zDocPageDisplayTypes),
        z.null()
    ]).optional(),
    parts_top: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_header: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_pre: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_post: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_footer: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    parts_bottom: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional()
});

/**
 * DocDataTypes
 */
export const zDocDataTypes = z.object({
    data: z.array(zDocDisplayTypes),
    pagination: zPagination
});

/**
 * DocPageDisplayColumnsTypes
 */
export const zDocPageDisplayColumnsTypes = z.object({
    id: z.number().int(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    content: z.union([
        z.array(zDocPartDisplayTypes),
        z.null()
    ]).optional(),
    page_number: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * DocPageDataColumnsTypes
 */
export const zDocPageDataColumnsTypes = z.object({
    data: z.array(zDocPageDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * DocPageDataTypes
 */
export const zDocPageDataTypes = z.object({
    data: z.array(zDocPageDisplayTypes),
    pagination: zPagination
});

/**
 * DocPageUpdateTypes
 */
export const zDocPageUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    content: z.union([
        z.array(zDocPartCreateTypes),
        z.null()
    ]).optional(),
    page_number: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional()
});

/**
 * DocPartDisplayColumnsTypes
 */
export const zDocPartDisplayColumnsTypes = z.object({
    id: z.number().int(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocPartsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    type: z.union([
        zPartTypeEnum,
        z.null()
    ]).optional(),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * DocPartDataColumnsTypes
 */
export const zDocPartDataColumnsTypes = z.object({
    data: z.array(zDocPartDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * DocPartDataTypes
 */
export const zDocPartDataTypes = z.object({
    data: z.array(zDocPartDisplayTypes),
    pagination: zPagination
});

/**
 * DocPartUpdateTypes
 */
export const zDocPartUpdateTypes = z.object({
    id: z.union([
        z.number().int(),
        z.string()
    ]),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocPartsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    type: zPartTypeEnum,
    is_template: z.boolean()
});

/**
 * DocTableTypes
 */
export const zDocTableTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    lang: z.string(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    type: z.string().optional().default('document'),
    is_template: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * DocTemplatesTypes
 */
export const zDocTemplatesTypes = z.object({
    doc_templates: z.array(zDocDisplayTypes),
    parts_top: z.array(zDocPartDisplayTypes),
    parts_header: z.array(zDocPartDisplayTypes),
    parts_pre: z.array(zDocPartDisplayTypes),
    parts_content_text: z.array(zDocPartDisplayTypes),
    parts_content_table: z.array(zDocPartDisplayTypes),
    parts_post: z.array(zDocPartDisplayTypes),
    parts_footer: z.array(zDocPartDisplayTypes),
    parts_bottom: z.array(zDocPartDisplayTypes)
});

/**
 * DocUpdateTypes
 */
export const zDocUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    mjml: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    doc_type: z.union([
        zAppSchemasCoreDocsSchemasDocTypeEnum,
        z.null()
    ]).optional(),
    is_draft: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    pdf_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    pages: z.union([
        z.array(zDocPageUpdateTypes),
        z.null()
    ]).optional(),
    parts_top: z.union([
        z.array(zDocPartUpdateTypes),
        z.null()
    ]).optional(),
    parts_header: z.union([
        z.array(zDocPartUpdateTypes),
        z.null()
    ]).optional(),
    parts_pre: z.union([
        z.array(zDocPartUpdateTypes),
        z.null()
    ]).optional(),
    parts_post: z.union([
        z.array(zDocPartUpdateTypes),
        z.null()
    ]).optional(),
    parts_footer: z.union([
        z.array(zDocPartUpdateTypes),
        z.null()
    ]).optional(),
    parts_bottom: z.union([
        z.array(zDocPartUpdateTypes),
        z.null()
    ]).optional()
});

/**
 * EmailCreateTypes
 */
export const zEmailCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    bcc: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    type_id: z.number().int(),
    subject: z.string(),
    text: z.string(),
    html: z.string(),
    is_sent: z.boolean().optional().default(false)
});

/**
 * EmailDisplayColumnsTypes
 */
export const zEmailDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    bcc: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    subject: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_sent: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * EmailDataColumnsTypes
 */
export const zEmailDataColumnsTypes = z.object({
    data: z.array(zEmailDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * EmailDisplayTypes
 */
export const zEmailDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    bcc: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    type_id: z.number().int(),
    subject: z.string(),
    text: z.string(),
    html: z.string(),
    is_sent: z.boolean().optional().default(false)
});

/**
 * EmailDataTypes
 */
export const zEmailDataTypes = z.object({
    data: z.array(zEmailDisplayTypes),
    pagination: zPagination
});

/**
 * EmailTypeCreateTypes
 */
export const zEmailTypeCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    email_type: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_optional: z.boolean(),
    org_id: z.number().int()
});

/**
 * EmailTypeDisplayColumnsTypes
 */
export const zEmailTypeDisplayColumnsTypes = z.object({
    id: z.number().int(),
    email_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_optional: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * EmailTypeDataColumnsTypes
 */
export const zEmailTypeDataColumnsTypes = z.object({
    data: z.array(zEmailTypeDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * EmailTypeDisplayTypes
 */
export const zEmailTypeDisplayTypes = z.object({
    id: z.number().int(),
    email_type: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_optional: z.boolean()
});

/**
 * EmailTypeDataTypes
 */
export const zEmailTypeDataTypes = z.object({
    data: z.array(zEmailTypeDisplayTypes),
    pagination: zPagination
});

/**
 * EmailTypeUpdateTypes
 */
export const zEmailTypeUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    email_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_optional: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * EmailUpdateTypes
 */
export const zEmailUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    bcc: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    subject: z.union([
        z.string(),
        z.null()
    ]).optional(),
    text: z.union([
        z.string(),
        z.null()
    ]).optional(),
    html: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_sent: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * FileStatus
 */
export const zFileStatus = z.enum([
    'DRAFT',
    'CONFIRMED',
    'IN_PROGRESS',
    'COMPLETED',
    'CANCELLED'
]);

/**
 * FilesCreateTypes
 */
export const zFilesCreateTypes = z.object({
    created_by: z.union([
        z.number().int(),
        z.string()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    file_name: z.string(),
    th_file_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    file_id: z.string(),
    th_file_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.string(),
    th_url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_data: z.union([
        z.string(),
        z.string(),
        z.null()
    ]).optional(),
    s3_user_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    s3_bucket: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_object_extra: z.union([
        z.boolean(),
        z.string()
    ]).optional(),
    is_doc: z.union([
        z.boolean(),
        z.string()
    ]).optional(),
    generated_pdf_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.string()
    ]),
    mimetype: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * FilesDisplayColumnsTypes
 */
export const zFilesDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: z.union([
        zFileStatus,
        z.null()
    ]).optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.union([
        z.number(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * FilesDataColumnsTypes
 */
export const zFilesDataColumnsTypes = z.object({
    data: z.array(zFilesDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * FilesDataTypes
 */
export const zFilesDataTypes = z.object({
    data: z.array(zFilesDisplayTypes),
    pagination: zPagination
});

/**
 * FilesUpdateTypes
 */
export const zFilesUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    file_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_file_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    file_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_file_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_url: z.union([
        z.string(),
        z.null()
    ]).optional(),
    th_data: z.union([
        z.string(),
        z.null()
    ]).optional(),
    s3_user_id: z.union([
        z.string(),
        z.null()
    ]).optional(),
    s3_bucket: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_object_extra: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_doc: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    generated_pdf_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_table: z.union([
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    mimetype: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * GenericDataResponse
 */
export const zGenericDataResponse = z.object({
    data: z.array(z.object({})),
    pagination: zPagination,
    timing: z.object({})
});

/**
 * ValidationError
 */
export const zValidationError = z.object({
    loc: z.array(z.union([
        z.string(),
        z.number().int()
    ])),
    msg: z.string(),
    type: z.string()
});

/**
 * HTTPValidationError
 */
export const zHttpValidationError = z.object({
    detail: z.array(zValidationError).optional()
});

/**
 * JobStatus
 */
export const zJobStatus = z.enum([
    'DRAFT',
    'CONFIRMED',
    'IN_PROGRESS',
    'COMPLETED',
    'CANCELLED'
]);

/**
 * JobCreateTypes
 */
export const zJobCreateTypes = z.object({
    created_by: z.number().int(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    proposal_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: zJobStatus,
    type: z.string(),
    is_public: z.boolean(),
    start_date: z.string().datetime(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.number(),
    org_id: z.number().int(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    objects: z.union([
        z.array(z.number().int()),
        z.null()
    ]).optional(),
    is_schedule: z.boolean(),
    is_regular: z.boolean(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobDisplayColumnsTypes
 */
export const zJobDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: z.union([
        zJobStatus,
        z.null()
    ]).optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.union([
        z.number(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobDataColumnsTypes
 */
export const zJobDataColumnsTypes = z.object({
    data: z.array(zJobDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * JobDisplayTypes
 */
export const zJobDisplayTypes = z.object({
    id: z.number().int(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: zJobStatus,
    type: z.string(),
    is_public: z.boolean().optional().default(false),
    start_date: z.string().datetime(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.number(),
    org_id: z.number().int(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.boolean().optional().default(false),
    is_regular: z.boolean().optional().default(false),
    sch_dates: z.array(z.string().date()).optional().default([]),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobDataTypes
 */
export const zJobDataTypes = z.object({
    data: z.array(zJobDisplayTypes),
    pagination: zPagination
});

/**
 * ObjectsList
 */
export const zObjectsList = z.object({
    id: z.number().int(),
    name: z.string()
});

/**
 * JobOptions
 */
export const zJobOptionsInput = z.object({
    calendar_objects: z.array(zObjectsList).optional().default([]),
    calendar_period: z.array(z.number().int()).optional().default([0, 7]),
    job_start: z.string().time().optional().default('15:00:00'),
    job_end: z.string().time().optional().default('11:00:00')
});

/**
 * JobOptions
 */
export const zJobOptionsOutput = z.object({
    calendar_objects: z.array(zObjectsList).optional().default([]),
    calendar_period: z.array(z.number().int()).optional().default([0, 7]),
    job_start: z.string().optional(),
    job_end: z.string().optional()
});

/**
 * JobTaskStatus
 */
export const zJobTaskStatus = z.enum([
    'DRAFT',
    'CONFIRMED',
    'IN_PROGRESS',
    'COMPLETED',
    'CANCELLED'
]);

/**
 * JobTaskCreateTypes
 */
export const zJobTaskCreateTypes = z.object({
    created_by: z.number().int(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string().optional().default('pl'),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    proposal_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: zJobTaskStatus.optional(),
    type: z.string(),
    is_public: z.boolean().optional().default(false),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.number().optional().default(0),
    org_id: z.number().int(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    objects: z.union([
        z.array(z.number().int()),
        z.null()
    ]).optional(),
    is_schedule: z.boolean().optional().default(false),
    is_regular: z.boolean().optional().default(false),
    sch_dates: z.array(z.string().date()).optional().default([]),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobTaskDisplayColumnsTypes
 */
export const zJobTaskDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: z.union([
        zJobTaskStatus,
        z.null()
    ]).optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.union([
        z.number(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobTaskDataColumnsTypes
 */
export const zJobTaskDataColumnsTypes = z.object({
    data: z.array(zJobTaskDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * JobTaskDisplayTypes
 */
export const zJobTaskDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string().optional().default('pl'),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: zJobTaskStatus,
    type: z.string(),
    is_public: z.boolean().optional().default(false),
    start_date: z.string().datetime(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.number(),
    org_id: z.number().int(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.boolean().optional().default(false),
    is_regular: z.boolean().optional().default(false),
    sch_dates: z.array(z.string().date()).optional().default([]),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobTaskDataTypes
 */
export const zJobTaskDataTypes = z.object({
    data: z.array(zJobTaskDisplayTypes),
    pagination: zPagination
});

/**
 * JobTaskUpdateTypes
 */
export const zJobTaskUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: z.union([
        zJobTaskStatus,
        z.null()
    ]).optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.union([
        z.number(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JobUpdateTypes
 */
export const zJobUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    status: z.union([
        zJobStatus,
        z.null()
    ]).optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.union([
        z.number(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * JsonMetadata
 */
export const zJsonMetadataInput = z.object({
    storage: z.union([
        zStorageData,
        z.null()
    ]).optional()
});

/**
 * MemberOptions
 */
export const zMemberOptionsOutput = z.object({
    is_voting: z.boolean().optional().default(false),
    shares: z.number().optional(),
    znaczaca_operacja_limit: z.number().optional(),
    member_object_ids: z.array(z.number().int()).optional().default([])
});

/**
 * Pinning
 */
export const zPinning = z.object({
    top: z.array(z.string()).optional().default([]),
    bottom: z.array(z.string()).optional().default([])
});

/**
 * PathOptions
 */
export const zPathOptions = z.object({
    columnSizing: z.object({}).optional(),
    columnSizingInfo: z.object({}).optional(),
    rowSelection: z.object({}).optional(),
    rowPinning: zPinning.optional(),
    expanded: z.object({}).optional(),
    grouping: z.array(z.string()).optional(),
    sorting: z.array(zSorting).optional(),
    columnFilters: z.array(zColumns).optional(),
    columnPinning: z.object({}).optional(),
    columnOrder: z.array(z.string()).optional(),
    columnVisibility: z.object({}).optional(),
    pagination: zClientPagination.optional(),
    start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    date_column: z.union([
        z.string(),
        z.null()
    ]).optional(),
    serverPagination: zPagination.optional(),
    serverSearch: z.array(zColumns).optional()
});

/**
 * Variant
 */
export const zVariantOutput = z.object({
    name: z.string(),
    path: z.string(),
    config: z.array(zPathOptions)
});

/**
 * Options
 */
export const zOptionsOutput = z.object({
    dashboard_modules: zDashboardModules.optional(),
    jobs: zJobOptionsOutput.optional(),
    member: zMemberOptionsOutput.optional(),
    variants: z.array(zVariantOutput).optional().default([])
});

/**
 * ProfileDisplayTypes
 */
export const zProfileDisplayTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: zOptionsOutput.optional(),
    user_id: z.number().int(),
    org_id: z.number().int(),
    type_id: z.number().int(),
    display_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_current: z.boolean().optional().default(false),
    is_company: z.boolean().optional().default(false),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * KeycloakUserTypes
 */
export const zKeycloakUserTypes = z.object({
    id: z.number().int(),
    first_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    last_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keycloak_id: z.union([
        z.string().uuid(),
        z.null()
    ]).optional(),
    profiles: z.union([
        z.array(zProfileDisplayTypes),
        z.null()
    ]).optional()
});

/**
 * LinkContrahentToUserTypes
 */
export const zLinkContrahentToUserTypes = z.object({
    user_id: z.number().int(),
    contrahent_id: z.number().int(),
    org_id: z.number().int(),
    created_by: z.number().int(),
    lang: z.string()
});

/**
 * MemberOptions
 */
export const zMemberOptionsInput = z.object({
    is_voting: z.boolean().optional().default(false),
    shares: z.union([
        z.number(),
        z.string()
    ]).optional(),
    znaczaca_operacja_limit: z.union([
        z.number(),
        z.string()
    ]).optional(),
    member_object_ids: z.array(z.number().int()).optional().default([])
});

/**
 * ObjectCreateTypes
 */
export const zObjectCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_type: z.string(),
    object_type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    object_type_group: z.union([
        z.string(),
        z.null()
    ]).optional(),
    width: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    length: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    height: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    tree_level: z.number().int(),
    org_id: z.number().int(),
    surface: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    volume: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    circumference: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    is_branch: z.boolean().optional().default(false)
});

/**
 * ObjectDisplayColumnsTypes
 */
export const zObjectDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional()
});

/**
 * ObjectDataColumnsTypes
 */
export const zObjectDataColumnsTypes = z.object({
    data: z.array(zObjectDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * ObjectDataTypes
 */
export const zObjectDataTypes = z.object({
    data: z.array(zObjectDisplayTypes),
    pagination: zPagination
});

/**
 * ObjectSystemCreateTypes
 */
export const zObjectSystemCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    object_id: z.number().int(),
    system_type: z.string()
});

/**
 * ObjectSystemDisplayColumnsTypes
 */
export const zObjectSystemDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    object_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    system_type: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * ObjectSystemDataColumnsTypes
 */
export const zObjectSystemDataColumnsTypes = z.object({
    data: z.array(zObjectSystemDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * ObjectSystemDisplayTypes
 */
export const zObjectSystemDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    object_id: z.number().int(),
    system_type: z.string()
});

/**
 * ObjectSystemDataTypes
 */
export const zObjectSystemDataTypes = z.object({
    data: z.array(zObjectSystemDisplayTypes),
    pagination: zPagination
});

/**
 * ObjectSystemUpdateTypes
 */
export const zObjectSystemUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    object_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    system_type: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * ObjectUpdateTypes
 */
export const zObjectUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    object_type_group: z.union([
        z.string(),
        z.null()
    ]).optional(),
    width: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    length: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    height: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    parent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    tree_level: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    surface: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    volume: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    circumference: z.union([
        z.number(),
        z.string(),
        z.null()
    ]).optional(),
    is_branch: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * Variant
 */
export const zVariantInput = z.object({
    name: z.string(),
    path: z.string(),
    config: z.array(zPathOptions)
});

/**
 * Options
 */
export const zOptionsInput = z.object({
    dashboard_modules: zDashboardModules.optional(),
    jobs: zJobOptionsInput.optional(),
    member: zMemberOptionsInput.optional(),
    variants: z.array(zVariantInput).optional().default([])
});

/**
 * OrderColumn
 */
export const zOrderColumn = z.object({
    column_name: z.string(),
    direction: z.string()
});

/**
 * OrgCreateTypes
 */
export const zOrgCreateTypes = z.object({
    created_by: z.number().int(),
    name: z.string(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string().optional().default('pl'),
    json_metadata: z.union([
        zJsonMetadataInput,
        z.null()
    ]).optional(),
    is_public: z.boolean().optional().default(true),
    address_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_formal: z.boolean().optional().default(false),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    max_img_width: z.number().int().optional().default(1500),
    total_shares: z.number().int().optional().default(4),
    accounts_set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    voting_days: z.number().int().optional().default(7),
    members_by_admin: z.boolean().optional().default(true),
    curr_acc_period_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_acc_period_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_acc_period_start: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    curr_acc_period_end: z.union([
        z.string().date(),
        z.null()
    ]).optional()
});

/**
 * OrgDisplayColumnsTypes
 */
export const zOrgDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        zJsonMetadataOutput,
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    address_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_formal: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    max_img_width: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    total_shares: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accounts_set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    voting_days: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    members_by_admin: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    curr_acc_period_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_acc_period_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_acc_period_start: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    curr_acc_period_end: z.union([
        z.string().date(),
        z.null()
    ]).optional()
});

/**
 * OrgDataColumnsTypes
 */
export const zOrgDataColumnsTypes = z.object({
    data: z.array(zOrgDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * OrgDataTypes
 */
export const zOrgDataTypes = z.object({
    data: z.array(zOrgDisplayTypes),
    pagination: zPagination
});

/**
 * OrgSplitCreateTypes
 */
export const zOrgSplitCreateTypes = z.object({
    created_by: z.number().int(),
    lang: z.string().optional().default('pl'),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    account_id: z.number().int(),
    transaction_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    debit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    credit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    due_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    is_debit_minus: z.boolean().optional().default(false),
    is_schedule: z.boolean().optional().default(false),
    is_saved: z.boolean().optional().default(false),
    org_id: z.number().int(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * OrgSplitDisplayColumnsTypes
 */
export const zOrgSplitDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    account_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    transaction_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    debit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    credit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    due_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    is_debit_minus: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_saved: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * OrgSplitDataColumnsTypes
 */
export const zOrgSplitDataColumnsTypes = z.object({
    data: z.array(zOrgSplitDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * OrgSplitDisplayTypes
 */
export const zOrgSplitDisplayTypes = z.object({
    id: z.number().int(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    created_by: z.number().int(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]),
    account_id: z.number().int(),
    transaction_id: z.number().int(),
    debit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    credit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    due_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    is_debit_minus: z.boolean(),
    is_schedule: z.boolean(),
    is_saved: z.boolean(),
    org_id: z.number().int(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ])
});

/**
 * OrgSplitDataTypes
 */
export const zOrgSplitDataTypes = z.object({
    data: z.array(zOrgSplitDisplayTypes),
    pagination: zPagination
});

/**
 * OrgSplitUpdateTypes
 */
export const zOrgSplitUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    account_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    transaction_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    debit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    credit: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    due_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    is_debit_minus: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_saved: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * OrgTransactionCreateTypes
 */
export const zOrgTransactionCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    lang: z.string().optional().default('pl'),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.null().optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    due_date: z.null().optional(),
    amount: z.number().int(),
    job_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    comment: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_saved: z.boolean().optional().default(false),
    is_schedule: z.boolean().optional().default(false),
    is_regular: z.boolean().optional().default(false),
    sch_dates: z.union([
        z.array(z.null()),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    sch_start_date: z.null().optional(),
    sch_end_date: z.null().optional(),
    posted_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    cust_ref: z.union([
        z.string(),
        z.null()
    ]).optional(),
    our_ref: z.union([
        z.string(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    contrahent_type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    template_used: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    org_splits: z.array(zOrgSplitCreateTypes)
});

/**
 * OrgTransactionDisplayTypes
 */
export const zOrgTransactionDisplayTypes = z.object({
    id: z.number().int(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    lang: z.string().optional().default('pl'),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.null().optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.number().int(),
    due_date: z.null().optional(),
    amount: z.number().int(),
    job_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    comment: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_saved: z.boolean().optional().default(false),
    is_schedule: z.boolean().optional().default(false),
    is_regular: z.boolean().optional().default(false),
    sch_dates: z.union([
        z.array(z.null()),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    sch_start_date: z.null().optional(),
    sch_end_date: z.null().optional(),
    posted_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    cust_ref: z.union([
        z.string(),
        z.null()
    ]).optional(),
    our_ref: z.union([
        z.string(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    contrahent_type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    template_used: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    org_splits: z.array(zOrgSplitDisplayTypes),
    files: z.array(zFilesDisplayTypes).optional().default([])
});

/**
 * OrgTransactionDataTypes
 */
export const zOrgTransactionDataTypes = z.object({
    data: z.array(zOrgTransactionDisplayTypes),
    pagination: zPagination
});

/**
 * OrgTransactionDisplayColumnsTypes
 */
export const zOrgTransactionDisplayColumnsTypes = z.object({
    id: z.number().int(),
    created_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    updated_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    created_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    updated_by: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    budget: z.union([
        z.number(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accepted_offer_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.string().date()),
        z.null()
    ]).optional(),
    sch_start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    interval_day: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * OrgTransactionScheduledTypes
 */
export const zOrgTransactionScheduledTypes = z.object({
    fixed_dates: z.array(zOrgTransactionDisplayTypes),
    fixed_end_date: z.array(zOrgTransactionDisplayTypes),
    open_end_date: z.array(zOrgTransactionDisplayTypes)
});

/**
 * OrgTransactionUpdateTypes
 */
export const zOrgTransactionUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    date: z.null().optional(),
    type: z.union([
        z.string(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    due_date: z.null().optional(),
    amount: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    job_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    comment: z.union([
        z.string(),
        z.null()
    ]).optional(),
    object_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_saved: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_schedule: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_regular: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    sch_dates: z.union([
        z.array(z.null()),
        z.null()
    ]).optional(),
    sch_interval: z.union([
        z.string(),
        z.null()
    ]).optional(),
    sch_start_date: z.null().optional(),
    sch_end_date: z.null().optional(),
    posted_at: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    cust_ref: z.union([
        z.string(),
        z.null()
    ]).optional(),
    our_ref: z.union([
        z.string(),
        z.null()
    ]).optional(),
    memo: z.union([
        z.string(),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    contrahent_type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    set_item_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    template_used: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    org_splits: z.array(zOrgSplitCreateTypes)
});

/**
 * OrgUpdateTypes
 */
export const zOrgUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tag: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        zJsonMetadataInput,
        z.null()
    ]).optional(),
    is_public: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    address_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    is_formal: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    max_img_width: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    total_shares: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    accounts_set_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    voting_days: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    members_by_admin: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    curr_acc_period_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_acc_period_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_acc_period_start: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    curr_acc_period_end: z.union([
        z.string().date(),
        z.null()
    ]).optional()
});

/**
 * Period
 */
export const zPeriod = z.enum([
    'TODAY',
    'YESTERDAY',
    'LAST_7',
    'LAST_30',
    'THIS_WEEK',
    'LAST_WEEK',
    'NEXT_WEEK',
    'THIS_MONTH',
    'LAST_MONTH',
    'NEXT_MONTH',
    'THIS_YEAR',
    'LAST_YEAR',
    'NEXT_YEAR',
    'CUSTOM',
    'DATES'
]);

/**
 * ProfileCreateTypes
 */
export const zProfileCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: zOptionsInput.optional(),
    user_id: z.number().int(),
    org_id: z.number().int(),
    type_id: z.number().int(),
    display_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_current: z.boolean().optional().default(false),
    is_company: z.boolean().optional().default(false),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * ProfileDisplayColumnsTypes
 */
export const zProfileDisplayColumnsTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        zOptionsOutput,
        z.null()
    ]).optional(),
    user_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    display_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_current: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_company: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional(),
    contrahents: z.union([
        z.array(zContrahentBasicTypes),
        z.null()
    ]).optional()
});

/**
 * ProfileDataColumnsTypes
 */
export const zProfileDataColumnsTypes = z.object({
    data: z.array(zProfileDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * ProfileDataTypes
 */
export const zProfileDataTypes = z.object({
    data: z.array(zProfileDisplayTypes),
    pagination: zPagination
});

/**
 * ProfileUpdateTypes
 */
export const zProfileUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: zOptionsInput.optional(),
    user_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    type_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    display_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    is_current: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_company: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    nip: z.union([
        z.string(),
        z.null()
    ]).optional(),
    regon: z.union([
        z.string(),
        z.null()
    ]).optional()
});

/**
 * SearchTerm
 */
export const zSearchTerm = z.object({
    column_name: z.string(),
    term: z.string()
});

/**
 * ServerOptions
 */
export const zServerOptions = z.object({
    filters: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    period: z.union([
        zPeriod,
        z.null()
    ]).optional(),
    date_column: z.union([
        z.string(),
        z.null()
    ]).optional(),
    start_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    end_date: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    page_index: z.number().int().optional().default(0),
    page_size: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    search: z.union([
        z.array(zSearchTerm),
        z.null()
    ]).optional(),
    order: z.union([
        z.array(zOrderColumn),
        z.null()
    ]).optional(),
    columns: z.union([
        z.array(z.string()),
        z.null()
    ]).optional()
});

/**
 * TypeCreateTypes
 */
export const zTypeCreateTypes = z.object({
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    order: z.number().int()
});

/**
 * TypeDisplayColumnsTypes
 */
export const zTypeDisplayColumnsTypes = z.object({
    id: z.number().int(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    order: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * TypeDataColumnsTypes
 */
export const zTypeDataColumnsTypes = z.object({
    data: z.array(zTypeDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * TypeDisplayTypes
 */
export const zTypeDisplayTypes = z.object({
    id: z.number().int(),
    name: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    order: z.number().int()
});

/**
 * TypeDataTypes
 */
export const zTypeDataTypes = z.object({
    data: z.array(zTypeDisplayTypes),
    pagination: zPagination
});

/**
 * TypeUpdateTypes
 */
export const zTypeUpdateTypes = z.object({
    id: z.number().int(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    order: z.union([
        z.number().int(),
        z.null()
    ]).optional()
});

/**
 * UserCreateTypes
 */
export const zUserCreateTypes = z.object({
    created_by: z.number().int().optional().default(1),
    first_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    last_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string().optional().default('pl'),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.string(),
    email_verified: z.boolean().optional().default(false),
    is_superuser: z.boolean().optional().default(false),
    is_admin: z.boolean().optional().default(false),
    is_limited: z.boolean().optional().default(false),
    is_disabled: z.boolean().optional().default(false),
    is_in_credit: z.boolean().optional().default(true),
    is_temp: z.boolean().optional().default(false),
    is_deleted: z.boolean().optional().default(false),
    curr_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keycloak_id: z.union([
        z.string().uuid(),
        z.null()
    ]).optional()
});

/**
 * UserDisplayColumnsTypes
 */
export const zUserDisplayColumnsTypes = z.object({
    id: z.number().int(),
    first_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    last_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keycloak_id: z.union([
        z.string().uuid(),
        z.null()
    ]).optional()
});

/**
 * UserDataColumnsTypes
 */
export const zUserDataColumnsTypes = z.object({
    data: z.array(zUserDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * UserDisplayTypes
 */
export const zUserDisplayTypes = z.object({
    id: z.number().int(),
    first_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    last_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    curr_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keycloak_id: z.union([
        z.string().uuid(),
        z.null()
    ]).optional()
});

/**
 * UserDataTypes
 */
export const zUserDataTypes = z.object({
    data: z.array(zUserDisplayTypes),
    pagination: zPagination
});

/**
 * UserRegisterTypes
 */
export const zUserRegisterTypes = z.object({
    created_by: z.number().int().optional().default(1),
    email: z.string(),
    first_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    last_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.string().optional().default('pl'),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    contrahent_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    source_profile_id: z.number().int()
});

/**
 * UserUpdateTypes
 */
export const zUserUpdateTypes = z.object({
    id: z.number().int(),
    updated_by: z.number().int().optional().default(1),
    first_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    last_name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    lang: z.union([
        z.string(),
        z.null()
    ]).optional(),
    description: z.union([
        z.string(),
        z.null()
    ]).optional(),
    json_metadata: z.union([
        z.object({}),
        z.null()
    ]).optional(),
    phone: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email: z.union([
        z.string(),
        z.null()
    ]).optional(),
    email_verified: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_superuser: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_admin: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_limited: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_disabled: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_in_credit: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_temp: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    is_deleted: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    curr_profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    curr_org_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    keycloak_id: z.union([
        z.string().uuid(),
        z.null()
    ]).optional()
});

/**
 * VariantCreateTypes
 */
export const zVariantCreateTypes = z.object({
    deactivate_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    profile_id: z.number().int(),
    name: z.string(),
    path: z.string(),
    client_ops: zClientOptionsTypes.optional(),
    server_ops: zServerOptions.optional(),
    is_active: z.boolean().optional().default(true)
});

/**
 * VariantDisplayColumnsTypes
 */
export const zVariantDisplayColumnsTypes = z.object({
    id: z.number().int(),
    profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    path: z.union([
        z.string(),
        z.null()
    ]).optional(),
    client_ops: z.union([
        zClientOptionsTypes,
        z.null()
    ]).optional(),
    server_ops: z.union([
        zServerOptions,
        z.null()
    ]).optional(),
    is_active: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

/**
 * VariantDataColumnsTypes
 */
export const zVariantDataColumnsTypes = z.object({
    data: z.array(zVariantDisplayColumnsTypes),
    pagination: zPagination
});

/**
 * VariantDisplayTypes
 */
export const zVariantDisplayTypes = z.object({
    id: z.number().int(),
    profile_id: z.number().int(),
    name: z.string(),
    path: z.string(),
    is_active: z.boolean(),
    client_ops: z.union([
        zClientOptionsTypes,
        z.null()
    ]).optional(),
    server_ops: z.union([
        zServerOptions,
        z.null()
    ]).optional()
});

/**
 * VariantDataTypes
 */
export const zVariantDataTypes = z.object({
    data: z.array(zVariantDisplayTypes),
    pagination: zPagination
});

/**
 * VariantUpdateTypes
 */
export const zVariantUpdateTypes = z.object({
    id: z.number().int(),
    deactivate_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    profile_id: z.union([
        z.number().int(),
        z.null()
    ]).optional(),
    name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    path: z.union([
        z.string(),
        z.null()
    ]).optional(),
    client_ops: z.union([
        zClientOptionsTypes,
        z.null()
    ]).optional(),
    server_ops: z.union([
        zServerOptions,
        z.null()
    ]).optional(),
    is_active: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

export const zCreateUsersApiV1AuthUsersPostData = z.object({
    body: z.array(zUserCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Users Api V1 Auth Users  Post
 * Successful Response
 */
export const zCreateUsersApiV1AuthUsersPostResponse = z.array(zUserDisplayTypes);

export const zUpdateUsersApiV1AuthUsersPutData = z.object({
    body: z.array(zUserUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Users Api V1 Auth Users  Put
 * Successful Response
 */
export const zUpdateUsersApiV1AuthUsersPutResponse = z.array(zUserDisplayTypes);

export const zGetUsersOneApiV1AuthUsersKcKcIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        kc_id: z.string().uuid()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetUsersOneApiV1AuthUsersKcKcIdGetResponse = zKeycloakUserTypes;

export const zDeleteUsersApiV1AuthUsersItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetUsersOneApiV1AuthUsersItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetUsersOneApiV1AuthUsersItemIdGetResponse = zUserDisplayTypes;

export const zGetUsersAllApiV1AuthUsersGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Users All Api V1 Auth Users Get All Post
 * Successful Response
 */
export const zGetUsersAllApiV1AuthUsersGetAllPostResponse = z.union([
    zUserDataTypes,
    zUserDataColumnsTypes
]);

export const zRegisterApiV1AuthUsersRegisterPostData = z.object({
    body: zUserRegisterTypes,
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zRegisterApiV1AuthUsersRegisterPostResponse = zUserDisplayTypes;

export const zLinkContrahentApiV1AuthUsersLinkContrahentPostData = z.object({
    body: zLinkContrahentToUserTypes,
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Link Contrahent Api V1 Auth Users Link Contrahent Post
 * Successful Response
 */
export const zLinkContrahentApiV1AuthUsersLinkContrahentPostResponse = z.object({});

export const zCreateJobsApiV1CoreJobsPostData = z.object({
    body: z.array(zJobCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Jobs Api V1 Core Jobs  Post
 * Successful Response
 */
export const zCreateJobsApiV1CoreJobsPostResponse = z.array(zJobDisplayTypes);

export const zUpdateJobsApiV1CoreJobsPutData = z.object({
    body: z.array(zJobUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Jobs Api V1 Core Jobs  Put
 * Successful Response
 */
export const zUpdateJobsApiV1CoreJobsPutResponse = z.array(zJobDisplayTypes);

export const zDeleteJobsApiV1CoreJobsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zDeleteJobsApiV1CoreJobsItemIdDeleteResponse = z.void();

export const zGetJobsOneApiV1CoreJobsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetJobsOneApiV1CoreJobsItemIdGetResponse = zJobDisplayTypes;

export const zGetJobsAllApiV1CoreJobsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Jobs All Api V1 Core Jobs Get All Post
 * Successful Response
 */
export const zGetJobsAllApiV1CoreJobsGetAllPostResponse = z.union([
    zJobDataTypes,
    zJobDataColumnsTypes
]);

export const zGetJobTasksAllApiV1CoreJobTasksGetData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Job Tasks All Api V1 Core Job Tasks  Get
 * Successful Response
 */
export const zGetJobTasksAllApiV1CoreJobTasksGetResponse = z.union([
    zJobTaskDataTypes,
    zJobTaskDataColumnsTypes
]);

export const zCreateJobTasksApiV1CoreJobTasksPostData = z.object({
    body: z.array(zJobTaskCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Job Tasks Api V1 Core Job Tasks  Post
 * Successful Response
 */
export const zCreateJobTasksApiV1CoreJobTasksPostResponse = z.array(zJobTaskDisplayTypes);

export const zUpdateJobTasksApiV1CoreJobTasksPutData = z.object({
    body: z.array(zJobTaskUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Job Tasks Api V1 Core Job Tasks  Put
 * Successful Response
 */
export const zUpdateJobTasksApiV1CoreJobTasksPutResponse = z.array(zJobTaskDisplayTypes);

export const zDeleteJobTasksApiV1CoreJobTasksItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zDeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponse = z.void();

export const zGetJobTasksOneApiV1CoreJobTasksItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetJobTasksOneApiV1CoreJobTasksItemIdGetResponse = zJobTaskDisplayTypes;

export const zCreateFilesApiV1CoreFilesPostData = z.object({
    body: z.array(zFilesCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Files Api V1 Core Files  Post
 * Successful Response
 */
export const zCreateFilesApiV1CoreFilesPostResponse = z.array(zFilesDisplayTypes);

export const zUpdateFilesApiV1CoreFilesPutData = z.object({
    body: z.array(zFilesUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Files Api V1 Core Files  Put
 * Successful Response
 */
export const zUpdateFilesApiV1CoreFilesPutResponse = z.array(zFilesDisplayTypes);

export const zDeleteFilesApiV1CoreFilesItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zDeleteFilesApiV1CoreFilesItemIdDeleteResponse = z.void();

export const zGetFilesOneApiV1CoreFilesItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetFilesOneApiV1CoreFilesItemIdGetResponse = zFilesDisplayTypes;

export const zGetFilesAllApiV1CoreFilesGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Files All Api V1 Core Files Get All Post
 * Successful Response
 */
export const zGetFilesAllApiV1CoreFilesGetAllPostResponse = z.union([
    zFilesDataTypes,
    zFilesDataColumnsTypes
]);

export const zCreateObjectsApiV1CoreObjectsPostData = z.object({
    body: z.array(zObjectCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Objects Api V1 Core Objects  Post
 * Successful Response
 */
export const zCreateObjectsApiV1CoreObjectsPostResponse = z.array(zObjectDisplayTypes);

export const zUpdateObjectsApiV1CoreObjectsPutData = z.object({
    body: z.array(zObjectUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Objects Api V1 Core Objects  Put
 * Successful Response
 */
export const zUpdateObjectsApiV1CoreObjectsPutResponse = z.array(zObjectDisplayTypes);

export const zDeleteObjectsApiV1CoreObjectsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetObjectsOneApiV1CoreObjectsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetObjectsOneApiV1CoreObjectsItemIdGetResponse = zObjectDisplayTypes;

export const zGetObjectsAllApiV1CoreObjectsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Objects All Api V1 Core Objects Get All Post
 * Successful Response
 */
export const zGetObjectsAllApiV1CoreObjectsGetAllPostResponse = z.union([
    zObjectDataTypes,
    zObjectDataColumnsTypes
]);

export const zCreateObjectSystemsApiV1CoreObjectSystemsPostData = z.object({
    body: z.array(zObjectSystemCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Object Systems Api V1 Core Object Systems  Post
 * Successful Response
 */
export const zCreateObjectSystemsApiV1CoreObjectSystemsPostResponse = z.array(zObjectSystemDisplayTypes);

export const zUpdateObjectSystemsApiV1CoreObjectSystemsPutData = z.object({
    body: z.array(zObjectSystemUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Object Systems Api V1 Core Object Systems  Put
 * Successful Response
 */
export const zUpdateObjectSystemsApiV1CoreObjectSystemsPutResponse = z.array(zObjectSystemDisplayTypes);

export const zDeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetResponse = zObjectSystemDisplayTypes;

export const zGetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Object Systems All Api V1 Core Object Systems Get All Post
 * Successful Response
 */
export const zGetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponse = z.union([
    zObjectSystemDataTypes,
    zObjectSystemDataColumnsTypes
]);

export const zCreateOrgsApiV1CoreOrgsPostData = z.object({
    body: z.array(zOrgCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Orgs Api V1 Core Orgs  Post
 * Successful Response
 */
export const zCreateOrgsApiV1CoreOrgsPostResponse = z.array(zOrgDisplayTypes);

export const zUpdateOrgsApiV1CoreOrgsPutData = z.object({
    body: z.array(zOrgUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Orgs Api V1 Core Orgs  Put
 * Successful Response
 */
export const zUpdateOrgsApiV1CoreOrgsPutResponse = z.array(zOrgDisplayTypes);

export const zDeleteOrgsApiV1CoreOrgsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetOrgsOneApiV1CoreOrgsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetOrgsOneApiV1CoreOrgsItemIdGetResponse = zOrgDisplayTypes;

export const zGetOrgsAllApiV1CoreOrgsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Orgs All Api V1 Core Orgs Get All Post
 * Successful Response
 */
export const zGetOrgsAllApiV1CoreOrgsGetAllPostResponse = z.union([
    zOrgDataTypes,
    zOrgDataColumnsTypes
]);

export const zCreateEmailTypesApiV1CoreEmailTypesPostData = z.object({
    body: z.array(zEmailTypeCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Email Types Api V1 Core Email Types  Post
 * Successful Response
 */
export const zCreateEmailTypesApiV1CoreEmailTypesPostResponse = z.array(zEmailTypeDisplayTypes);

export const zUpdateEmailTypesApiV1CoreEmailTypesPutData = z.object({
    body: z.array(zEmailTypeUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Email Types Api V1 Core Email Types  Put
 * Successful Response
 */
export const zUpdateEmailTypesApiV1CoreEmailTypesPutResponse = z.array(zEmailTypeDisplayTypes);

export const zDeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetEmailTypesOneApiV1CoreEmailTypesItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetEmailTypesOneApiV1CoreEmailTypesItemIdGetResponse = zEmailTypeDisplayTypes;

export const zGetEmailTypesAllApiV1CoreEmailTypesGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Email Types All Api V1 Core Email Types Get All Post
 * Successful Response
 */
export const zGetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponse = z.union([
    zEmailTypeDataTypes,
    zEmailTypeDataColumnsTypes
]);

export const zCreateEmailsApiV1CoreEmailsPostData = z.object({
    body: z.array(zEmailCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Emails Api V1 Core Emails  Post
 * Successful Response
 */
export const zCreateEmailsApiV1CoreEmailsPostResponse = z.array(zEmailDisplayTypes);

export const zUpdateEmailsApiV1CoreEmailsPutData = z.object({
    body: z.array(zEmailUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Emails Api V1 Core Emails  Put
 * Successful Response
 */
export const zUpdateEmailsApiV1CoreEmailsPutResponse = z.array(zEmailDisplayTypes);

export const zDeleteEmailsApiV1CoreEmailsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetEmailsOneApiV1CoreEmailsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetEmailsOneApiV1CoreEmailsItemIdGetResponse = zEmailDisplayTypes;

export const zGetEmailsAllApiV1CoreEmailsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Emails All Api V1 Core Emails Get All Post
 * Successful Response
 */
export const zGetEmailsAllApiV1CoreEmailsGetAllPostResponse = z.union([
    zEmailDataTypes,
    zEmailDataColumnsTypes
]);

export const zCreateCommentsApiV1CoreCommentsPostData = z.object({
    body: z.array(zCommentCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Comments Api V1 Core Comments  Post
 * Successful Response
 */
export const zCreateCommentsApiV1CoreCommentsPostResponse = z.array(zCommentDisplayTypes);

export const zUpdateCommentsApiV1CoreCommentsPutData = z.object({
    body: z.array(zCommentUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Comments Api V1 Core Comments  Put
 * Successful Response
 */
export const zUpdateCommentsApiV1CoreCommentsPutResponse = z.array(zCommentDisplayTypes);

export const zDeleteCommentsApiV1CoreCommentsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetCommentsOneApiV1CoreCommentsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetCommentsOneApiV1CoreCommentsItemIdGetResponse = zCommentDisplayTypes;

export const zGetCommentsAllApiV1CoreCommentsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Comments All Api V1 Core Comments Get All Post
 * Successful Response
 */
export const zGetCommentsAllApiV1CoreCommentsGetAllPostResponse = z.union([
    zCommentDataTypes,
    zCommentDataColumnsTypes
]);

export const zCreateDocsApiV1CoreDocsPostData = z.object({
    body: z.array(zDocCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Docs Api V1 Core Docs  Post
 * Successful Response
 */
export const zCreateDocsApiV1CoreDocsPostResponse = z.array(zDocDisplayTypes);

export const zUpdateDocsApiV1CoreDocsPutData = z.object({
    body: z.array(zDocUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Docs Api V1 Core Docs  Put
 * Successful Response
 */
export const zUpdateDocsApiV1CoreDocsPutResponse = z.array(zDocDisplayTypes);

export const zGetDocTemplatesApiV1CoreDocsTemplatesPostData = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        org_id: z.number().int(),
        doc_type: z.union([
            zAppSchemasCoreDocsSchemasDocTypeEnum,
            z.null()
        ]).optional()
    })
});

/**
 * Successful Response
 */
export const zGetDocTemplatesApiV1CoreDocsTemplatesPostResponse = zDocTemplatesTypes;

export const zGetTemplatesTableApiV1CoreDocsTemplatesTablePostData = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        org_id: z.number().int(),
        doc_type: z.union([
            zAppSchemasCoreDocsSchemasDocTypeEnum,
            z.null()
        ]).optional()
    })
});

/**
 * Response Get Templates Table Api V1 Core Docs Templates Table Post
 * Successful Response
 */
export const zGetTemplatesTableApiV1CoreDocsTemplatesTablePostResponse = z.array(z.union([
    zDocTableTypes,
    zDocPartDisplayTypes
]));

export const zDeleteDocsApiV1CoreDocsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetDocsOneApiV1CoreDocsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetDocsOneApiV1CoreDocsItemIdGetResponse = zDocDisplayTypes;

export const zGetDocsAllApiV1CoreDocsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Docs All Api V1 Core Docs Get All Post
 * Successful Response
 */
export const zGetDocsAllApiV1CoreDocsGetAllPostResponse = z.union([
    zDocDataTypes,
    zDocDataColumnsTypes
]);

export const zGetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Docs All With Content Api V1 Core Docs Get All With Content Post
 * Successful Response
 */
export const zGetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponse = z.union([
    zDocDataTypes,
    zDocDataColumnsTypes
]);

export const zUpdateDocsWithContentApiV1CoreDocsWithContentPutData = z.object({
    body: z.array(zDocUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Docs With Content Api V1 Core Docs With Content Put
 * Successful Response
 */
export const zUpdateDocsWithContentApiV1CoreDocsWithContentPutResponse = z.array(zDocDisplayTypes);

export const zCreateDocPagesApiV1CoreDocPagesPostData = z.object({
    body: z.array(zDocPageCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Doc Pages Api V1 Core Doc Pages  Post
 * Successful Response
 */
export const zCreateDocPagesApiV1CoreDocPagesPostResponse = z.array(zDocPageDisplayTypes);

export const zUpdateDocPagesWithPartsApiV1CoreDocPagesPutData = z.object({
    body: z.array(zDocPageUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Doc Pages With Parts Api V1 Core Doc Pages  Put
 * Successful Response
 */
export const zUpdateDocPagesWithPartsApiV1CoreDocPagesPutResponse = z.array(zDocPageDisplayTypes);

export const zDeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetDocPagesOneApiV1CoreDocPagesItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetDocPagesOneApiV1CoreDocPagesItemIdGetResponse = zDocPageDisplayTypes;

export const zGetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponse = zDocPageDataTypes;

export const zGetDocPagesAllApiV1CoreDocPagesGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Doc Pages All Api V1 Core Doc Pages Get All Post
 * Successful Response
 */
export const zGetDocPagesAllApiV1CoreDocPagesGetAllPostResponse = z.union([
    zDocPageDataTypes,
    zDocPageDataColumnsTypes
]);

export const zCreateDocPartsApiV1CoreDocPartsPostData = z.object({
    body: z.array(zDocPartCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Doc Parts Api V1 Core Doc Parts  Post
 * Successful Response
 */
export const zCreateDocPartsApiV1CoreDocPartsPostResponse = z.array(zDocPartDisplayTypes);

export const zUpdateDocPartsApiV1CoreDocPartsPutData = z.object({
    body: z.array(zDocPartUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Doc Parts Api V1 Core Doc Parts  Put
 * Successful Response
 */
export const zUpdateDocPartsApiV1CoreDocPartsPutResponse = z.array(zDocPartDisplayTypes);

export const zDeleteDocPartsApiV1CoreDocPartsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetDocPartsOneApiV1CoreDocPartsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetDocPartsOneApiV1CoreDocPartsItemIdGetResponse = zDocPartDisplayTypes;

export const zGetDocPartsAllApiV1CoreDocPartsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Doc Parts All Api V1 Core Doc Parts Get All Post
 * Successful Response
 */
export const zGetDocPartsAllApiV1CoreDocPartsGetAllPostResponse = z.union([
    zDocPartDataTypes,
    zDocPartDataColumnsTypes
]);

export const zCreateVariantsApiV1CoreVariantsPostData = z.object({
    body: z.array(zVariantCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Variants Api V1 Core Variants  Post
 * Successful Response
 */
export const zCreateVariantsApiV1CoreVariantsPostResponse = z.array(zVariantDisplayTypes);

export const zUpdateVariantsApiV1CoreVariantsPutData = z.object({
    body: z.array(zVariantUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Variants Api V1 Core Variants  Put
 * Successful Response
 */
export const zUpdateVariantsApiV1CoreVariantsPutResponse = z.array(zVariantDisplayTypes);

export const zDeleteVariantsApiV1CoreVariantsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetVariantsOneApiV1CoreVariantsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetVariantsOneApiV1CoreVariantsItemIdGetResponse = zVariantDisplayTypes;

export const zGetVariantsAllApiV1CoreVariantsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Variants All Api V1 Core Variants Get All Post
 * Successful Response
 */
export const zGetVariantsAllApiV1CoreVariantsGetAllPostResponse = z.union([
    zVariantDataTypes,
    zVariantDataColumnsTypes
]);

export const zCreateAddressesApiV1CrmAddressesPostData = z.object({
    body: z.array(zAddressCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Addresses Api V1 Crm Addresses  Post
 * Successful Response
 */
export const zCreateAddressesApiV1CrmAddressesPostResponse = z.array(zAddressDisplayTypes);

export const zUpdateAddressesApiV1CrmAddressesPutData = z.object({
    body: z.array(zAddressUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Addresses Api V1 Crm Addresses  Put
 * Successful Response
 */
export const zUpdateAddressesApiV1CrmAddressesPutResponse = z.array(zAddressDisplayTypes);

export const zDeleteAddressesApiV1CrmAddressesItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetAddressesOneApiV1CrmAddressesItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetAddressesOneApiV1CrmAddressesItemIdGetResponse = zAddressDisplayTypes;

export const zGetAddressesAllApiV1CrmAddressesGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Addresses All Api V1 Crm Addresses Get All Post
 * Successful Response
 */
export const zGetAddressesAllApiV1CrmAddressesGetAllPostResponse = z.union([
    zAddressDataTypes,
    zAddressDataColumnsTypes
]);

export const zCreateContactsApiV1CrmContactsPostData = z.object({
    body: z.array(zContactCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Contacts Api V1 Crm Contacts  Post
 * Successful Response
 */
export const zCreateContactsApiV1CrmContactsPostResponse = z.array(zContactDisplayTypes);

export const zUpdateContactsApiV1CrmContactsPutData = z.object({
    body: z.array(zContactUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Contacts Api V1 Crm Contacts  Put
 * Successful Response
 */
export const zUpdateContactsApiV1CrmContactsPutResponse = z.array(zContactDisplayTypes);

export const zDeleteContactsApiV1CrmContactsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetContactsOneApiV1CrmContactsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetContactsOneApiV1CrmContactsItemIdGetResponse = zContactDisplayTypes;

export const zGetContactsAllApiV1CrmContactsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Contacts All Api V1 Crm Contacts Get All Post
 * Successful Response
 */
export const zGetContactsAllApiV1CrmContactsGetAllPostResponse = z.union([
    zContactDataTypes,
    zContactDataColumnsTypes
]);

export const zCreateContrahentsApiV1CrmContrahentsPostData = z.object({
    body: z.array(zContrahentCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Contrahents Api V1 Crm Contrahents  Post
 * Successful Response
 */
export const zCreateContrahentsApiV1CrmContrahentsPostResponse = z.array(zContrahentDisplayTypes);

export const zUpdateContrahentsApiV1CrmContrahentsPutData = z.object({
    body: z.array(zContrahentUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Contrahents Api V1 Crm Contrahents  Put
 * Successful Response
 */
export const zUpdateContrahentsApiV1CrmContrahentsPutResponse = z.array(zContrahentDisplayTypes);

export const zDeleteContrahentsApiV1CrmContrahentsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetContrahentsOneApiV1CrmContrahentsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetContrahentsOneApiV1CrmContrahentsItemIdGetResponse = zContrahentDisplayTypes;

export const zGetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetResponse = zContrahentWithRelationsDisplayTypes;

export const zGetContrahentsAllApiV1CrmContrahentsGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Contrahents All Api V1 Crm Contrahents Get All Post
 * Successful Response
 */
export const zGetContrahentsAllApiV1CrmContrahentsGetAllPostResponse = z.union([
    zContrahentDataTypes,
    zContrahentDataColumnsTypes
]);

export const zReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        org_id: z.number().int()
    }),
    query: z.object({
        skip: z.number().int().optional().default(0),
        limit: z.number().int().optional().default(100)
    }).optional()
});

/**
 * Successful Response
 */
export const zReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetResponse = zContrahentDataTypes;

export const zGetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        org_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Response Get Car Owners Api V1 Crm Contrahents Car Owners  Org Id  Get
 * Successful Response
 */
export const zGetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetResponse = z.array(zContrahentWithVehiclesDisplayTypes);

export const zCreateProfilesApiV1CrmProfilesPostData = z.object({
    body: z.array(zProfileCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Profiles Api V1 Crm Profiles  Post
 * Successful Response
 */
export const zCreateProfilesApiV1CrmProfilesPostResponse = z.array(zProfileDisplayTypes);

export const zUpdateProfilesApiV1CrmProfilesPutData = z.object({
    body: z.array(zProfileUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Profiles Api V1 Crm Profiles  Put
 * Successful Response
 */
export const zUpdateProfilesApiV1CrmProfilesPutResponse = z.array(zProfileDisplayTypes);

export const zDeleteProfilesApiV1CrmProfilesItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetProfilesOneApiV1CrmProfilesItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetProfilesOneApiV1CrmProfilesItemIdGetResponse = zProfileDisplayTypes;

export const zGetProfilesAllApiV1CrmProfilesGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Profiles All Api V1 Crm Profiles Get All Post
 * Successful Response
 */
export const zGetProfilesAllApiV1CrmProfilesGetAllPostResponse = z.union([
    zProfileDataTypes,
    zProfileDataColumnsTypes
]);

export const zCreateTypesApiV1CrmTypesPostData = z.object({
    body: z.array(zTypeCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Types Api V1 Crm Types  Post
 * Successful Response
 */
export const zCreateTypesApiV1CrmTypesPostResponse = z.array(zTypeDisplayTypes);

export const zUpdateTypesApiV1CrmTypesPutData = z.object({
    body: z.array(zTypeUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Types Api V1 Crm Types  Put
 * Successful Response
 */
export const zUpdateTypesApiV1CrmTypesPutResponse = z.array(zTypeDisplayTypes);

export const zDeleteTypesApiV1CrmTypesItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetTypesOneApiV1CrmTypesItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetTypesOneApiV1CrmTypesItemIdGetResponse = zTypeDisplayTypes;

export const zGetTypesAllApiV1CrmTypesGetAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Types All Api V1 Crm Types Get All Post
 * Successful Response
 */
export const zGetTypesAllApiV1CrmTypesGetAllPostResponse = z.union([
    zTypeDataTypes,
    zTypeDataColumnsTypes
]);

export const zGetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        org_id: z.number().int(),
        is_public: z.union([
            z.boolean(),
            z.null()
        ]).optional(),
        contrahent_id: z.union([
            z.number().int(),
            z.null()
        ]).optional(),
        date_column: z.union([
            z.string(),
            z.null()
        ]).optional(),
        start_date: z.union([
            z.string(),
            z.null()
        ]).optional(),
        end_date: z.union([
            z.string(),
            z.null()
        ]).optional(),
        statuses: z.union([
            z.string(),
            z.null()
        ]).optional(),
        page_index: z.number().int().gte(0).optional().default(0),
        page_size: z.union([
            z.number().int().gt(0),
            z.null()
        ]).optional(),
        search: z.union([
            z.string(),
            z.null()
        ]).optional(),
        order: z.union([
            z.string(),
            z.null()
        ]).optional(),
        columns: z.union([
            z.string(),
            z.null()
        ]).optional()
    })
});

/**
 * Response Get Org Transactions All Api V1 Money Org Transactions  Get
 * Successful Response
 */
export const zGetOrgTransactionsAllApiV1MoneyOrgTransactionsGetResponse = z.union([
    zOrgTransactionDataTypes,
    zOrgTransactionDisplayColumnsTypes
]);

export const zCreateOrgTransactionsApiV1MoneyOrgTransactionsPostData = z.object({
    body: z.array(zOrgTransactionCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Org Transactions Api V1 Money Org Transactions  Post
 * Successful Response
 */
export const zCreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponse = z.array(zOrgTransactionDisplayTypes);

export const zUpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData = z.object({
    body: z.array(zOrgTransactionUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Org Transactions Api V1 Money Org Transactions  Put
 * Successful Response
 */
export const zUpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponse = z.array(zOrgTransactionDisplayTypes);

export const zCreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData = z.object({
    body: z.array(zOrgTransactionCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Org Transactions Saved Api V1 Money Org Transactions Saved Post
 * Successful Response
 */
export const zCreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponse = z.array(zOrgTransactionDisplayTypes);

export const zDeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetResponse = zOrgTransactionDisplayTypes;

export const zReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        org_id: z.number().int()
    }),
    query: z.object({
        skip: z.number().int().optional().default(0),
        limit: z.number().int().optional().default(100)
    }).optional()
});

/**
 * Successful Response
 */
export const zReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetResponse = zOrgTransactionDataTypes;

export const zReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        org_id: z.number().int()
    }),
    query: z.object({
        period_start: z.string().date(),
        period_end: z.string().date()
    })
});

/**
 * Successful Response
 */
export const zReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetResponse = zOrgTransactionScheduledTypes;

export const zCreateOrgSplitsApiV1MoneyOrgSplitsPostData = z.object({
    body: z.array(zOrgSplitCreateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Create Org Splits Api V1 Money Org Splits  Post
 * Successful Response
 */
export const zCreateOrgSplitsApiV1MoneyOrgSplitsPostResponse = z.array(zOrgSplitDisplayTypes);

export const zUpdateOrgSplitsApiV1MoneyOrgSplitsPutData = z.object({
    body: z.array(zOrgSplitUpdateTypes),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * Response Update Org Splits Api V1 Money Org Splits  Put
 * Successful Response
 */
export const zUpdateOrgSplitsApiV1MoneyOrgSplitsPutResponse = z.array(zOrgSplitDisplayTypes);

export const zDeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zGetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        item_id: z.number().int()
    }),
    query: z.never().optional()
});

/**
 * Successful Response
 */
export const zGetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetResponse = zOrgSplitDisplayTypes;

export const zGetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Response Get Org Splits All Api V1 Money Org Splits All Post
 * Successful Response
 */
export const zGetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponse = z.union([
    zOrgSplitDataTypes,
    zOrgSplitDataColumnsTypes
]);

export const zGetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Successful Response
 */
export const zGetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponse = zGenericDataResponse;

export const zGetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData = z.object({
    body: zServerOptions,
    path: z.never().optional(),
    query: z.object({
        skip_validation: z.boolean().optional().default(false)
    }).optional()
});

/**
 * Successful Response
 */
export const zGetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponse = zGenericDataResponse;

export const zGetJobJobsDatesJobIdGetData = z.object({
    body: z.never().optional(),
    path: z.object({
        job_id: z.number().int()
    }),
    query: z.never().optional()
});

export const zHealthCheckHealthGetData = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});