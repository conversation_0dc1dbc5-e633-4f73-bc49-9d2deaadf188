// This file is auto-generated by @hey-api/openapi-ts

import { type Options, createUsersApiV1AuthUsersPost, updateUsersApiV1AuthUsersPut, getUsersOneApiV1AuthUsersKcKcIdGet, deleteUsersApiV1AuthUsersItemIdDelete, getUsersOneApiV1AuthUsersItemIdGet, getUsersAllApiV1AuthUsersGetAllPost, registerApiV1AuthUsersRegisterPost, linkContrahentApiV1AuthUsersLinkContrahentPost, createJobsApiV1CoreJobsPost, updateJobsApiV1CoreJobsPut, deleteJobsApiV1CoreJobsItemIdDelete, getJobsOneApiV1CoreJobsItemIdGet, getJobsAllApiV1CoreJobsGetAllPost, getJobTasksAllApiV1CoreJobTasksGet, createJobTasksApiV1CoreJobTasksPost, updateJobTasksApiV1CoreJobTasksPut, deleteJobTasksApiV1CoreJobTasksItemIdDelete, getJobTasksOneApiV1CoreJobTasksItemIdGet, createFilesApiV1CoreFilesPost, updateFilesApiV1CoreFilesPut, deleteFilesApiV1CoreFilesItemIdDelete, getFilesOneApiV1CoreFilesItemIdGet, getFilesAllApiV1CoreFilesGetAllPost, createObjectsApiV1CoreObjectsPost, updateObjectsApiV1CoreObjectsPut, deleteObjectsApiV1CoreObjectsItemIdDelete, getObjectsOneApiV1CoreObjectsItemIdGet, getObjectsAllApiV1CoreObjectsGetAllPost, createObjectSystemsApiV1CoreObjectSystemsPost, updateObjectSystemsApiV1CoreObjectSystemsPut, deleteObjectSystemsApiV1CoreObjectSystemsItemIdDelete, getObjectSystemsOneApiV1CoreObjectSystemsItemIdGet, getObjectSystemsAllApiV1CoreObjectSystemsGetAllPost, createOrgsApiV1CoreOrgsPost, updateOrgsApiV1CoreOrgsPut, deleteOrgsApiV1CoreOrgsItemIdDelete, getOrgsOneApiV1CoreOrgsItemIdGet, getOrgsAllApiV1CoreOrgsGetAllPost, createEmailTypesApiV1CoreEmailTypesPost, updateEmailTypesApiV1CoreEmailTypesPut, deleteEmailTypesApiV1CoreEmailTypesItemIdDelete, getEmailTypesOneApiV1CoreEmailTypesItemIdGet, getEmailTypesAllApiV1CoreEmailTypesGetAllPost, createEmailsApiV1CoreEmailsPost, updateEmailsApiV1CoreEmailsPut, deleteEmailsApiV1CoreEmailsItemIdDelete, getEmailsOneApiV1CoreEmailsItemIdGet, getEmailsAllApiV1CoreEmailsGetAllPost, createCommentsApiV1CoreCommentsPost, updateCommentsApiV1CoreCommentsPut, deleteCommentsApiV1CoreCommentsItemIdDelete, getCommentsOneApiV1CoreCommentsItemIdGet, getCommentsAllApiV1CoreCommentsGetAllPost, createDocsApiV1CoreDocsPost, updateDocsApiV1CoreDocsPut, getDocTemplatesApiV1CoreDocsTemplatesPost, getTemplatesTableApiV1CoreDocsTemplatesTablePost, deleteDocsApiV1CoreDocsItemIdDelete, getDocsOneApiV1CoreDocsItemIdGet, getDocsAllApiV1CoreDocsGetAllPost, getDocsAllWithContentApiV1CoreDocsGetAllWithContentPost, updateDocsWithContentApiV1CoreDocsWithContentPut, createDocPagesApiV1CoreDocPagesPost, updateDocPagesWithPartsApiV1CoreDocPagesPut, deleteDocPagesWithLinksApiV1CoreDocPagesItemIdDelete, getDocPagesOneApiV1CoreDocPagesItemIdGet, getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPost, getDocPagesAllApiV1CoreDocPagesGetAllPost, createDocPartsApiV1CoreDocPartsPost, updateDocPartsApiV1CoreDocPartsPut, deleteDocPartsApiV1CoreDocPartsItemIdDelete, getDocPartsOneApiV1CoreDocPartsItemIdGet, getDocPartsAllApiV1CoreDocPartsGetAllPost, createVariantsApiV1CoreVariantsPost, updateVariantsApiV1CoreVariantsPut, deleteVariantsApiV1CoreVariantsItemIdDelete, getVariantsOneApiV1CoreVariantsItemIdGet, getVariantsAllApiV1CoreVariantsGetAllPost, createAddressesApiV1CrmAddressesPost, updateAddressesApiV1CrmAddressesPut, deleteAddressesApiV1CrmAddressesItemIdDelete, getAddressesOneApiV1CrmAddressesItemIdGet, getAddressesAllApiV1CrmAddressesGetAllPost, createContactsApiV1CrmContactsPost, updateContactsApiV1CrmContactsPut, deleteContactsApiV1CrmContactsItemIdDelete, getContactsOneApiV1CrmContactsItemIdGet, getContactsAllApiV1CrmContactsGetAllPost, createContrahentsApiV1CrmContrahentsPost, updateContrahentsApiV1CrmContrahentsPut, deleteContrahentsApiV1CrmContrahentsItemIdDelete, getContrahentsOneApiV1CrmContrahentsItemIdGet, getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGet, getContrahentsAllApiV1CrmContrahentsGetAllPost, readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGet, getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGet, createProfilesApiV1CrmProfilesPost, updateProfilesApiV1CrmProfilesPut, deleteProfilesApiV1CrmProfilesItemIdDelete, getProfilesOneApiV1CrmProfilesItemIdGet, getProfilesAllApiV1CrmProfilesGetAllPost, createTypesApiV1CrmTypesPost, updateTypesApiV1CrmTypesPut, deleteTypesApiV1CrmTypesItemIdDelete, getTypesOneApiV1CrmTypesItemIdGet, getTypesAllApiV1CrmTypesGetAllPost, getOrgTransactionsAllApiV1MoneyOrgTransactionsGet, createOrgTransactionsApiV1MoneyOrgTransactionsPost, updateOrgTransactionsApiV1MoneyOrgTransactionsPut, createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPost, deleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDelete, getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGet, readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGet, readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGet, createOrgSplitsApiV1MoneyOrgSplitsPost, updateOrgSplitsApiV1MoneyOrgSplitsPut, deleteOrgSplitsApiV1MoneyOrgSplitsItemIdDelete, getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGet, getOrgSplitsAllApiV1MoneyOrgSplitsAllPost, getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPost, getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePost, getJobJobsDatesJobIdGet, healthCheckHealthGet } from '../sdk.gen';
import { queryOptions, type UseMutationOptions } from '@tanstack/react-query';
import type { CreateUsersApiV1AuthUsersPostData, CreateUsersApiV1AuthUsersPostError, CreateUsersApiV1AuthUsersPostResponse, UpdateUsersApiV1AuthUsersPutData, UpdateUsersApiV1AuthUsersPutError, UpdateUsersApiV1AuthUsersPutResponse, GetUsersOneApiV1AuthUsersKcKcIdGetData, DeleteUsersApiV1AuthUsersItemIdDeleteData, DeleteUsersApiV1AuthUsersItemIdDeleteError, GetUsersOneApiV1AuthUsersItemIdGetData, GetUsersAllApiV1AuthUsersGetAllPostData, GetUsersAllApiV1AuthUsersGetAllPostError, GetUsersAllApiV1AuthUsersGetAllPostResponse, RegisterApiV1AuthUsersRegisterPostData, RegisterApiV1AuthUsersRegisterPostError, RegisterApiV1AuthUsersRegisterPostResponse, LinkContrahentApiV1AuthUsersLinkContrahentPostData, LinkContrahentApiV1AuthUsersLinkContrahentPostError, LinkContrahentApiV1AuthUsersLinkContrahentPostResponse, CreateJobsApiV1CoreJobsPostData, CreateJobsApiV1CoreJobsPostError, CreateJobsApiV1CoreJobsPostResponse, UpdateJobsApiV1CoreJobsPutData, UpdateJobsApiV1CoreJobsPutError, UpdateJobsApiV1CoreJobsPutResponse, DeleteJobsApiV1CoreJobsItemIdDeleteData, DeleteJobsApiV1CoreJobsItemIdDeleteError, DeleteJobsApiV1CoreJobsItemIdDeleteResponse, GetJobsOneApiV1CoreJobsItemIdGetData, GetJobsAllApiV1CoreJobsGetAllPostData, GetJobsAllApiV1CoreJobsGetAllPostError, GetJobsAllApiV1CoreJobsGetAllPostResponse, GetJobTasksAllApiV1CoreJobTasksGetData, CreateJobTasksApiV1CoreJobTasksPostData, CreateJobTasksApiV1CoreJobTasksPostError, CreateJobTasksApiV1CoreJobTasksPostResponse, UpdateJobTasksApiV1CoreJobTasksPutData, UpdateJobTasksApiV1CoreJobTasksPutError, UpdateJobTasksApiV1CoreJobTasksPutResponse, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteError, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponse, GetJobTasksOneApiV1CoreJobTasksItemIdGetData, CreateFilesApiV1CoreFilesPostData, CreateFilesApiV1CoreFilesPostError, CreateFilesApiV1CoreFilesPostResponse, UpdateFilesApiV1CoreFilesPutData, UpdateFilesApiV1CoreFilesPutError, UpdateFilesApiV1CoreFilesPutResponse, DeleteFilesApiV1CoreFilesItemIdDeleteData, DeleteFilesApiV1CoreFilesItemIdDeleteError, DeleteFilesApiV1CoreFilesItemIdDeleteResponse, GetFilesOneApiV1CoreFilesItemIdGetData, GetFilesAllApiV1CoreFilesGetAllPostData, GetFilesAllApiV1CoreFilesGetAllPostError, GetFilesAllApiV1CoreFilesGetAllPostResponse, CreateObjectsApiV1CoreObjectsPostData, CreateObjectsApiV1CoreObjectsPostError, CreateObjectsApiV1CoreObjectsPostResponse, UpdateObjectsApiV1CoreObjectsPutData, UpdateObjectsApiV1CoreObjectsPutError, UpdateObjectsApiV1CoreObjectsPutResponse, DeleteObjectsApiV1CoreObjectsItemIdDeleteData, DeleteObjectsApiV1CoreObjectsItemIdDeleteError, GetObjectsOneApiV1CoreObjectsItemIdGetData, GetObjectsAllApiV1CoreObjectsGetAllPostData, GetObjectsAllApiV1CoreObjectsGetAllPostError, GetObjectsAllApiV1CoreObjectsGetAllPostResponse, CreateObjectSystemsApiV1CoreObjectSystemsPostData, CreateObjectSystemsApiV1CoreObjectSystemsPostError, CreateObjectSystemsApiV1CoreObjectSystemsPostResponse, UpdateObjectSystemsApiV1CoreObjectSystemsPutData, UpdateObjectSystemsApiV1CoreObjectSystemsPutError, UpdateObjectSystemsApiV1CoreObjectSystemsPutResponse, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteError, GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostError, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponse, CreateOrgsApiV1CoreOrgsPostData, CreateOrgsApiV1CoreOrgsPostError, CreateOrgsApiV1CoreOrgsPostResponse, UpdateOrgsApiV1CoreOrgsPutData, UpdateOrgsApiV1CoreOrgsPutError, UpdateOrgsApiV1CoreOrgsPutResponse, DeleteOrgsApiV1CoreOrgsItemIdDeleteData, DeleteOrgsApiV1CoreOrgsItemIdDeleteError, GetOrgsOneApiV1CoreOrgsItemIdGetData, GetOrgsAllApiV1CoreOrgsGetAllPostData, GetOrgsAllApiV1CoreOrgsGetAllPostError, GetOrgsAllApiV1CoreOrgsGetAllPostResponse, CreateEmailTypesApiV1CoreEmailTypesPostData, CreateEmailTypesApiV1CoreEmailTypesPostError, CreateEmailTypesApiV1CoreEmailTypesPostResponse, UpdateEmailTypesApiV1CoreEmailTypesPutData, UpdateEmailTypesApiV1CoreEmailTypesPutError, UpdateEmailTypesApiV1CoreEmailTypesPutResponse, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteError, GetEmailTypesOneApiV1CoreEmailTypesItemIdGetData, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostError, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponse, CreateEmailsApiV1CoreEmailsPostData, CreateEmailsApiV1CoreEmailsPostError, CreateEmailsApiV1CoreEmailsPostResponse, UpdateEmailsApiV1CoreEmailsPutData, UpdateEmailsApiV1CoreEmailsPutError, UpdateEmailsApiV1CoreEmailsPutResponse, DeleteEmailsApiV1CoreEmailsItemIdDeleteData, DeleteEmailsApiV1CoreEmailsItemIdDeleteError, GetEmailsOneApiV1CoreEmailsItemIdGetData, GetEmailsAllApiV1CoreEmailsGetAllPostData, GetEmailsAllApiV1CoreEmailsGetAllPostError, GetEmailsAllApiV1CoreEmailsGetAllPostResponse, CreateCommentsApiV1CoreCommentsPostData, CreateCommentsApiV1CoreCommentsPostError, CreateCommentsApiV1CoreCommentsPostResponse, UpdateCommentsApiV1CoreCommentsPutData, UpdateCommentsApiV1CoreCommentsPutError, UpdateCommentsApiV1CoreCommentsPutResponse, DeleteCommentsApiV1CoreCommentsItemIdDeleteData, DeleteCommentsApiV1CoreCommentsItemIdDeleteError, GetCommentsOneApiV1CoreCommentsItemIdGetData, GetCommentsAllApiV1CoreCommentsGetAllPostData, GetCommentsAllApiV1CoreCommentsGetAllPostError, GetCommentsAllApiV1CoreCommentsGetAllPostResponse, CreateDocsApiV1CoreDocsPostData, CreateDocsApiV1CoreDocsPostError, CreateDocsApiV1CoreDocsPostResponse, UpdateDocsApiV1CoreDocsPutData, UpdateDocsApiV1CoreDocsPutError, UpdateDocsApiV1CoreDocsPutResponse, GetDocTemplatesApiV1CoreDocsTemplatesPostData, GetDocTemplatesApiV1CoreDocsTemplatesPostError, GetDocTemplatesApiV1CoreDocsTemplatesPostResponse, GetTemplatesTableApiV1CoreDocsTemplatesTablePostData, GetTemplatesTableApiV1CoreDocsTemplatesTablePostError, GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponse, DeleteDocsApiV1CoreDocsItemIdDeleteData, DeleteDocsApiV1CoreDocsItemIdDeleteError, GetDocsOneApiV1CoreDocsItemIdGetData, GetDocsAllApiV1CoreDocsGetAllPostData, GetDocsAllApiV1CoreDocsGetAllPostError, GetDocsAllApiV1CoreDocsGetAllPostResponse, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostError, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponse, UpdateDocsWithContentApiV1CoreDocsWithContentPutData, UpdateDocsWithContentApiV1CoreDocsWithContentPutError, UpdateDocsWithContentApiV1CoreDocsWithContentPutResponse, CreateDocPagesApiV1CoreDocPagesPostData, CreateDocPagesApiV1CoreDocPagesPostError, CreateDocPagesApiV1CoreDocPagesPostResponse, UpdateDocPagesWithPartsApiV1CoreDocPagesPutData, UpdateDocPagesWithPartsApiV1CoreDocPagesPutError, UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponse, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteError, GetDocPagesOneApiV1CoreDocPagesItemIdGetData, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostError, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponse, GetDocPagesAllApiV1CoreDocPagesGetAllPostData, GetDocPagesAllApiV1CoreDocPagesGetAllPostError, GetDocPagesAllApiV1CoreDocPagesGetAllPostResponse, CreateDocPartsApiV1CoreDocPartsPostData, CreateDocPartsApiV1CoreDocPartsPostError, CreateDocPartsApiV1CoreDocPartsPostResponse, UpdateDocPartsApiV1CoreDocPartsPutData, UpdateDocPartsApiV1CoreDocPartsPutError, UpdateDocPartsApiV1CoreDocPartsPutResponse, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteError, GetDocPartsOneApiV1CoreDocPartsItemIdGetData, GetDocPartsAllApiV1CoreDocPartsGetAllPostData, GetDocPartsAllApiV1CoreDocPartsGetAllPostError, GetDocPartsAllApiV1CoreDocPartsGetAllPostResponse, CreateVariantsApiV1CoreVariantsPostData, CreateVariantsApiV1CoreVariantsPostError, CreateVariantsApiV1CoreVariantsPostResponse, UpdateVariantsApiV1CoreVariantsPutData, UpdateVariantsApiV1CoreVariantsPutError, UpdateVariantsApiV1CoreVariantsPutResponse, DeleteVariantsApiV1CoreVariantsItemIdDeleteData, DeleteVariantsApiV1CoreVariantsItemIdDeleteError, GetVariantsOneApiV1CoreVariantsItemIdGetData, GetVariantsAllApiV1CoreVariantsGetAllPostData, GetVariantsAllApiV1CoreVariantsGetAllPostError, GetVariantsAllApiV1CoreVariantsGetAllPostResponse, CreateAddressesApiV1CrmAddressesPostData, CreateAddressesApiV1CrmAddressesPostError, CreateAddressesApiV1CrmAddressesPostResponse, UpdateAddressesApiV1CrmAddressesPutData, UpdateAddressesApiV1CrmAddressesPutError, UpdateAddressesApiV1CrmAddressesPutResponse, DeleteAddressesApiV1CrmAddressesItemIdDeleteData, DeleteAddressesApiV1CrmAddressesItemIdDeleteError, GetAddressesOneApiV1CrmAddressesItemIdGetData, GetAddressesAllApiV1CrmAddressesGetAllPostData, GetAddressesAllApiV1CrmAddressesGetAllPostError, GetAddressesAllApiV1CrmAddressesGetAllPostResponse, CreateContactsApiV1CrmContactsPostData, CreateContactsApiV1CrmContactsPostError, CreateContactsApiV1CrmContactsPostResponse, UpdateContactsApiV1CrmContactsPutData, UpdateContactsApiV1CrmContactsPutError, UpdateContactsApiV1CrmContactsPutResponse, DeleteContactsApiV1CrmContactsItemIdDeleteData, DeleteContactsApiV1CrmContactsItemIdDeleteError, GetContactsOneApiV1CrmContactsItemIdGetData, GetContactsAllApiV1CrmContactsGetAllPostData, GetContactsAllApiV1CrmContactsGetAllPostError, GetContactsAllApiV1CrmContactsGetAllPostResponse, CreateContrahentsApiV1CrmContrahentsPostData, CreateContrahentsApiV1CrmContrahentsPostError, CreateContrahentsApiV1CrmContrahentsPostResponse, UpdateContrahentsApiV1CrmContrahentsPutData, UpdateContrahentsApiV1CrmContrahentsPutError, UpdateContrahentsApiV1CrmContrahentsPutResponse, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteError, GetContrahentsOneApiV1CrmContrahentsItemIdGetData, GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData, GetContrahentsAllApiV1CrmContrahentsGetAllPostData, GetContrahentsAllApiV1CrmContrahentsGetAllPostError, GetContrahentsAllApiV1CrmContrahentsGetAllPostResponse, ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData, GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData, CreateProfilesApiV1CrmProfilesPostData, CreateProfilesApiV1CrmProfilesPostError, CreateProfilesApiV1CrmProfilesPostResponse, UpdateProfilesApiV1CrmProfilesPutData, UpdateProfilesApiV1CrmProfilesPutError, UpdateProfilesApiV1CrmProfilesPutResponse, DeleteProfilesApiV1CrmProfilesItemIdDeleteData, DeleteProfilesApiV1CrmProfilesItemIdDeleteError, GetProfilesOneApiV1CrmProfilesItemIdGetData, GetProfilesAllApiV1CrmProfilesGetAllPostData, GetProfilesAllApiV1CrmProfilesGetAllPostError, GetProfilesAllApiV1CrmProfilesGetAllPostResponse, CreateTypesApiV1CrmTypesPostData, CreateTypesApiV1CrmTypesPostError, CreateTypesApiV1CrmTypesPostResponse, UpdateTypesApiV1CrmTypesPutData, UpdateTypesApiV1CrmTypesPutError, UpdateTypesApiV1CrmTypesPutResponse, DeleteTypesApiV1CrmTypesItemIdDeleteData, DeleteTypesApiV1CrmTypesItemIdDeleteError, GetTypesOneApiV1CrmTypesItemIdGetData, GetTypesAllApiV1CrmTypesGetAllPostData, GetTypesAllApiV1CrmTypesGetAllPostError, GetTypesAllApiV1CrmTypesGetAllPostResponse, GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostError, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponse, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutError, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponse, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostError, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponse, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteError, GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData, ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData, ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData, CreateOrgSplitsApiV1MoneyOrgSplitsPostData, CreateOrgSplitsApiV1MoneyOrgSplitsPostError, CreateOrgSplitsApiV1MoneyOrgSplitsPostResponse, UpdateOrgSplitsApiV1MoneyOrgSplitsPutData, UpdateOrgSplitsApiV1MoneyOrgSplitsPutError, UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponse, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteError, GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostError, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponse, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostError, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponse, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostError, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponse, GetJobJobsDatesJobIdGetData, HealthCheckHealthGetData } from '../types.gen';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseUrl' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseUrl: (options?.client ?? _heyApiClient).getConfig().baseUrl } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const createUsersApiV1AuthUsersPostQueryKey = (options: Options<CreateUsersApiV1AuthUsersPostData>) => createQueryKey('createUsersApiV1AuthUsersPost', options);

/**
 * Create Users
 * Create new users
 */
export const createUsersApiV1AuthUsersPostOptions = (options: Options<CreateUsersApiV1AuthUsersPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createUsersApiV1AuthUsersPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createUsersApiV1AuthUsersPostQueryKey(options)
    });
};

/**
 * Create Users
 * Create new users
 */
export const createUsersApiV1AuthUsersPostMutation = (options?: Partial<Options<CreateUsersApiV1AuthUsersPostData>>): UseMutationOptions<CreateUsersApiV1AuthUsersPostResponse, CreateUsersApiV1AuthUsersPostError, Options<CreateUsersApiV1AuthUsersPostData>> => {
    const mutationOptions: UseMutationOptions<CreateUsersApiV1AuthUsersPostResponse, CreateUsersApiV1AuthUsersPostError, Options<CreateUsersApiV1AuthUsersPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createUsersApiV1AuthUsersPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Users
 * Update users
 */
export const updateUsersApiV1AuthUsersPutMutation = (options?: Partial<Options<UpdateUsersApiV1AuthUsersPutData>>): UseMutationOptions<UpdateUsersApiV1AuthUsersPutResponse, UpdateUsersApiV1AuthUsersPutError, Options<UpdateUsersApiV1AuthUsersPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateUsersApiV1AuthUsersPutResponse, UpdateUsersApiV1AuthUsersPutError, Options<UpdateUsersApiV1AuthUsersPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateUsersApiV1AuthUsersPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getUsersOneApiV1AuthUsersKcKcIdGetQueryKey = (options: Options<GetUsersOneApiV1AuthUsersKcKcIdGetData>) => createQueryKey('getUsersOneApiV1AuthUsersKcKcIdGet', options);

/**
 * Get Users One
 * Get a user by their Keycloak ID.
 *
 * Args:
 * keycloak_id: The Keycloak user ID (UUID string)
 *
 * Returns:
 * UserDisplayTypes: User data if found
 *
 * Raises:
 * HTTPException: If user is not found, invalid UUID format, or an error occurs
 */
export const getUsersOneApiV1AuthUsersKcKcIdGetOptions = (options: Options<GetUsersOneApiV1AuthUsersKcKcIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUsersOneApiV1AuthUsersKcKcIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUsersOneApiV1AuthUsersKcKcIdGetQueryKey(options)
    });
};

/**
 * Delete Users
 * Delete users by ID.
 */
export const deleteUsersApiV1AuthUsersItemIdDeleteMutation = (options?: Partial<Options<DeleteUsersApiV1AuthUsersItemIdDeleteData>>): UseMutationOptions<unknown, DeleteUsersApiV1AuthUsersItemIdDeleteError, Options<DeleteUsersApiV1AuthUsersItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteUsersApiV1AuthUsersItemIdDeleteError, Options<DeleteUsersApiV1AuthUsersItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteUsersApiV1AuthUsersItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getUsersOneApiV1AuthUsersItemIdGetQueryKey = (options: Options<GetUsersOneApiV1AuthUsersItemIdGetData>) => createQueryKey('getUsersOneApiV1AuthUsersItemIdGet', options);

/**
 * Get Users One
 * Get users by ID.
 */
export const getUsersOneApiV1AuthUsersItemIdGetOptions = (options: Options<GetUsersOneApiV1AuthUsersItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUsersOneApiV1AuthUsersItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUsersOneApiV1AuthUsersItemIdGetQueryKey(options)
    });
};

export const getUsersAllApiV1AuthUsersGetAllPostQueryKey = (options: Options<GetUsersAllApiV1AuthUsersGetAllPostData>) => createQueryKey('getUsersAllApiV1AuthUsersGetAllPost', options);

/**
 * Get Users All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getUsersAllApiV1AuthUsersGetAllPostOptions = (options: Options<GetUsersAllApiV1AuthUsersGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getUsersAllApiV1AuthUsersGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getUsersAllApiV1AuthUsersGetAllPostQueryKey(options)
    });
};

/**
 * Get Users All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getUsersAllApiV1AuthUsersGetAllPostMutation = (options?: Partial<Options<GetUsersAllApiV1AuthUsersGetAllPostData>>): UseMutationOptions<GetUsersAllApiV1AuthUsersGetAllPostResponse, GetUsersAllApiV1AuthUsersGetAllPostError, Options<GetUsersAllApiV1AuthUsersGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetUsersAllApiV1AuthUsersGetAllPostResponse, GetUsersAllApiV1AuthUsersGetAllPostError, Options<GetUsersAllApiV1AuthUsersGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getUsersAllApiV1AuthUsersGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const registerApiV1AuthUsersRegisterPostQueryKey = (options: Options<RegisterApiV1AuthUsersRegisterPostData>) => createQueryKey('registerApiV1AuthUsersRegisterPost', options);

/**
 * Register
 * Register a new user in both the database and Keycloak.
 *
 * Rate limited to 5 requests per minute to prevent abuse.
 */
export const registerApiV1AuthUsersRegisterPostOptions = (options: Options<RegisterApiV1AuthUsersRegisterPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await registerApiV1AuthUsersRegisterPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: registerApiV1AuthUsersRegisterPostQueryKey(options)
    });
};

/**
 * Register
 * Register a new user in both the database and Keycloak.
 *
 * Rate limited to 5 requests per minute to prevent abuse.
 */
export const registerApiV1AuthUsersRegisterPostMutation = (options?: Partial<Options<RegisterApiV1AuthUsersRegisterPostData>>): UseMutationOptions<RegisterApiV1AuthUsersRegisterPostResponse, RegisterApiV1AuthUsersRegisterPostError, Options<RegisterApiV1AuthUsersRegisterPostData>> => {
    const mutationOptions: UseMutationOptions<RegisterApiV1AuthUsersRegisterPostResponse, RegisterApiV1AuthUsersRegisterPostError, Options<RegisterApiV1AuthUsersRegisterPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await registerApiV1AuthUsersRegisterPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const linkContrahentApiV1AuthUsersLinkContrahentPostQueryKey = (options: Options<LinkContrahentApiV1AuthUsersLinkContrahentPostData>) => createQueryKey('linkContrahentApiV1AuthUsersLinkContrahentPost', options);

/**
 * Link Contrahent
 * Links a contrahent to a user, creating a new profile and updating Keycloak roles.
 */
export const linkContrahentApiV1AuthUsersLinkContrahentPostOptions = (options: Options<LinkContrahentApiV1AuthUsersLinkContrahentPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await linkContrahentApiV1AuthUsersLinkContrahentPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: linkContrahentApiV1AuthUsersLinkContrahentPostQueryKey(options)
    });
};

/**
 * Link Contrahent
 * Links a contrahent to a user, creating a new profile and updating Keycloak roles.
 */
export const linkContrahentApiV1AuthUsersLinkContrahentPostMutation = (options?: Partial<Options<LinkContrahentApiV1AuthUsersLinkContrahentPostData>>): UseMutationOptions<LinkContrahentApiV1AuthUsersLinkContrahentPostResponse, LinkContrahentApiV1AuthUsersLinkContrahentPostError, Options<LinkContrahentApiV1AuthUsersLinkContrahentPostData>> => {
    const mutationOptions: UseMutationOptions<LinkContrahentApiV1AuthUsersLinkContrahentPostResponse, LinkContrahentApiV1AuthUsersLinkContrahentPostError, Options<LinkContrahentApiV1AuthUsersLinkContrahentPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await linkContrahentApiV1AuthUsersLinkContrahentPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createJobsApiV1CoreJobsPostQueryKey = (options: Options<CreateJobsApiV1CoreJobsPostData>) => createQueryKey('createJobsApiV1CoreJobsPost', options);

/**
 * Create Jobs
 * Create new jobs with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createJobsApiV1CoreJobsPostOptions = (options: Options<CreateJobsApiV1CoreJobsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createJobsApiV1CoreJobsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createJobsApiV1CoreJobsPostQueryKey(options)
    });
};

/**
 * Create Jobs
 * Create new jobs with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createJobsApiV1CoreJobsPostMutation = (options?: Partial<Options<CreateJobsApiV1CoreJobsPostData>>): UseMutationOptions<CreateJobsApiV1CoreJobsPostResponse, CreateJobsApiV1CoreJobsPostError, Options<CreateJobsApiV1CoreJobsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateJobsApiV1CoreJobsPostResponse, CreateJobsApiV1CoreJobsPostError, Options<CreateJobsApiV1CoreJobsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createJobsApiV1CoreJobsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Jobs
 * Update jobs by ID.
 */
export const updateJobsApiV1CoreJobsPutMutation = (options?: Partial<Options<UpdateJobsApiV1CoreJobsPutData>>): UseMutationOptions<UpdateJobsApiV1CoreJobsPutResponse, UpdateJobsApiV1CoreJobsPutError, Options<UpdateJobsApiV1CoreJobsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateJobsApiV1CoreJobsPutResponse, UpdateJobsApiV1CoreJobsPutError, Options<UpdateJobsApiV1CoreJobsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateJobsApiV1CoreJobsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Jobs
 * Delete jobs by ID.
 */
export const deleteJobsApiV1CoreJobsItemIdDeleteMutation = (options?: Partial<Options<DeleteJobsApiV1CoreJobsItemIdDeleteData>>): UseMutationOptions<DeleteJobsApiV1CoreJobsItemIdDeleteResponse, DeleteJobsApiV1CoreJobsItemIdDeleteError, Options<DeleteJobsApiV1CoreJobsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<DeleteJobsApiV1CoreJobsItemIdDeleteResponse, DeleteJobsApiV1CoreJobsItemIdDeleteError, Options<DeleteJobsApiV1CoreJobsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteJobsApiV1CoreJobsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getJobsOneApiV1CoreJobsItemIdGetQueryKey = (options: Options<GetJobsOneApiV1CoreJobsItemIdGetData>) => createQueryKey('getJobsOneApiV1CoreJobsItemIdGet', options);

/**
 * Get Jobs One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getJobsOneApiV1CoreJobsItemIdGetOptions = (options: Options<GetJobsOneApiV1CoreJobsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getJobsOneApiV1CoreJobsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getJobsOneApiV1CoreJobsItemIdGetQueryKey(options)
    });
};

export const getJobsAllApiV1CoreJobsGetAllPostQueryKey = (options: Options<GetJobsAllApiV1CoreJobsGetAllPostData>) => createQueryKey('getJobsAllApiV1CoreJobsGetAllPost', options);

/**
 * Get Jobs All
 * Get all Jobs paginated list with advanced filtering and sorting options.
 */
export const getJobsAllApiV1CoreJobsGetAllPostOptions = (options: Options<GetJobsAllApiV1CoreJobsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getJobsAllApiV1CoreJobsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getJobsAllApiV1CoreJobsGetAllPostQueryKey(options)
    });
};

/**
 * Get Jobs All
 * Get all Jobs paginated list with advanced filtering and sorting options.
 */
export const getJobsAllApiV1CoreJobsGetAllPostMutation = (options?: Partial<Options<GetJobsAllApiV1CoreJobsGetAllPostData>>): UseMutationOptions<GetJobsAllApiV1CoreJobsGetAllPostResponse, GetJobsAllApiV1CoreJobsGetAllPostError, Options<GetJobsAllApiV1CoreJobsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetJobsAllApiV1CoreJobsGetAllPostResponse, GetJobsAllApiV1CoreJobsGetAllPostError, Options<GetJobsAllApiV1CoreJobsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getJobsAllApiV1CoreJobsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getJobTasksAllApiV1CoreJobTasksGetQueryKey = (options: Options<GetJobTasksAllApiV1CoreJobTasksGetData>) => createQueryKey('getJobTasksAllApiV1CoreJobTasksGet', options);

/**
 * Get Job Tasks All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getJobTasksAllApiV1CoreJobTasksGetOptions = (options: Options<GetJobTasksAllApiV1CoreJobTasksGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getJobTasksAllApiV1CoreJobTasksGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getJobTasksAllApiV1CoreJobTasksGetQueryKey(options)
    });
};

export const createJobTasksApiV1CoreJobTasksPostQueryKey = (options: Options<CreateJobTasksApiV1CoreJobTasksPostData>) => createQueryKey('createJobTasksApiV1CoreJobTasksPost', options);

/**
 * Create Job Tasks
 * Create new job_tasks with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createJobTasksApiV1CoreJobTasksPostOptions = (options: Options<CreateJobTasksApiV1CoreJobTasksPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createJobTasksApiV1CoreJobTasksPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createJobTasksApiV1CoreJobTasksPostQueryKey(options)
    });
};

/**
 * Create Job Tasks
 * Create new job_tasks with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createJobTasksApiV1CoreJobTasksPostMutation = (options?: Partial<Options<CreateJobTasksApiV1CoreJobTasksPostData>>): UseMutationOptions<CreateJobTasksApiV1CoreJobTasksPostResponse, CreateJobTasksApiV1CoreJobTasksPostError, Options<CreateJobTasksApiV1CoreJobTasksPostData>> => {
    const mutationOptions: UseMutationOptions<CreateJobTasksApiV1CoreJobTasksPostResponse, CreateJobTasksApiV1CoreJobTasksPostError, Options<CreateJobTasksApiV1CoreJobTasksPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createJobTasksApiV1CoreJobTasksPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Job Tasks
 * Update job_tasks by ID.
 */
export const updateJobTasksApiV1CoreJobTasksPutMutation = (options?: Partial<Options<UpdateJobTasksApiV1CoreJobTasksPutData>>): UseMutationOptions<UpdateJobTasksApiV1CoreJobTasksPutResponse, UpdateJobTasksApiV1CoreJobTasksPutError, Options<UpdateJobTasksApiV1CoreJobTasksPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateJobTasksApiV1CoreJobTasksPutResponse, UpdateJobTasksApiV1CoreJobTasksPutError, Options<UpdateJobTasksApiV1CoreJobTasksPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateJobTasksApiV1CoreJobTasksPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Job Tasks
 * Delete job_tasks by ID.
 */
export const deleteJobTasksApiV1CoreJobTasksItemIdDeleteMutation = (options?: Partial<Options<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData>>): UseMutationOptions<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponse, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteError, Options<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteResponse, DeleteJobTasksApiV1CoreJobTasksItemIdDeleteError, Options<DeleteJobTasksApiV1CoreJobTasksItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteJobTasksApiV1CoreJobTasksItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getJobTasksOneApiV1CoreJobTasksItemIdGetQueryKey = (options: Options<GetJobTasksOneApiV1CoreJobTasksItemIdGetData>) => createQueryKey('getJobTasksOneApiV1CoreJobTasksItemIdGet', options);

/**
 * Get Job Tasks One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getJobTasksOneApiV1CoreJobTasksItemIdGetOptions = (options: Options<GetJobTasksOneApiV1CoreJobTasksItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getJobTasksOneApiV1CoreJobTasksItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getJobTasksOneApiV1CoreJobTasksItemIdGetQueryKey(options)
    });
};

export const createFilesApiV1CoreFilesPostQueryKey = (options: Options<CreateFilesApiV1CoreFilesPostData>) => createQueryKey('createFilesApiV1CoreFilesPost', options);

/**
 * Create Files
 * Create new files with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createFilesApiV1CoreFilesPostOptions = (options: Options<CreateFilesApiV1CoreFilesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createFilesApiV1CoreFilesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createFilesApiV1CoreFilesPostQueryKey(options)
    });
};

/**
 * Create Files
 * Create new files with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createFilesApiV1CoreFilesPostMutation = (options?: Partial<Options<CreateFilesApiV1CoreFilesPostData>>): UseMutationOptions<CreateFilesApiV1CoreFilesPostResponse, CreateFilesApiV1CoreFilesPostError, Options<CreateFilesApiV1CoreFilesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateFilesApiV1CoreFilesPostResponse, CreateFilesApiV1CoreFilesPostError, Options<CreateFilesApiV1CoreFilesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createFilesApiV1CoreFilesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Files
 * Update files by ID.
 */
export const updateFilesApiV1CoreFilesPutMutation = (options?: Partial<Options<UpdateFilesApiV1CoreFilesPutData>>): UseMutationOptions<UpdateFilesApiV1CoreFilesPutResponse, UpdateFilesApiV1CoreFilesPutError, Options<UpdateFilesApiV1CoreFilesPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateFilesApiV1CoreFilesPutResponse, UpdateFilesApiV1CoreFilesPutError, Options<UpdateFilesApiV1CoreFilesPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateFilesApiV1CoreFilesPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Files
 * Delete files by ID.
 */
export const deleteFilesApiV1CoreFilesItemIdDeleteMutation = (options?: Partial<Options<DeleteFilesApiV1CoreFilesItemIdDeleteData>>): UseMutationOptions<DeleteFilesApiV1CoreFilesItemIdDeleteResponse, DeleteFilesApiV1CoreFilesItemIdDeleteError, Options<DeleteFilesApiV1CoreFilesItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<DeleteFilesApiV1CoreFilesItemIdDeleteResponse, DeleteFilesApiV1CoreFilesItemIdDeleteError, Options<DeleteFilesApiV1CoreFilesItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteFilesApiV1CoreFilesItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getFilesOneApiV1CoreFilesItemIdGetQueryKey = (options: Options<GetFilesOneApiV1CoreFilesItemIdGetData>) => createQueryKey('getFilesOneApiV1CoreFilesItemIdGet', options);

/**
 * Get Files One
 * Get file.
 * Get a single file with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getFilesOneApiV1CoreFilesItemIdGetOptions = (options: Options<GetFilesOneApiV1CoreFilesItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getFilesOneApiV1CoreFilesItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getFilesOneApiV1CoreFilesItemIdGetQueryKey(options)
    });
};

export const getFilesAllApiV1CoreFilesGetAllPostQueryKey = (options: Options<GetFilesAllApiV1CoreFilesGetAllPostData>) => createQueryKey('getFilesAllApiV1CoreFilesGetAllPost', options);

/**
 * Get Files All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getFilesAllApiV1CoreFilesGetAllPostOptions = (options: Options<GetFilesAllApiV1CoreFilesGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getFilesAllApiV1CoreFilesGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getFilesAllApiV1CoreFilesGetAllPostQueryKey(options)
    });
};

/**
 * Get Files All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getFilesAllApiV1CoreFilesGetAllPostMutation = (options?: Partial<Options<GetFilesAllApiV1CoreFilesGetAllPostData>>): UseMutationOptions<GetFilesAllApiV1CoreFilesGetAllPostResponse, GetFilesAllApiV1CoreFilesGetAllPostError, Options<GetFilesAllApiV1CoreFilesGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetFilesAllApiV1CoreFilesGetAllPostResponse, GetFilesAllApiV1CoreFilesGetAllPostError, Options<GetFilesAllApiV1CoreFilesGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getFilesAllApiV1CoreFilesGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createObjectsApiV1CoreObjectsPostQueryKey = (options: Options<CreateObjectsApiV1CoreObjectsPostData>) => createQueryKey('createObjectsApiV1CoreObjectsPost', options);

/**
 * Create Objects
 * Create new objects
 */
export const createObjectsApiV1CoreObjectsPostOptions = (options: Options<CreateObjectsApiV1CoreObjectsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createObjectsApiV1CoreObjectsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createObjectsApiV1CoreObjectsPostQueryKey(options)
    });
};

/**
 * Create Objects
 * Create new objects
 */
export const createObjectsApiV1CoreObjectsPostMutation = (options?: Partial<Options<CreateObjectsApiV1CoreObjectsPostData>>): UseMutationOptions<CreateObjectsApiV1CoreObjectsPostResponse, CreateObjectsApiV1CoreObjectsPostError, Options<CreateObjectsApiV1CoreObjectsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateObjectsApiV1CoreObjectsPostResponse, CreateObjectsApiV1CoreObjectsPostError, Options<CreateObjectsApiV1CoreObjectsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createObjectsApiV1CoreObjectsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Objects
 * Update objects
 */
export const updateObjectsApiV1CoreObjectsPutMutation = (options?: Partial<Options<UpdateObjectsApiV1CoreObjectsPutData>>): UseMutationOptions<UpdateObjectsApiV1CoreObjectsPutResponse, UpdateObjectsApiV1CoreObjectsPutError, Options<UpdateObjectsApiV1CoreObjectsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateObjectsApiV1CoreObjectsPutResponse, UpdateObjectsApiV1CoreObjectsPutError, Options<UpdateObjectsApiV1CoreObjectsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateObjectsApiV1CoreObjectsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Objects
 * Delete objects by ID.
 */
export const deleteObjectsApiV1CoreObjectsItemIdDeleteMutation = (options?: Partial<Options<DeleteObjectsApiV1CoreObjectsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteObjectsApiV1CoreObjectsItemIdDeleteError, Options<DeleteObjectsApiV1CoreObjectsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteObjectsApiV1CoreObjectsItemIdDeleteError, Options<DeleteObjectsApiV1CoreObjectsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteObjectsApiV1CoreObjectsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getObjectsOneApiV1CoreObjectsItemIdGetQueryKey = (options: Options<GetObjectsOneApiV1CoreObjectsItemIdGetData>) => createQueryKey('getObjectsOneApiV1CoreObjectsItemIdGet', options);

/**
 * Get Objects One
 * Get objects by ID.
 */
export const getObjectsOneApiV1CoreObjectsItemIdGetOptions = (options: Options<GetObjectsOneApiV1CoreObjectsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getObjectsOneApiV1CoreObjectsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getObjectsOneApiV1CoreObjectsItemIdGetQueryKey(options)
    });
};

export const getObjectsAllApiV1CoreObjectsGetAllPostQueryKey = (options: Options<GetObjectsAllApiV1CoreObjectsGetAllPostData>) => createQueryKey('getObjectsAllApiV1CoreObjectsGetAllPost', options);

/**
 * Get Objects All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getObjectsAllApiV1CoreObjectsGetAllPostOptions = (options: Options<GetObjectsAllApiV1CoreObjectsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getObjectsAllApiV1CoreObjectsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getObjectsAllApiV1CoreObjectsGetAllPostQueryKey(options)
    });
};

/**
 * Get Objects All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getObjectsAllApiV1CoreObjectsGetAllPostMutation = (options?: Partial<Options<GetObjectsAllApiV1CoreObjectsGetAllPostData>>): UseMutationOptions<GetObjectsAllApiV1CoreObjectsGetAllPostResponse, GetObjectsAllApiV1CoreObjectsGetAllPostError, Options<GetObjectsAllApiV1CoreObjectsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetObjectsAllApiV1CoreObjectsGetAllPostResponse, GetObjectsAllApiV1CoreObjectsGetAllPostError, Options<GetObjectsAllApiV1CoreObjectsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getObjectsAllApiV1CoreObjectsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createObjectSystemsApiV1CoreObjectSystemsPostQueryKey = (options: Options<CreateObjectSystemsApiV1CoreObjectSystemsPostData>) => createQueryKey('createObjectSystemsApiV1CoreObjectSystemsPost', options);

/**
 * Create Object Systems
 * Create new object_systems
 */
export const createObjectSystemsApiV1CoreObjectSystemsPostOptions = (options: Options<CreateObjectSystemsApiV1CoreObjectSystemsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createObjectSystemsApiV1CoreObjectSystemsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createObjectSystemsApiV1CoreObjectSystemsPostQueryKey(options)
    });
};

/**
 * Create Object Systems
 * Create new object_systems
 */
export const createObjectSystemsApiV1CoreObjectSystemsPostMutation = (options?: Partial<Options<CreateObjectSystemsApiV1CoreObjectSystemsPostData>>): UseMutationOptions<CreateObjectSystemsApiV1CoreObjectSystemsPostResponse, CreateObjectSystemsApiV1CoreObjectSystemsPostError, Options<CreateObjectSystemsApiV1CoreObjectSystemsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateObjectSystemsApiV1CoreObjectSystemsPostResponse, CreateObjectSystemsApiV1CoreObjectSystemsPostError, Options<CreateObjectSystemsApiV1CoreObjectSystemsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createObjectSystemsApiV1CoreObjectSystemsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Object Systems
 * Update object_systems
 */
export const updateObjectSystemsApiV1CoreObjectSystemsPutMutation = (options?: Partial<Options<UpdateObjectSystemsApiV1CoreObjectSystemsPutData>>): UseMutationOptions<UpdateObjectSystemsApiV1CoreObjectSystemsPutResponse, UpdateObjectSystemsApiV1CoreObjectSystemsPutError, Options<UpdateObjectSystemsApiV1CoreObjectSystemsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateObjectSystemsApiV1CoreObjectSystemsPutResponse, UpdateObjectSystemsApiV1CoreObjectSystemsPutError, Options<UpdateObjectSystemsApiV1CoreObjectSystemsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateObjectSystemsApiV1CoreObjectSystemsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Object Systems
 * Delete object_systems by ID.
 */
export const deleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteMutation = (options?: Partial<Options<DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteError, Options<DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteError, Options<DeleteObjectSystemsApiV1CoreObjectSystemsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteObjectSystemsApiV1CoreObjectSystemsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getObjectSystemsOneApiV1CoreObjectSystemsItemIdGetQueryKey = (options: Options<GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData>) => createQueryKey('getObjectSystemsOneApiV1CoreObjectSystemsItemIdGet', options);

/**
 * Get Object Systems One
 * Get object_systems by ID.
 */
export const getObjectSystemsOneApiV1CoreObjectSystemsItemIdGetOptions = (options: Options<GetObjectSystemsOneApiV1CoreObjectSystemsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getObjectSystemsOneApiV1CoreObjectSystemsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getObjectSystemsOneApiV1CoreObjectSystemsItemIdGetQueryKey(options)
    });
};

export const getObjectSystemsAllApiV1CoreObjectSystemsGetAllPostQueryKey = (options: Options<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData>) => createQueryKey('getObjectSystemsAllApiV1CoreObjectSystemsGetAllPost', options);

/**
 * Get Object Systems All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getObjectSystemsAllApiV1CoreObjectSystemsGetAllPostOptions = (options: Options<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getObjectSystemsAllApiV1CoreObjectSystemsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getObjectSystemsAllApiV1CoreObjectSystemsGetAllPostQueryKey(options)
    });
};

/**
 * Get Object Systems All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getObjectSystemsAllApiV1CoreObjectSystemsGetAllPostMutation = (options?: Partial<Options<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData>>): UseMutationOptions<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponse, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostError, Options<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostResponse, GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostError, Options<GetObjectSystemsAllApiV1CoreObjectSystemsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getObjectSystemsAllApiV1CoreObjectSystemsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createOrgsApiV1CoreOrgsPostQueryKey = (options: Options<CreateOrgsApiV1CoreOrgsPostData>) => createQueryKey('createOrgsApiV1CoreOrgsPost', options);

/**
 * Create Orgs
 * Create new orgs
 */
export const createOrgsApiV1CoreOrgsPostOptions = (options: Options<CreateOrgsApiV1CoreOrgsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrgsApiV1CoreOrgsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrgsApiV1CoreOrgsPostQueryKey(options)
    });
};

/**
 * Create Orgs
 * Create new orgs
 */
export const createOrgsApiV1CoreOrgsPostMutation = (options?: Partial<Options<CreateOrgsApiV1CoreOrgsPostData>>): UseMutationOptions<CreateOrgsApiV1CoreOrgsPostResponse, CreateOrgsApiV1CoreOrgsPostError, Options<CreateOrgsApiV1CoreOrgsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrgsApiV1CoreOrgsPostResponse, CreateOrgsApiV1CoreOrgsPostError, Options<CreateOrgsApiV1CoreOrgsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrgsApiV1CoreOrgsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Orgs
 * Update orgs
 */
export const updateOrgsApiV1CoreOrgsPutMutation = (options?: Partial<Options<UpdateOrgsApiV1CoreOrgsPutData>>): UseMutationOptions<UpdateOrgsApiV1CoreOrgsPutResponse, UpdateOrgsApiV1CoreOrgsPutError, Options<UpdateOrgsApiV1CoreOrgsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrgsApiV1CoreOrgsPutResponse, UpdateOrgsApiV1CoreOrgsPutError, Options<UpdateOrgsApiV1CoreOrgsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrgsApiV1CoreOrgsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Orgs
 * Delete orgs by ID.
 */
export const deleteOrgsApiV1CoreOrgsItemIdDeleteMutation = (options?: Partial<Options<DeleteOrgsApiV1CoreOrgsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteOrgsApiV1CoreOrgsItemIdDeleteError, Options<DeleteOrgsApiV1CoreOrgsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteOrgsApiV1CoreOrgsItemIdDeleteError, Options<DeleteOrgsApiV1CoreOrgsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteOrgsApiV1CoreOrgsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrgsOneApiV1CoreOrgsItemIdGetQueryKey = (options: Options<GetOrgsOneApiV1CoreOrgsItemIdGetData>) => createQueryKey('getOrgsOneApiV1CoreOrgsItemIdGet', options);

/**
 * Get Orgs One
 * Get orgs by ID.
 */
export const getOrgsOneApiV1CoreOrgsItemIdGetOptions = (options: Options<GetOrgsOneApiV1CoreOrgsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgsOneApiV1CoreOrgsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgsOneApiV1CoreOrgsItemIdGetQueryKey(options)
    });
};

export const getOrgsAllApiV1CoreOrgsGetAllPostQueryKey = (options: Options<GetOrgsAllApiV1CoreOrgsGetAllPostData>) => createQueryKey('getOrgsAllApiV1CoreOrgsGetAllPost', options);

/**
 * Get Orgs All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getOrgsAllApiV1CoreOrgsGetAllPostOptions = (options: Options<GetOrgsAllApiV1CoreOrgsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgsAllApiV1CoreOrgsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgsAllApiV1CoreOrgsGetAllPostQueryKey(options)
    });
};

/**
 * Get Orgs All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getOrgsAllApiV1CoreOrgsGetAllPostMutation = (options?: Partial<Options<GetOrgsAllApiV1CoreOrgsGetAllPostData>>): UseMutationOptions<GetOrgsAllApiV1CoreOrgsGetAllPostResponse, GetOrgsAllApiV1CoreOrgsGetAllPostError, Options<GetOrgsAllApiV1CoreOrgsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetOrgsAllApiV1CoreOrgsGetAllPostResponse, GetOrgsAllApiV1CoreOrgsGetAllPostError, Options<GetOrgsAllApiV1CoreOrgsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getOrgsAllApiV1CoreOrgsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createEmailTypesApiV1CoreEmailTypesPostQueryKey = (options: Options<CreateEmailTypesApiV1CoreEmailTypesPostData>) => createQueryKey('createEmailTypesApiV1CoreEmailTypesPost', options);

/**
 * Create Email Types
 * Create new email_types
 */
export const createEmailTypesApiV1CoreEmailTypesPostOptions = (options: Options<CreateEmailTypesApiV1CoreEmailTypesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createEmailTypesApiV1CoreEmailTypesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createEmailTypesApiV1CoreEmailTypesPostQueryKey(options)
    });
};

/**
 * Create Email Types
 * Create new email_types
 */
export const createEmailTypesApiV1CoreEmailTypesPostMutation = (options?: Partial<Options<CreateEmailTypesApiV1CoreEmailTypesPostData>>): UseMutationOptions<CreateEmailTypesApiV1CoreEmailTypesPostResponse, CreateEmailTypesApiV1CoreEmailTypesPostError, Options<CreateEmailTypesApiV1CoreEmailTypesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateEmailTypesApiV1CoreEmailTypesPostResponse, CreateEmailTypesApiV1CoreEmailTypesPostError, Options<CreateEmailTypesApiV1CoreEmailTypesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createEmailTypesApiV1CoreEmailTypesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Email Types
 * Update email_types
 */
export const updateEmailTypesApiV1CoreEmailTypesPutMutation = (options?: Partial<Options<UpdateEmailTypesApiV1CoreEmailTypesPutData>>): UseMutationOptions<UpdateEmailTypesApiV1CoreEmailTypesPutResponse, UpdateEmailTypesApiV1CoreEmailTypesPutError, Options<UpdateEmailTypesApiV1CoreEmailTypesPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateEmailTypesApiV1CoreEmailTypesPutResponse, UpdateEmailTypesApiV1CoreEmailTypesPutError, Options<UpdateEmailTypesApiV1CoreEmailTypesPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateEmailTypesApiV1CoreEmailTypesPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Email Types
 * Delete email_types by ID.
 */
export const deleteEmailTypesApiV1CoreEmailTypesItemIdDeleteMutation = (options?: Partial<Options<DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData>>): UseMutationOptions<unknown, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteError, Options<DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteError, Options<DeleteEmailTypesApiV1CoreEmailTypesItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteEmailTypesApiV1CoreEmailTypesItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getEmailTypesOneApiV1CoreEmailTypesItemIdGetQueryKey = (options: Options<GetEmailTypesOneApiV1CoreEmailTypesItemIdGetData>) => createQueryKey('getEmailTypesOneApiV1CoreEmailTypesItemIdGet', options);

/**
 * Get Email Types One
 * Get email_types by ID.
 */
export const getEmailTypesOneApiV1CoreEmailTypesItemIdGetOptions = (options: Options<GetEmailTypesOneApiV1CoreEmailTypesItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEmailTypesOneApiV1CoreEmailTypesItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEmailTypesOneApiV1CoreEmailTypesItemIdGetQueryKey(options)
    });
};

export const getEmailTypesAllApiV1CoreEmailTypesGetAllPostQueryKey = (options: Options<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData>) => createQueryKey('getEmailTypesAllApiV1CoreEmailTypesGetAllPost', options);

/**
 * Get Email Types All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getEmailTypesAllApiV1CoreEmailTypesGetAllPostOptions = (options: Options<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEmailTypesAllApiV1CoreEmailTypesGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEmailTypesAllApiV1CoreEmailTypesGetAllPostQueryKey(options)
    });
};

/**
 * Get Email Types All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getEmailTypesAllApiV1CoreEmailTypesGetAllPostMutation = (options?: Partial<Options<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData>>): UseMutationOptions<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponse, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostError, Options<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostResponse, GetEmailTypesAllApiV1CoreEmailTypesGetAllPostError, Options<GetEmailTypesAllApiV1CoreEmailTypesGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getEmailTypesAllApiV1CoreEmailTypesGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createEmailsApiV1CoreEmailsPostQueryKey = (options: Options<CreateEmailsApiV1CoreEmailsPostData>) => createQueryKey('createEmailsApiV1CoreEmailsPost', options);

/**
 * Create Emails
 * Create new emails
 */
export const createEmailsApiV1CoreEmailsPostOptions = (options: Options<CreateEmailsApiV1CoreEmailsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createEmailsApiV1CoreEmailsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createEmailsApiV1CoreEmailsPostQueryKey(options)
    });
};

/**
 * Create Emails
 * Create new emails
 */
export const createEmailsApiV1CoreEmailsPostMutation = (options?: Partial<Options<CreateEmailsApiV1CoreEmailsPostData>>): UseMutationOptions<CreateEmailsApiV1CoreEmailsPostResponse, CreateEmailsApiV1CoreEmailsPostError, Options<CreateEmailsApiV1CoreEmailsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateEmailsApiV1CoreEmailsPostResponse, CreateEmailsApiV1CoreEmailsPostError, Options<CreateEmailsApiV1CoreEmailsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createEmailsApiV1CoreEmailsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Emails
 * Update emails
 */
export const updateEmailsApiV1CoreEmailsPutMutation = (options?: Partial<Options<UpdateEmailsApiV1CoreEmailsPutData>>): UseMutationOptions<UpdateEmailsApiV1CoreEmailsPutResponse, UpdateEmailsApiV1CoreEmailsPutError, Options<UpdateEmailsApiV1CoreEmailsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateEmailsApiV1CoreEmailsPutResponse, UpdateEmailsApiV1CoreEmailsPutError, Options<UpdateEmailsApiV1CoreEmailsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateEmailsApiV1CoreEmailsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Emails
 * Delete emails by ID.
 */
export const deleteEmailsApiV1CoreEmailsItemIdDeleteMutation = (options?: Partial<Options<DeleteEmailsApiV1CoreEmailsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteEmailsApiV1CoreEmailsItemIdDeleteError, Options<DeleteEmailsApiV1CoreEmailsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteEmailsApiV1CoreEmailsItemIdDeleteError, Options<DeleteEmailsApiV1CoreEmailsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteEmailsApiV1CoreEmailsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getEmailsOneApiV1CoreEmailsItemIdGetQueryKey = (options: Options<GetEmailsOneApiV1CoreEmailsItemIdGetData>) => createQueryKey('getEmailsOneApiV1CoreEmailsItemIdGet', options);

/**
 * Get Emails One
 * Get emails by ID.
 */
export const getEmailsOneApiV1CoreEmailsItemIdGetOptions = (options: Options<GetEmailsOneApiV1CoreEmailsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEmailsOneApiV1CoreEmailsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEmailsOneApiV1CoreEmailsItemIdGetQueryKey(options)
    });
};

export const getEmailsAllApiV1CoreEmailsGetAllPostQueryKey = (options: Options<GetEmailsAllApiV1CoreEmailsGetAllPostData>) => createQueryKey('getEmailsAllApiV1CoreEmailsGetAllPost', options);

/**
 * Get Emails All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getEmailsAllApiV1CoreEmailsGetAllPostOptions = (options: Options<GetEmailsAllApiV1CoreEmailsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getEmailsAllApiV1CoreEmailsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getEmailsAllApiV1CoreEmailsGetAllPostQueryKey(options)
    });
};

/**
 * Get Emails All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getEmailsAllApiV1CoreEmailsGetAllPostMutation = (options?: Partial<Options<GetEmailsAllApiV1CoreEmailsGetAllPostData>>): UseMutationOptions<GetEmailsAllApiV1CoreEmailsGetAllPostResponse, GetEmailsAllApiV1CoreEmailsGetAllPostError, Options<GetEmailsAllApiV1CoreEmailsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetEmailsAllApiV1CoreEmailsGetAllPostResponse, GetEmailsAllApiV1CoreEmailsGetAllPostError, Options<GetEmailsAllApiV1CoreEmailsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getEmailsAllApiV1CoreEmailsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createCommentsApiV1CoreCommentsPostQueryKey = (options: Options<CreateCommentsApiV1CoreCommentsPostData>) => createQueryKey('createCommentsApiV1CoreCommentsPost', options);

/**
 * Create Comments
 * Create new comments
 */
export const createCommentsApiV1CoreCommentsPostOptions = (options: Options<CreateCommentsApiV1CoreCommentsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createCommentsApiV1CoreCommentsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createCommentsApiV1CoreCommentsPostQueryKey(options)
    });
};

/**
 * Create Comments
 * Create new comments
 */
export const createCommentsApiV1CoreCommentsPostMutation = (options?: Partial<Options<CreateCommentsApiV1CoreCommentsPostData>>): UseMutationOptions<CreateCommentsApiV1CoreCommentsPostResponse, CreateCommentsApiV1CoreCommentsPostError, Options<CreateCommentsApiV1CoreCommentsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateCommentsApiV1CoreCommentsPostResponse, CreateCommentsApiV1CoreCommentsPostError, Options<CreateCommentsApiV1CoreCommentsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createCommentsApiV1CoreCommentsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Comments
 * Update comments
 */
export const updateCommentsApiV1CoreCommentsPutMutation = (options?: Partial<Options<UpdateCommentsApiV1CoreCommentsPutData>>): UseMutationOptions<UpdateCommentsApiV1CoreCommentsPutResponse, UpdateCommentsApiV1CoreCommentsPutError, Options<UpdateCommentsApiV1CoreCommentsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateCommentsApiV1CoreCommentsPutResponse, UpdateCommentsApiV1CoreCommentsPutError, Options<UpdateCommentsApiV1CoreCommentsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateCommentsApiV1CoreCommentsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Comments
 * Delete comments by ID.
 */
export const deleteCommentsApiV1CoreCommentsItemIdDeleteMutation = (options?: Partial<Options<DeleteCommentsApiV1CoreCommentsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteCommentsApiV1CoreCommentsItemIdDeleteError, Options<DeleteCommentsApiV1CoreCommentsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteCommentsApiV1CoreCommentsItemIdDeleteError, Options<DeleteCommentsApiV1CoreCommentsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteCommentsApiV1CoreCommentsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getCommentsOneApiV1CoreCommentsItemIdGetQueryKey = (options: Options<GetCommentsOneApiV1CoreCommentsItemIdGetData>) => createQueryKey('getCommentsOneApiV1CoreCommentsItemIdGet', options);

/**
 * Get Comments One
 * Get comments by ID.
 */
export const getCommentsOneApiV1CoreCommentsItemIdGetOptions = (options: Options<GetCommentsOneApiV1CoreCommentsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCommentsOneApiV1CoreCommentsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCommentsOneApiV1CoreCommentsItemIdGetQueryKey(options)
    });
};

export const getCommentsAllApiV1CoreCommentsGetAllPostQueryKey = (options: Options<GetCommentsAllApiV1CoreCommentsGetAllPostData>) => createQueryKey('getCommentsAllApiV1CoreCommentsGetAllPost', options);

/**
 * Get Comments All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getCommentsAllApiV1CoreCommentsGetAllPostOptions = (options: Options<GetCommentsAllApiV1CoreCommentsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCommentsAllApiV1CoreCommentsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCommentsAllApiV1CoreCommentsGetAllPostQueryKey(options)
    });
};

/**
 * Get Comments All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getCommentsAllApiV1CoreCommentsGetAllPostMutation = (options?: Partial<Options<GetCommentsAllApiV1CoreCommentsGetAllPostData>>): UseMutationOptions<GetCommentsAllApiV1CoreCommentsGetAllPostResponse, GetCommentsAllApiV1CoreCommentsGetAllPostError, Options<GetCommentsAllApiV1CoreCommentsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetCommentsAllApiV1CoreCommentsGetAllPostResponse, GetCommentsAllApiV1CoreCommentsGetAllPostError, Options<GetCommentsAllApiV1CoreCommentsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getCommentsAllApiV1CoreCommentsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createDocsApiV1CoreDocsPostQueryKey = (options: Options<CreateDocsApiV1CoreDocsPostData>) => createQueryKey('createDocsApiV1CoreDocsPost', options);

/**
 * Create Docs
 * Create new docs
 */
export const createDocsApiV1CoreDocsPostOptions = (options: Options<CreateDocsApiV1CoreDocsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createDocsApiV1CoreDocsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createDocsApiV1CoreDocsPostQueryKey(options)
    });
};

/**
 * Create Docs
 * Create new docs
 */
export const createDocsApiV1CoreDocsPostMutation = (options?: Partial<Options<CreateDocsApiV1CoreDocsPostData>>): UseMutationOptions<CreateDocsApiV1CoreDocsPostResponse, CreateDocsApiV1CoreDocsPostError, Options<CreateDocsApiV1CoreDocsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateDocsApiV1CoreDocsPostResponse, CreateDocsApiV1CoreDocsPostError, Options<CreateDocsApiV1CoreDocsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createDocsApiV1CoreDocsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Docs
 * Update docs
 */
export const updateDocsApiV1CoreDocsPutMutation = (options?: Partial<Options<UpdateDocsApiV1CoreDocsPutData>>): UseMutationOptions<UpdateDocsApiV1CoreDocsPutResponse, UpdateDocsApiV1CoreDocsPutError, Options<UpdateDocsApiV1CoreDocsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateDocsApiV1CoreDocsPutResponse, UpdateDocsApiV1CoreDocsPutError, Options<UpdateDocsApiV1CoreDocsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateDocsApiV1CoreDocsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDocTemplatesApiV1CoreDocsTemplatesPostQueryKey = (options: Options<GetDocTemplatesApiV1CoreDocsTemplatesPostData>) => createQueryKey('getDocTemplatesApiV1CoreDocsTemplatesPost', options);

/**
 * Get Doc Templates
 * Get document templates and part templates for a specific organization.
 *
 * Args:
 * org_id: Required - The organization ID
 * doc_type: Optional - Filter by document type
 *
 * Returns:
 * DocTemplatesTypes - Object containing doc templates and part templates
 */
export const getDocTemplatesApiV1CoreDocsTemplatesPostOptions = (options: Options<GetDocTemplatesApiV1CoreDocsTemplatesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocTemplatesApiV1CoreDocsTemplatesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocTemplatesApiV1CoreDocsTemplatesPostQueryKey(options)
    });
};

/**
 * Get Doc Templates
 * Get document templates and part templates for a specific organization.
 *
 * Args:
 * org_id: Required - The organization ID
 * doc_type: Optional - Filter by document type
 *
 * Returns:
 * DocTemplatesTypes - Object containing doc templates and part templates
 */
export const getDocTemplatesApiV1CoreDocsTemplatesPostMutation = (options?: Partial<Options<GetDocTemplatesApiV1CoreDocsTemplatesPostData>>): UseMutationOptions<GetDocTemplatesApiV1CoreDocsTemplatesPostResponse, GetDocTemplatesApiV1CoreDocsTemplatesPostError, Options<GetDocTemplatesApiV1CoreDocsTemplatesPostData>> => {
    const mutationOptions: UseMutationOptions<GetDocTemplatesApiV1CoreDocsTemplatesPostResponse, GetDocTemplatesApiV1CoreDocsTemplatesPostError, Options<GetDocTemplatesApiV1CoreDocsTemplatesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getDocTemplatesApiV1CoreDocsTemplatesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getTemplatesTableApiV1CoreDocsTemplatesTablePostQueryKey = (options: Options<GetTemplatesTableApiV1CoreDocsTemplatesTablePostData>) => createQueryKey('getTemplatesTableApiV1CoreDocsTemplatesTablePost', options);

/**
 * Get Templates Table
 * Get all templates (docs and doc_parts) for a specific organization in a combined
 * table format.
 *
 * Args:
 * org_id: Required - The organization ID
 * doc_type: Optional - Filter by document type
 *
 * Returns:
 * List[Union[DocTableTypes, DocPartDisplayTypes]] - Combined list of document and
 * part templates
 */
export const getTemplatesTableApiV1CoreDocsTemplatesTablePostOptions = (options: Options<GetTemplatesTableApiV1CoreDocsTemplatesTablePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTemplatesTableApiV1CoreDocsTemplatesTablePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTemplatesTableApiV1CoreDocsTemplatesTablePostQueryKey(options)
    });
};

/**
 * Get Templates Table
 * Get all templates (docs and doc_parts) for a specific organization in a combined
 * table format.
 *
 * Args:
 * org_id: Required - The organization ID
 * doc_type: Optional - Filter by document type
 *
 * Returns:
 * List[Union[DocTableTypes, DocPartDisplayTypes]] - Combined list of document and
 * part templates
 */
export const getTemplatesTableApiV1CoreDocsTemplatesTablePostMutation = (options?: Partial<Options<GetTemplatesTableApiV1CoreDocsTemplatesTablePostData>>): UseMutationOptions<GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponse, GetTemplatesTableApiV1CoreDocsTemplatesTablePostError, Options<GetTemplatesTableApiV1CoreDocsTemplatesTablePostData>> => {
    const mutationOptions: UseMutationOptions<GetTemplatesTableApiV1CoreDocsTemplatesTablePostResponse, GetTemplatesTableApiV1CoreDocsTemplatesTablePostError, Options<GetTemplatesTableApiV1CoreDocsTemplatesTablePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getTemplatesTableApiV1CoreDocsTemplatesTablePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Docs
 * Delete docs by ID.
 */
export const deleteDocsApiV1CoreDocsItemIdDeleteMutation = (options?: Partial<Options<DeleteDocsApiV1CoreDocsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteDocsApiV1CoreDocsItemIdDeleteError, Options<DeleteDocsApiV1CoreDocsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteDocsApiV1CoreDocsItemIdDeleteError, Options<DeleteDocsApiV1CoreDocsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteDocsApiV1CoreDocsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDocsOneApiV1CoreDocsItemIdGetQueryKey = (options: Options<GetDocsOneApiV1CoreDocsItemIdGetData>) => createQueryKey('getDocsOneApiV1CoreDocsItemIdGet', options);

/**
 * Get Docs One
 * Get docs by ID.
 */
export const getDocsOneApiV1CoreDocsItemIdGetOptions = (options: Options<GetDocsOneApiV1CoreDocsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocsOneApiV1CoreDocsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocsOneApiV1CoreDocsItemIdGetQueryKey(options)
    });
};

export const getDocsAllApiV1CoreDocsGetAllPostQueryKey = (options: Options<GetDocsAllApiV1CoreDocsGetAllPostData>) => createQueryKey('getDocsAllApiV1CoreDocsGetAllPost', options);

/**
 * Get Docs All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocsAllApiV1CoreDocsGetAllPostOptions = (options: Options<GetDocsAllApiV1CoreDocsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocsAllApiV1CoreDocsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocsAllApiV1CoreDocsGetAllPostQueryKey(options)
    });
};

/**
 * Get Docs All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocsAllApiV1CoreDocsGetAllPostMutation = (options?: Partial<Options<GetDocsAllApiV1CoreDocsGetAllPostData>>): UseMutationOptions<GetDocsAllApiV1CoreDocsGetAllPostResponse, GetDocsAllApiV1CoreDocsGetAllPostError, Options<GetDocsAllApiV1CoreDocsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetDocsAllApiV1CoreDocsGetAllPostResponse, GetDocsAllApiV1CoreDocsGetAllPostError, Options<GetDocsAllApiV1CoreDocsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getDocsAllApiV1CoreDocsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDocsAllWithContentApiV1CoreDocsGetAllWithContentPostQueryKey = (options: Options<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData>) => createQueryKey('getDocsAllWithContentApiV1CoreDocsGetAllWithContentPost', options);

/**
 * Get Docs All With Content
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocsAllWithContentApiV1CoreDocsGetAllWithContentPostOptions = (options: Options<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocsAllWithContentApiV1CoreDocsGetAllWithContentPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocsAllWithContentApiV1CoreDocsGetAllWithContentPostQueryKey(options)
    });
};

/**
 * Get Docs All With Content
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocsAllWithContentApiV1CoreDocsGetAllWithContentPostMutation = (options?: Partial<Options<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData>>): UseMutationOptions<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponse, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostError, Options<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData>> => {
    const mutationOptions: UseMutationOptions<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostResponse, GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostError, Options<GetDocsAllWithContentApiV1CoreDocsGetAllWithContentPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getDocsAllWithContentApiV1CoreDocsGetAllWithContentPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Docs With Content
 * Update docs
 */
export const updateDocsWithContentApiV1CoreDocsWithContentPutMutation = (options?: Partial<Options<UpdateDocsWithContentApiV1CoreDocsWithContentPutData>>): UseMutationOptions<UpdateDocsWithContentApiV1CoreDocsWithContentPutResponse, UpdateDocsWithContentApiV1CoreDocsWithContentPutError, Options<UpdateDocsWithContentApiV1CoreDocsWithContentPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateDocsWithContentApiV1CoreDocsWithContentPutResponse, UpdateDocsWithContentApiV1CoreDocsWithContentPutError, Options<UpdateDocsWithContentApiV1CoreDocsWithContentPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateDocsWithContentApiV1CoreDocsWithContentPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createDocPagesApiV1CoreDocPagesPostQueryKey = (options: Options<CreateDocPagesApiV1CoreDocPagesPostData>) => createQueryKey('createDocPagesApiV1CoreDocPagesPost', options);

/**
 * Create Doc Pages
 * Create new doc_pages
 */
export const createDocPagesApiV1CoreDocPagesPostOptions = (options: Options<CreateDocPagesApiV1CoreDocPagesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createDocPagesApiV1CoreDocPagesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createDocPagesApiV1CoreDocPagesPostQueryKey(options)
    });
};

/**
 * Create Doc Pages
 * Create new doc_pages
 */
export const createDocPagesApiV1CoreDocPagesPostMutation = (options?: Partial<Options<CreateDocPagesApiV1CoreDocPagesPostData>>): UseMutationOptions<CreateDocPagesApiV1CoreDocPagesPostResponse, CreateDocPagesApiV1CoreDocPagesPostError, Options<CreateDocPagesApiV1CoreDocPagesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateDocPagesApiV1CoreDocPagesPostResponse, CreateDocPagesApiV1CoreDocPagesPostError, Options<CreateDocPagesApiV1CoreDocPagesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createDocPagesApiV1CoreDocPagesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Doc Pages With Parts
 * Update doc_pages
 */
export const updateDocPagesWithPartsApiV1CoreDocPagesPutMutation = (options?: Partial<Options<UpdateDocPagesWithPartsApiV1CoreDocPagesPutData>>): UseMutationOptions<UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponse, UpdateDocPagesWithPartsApiV1CoreDocPagesPutError, Options<UpdateDocPagesWithPartsApiV1CoreDocPagesPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateDocPagesWithPartsApiV1CoreDocPagesPutResponse, UpdateDocPagesWithPartsApiV1CoreDocPagesPutError, Options<UpdateDocPagesWithPartsApiV1CoreDocPagesPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateDocPagesWithPartsApiV1CoreDocPagesPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Doc Pages With Links
 * Delete doc_pages by ID.
 */
export const deleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteMutation = (options?: Partial<Options<DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData>>): UseMutationOptions<unknown, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteError, Options<DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteError, Options<DeleteDocPagesWithLinksApiV1CoreDocPagesItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteDocPagesWithLinksApiV1CoreDocPagesItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDocPagesOneApiV1CoreDocPagesItemIdGetQueryKey = (options: Options<GetDocPagesOneApiV1CoreDocPagesItemIdGetData>) => createQueryKey('getDocPagesOneApiV1CoreDocPagesItemIdGet', options);

/**
 * Get Doc Pages One
 * Get doc_pages by ID.
 */
export const getDocPagesOneApiV1CoreDocPagesItemIdGetOptions = (options: Options<GetDocPagesOneApiV1CoreDocPagesItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocPagesOneApiV1CoreDocPagesItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocPagesOneApiV1CoreDocPagesItemIdGetQueryKey(options)
    });
};

export const getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostQueryKey = (options: Options<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData>) => createQueryKey('getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPost', options);

/**
 * Get Doc Pages All With Parts
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostOptions = (options: Options<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostQueryKey(options)
    });
};

/**
 * Get Doc Pages All With Parts
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostMutation = (options?: Partial<Options<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData>>): UseMutationOptions<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponse, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostError, Options<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData>> => {
    const mutationOptions: UseMutationOptions<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostResponse, GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostError, Options<GetDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getDocPagesAllWithPartsApiV1CoreDocPagesGetAllWithPartsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDocPagesAllApiV1CoreDocPagesGetAllPostQueryKey = (options: Options<GetDocPagesAllApiV1CoreDocPagesGetAllPostData>) => createQueryKey('getDocPagesAllApiV1CoreDocPagesGetAllPost', options);

/**
 * Get Doc Pages All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPagesAllApiV1CoreDocPagesGetAllPostOptions = (options: Options<GetDocPagesAllApiV1CoreDocPagesGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocPagesAllApiV1CoreDocPagesGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocPagesAllApiV1CoreDocPagesGetAllPostQueryKey(options)
    });
};

/**
 * Get Doc Pages All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPagesAllApiV1CoreDocPagesGetAllPostMutation = (options?: Partial<Options<GetDocPagesAllApiV1CoreDocPagesGetAllPostData>>): UseMutationOptions<GetDocPagesAllApiV1CoreDocPagesGetAllPostResponse, GetDocPagesAllApiV1CoreDocPagesGetAllPostError, Options<GetDocPagesAllApiV1CoreDocPagesGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetDocPagesAllApiV1CoreDocPagesGetAllPostResponse, GetDocPagesAllApiV1CoreDocPagesGetAllPostError, Options<GetDocPagesAllApiV1CoreDocPagesGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getDocPagesAllApiV1CoreDocPagesGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createDocPartsApiV1CoreDocPartsPostQueryKey = (options: Options<CreateDocPartsApiV1CoreDocPartsPostData>) => createQueryKey('createDocPartsApiV1CoreDocPartsPost', options);

/**
 * Create Doc Parts
 * Create new doc_parts
 */
export const createDocPartsApiV1CoreDocPartsPostOptions = (options: Options<CreateDocPartsApiV1CoreDocPartsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createDocPartsApiV1CoreDocPartsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createDocPartsApiV1CoreDocPartsPostQueryKey(options)
    });
};

/**
 * Create Doc Parts
 * Create new doc_parts
 */
export const createDocPartsApiV1CoreDocPartsPostMutation = (options?: Partial<Options<CreateDocPartsApiV1CoreDocPartsPostData>>): UseMutationOptions<CreateDocPartsApiV1CoreDocPartsPostResponse, CreateDocPartsApiV1CoreDocPartsPostError, Options<CreateDocPartsApiV1CoreDocPartsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateDocPartsApiV1CoreDocPartsPostResponse, CreateDocPartsApiV1CoreDocPartsPostError, Options<CreateDocPartsApiV1CoreDocPartsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createDocPartsApiV1CoreDocPartsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Doc Parts
 * Update doc_parts
 */
export const updateDocPartsApiV1CoreDocPartsPutMutation = (options?: Partial<Options<UpdateDocPartsApiV1CoreDocPartsPutData>>): UseMutationOptions<UpdateDocPartsApiV1CoreDocPartsPutResponse, UpdateDocPartsApiV1CoreDocPartsPutError, Options<UpdateDocPartsApiV1CoreDocPartsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateDocPartsApiV1CoreDocPartsPutResponse, UpdateDocPartsApiV1CoreDocPartsPutError, Options<UpdateDocPartsApiV1CoreDocPartsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateDocPartsApiV1CoreDocPartsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Doc Parts
 * Delete doc_parts by ID.
 */
export const deleteDocPartsApiV1CoreDocPartsItemIdDeleteMutation = (options?: Partial<Options<DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteError, Options<DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteDocPartsApiV1CoreDocPartsItemIdDeleteError, Options<DeleteDocPartsApiV1CoreDocPartsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteDocPartsApiV1CoreDocPartsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getDocPartsOneApiV1CoreDocPartsItemIdGetQueryKey = (options: Options<GetDocPartsOneApiV1CoreDocPartsItemIdGetData>) => createQueryKey('getDocPartsOneApiV1CoreDocPartsItemIdGet', options);

/**
 * Get Doc Parts One
 * Get doc_parts by ID.
 */
export const getDocPartsOneApiV1CoreDocPartsItemIdGetOptions = (options: Options<GetDocPartsOneApiV1CoreDocPartsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocPartsOneApiV1CoreDocPartsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocPartsOneApiV1CoreDocPartsItemIdGetQueryKey(options)
    });
};

export const getDocPartsAllApiV1CoreDocPartsGetAllPostQueryKey = (options: Options<GetDocPartsAllApiV1CoreDocPartsGetAllPostData>) => createQueryKey('getDocPartsAllApiV1CoreDocPartsGetAllPost', options);

/**
 * Get Doc Parts All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPartsAllApiV1CoreDocPartsGetAllPostOptions = (options: Options<GetDocPartsAllApiV1CoreDocPartsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getDocPartsAllApiV1CoreDocPartsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getDocPartsAllApiV1CoreDocPartsGetAllPostQueryKey(options)
    });
};

/**
 * Get Doc Parts All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getDocPartsAllApiV1CoreDocPartsGetAllPostMutation = (options?: Partial<Options<GetDocPartsAllApiV1CoreDocPartsGetAllPostData>>): UseMutationOptions<GetDocPartsAllApiV1CoreDocPartsGetAllPostResponse, GetDocPartsAllApiV1CoreDocPartsGetAllPostError, Options<GetDocPartsAllApiV1CoreDocPartsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetDocPartsAllApiV1CoreDocPartsGetAllPostResponse, GetDocPartsAllApiV1CoreDocPartsGetAllPostError, Options<GetDocPartsAllApiV1CoreDocPartsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getDocPartsAllApiV1CoreDocPartsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createVariantsApiV1CoreVariantsPostQueryKey = (options: Options<CreateVariantsApiV1CoreVariantsPostData>) => createQueryKey('createVariantsApiV1CoreVariantsPost', options);

/**
 * Create Variants
 * Create new variants
 */
export const createVariantsApiV1CoreVariantsPostOptions = (options: Options<CreateVariantsApiV1CoreVariantsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createVariantsApiV1CoreVariantsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createVariantsApiV1CoreVariantsPostQueryKey(options)
    });
};

/**
 * Create Variants
 * Create new variants
 */
export const createVariantsApiV1CoreVariantsPostMutation = (options?: Partial<Options<CreateVariantsApiV1CoreVariantsPostData>>): UseMutationOptions<CreateVariantsApiV1CoreVariantsPostResponse, CreateVariantsApiV1CoreVariantsPostError, Options<CreateVariantsApiV1CoreVariantsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateVariantsApiV1CoreVariantsPostResponse, CreateVariantsApiV1CoreVariantsPostError, Options<CreateVariantsApiV1CoreVariantsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createVariantsApiV1CoreVariantsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Variants
 * Update variants
 */
export const updateVariantsApiV1CoreVariantsPutMutation = (options?: Partial<Options<UpdateVariantsApiV1CoreVariantsPutData>>): UseMutationOptions<UpdateVariantsApiV1CoreVariantsPutResponse, UpdateVariantsApiV1CoreVariantsPutError, Options<UpdateVariantsApiV1CoreVariantsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateVariantsApiV1CoreVariantsPutResponse, UpdateVariantsApiV1CoreVariantsPutError, Options<UpdateVariantsApiV1CoreVariantsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateVariantsApiV1CoreVariantsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Variants
 * Delete variants by ID.
 */
export const deleteVariantsApiV1CoreVariantsItemIdDeleteMutation = (options?: Partial<Options<DeleteVariantsApiV1CoreVariantsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteVariantsApiV1CoreVariantsItemIdDeleteError, Options<DeleteVariantsApiV1CoreVariantsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteVariantsApiV1CoreVariantsItemIdDeleteError, Options<DeleteVariantsApiV1CoreVariantsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteVariantsApiV1CoreVariantsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getVariantsOneApiV1CoreVariantsItemIdGetQueryKey = (options: Options<GetVariantsOneApiV1CoreVariantsItemIdGetData>) => createQueryKey('getVariantsOneApiV1CoreVariantsItemIdGet', options);

/**
 * Get Variants One
 * Get variants by ID.
 */
export const getVariantsOneApiV1CoreVariantsItemIdGetOptions = (options: Options<GetVariantsOneApiV1CoreVariantsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getVariantsOneApiV1CoreVariantsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getVariantsOneApiV1CoreVariantsItemIdGetQueryKey(options)
    });
};

export const getVariantsAllApiV1CoreVariantsGetAllPostQueryKey = (options: Options<GetVariantsAllApiV1CoreVariantsGetAllPostData>) => createQueryKey('getVariantsAllApiV1CoreVariantsGetAllPost', options);

/**
 * Get Variants All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getVariantsAllApiV1CoreVariantsGetAllPostOptions = (options: Options<GetVariantsAllApiV1CoreVariantsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getVariantsAllApiV1CoreVariantsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getVariantsAllApiV1CoreVariantsGetAllPostQueryKey(options)
    });
};

/**
 * Get Variants All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getVariantsAllApiV1CoreVariantsGetAllPostMutation = (options?: Partial<Options<GetVariantsAllApiV1CoreVariantsGetAllPostData>>): UseMutationOptions<GetVariantsAllApiV1CoreVariantsGetAllPostResponse, GetVariantsAllApiV1CoreVariantsGetAllPostError, Options<GetVariantsAllApiV1CoreVariantsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetVariantsAllApiV1CoreVariantsGetAllPostResponse, GetVariantsAllApiV1CoreVariantsGetAllPostError, Options<GetVariantsAllApiV1CoreVariantsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getVariantsAllApiV1CoreVariantsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createAddressesApiV1CrmAddressesPostQueryKey = (options: Options<CreateAddressesApiV1CrmAddressesPostData>) => createQueryKey('createAddressesApiV1CrmAddressesPost', options);

/**
 * Create Addresses
 * Create new addresses
 */
export const createAddressesApiV1CrmAddressesPostOptions = (options: Options<CreateAddressesApiV1CrmAddressesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createAddressesApiV1CrmAddressesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createAddressesApiV1CrmAddressesPostQueryKey(options)
    });
};

/**
 * Create Addresses
 * Create new addresses
 */
export const createAddressesApiV1CrmAddressesPostMutation = (options?: Partial<Options<CreateAddressesApiV1CrmAddressesPostData>>): UseMutationOptions<CreateAddressesApiV1CrmAddressesPostResponse, CreateAddressesApiV1CrmAddressesPostError, Options<CreateAddressesApiV1CrmAddressesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateAddressesApiV1CrmAddressesPostResponse, CreateAddressesApiV1CrmAddressesPostError, Options<CreateAddressesApiV1CrmAddressesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createAddressesApiV1CrmAddressesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Addresses
 * Update addresses
 */
export const updateAddressesApiV1CrmAddressesPutMutation = (options?: Partial<Options<UpdateAddressesApiV1CrmAddressesPutData>>): UseMutationOptions<UpdateAddressesApiV1CrmAddressesPutResponse, UpdateAddressesApiV1CrmAddressesPutError, Options<UpdateAddressesApiV1CrmAddressesPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateAddressesApiV1CrmAddressesPutResponse, UpdateAddressesApiV1CrmAddressesPutError, Options<UpdateAddressesApiV1CrmAddressesPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateAddressesApiV1CrmAddressesPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Addresses
 * Delete addresses by ID.
 */
export const deleteAddressesApiV1CrmAddressesItemIdDeleteMutation = (options?: Partial<Options<DeleteAddressesApiV1CrmAddressesItemIdDeleteData>>): UseMutationOptions<unknown, DeleteAddressesApiV1CrmAddressesItemIdDeleteError, Options<DeleteAddressesApiV1CrmAddressesItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteAddressesApiV1CrmAddressesItemIdDeleteError, Options<DeleteAddressesApiV1CrmAddressesItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteAddressesApiV1CrmAddressesItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getAddressesOneApiV1CrmAddressesItemIdGetQueryKey = (options: Options<GetAddressesOneApiV1CrmAddressesItemIdGetData>) => createQueryKey('getAddressesOneApiV1CrmAddressesItemIdGet', options);

/**
 * Get Addresses One
 * Get addresses by ID.
 */
export const getAddressesOneApiV1CrmAddressesItemIdGetOptions = (options: Options<GetAddressesOneApiV1CrmAddressesItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAddressesOneApiV1CrmAddressesItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAddressesOneApiV1CrmAddressesItemIdGetQueryKey(options)
    });
};

export const getAddressesAllApiV1CrmAddressesGetAllPostQueryKey = (options: Options<GetAddressesAllApiV1CrmAddressesGetAllPostData>) => createQueryKey('getAddressesAllApiV1CrmAddressesGetAllPost', options);

/**
 * Get Addresses All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getAddressesAllApiV1CrmAddressesGetAllPostOptions = (options: Options<GetAddressesAllApiV1CrmAddressesGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getAddressesAllApiV1CrmAddressesGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getAddressesAllApiV1CrmAddressesGetAllPostQueryKey(options)
    });
};

/**
 * Get Addresses All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getAddressesAllApiV1CrmAddressesGetAllPostMutation = (options?: Partial<Options<GetAddressesAllApiV1CrmAddressesGetAllPostData>>): UseMutationOptions<GetAddressesAllApiV1CrmAddressesGetAllPostResponse, GetAddressesAllApiV1CrmAddressesGetAllPostError, Options<GetAddressesAllApiV1CrmAddressesGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetAddressesAllApiV1CrmAddressesGetAllPostResponse, GetAddressesAllApiV1CrmAddressesGetAllPostError, Options<GetAddressesAllApiV1CrmAddressesGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getAddressesAllApiV1CrmAddressesGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createContactsApiV1CrmContactsPostQueryKey = (options: Options<CreateContactsApiV1CrmContactsPostData>) => createQueryKey('createContactsApiV1CrmContactsPost', options);

/**
 * Create Contacts
 * Create new contacts
 */
export const createContactsApiV1CrmContactsPostOptions = (options: Options<CreateContactsApiV1CrmContactsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createContactsApiV1CrmContactsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createContactsApiV1CrmContactsPostQueryKey(options)
    });
};

/**
 * Create Contacts
 * Create new contacts
 */
export const createContactsApiV1CrmContactsPostMutation = (options?: Partial<Options<CreateContactsApiV1CrmContactsPostData>>): UseMutationOptions<CreateContactsApiV1CrmContactsPostResponse, CreateContactsApiV1CrmContactsPostError, Options<CreateContactsApiV1CrmContactsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateContactsApiV1CrmContactsPostResponse, CreateContactsApiV1CrmContactsPostError, Options<CreateContactsApiV1CrmContactsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createContactsApiV1CrmContactsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Contacts
 * Update contacts
 */
export const updateContactsApiV1CrmContactsPutMutation = (options?: Partial<Options<UpdateContactsApiV1CrmContactsPutData>>): UseMutationOptions<UpdateContactsApiV1CrmContactsPutResponse, UpdateContactsApiV1CrmContactsPutError, Options<UpdateContactsApiV1CrmContactsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateContactsApiV1CrmContactsPutResponse, UpdateContactsApiV1CrmContactsPutError, Options<UpdateContactsApiV1CrmContactsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateContactsApiV1CrmContactsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Contacts
 * Delete contacts by ID.
 */
export const deleteContactsApiV1CrmContactsItemIdDeleteMutation = (options?: Partial<Options<DeleteContactsApiV1CrmContactsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteContactsApiV1CrmContactsItemIdDeleteError, Options<DeleteContactsApiV1CrmContactsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteContactsApiV1CrmContactsItemIdDeleteError, Options<DeleteContactsApiV1CrmContactsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteContactsApiV1CrmContactsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getContactsOneApiV1CrmContactsItemIdGetQueryKey = (options: Options<GetContactsOneApiV1CrmContactsItemIdGetData>) => createQueryKey('getContactsOneApiV1CrmContactsItemIdGet', options);

/**
 * Get Contacts One
 * Get contacts by ID.
 */
export const getContactsOneApiV1CrmContactsItemIdGetOptions = (options: Options<GetContactsOneApiV1CrmContactsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getContactsOneApiV1CrmContactsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getContactsOneApiV1CrmContactsItemIdGetQueryKey(options)
    });
};

export const getContactsAllApiV1CrmContactsGetAllPostQueryKey = (options: Options<GetContactsAllApiV1CrmContactsGetAllPostData>) => createQueryKey('getContactsAllApiV1CrmContactsGetAllPost', options);

/**
 * Get Contacts All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getContactsAllApiV1CrmContactsGetAllPostOptions = (options: Options<GetContactsAllApiV1CrmContactsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getContactsAllApiV1CrmContactsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getContactsAllApiV1CrmContactsGetAllPostQueryKey(options)
    });
};

/**
 * Get Contacts All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getContactsAllApiV1CrmContactsGetAllPostMutation = (options?: Partial<Options<GetContactsAllApiV1CrmContactsGetAllPostData>>): UseMutationOptions<GetContactsAllApiV1CrmContactsGetAllPostResponse, GetContactsAllApiV1CrmContactsGetAllPostError, Options<GetContactsAllApiV1CrmContactsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetContactsAllApiV1CrmContactsGetAllPostResponse, GetContactsAllApiV1CrmContactsGetAllPostError, Options<GetContactsAllApiV1CrmContactsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getContactsAllApiV1CrmContactsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createContrahentsApiV1CrmContrahentsPostQueryKey = (options: Options<CreateContrahentsApiV1CrmContrahentsPostData>) => createQueryKey('createContrahentsApiV1CrmContrahentsPost', options);

/**
 * Create Contrahents
 * Create new contrahents with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createContrahentsApiV1CrmContrahentsPostOptions = (options: Options<CreateContrahentsApiV1CrmContrahentsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createContrahentsApiV1CrmContrahentsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createContrahentsApiV1CrmContrahentsPostQueryKey(options)
    });
};

/**
 * Create Contrahents
 * Create new contrahents with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createContrahentsApiV1CrmContrahentsPostMutation = (options?: Partial<Options<CreateContrahentsApiV1CrmContrahentsPostData>>): UseMutationOptions<CreateContrahentsApiV1CrmContrahentsPostResponse, CreateContrahentsApiV1CrmContrahentsPostError, Options<CreateContrahentsApiV1CrmContrahentsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateContrahentsApiV1CrmContrahentsPostResponse, CreateContrahentsApiV1CrmContrahentsPostError, Options<CreateContrahentsApiV1CrmContrahentsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createContrahentsApiV1CrmContrahentsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Contrahents
 * Contrahent update
 */
export const updateContrahentsApiV1CrmContrahentsPutMutation = (options?: Partial<Options<UpdateContrahentsApiV1CrmContrahentsPutData>>): UseMutationOptions<UpdateContrahentsApiV1CrmContrahentsPutResponse, UpdateContrahentsApiV1CrmContrahentsPutError, Options<UpdateContrahentsApiV1CrmContrahentsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateContrahentsApiV1CrmContrahentsPutResponse, UpdateContrahentsApiV1CrmContrahentsPutError, Options<UpdateContrahentsApiV1CrmContrahentsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateContrahentsApiV1CrmContrahentsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Contrahents
 * Delete contrahents by ID.
 */
export const deleteContrahentsApiV1CrmContrahentsItemIdDeleteMutation = (options?: Partial<Options<DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteError, Options<DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteContrahentsApiV1CrmContrahentsItemIdDeleteError, Options<DeleteContrahentsApiV1CrmContrahentsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteContrahentsApiV1CrmContrahentsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getContrahentsOneApiV1CrmContrahentsItemIdGetQueryKey = (options: Options<GetContrahentsOneApiV1CrmContrahentsItemIdGetData>) => createQueryKey('getContrahentsOneApiV1CrmContrahentsItemIdGet', options);

/**
 * Get Contrahents One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getContrahentsOneApiV1CrmContrahentsItemIdGetOptions = (options: Options<GetContrahentsOneApiV1CrmContrahentsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getContrahentsOneApiV1CrmContrahentsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getContrahentsOneApiV1CrmContrahentsItemIdGetQueryKey(options)
    });
};

export const getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetQueryKey = (options: Options<GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData>) => createQueryKey('getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGet', options);

/**
 * Get Contrahents Relations One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetOptions = (options: Options<GetContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getContrahentsRelationsOneApiV1CrmContrahentsWithRelationsItemIdGetQueryKey(options)
    });
};

export const getContrahentsAllApiV1CrmContrahentsGetAllPostQueryKey = (options: Options<GetContrahentsAllApiV1CrmContrahentsGetAllPostData>) => createQueryKey('getContrahentsAllApiV1CrmContrahentsGetAllPost', options);

/**
 * Get Contrahents All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getContrahentsAllApiV1CrmContrahentsGetAllPostOptions = (options: Options<GetContrahentsAllApiV1CrmContrahentsGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getContrahentsAllApiV1CrmContrahentsGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getContrahentsAllApiV1CrmContrahentsGetAllPostQueryKey(options)
    });
};

/**
 * Get Contrahents All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getContrahentsAllApiV1CrmContrahentsGetAllPostMutation = (options?: Partial<Options<GetContrahentsAllApiV1CrmContrahentsGetAllPostData>>): UseMutationOptions<GetContrahentsAllApiV1CrmContrahentsGetAllPostResponse, GetContrahentsAllApiV1CrmContrahentsGetAllPostError, Options<GetContrahentsAllApiV1CrmContrahentsGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetContrahentsAllApiV1CrmContrahentsGetAllPostResponse, GetContrahentsAllApiV1CrmContrahentsGetAllPostError, Options<GetContrahentsAllApiV1CrmContrahentsGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getContrahentsAllApiV1CrmContrahentsGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetQueryKey = (options: Options<ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData>) => createQueryKey('readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGet', options);

/**
 * Get organization contrahents
 * Get all contrahents associated with a specific organization
 */
export const readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetOptions = (options: Options<ReadOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: readOrgContrahentsApiV1CrmContrahentsOrgOrgIdGetQueryKey(options)
    });
};

export const getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetQueryKey = (options: Options<GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData>) => createQueryKey('getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGet', options);

/**
 * Get Car Owners
 * Get all employees who have associated vehicles
 */
export const getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetOptions = (options: Options<GetCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getCarOwnersApiV1CrmContrahentsCarOwnersOrgIdGetQueryKey(options)
    });
};

export const createProfilesApiV1CrmProfilesPostQueryKey = (options: Options<CreateProfilesApiV1CrmProfilesPostData>) => createQueryKey('createProfilesApiV1CrmProfilesPost', options);

/**
 * Create Profiles
 * Create new profiles
 */
export const createProfilesApiV1CrmProfilesPostOptions = (options: Options<CreateProfilesApiV1CrmProfilesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createProfilesApiV1CrmProfilesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createProfilesApiV1CrmProfilesPostQueryKey(options)
    });
};

/**
 * Create Profiles
 * Create new profiles
 */
export const createProfilesApiV1CrmProfilesPostMutation = (options?: Partial<Options<CreateProfilesApiV1CrmProfilesPostData>>): UseMutationOptions<CreateProfilesApiV1CrmProfilesPostResponse, CreateProfilesApiV1CrmProfilesPostError, Options<CreateProfilesApiV1CrmProfilesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateProfilesApiV1CrmProfilesPostResponse, CreateProfilesApiV1CrmProfilesPostError, Options<CreateProfilesApiV1CrmProfilesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createProfilesApiV1CrmProfilesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Profiles
 * Update profiles
 */
export const updateProfilesApiV1CrmProfilesPutMutation = (options?: Partial<Options<UpdateProfilesApiV1CrmProfilesPutData>>): UseMutationOptions<UpdateProfilesApiV1CrmProfilesPutResponse, UpdateProfilesApiV1CrmProfilesPutError, Options<UpdateProfilesApiV1CrmProfilesPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateProfilesApiV1CrmProfilesPutResponse, UpdateProfilesApiV1CrmProfilesPutError, Options<UpdateProfilesApiV1CrmProfilesPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateProfilesApiV1CrmProfilesPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Profiles
 * Delete profiles by ID.
 */
export const deleteProfilesApiV1CrmProfilesItemIdDeleteMutation = (options?: Partial<Options<DeleteProfilesApiV1CrmProfilesItemIdDeleteData>>): UseMutationOptions<unknown, DeleteProfilesApiV1CrmProfilesItemIdDeleteError, Options<DeleteProfilesApiV1CrmProfilesItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteProfilesApiV1CrmProfilesItemIdDeleteError, Options<DeleteProfilesApiV1CrmProfilesItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteProfilesApiV1CrmProfilesItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getProfilesOneApiV1CrmProfilesItemIdGetQueryKey = (options: Options<GetProfilesOneApiV1CrmProfilesItemIdGetData>) => createQueryKey('getProfilesOneApiV1CrmProfilesItemIdGet', options);

/**
 * Get Profiles One
 * Get profiles by ID.
 */
export const getProfilesOneApiV1CrmProfilesItemIdGetOptions = (options: Options<GetProfilesOneApiV1CrmProfilesItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getProfilesOneApiV1CrmProfilesItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getProfilesOneApiV1CrmProfilesItemIdGetQueryKey(options)
    });
};

export const getProfilesAllApiV1CrmProfilesGetAllPostQueryKey = (options: Options<GetProfilesAllApiV1CrmProfilesGetAllPostData>) => createQueryKey('getProfilesAllApiV1CrmProfilesGetAllPost', options);

/**
 * Get Profiles All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getProfilesAllApiV1CrmProfilesGetAllPostOptions = (options: Options<GetProfilesAllApiV1CrmProfilesGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getProfilesAllApiV1CrmProfilesGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getProfilesAllApiV1CrmProfilesGetAllPostQueryKey(options)
    });
};

/**
 * Get Profiles All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getProfilesAllApiV1CrmProfilesGetAllPostMutation = (options?: Partial<Options<GetProfilesAllApiV1CrmProfilesGetAllPostData>>): UseMutationOptions<GetProfilesAllApiV1CrmProfilesGetAllPostResponse, GetProfilesAllApiV1CrmProfilesGetAllPostError, Options<GetProfilesAllApiV1CrmProfilesGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetProfilesAllApiV1CrmProfilesGetAllPostResponse, GetProfilesAllApiV1CrmProfilesGetAllPostError, Options<GetProfilesAllApiV1CrmProfilesGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getProfilesAllApiV1CrmProfilesGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createTypesApiV1CrmTypesPostQueryKey = (options: Options<CreateTypesApiV1CrmTypesPostData>) => createQueryKey('createTypesApiV1CrmTypesPost', options);

/**
 * Create Types
 * Create new types with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createTypesApiV1CrmTypesPostOptions = (options: Options<CreateTypesApiV1CrmTypesPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createTypesApiV1CrmTypesPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createTypesApiV1CrmTypesPostQueryKey(options)
    });
};

/**
 * Create Types
 * Create new types with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createTypesApiV1CrmTypesPostMutation = (options?: Partial<Options<CreateTypesApiV1CrmTypesPostData>>): UseMutationOptions<CreateTypesApiV1CrmTypesPostResponse, CreateTypesApiV1CrmTypesPostError, Options<CreateTypesApiV1CrmTypesPostData>> => {
    const mutationOptions: UseMutationOptions<CreateTypesApiV1CrmTypesPostResponse, CreateTypesApiV1CrmTypesPostError, Options<CreateTypesApiV1CrmTypesPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createTypesApiV1CrmTypesPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Types
 * type update
 */
export const updateTypesApiV1CrmTypesPutMutation = (options?: Partial<Options<UpdateTypesApiV1CrmTypesPutData>>): UseMutationOptions<UpdateTypesApiV1CrmTypesPutResponse, UpdateTypesApiV1CrmTypesPutError, Options<UpdateTypesApiV1CrmTypesPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateTypesApiV1CrmTypesPutResponse, UpdateTypesApiV1CrmTypesPutError, Options<UpdateTypesApiV1CrmTypesPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateTypesApiV1CrmTypesPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Types
 * Delete types by ID.
 */
export const deleteTypesApiV1CrmTypesItemIdDeleteMutation = (options?: Partial<Options<DeleteTypesApiV1CrmTypesItemIdDeleteData>>): UseMutationOptions<unknown, DeleteTypesApiV1CrmTypesItemIdDeleteError, Options<DeleteTypesApiV1CrmTypesItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteTypesApiV1CrmTypesItemIdDeleteError, Options<DeleteTypesApiV1CrmTypesItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteTypesApiV1CrmTypesItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getTypesOneApiV1CrmTypesItemIdGetQueryKey = (options: Options<GetTypesOneApiV1CrmTypesItemIdGetData>) => createQueryKey('getTypesOneApiV1CrmTypesItemIdGet', options);

/**
 * Get Types One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getTypesOneApiV1CrmTypesItemIdGetOptions = (options: Options<GetTypesOneApiV1CrmTypesItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTypesOneApiV1CrmTypesItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTypesOneApiV1CrmTypesItemIdGetQueryKey(options)
    });
};

export const getTypesAllApiV1CrmTypesGetAllPostQueryKey = (options: Options<GetTypesAllApiV1CrmTypesGetAllPostData>) => createQueryKey('getTypesAllApiV1CrmTypesGetAllPost', options);

/**
 * Get Types All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getTypesAllApiV1CrmTypesGetAllPostOptions = (options: Options<GetTypesAllApiV1CrmTypesGetAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getTypesAllApiV1CrmTypesGetAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getTypesAllApiV1CrmTypesGetAllPostQueryKey(options)
    });
};

/**
 * Get Types All
 * Get paginated list with advanced filtering and sorting options.
 */
export const getTypesAllApiV1CrmTypesGetAllPostMutation = (options?: Partial<Options<GetTypesAllApiV1CrmTypesGetAllPostData>>): UseMutationOptions<GetTypesAllApiV1CrmTypesGetAllPostResponse, GetTypesAllApiV1CrmTypesGetAllPostError, Options<GetTypesAllApiV1CrmTypesGetAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetTypesAllApiV1CrmTypesGetAllPostResponse, GetTypesAllApiV1CrmTypesGetAllPostError, Options<GetTypesAllApiV1CrmTypesGetAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getTypesAllApiV1CrmTypesGetAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrgTransactionsAllApiV1MoneyOrgTransactionsGetQueryKey = (options: Options<GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData>) => createQueryKey('getOrgTransactionsAllApiV1MoneyOrgTransactionsGet', options);

/**
 * Get Org Transactions All
 * Get all org org_transactions with advanced filtering, sorting, and pagination.
 */
export const getOrgTransactionsAllApiV1MoneyOrgTransactionsGetOptions = (options: Options<GetOrgTransactionsAllApiV1MoneyOrgTransactionsGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgTransactionsAllApiV1MoneyOrgTransactionsGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgTransactionsAllApiV1MoneyOrgTransactionsGetQueryKey(options)
    });
};

export const createOrgTransactionsApiV1MoneyOrgTransactionsPostQueryKey = (options: Options<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData>) => createQueryKey('createOrgTransactionsApiV1MoneyOrgTransactionsPost', options);

/**
 * Create Org Transactions
 * Create new org_transactions with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createOrgTransactionsApiV1MoneyOrgTransactionsPostOptions = (options: Options<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrgTransactionsApiV1MoneyOrgTransactionsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrgTransactionsApiV1MoneyOrgTransactionsPostQueryKey(options)
    });
};

/**
 * Create Org Transactions
 * Create new org_transactions with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createOrgTransactionsApiV1MoneyOrgTransactionsPostMutation = (options?: Partial<Options<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData>>): UseMutationOptions<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponse, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostError, Options<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostResponse, CreateOrgTransactionsApiV1MoneyOrgTransactionsPostError, Options<CreateOrgTransactionsApiV1MoneyOrgTransactionsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrgTransactionsApiV1MoneyOrgTransactionsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Org Transactions
 * Documentation.
 */
export const updateOrgTransactionsApiV1MoneyOrgTransactionsPutMutation = (options?: Partial<Options<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData>>): UseMutationOptions<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponse, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutError, Options<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutResponse, UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutError, Options<UpdateOrgTransactionsApiV1MoneyOrgTransactionsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrgTransactionsApiV1MoneyOrgTransactionsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostQueryKey = (options: Options<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData>) => createQueryKey('createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPost', options);

/**
 * Create Org Transactions Saved
 * Create row in table org_transactions.
 *
 * We create  template or schedule transaction.
 *
 * We DO NOT create current transaction.
 */
export const createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostOptions = (options: Options<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostQueryKey(options)
    });
};

/**
 * Create Org Transactions Saved
 * Create row in table org_transactions.
 *
 * We create  template or schedule transaction.
 *
 * We DO NOT create current transaction.
 */
export const createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostMutation = (options?: Partial<Options<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData>>): UseMutationOptions<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponse, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostError, Options<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostResponse, CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostError, Options<CreateOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Org Transactions
 * Delete org_transactions by ID.
 */
export const deleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteMutation = (options?: Partial<Options<DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteError, Options<DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteError, Options<DeleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteOrgTransactionsApiV1MoneyOrgTransactionsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetQueryKey = (options: Options<GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData>) => createQueryKey('getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGet', options);

/**
 * Get Org Transactions One
 * Get job.
 * Get a single job with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetOptions = (options: Options<GetOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgTransactionsOneApiV1MoneyOrgTransactionsItemIdGetQueryKey(options)
    });
};

export const readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetQueryKey = (options: Options<ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData>) => createQueryKey('readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGet', options);

/**
 * Read Org Transactions Saved
 * Get all saved transactions for an organization.
 */
export const readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetOptions = (options: Options<ReadOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: readOrgTransactionsSavedApiV1MoneyOrgTransactionsSavedOrgIdGetQueryKey(options)
    });
};

export const readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetQueryKey = (options: Options<ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData>) => createQueryKey('readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGet', options);

/**
 * Get scheduled transactions for an organization
 * Get scheduled transactions for an organization within a date range.
 *
 * Args:
 * org_id: Organization ID to get transactions for
 * period_start: Start date of the period
 * period_end: End date of the period
 *
 * Returns:
 * OrgTransactionScheduledTypes containing categorized scheduled transactions
 */
export const readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetOptions = (options: Options<ReadOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: readOrgTransactionsScheduledApiV1MoneyOrgTransactionsScheduledOrgIdGetQueryKey(options)
    });
};

export const createOrgSplitsApiV1MoneyOrgSplitsPostQueryKey = (options: Options<CreateOrgSplitsApiV1MoneyOrgSplitsPostData>) => createQueryKey('createOrgSplitsApiV1MoneyOrgSplitsPost', options);

/**
 * Create Org Splits
 * Create new org_splits with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createOrgSplitsApiV1MoneyOrgSplitsPostOptions = (options: Options<CreateOrgSplitsApiV1MoneyOrgSplitsPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await createOrgSplitsApiV1MoneyOrgSplitsPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: createOrgSplitsApiV1MoneyOrgSplitsPostQueryKey(options)
    });
};

/**
 * Create Org Splits
 * Create new org_splits with optional object associations.
 * Each job can specify its own objects to associate with.
 * Nonexistent objects will be skipped.
 */
export const createOrgSplitsApiV1MoneyOrgSplitsPostMutation = (options?: Partial<Options<CreateOrgSplitsApiV1MoneyOrgSplitsPostData>>): UseMutationOptions<CreateOrgSplitsApiV1MoneyOrgSplitsPostResponse, CreateOrgSplitsApiV1MoneyOrgSplitsPostError, Options<CreateOrgSplitsApiV1MoneyOrgSplitsPostData>> => {
    const mutationOptions: UseMutationOptions<CreateOrgSplitsApiV1MoneyOrgSplitsPostResponse, CreateOrgSplitsApiV1MoneyOrgSplitsPostError, Options<CreateOrgSplitsApiV1MoneyOrgSplitsPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await createOrgSplitsApiV1MoneyOrgSplitsPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Update Org Splits
 * Documentation.
 */
export const updateOrgSplitsApiV1MoneyOrgSplitsPutMutation = (options?: Partial<Options<UpdateOrgSplitsApiV1MoneyOrgSplitsPutData>>): UseMutationOptions<UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponse, UpdateOrgSplitsApiV1MoneyOrgSplitsPutError, Options<UpdateOrgSplitsApiV1MoneyOrgSplitsPutData>> => {
    const mutationOptions: UseMutationOptions<UpdateOrgSplitsApiV1MoneyOrgSplitsPutResponse, UpdateOrgSplitsApiV1MoneyOrgSplitsPutError, Options<UpdateOrgSplitsApiV1MoneyOrgSplitsPutData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await updateOrgSplitsApiV1MoneyOrgSplitsPut({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete Org Splits
 * Delete org_splits by ID.
 */
export const deleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteMutation = (options?: Partial<Options<DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData>>): UseMutationOptions<unknown, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteError, Options<DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData>> => {
    const mutationOptions: UseMutationOptions<unknown, DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteError, Options<DeleteOrgSplitsApiV1MoneyOrgSplitsItemIdDeleteData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteOrgSplitsApiV1MoneyOrgSplitsItemIdDelete({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetQueryKey = (options: Options<GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData>) => createQueryKey('getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGet', options);

/**
 * Get Org Splits One
 * Get org_split.
 * Get a single org_split with its associated objects and job tasks,
 * with tasks sorted under objects or the job.
 */
export const getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetOptions = (options: Options<GetOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgSplitsOneApiV1MoneyOrgSplitsItemIdGetQueryKey(options)
    });
};

export const getOrgSplitsAllApiV1MoneyOrgSplitsAllPostQueryKey = (options: Options<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData>) => createQueryKey('getOrgSplitsAllApiV1MoneyOrgSplitsAllPost', options);

/**
 * Get Org Splits All
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllApiV1MoneyOrgSplitsAllPostOptions = (options: Options<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgSplitsAllApiV1MoneyOrgSplitsAllPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgSplitsAllApiV1MoneyOrgSplitsAllPostQueryKey(options)
    });
};

/**
 * Get Org Splits All
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllApiV1MoneyOrgSplitsAllPostMutation = (options?: Partial<Options<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData>>): UseMutationOptions<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponse, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostError, Options<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData>> => {
    const mutationOptions: UseMutationOptions<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostResponse, GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostError, Options<GetOrgSplitsAllApiV1MoneyOrgSplitsAllPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getOrgSplitsAllApiV1MoneyOrgSplitsAllPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostQueryKey = (options: Options<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData>) => createQueryKey('getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPost', options);

/**
 * Get Org Splits All Raw Sql
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostOptions = (options: Options<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostQueryKey(options)
    });
};

/**
 * Get Org Splits All Raw Sql
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostMutation = (options?: Partial<Options<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData>>): UseMutationOptions<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponse, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostError, Options<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData>> => {
    const mutationOptions: UseMutationOptions<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostResponse, GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostError, Options<GetOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getOrgSplitsAllRawSqlApiV1MoneyOrgSplitsAllRawSqlPost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostQueryKey = (options: Options<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData>) => createQueryKey('getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePost', options);

/**
 * Get Org Splits All Core
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostOptions = (options: Options<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePost({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostQueryKey(options)
    });
};

/**
 * Get Org Splits All Core
 * Retrieve a paginated list of jobs with advanced filtering and sorting options.
 */
export const getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostMutation = (options?: Partial<Options<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData>>): UseMutationOptions<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponse, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostError, Options<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData>> => {
    const mutationOptions: UseMutationOptions<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostResponse, GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostError, Options<GetOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePostData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await getOrgSplitsAllCoreApiV1MoneyOrgSplitsAllCorePost({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getJobJobsDatesJobIdGetQueryKey = (options: Options<GetJobJobsDatesJobIdGetData>) => createQueryKey('getJobJobsDatesJobIdGet', options);

/**
 * Get Job
 */
export const getJobJobsDatesJobIdGetOptions = (options: Options<GetJobJobsDatesJobIdGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getJobJobsDatesJobIdGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getJobJobsDatesJobIdGetQueryKey(options)
    });
};

export const healthCheckHealthGetQueryKey = (options?: Options<HealthCheckHealthGetData>) => createQueryKey('healthCheckHealthGet', options);

/**
 * Health Check
 */
export const healthCheckHealthGetOptions = (options?: Options<HealthCheckHealthGetData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await healthCheckHealthGet({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: healthCheckHealthGetQueryKey(options)
    });
};