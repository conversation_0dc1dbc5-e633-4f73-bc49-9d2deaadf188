import type { ProfileDisplayTypes } from "@/api/_client";

export const defaultProfile: ProfileDisplayTypes = {
  id: 10,
  name: "",
  user_id: 1,
  type_id: 0,
  created_at: "",
  updated_at: "",
  created_by: 1,
  updated_by: null,
  lang: "",
  json_metadata: {
    dashboard_modules: {
      proposals: true,
      jobs: true,
      trips: true,
      transactions: true,
      problems: true,
    },
    jobs: {
      calendar_objects: [],
      calendar_period: [0, 7],
    },
    member: {
      is_voting: false,
      shares: 0,
      znaczaca_operacja_limit: 0,
    },
    query_args: [
      {
        id: "hfhdfhgfghfgh",
        name: "default",
        path: "/app/main/jobs/",
        server_args: {
          org_id: 1,
          is_public: false,
          contrahent_id: undefined,
          date_column: "start_date",
          start_date: undefined,
          end_date: undefined,
          statuses: [],
          page_index: 0,
          page_size: 500,
          search: [],
          order: [],
        },
        client_args: {
          pagination: {
            pageIndex: 0,
            pageSize: 5,
          },
          sorting: [],
          columnFilters: [],
          columnOrder: [],
          columnVisibility: {},
          globalFilter: "",
          startDate: "",
          endDate: "",
        },
      },
    ],
  },
  description: null,
  display_name: "oski",
  is_company: false,
  org_id: 1,
  is_current: false,
  org_name: "kkk",
  object_id: null,
};
