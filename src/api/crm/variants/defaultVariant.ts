import type { VariantDisplayColumnsTypes } from "@/api/_client";

export const defaultVariant: VariantDisplayColumnsTypes = {
  id: 0,
  name: "",
  path: "",
  is_active: false,
  client_ops: {
    columnFilters: undefined,
    columnSizing: {},
    columnSizingInfo: {},
    rowSelection: {},
    rowPinning: {
      top: [],
      bottom: [],
    },
    expanded: {},
    grouping: [],
    sorting: [],
    columnPinning: {},
    columnOrder: [],
    columnVisibility: undefined,
    pagination: {
      pageIndex: 5,
      pageSize: 50,
    },
    start_date: null,
    end_date: null,
    date_column: null,
  },
  server_ops: {
    filters: null,
    org_id: null,
    period: null,
    date_column: null,
    start_date: null,
    end_date: null,
    page_index: 0,
    page_size: null,
    search: null,
    order: null,
    columns: null,
  },
};
