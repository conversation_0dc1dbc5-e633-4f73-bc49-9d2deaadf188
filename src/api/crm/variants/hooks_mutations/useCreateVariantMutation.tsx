import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { createVariantsApiV1CoreVariantsPostMutation } from "@/api/_client/@tanstack/react-query.gen";

export function useCreateVariantMutation() {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const createVariantMutation = useMutation({
    ...createVariantsApiV1CoreVariantsPostMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readVariantsAllV1CoreVariantsGet"],
      });
      toast.success(t("common.success.label"));

      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  return createVariantMutation;
}
