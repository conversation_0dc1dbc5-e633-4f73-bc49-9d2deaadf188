import { useQuery } from "@tanstack/react-query";
import type { GetAllParams } from "@types";
import { toast } from "sonner";
import { getVariantsAllApiV1CoreVariantsGetAllPostOptions } from "@/api/_client/@tanstack/react-query.gen";

export function useVariantsData(params: GetAllParams) {
  const {
    data: dataVariants,
    error: errorVariants,
    isLoading: isLoadingVariants,
  } = useQuery(
    getVariantsAllApiV1CoreVariantsGetAllPostOptions({
      body: params,
    }),
  );

  if (errorVariants) {
    toast.error(errorVariants.message);
    console.error("errorVariants", errorVariants);
  }

  return {
    dataVariants,
    errorVariants,
    isLoadingVariants,
  };
}
