import { useMutation, useQueryClient } from "@tanstack/react-query";
import { t } from "i18next";
import { toast } from "sonner";
import { updateProfilesApiV1CrmProfilesPutMutation } from "@/api/_client/@tanstack/react-query.gen";

export function useProfileData() {
  const queryClient = useQueryClient();

  const updateProfileMutation = useMutation({
    ...updateProfilesApiV1CrmProfilesPutMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["readProfilesAllApiV1CrmProfilesGet"],
      });
      toast.success(t("common.success.label"));
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`${t("common.failed.label")} ${error}`);
    },
  });

  return {
    updateProfileMutation,
  };
}
