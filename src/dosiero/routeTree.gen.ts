/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from "./routes/__root"
import { Route as IndexRouteImport } from "./routes/index"
import { Route as MainJobsIndexRouteImport } from "./routes/main/jobs/index"

const IndexRoute = IndexRouteImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => rootRouteImport,
} as any)
const MainJobsIndexRoute = MainJobsIndexRouteImport.update({
  id: "/main/jobs/",
  path: "/main/jobs/",
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  "/": typeof IndexRoute
  "/main/jobs": typeof MainJobsIndexRoute
}
export interface FileRoutesByTo {
  "/": typeof IndexRoute
  "/main/jobs": typeof MainJobsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  "/": typeof IndexRoute
  "/main/jobs/": typeof MainJobsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: "/" | "/main/jobs"
  fileRoutesByTo: FileRoutesByTo
  to: "/" | "/main/jobs"
  id: "__root__" | "/" | "/main/jobs/"
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  MainJobsIndexRoute: typeof MainJobsIndexRoute
}

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/": {
      id: "/"
      path: "/"
      fullPath: "/"
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    "/main/jobs/": {
      id: "/main/jobs/"
      path: "/main/jobs"
      fullPath: "/main/jobs"
      preLoaderRoute: typeof MainJobsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  MainJobsIndexRoute: MainJobsIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
