import { Mutation<PERSON>ache, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createRouter, RouterProvider, useRouter } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import "@/utils/i18n/index";

import type { Query<PERSON>ey } from "@tanstack/react-query";
import { Log, UserManager, WebStorageStateStore } from "oidc-client-ts";
import React, { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { ErrorBoundary } from "react-error-boundary";
import { AuthProvider } from "react-oidc-context";
import { Provider } from "react-redux";
import { client } from "@/api/_client/client.gen";
import { UploadProvider } from "@/utils/contexts/UploadCtx";
import type { RootStateTypes } from "@/utils/redux/store";
import store from "@/utils/redux/store";
import App from "./App";
// Import the generated route tree
// import { routeTree } from "./routeTree.gen";
import "@/styles/tailwind/global.css";
// import { ErrorFallback } from "@/components/system/ErrorFallback";
// import reportWebVitals from './reportWebVitals.ts'
import { toast } from "sonner";
import { queryClient } from "@/utils/router";

if (import.meta.env.PUBLIC_NODE_ENV === "production") {
  console.log("OIDC: Running in production mode, version: " + import.meta.env.PUBLIC_DEPLOY_VERSION);
} else {
  console.log("OIDC: Running in development mode");
}

/**
 * See: {@link https://authts.github.io/oidc-client-ts/classes/UserManager.html}
 */
const userManager = new UserManager({
  authority: import.meta.env.PUBLIC_KC_REALM_URL,
  client_id: import.meta.env.PUBLIC_KC_CLIENT_ID,
  // redirect_uri: `${window.location.origin}/app/dashboard/jobs`,
  redirect_uri: `${window.location.origin}${window.location.pathname}${window.location.search}`,
  post_logout_redirect_uri: window.location.origin,
  scope: "openid profile",
  userStore: new WebStorageStateStore({ store: window.sessionStorage }),
  // userStore: new WebStorageStateStore({ store: window.localStorage }),
  // monitorSession: true, // this allows cross tab login/logout detection
  automaticSilentRenew: true,
});

userManager.events.addAccessTokenExpiring(() => {
  console.log("token expiring...");
});

// const onSigninCallback = (_user: User | void): void => {
//   window.history.replaceState({}, document.title, window.location.pathname);
// };
// const onSigninCallback = (_user: User | void): void => {
//   window.history.replaceState({}, document.title, window.location.pathname);
// };
// const onSigninCallback = () => {
//   window.history.replaceState({}, document.title, window.location.origin + window.location.pathname);
// };
// Simplified and corrected callback
const onSigninCallback = (): void => {
  // This is the simplest and safest way to clean the URL
  window.history.replaceState({}, document.title, window.location.pathname + window.location.search);
};

// In withAuthenticationRequired you can specify onBeforeSignin. That is the point before the authentication process, thus you can store (e.g. to sessionStorage to current location like const prevPath = window.location.pathname + window.location.search + window.location.hash;...) here the users current location

// react-oidc-context/src/withAuthenticationRequired.tsx

// Lines 16 to 19 in 4d3afe2
//      /** 
//       * Allows executing logic before the user is redirected to the signin page. 
//       */ 
//      onBeforeSignin?: () => Promise<void> | void; 

// After the authentication process is finished onSigninCallback is called and there you can restore the previous location (e.g. by looking into sessionStorage and apply like window.location.replace(prevPath);)...

Log.setLogger(console);



const apiBaseUrl = import.meta.env.PUBLIC_API_BASE_URL;

// configure internal service client
client.setConfig({
  // set default base url for requests
  baseUrl: apiBaseUrl,
  // set default headers for requests
  // headers: {
  //   Authorization: 'Bearer <token_from_service_client>',
  // },
});

// Usually here we would render to the DOM, but since Astro will handle
// this for us we are okay to just return the component
export const ReactApp = () => {
  console.log("window.location", window.location);

  return (<React.StrictMode>
    {/* <ErrorBoundary FallbackComponent={ErrorFallback}> */}
    <AuthProvider userManager={userManager} onSigninCallback={onSigninCallback}>
      <Provider store={store}>
        <UploadProvider>
          <QueryClientProvider client={queryClient}>
            <App />
          </QueryClientProvider>
        </UploadProvider>
      </Provider>
    </AuthProvider>
    {/* </ErrorBoundary> */}
  </React.StrictMode>)
};

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals()
