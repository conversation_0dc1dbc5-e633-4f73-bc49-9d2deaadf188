import { z } from "zod";

// Helper for preprocessing stringified JSON
const preprocessIfString = (schema: z.ZodTypeAny) =>
  z.preprocess((val) => {
    if (typeof val !== "string") return val;
    try {
      return JSON.parse(val);
    } catch {
      return undefined;
    }
  }, schema);

export const searchSchema = z.object({
  pagination: z
    .preprocess(
      (val) => {
        if (typeof val !== "string") return val;
        try {
          return JSON.parse(val);
        } catch {
          return undefined;
        }
      },
      z
        .object({
          pageIndex: z.number().optional(),
          pageSize: z.number().optional(),
        })
        .optional(),
    )
    .optional(),
  endDate: z.string().optional(),
  globalFilter: z.string().optional(),
  date_column: z.string().optional(),

  sorting: preprocessIfString(
    z
      .array(
        z.object({
          id: z.string().optional(),
          desc: z.boolean().optional(),
        }),
      )
      .optional(),
  ).optional(),

  columnFilters: preprocessIfString(
    z
      .array(
        z.object({
          id: z.string().optional(),
          value: z.unknown().optional(),
        }),
      )
      .optional(),
  ).optional(),

  columnVisibility: preprocessIfString(z.record(z.string(), z.boolean().optional())).optional(),

  columnOrder: preprocessIfString(z.array(z.string()).optional()).optional(),

  columnPinning: preprocessIfString(
    z
      .object({
        left: z.array(z.string()).optional(),
        right: z.array(z.string()).optional(),
      })
      .optional(),
  ).optional(),

  expanded: preprocessIfString(z.record(z.string(), z.boolean().optional())).optional(),

  grouping: preprocessIfString(z.array(z.string()).optional()).optional(),

  columnSizing: preprocessIfString(z.record(z.string(), z.number().optional()).optional()).optional(),

  columnSizingInfo: preprocessIfString(
    z
      .object({
        columnSizing: z.record(z.string(), z.number().optional()).optional(),
        isResizing: z.boolean().optional(),
        deltaOffset: z.number().optional(),
        startOffset: z.number().optional(),
        startSize: z.number().optional(),
      })
      .optional(),
  ).optional(),

  rowSelection: preprocessIfString(z.record(z.string(), z.boolean().optional()).optional()).optional(),
  _internal: z.string().optional(),
});
