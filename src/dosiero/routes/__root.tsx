import { createRootRouteWithContext, Outlet, useLocation } from "@tanstack/react-router";
import { useAuth } from "react-oidc-context";
import { client } from "@/api/_client/client.gen";
// import { AuthDataLoader } from "@/components/_system/AuthDataLoader";
// import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import AppLayout from "@/layouts/AppLayout";
import DocPreviewLayout from "@/layouts/DocPreviewLayout/DocPreviewLayout.jsx";
import PublicLayout from "@/layouts/PublicLayout/PublicLayout.tsx";
// import { getToken, initKeycloak, isLoggedIn } from "@/utils/auth";
import { useAppSelector } from "@/utils/redux/hooks";

interface RouterContext {
  queryClient: any;
  curr_org_id?: number | null;
  curr_profile_id?: number | null;
  curr_acc_period_id?: number | null;
  user_id?: number | null;
}

const BASE_URL = import.meta.env.PUBLIC_API_BASE_URL;

client.setConfig({
  baseUrl: BASE_URL,
});

// Define public routes that don't require authentication
const isPublicRoute = (pathname: string) => {
  return pathname === "/" || pathname.startsWith("/public") || pathname.startsWith("/doc-viewer");
};

export const Route = createRootRouteWithContext<RouterContext>()({
  beforeLoad: async ({ location }) => {
    console.log("beforeLoad pathname", location.pathname, "isPublicRoute", isPublicRoute(location.pathname));

    // if (isPublicRoute(location.pathname)) {
    //   // For public routes, initialize Keycloak with check-sso (no login required)
    //   // await initKeycloak(false);
    // } else {
    //   // For private routes, initialize Keycloak with login-required
    //   // await initKeycloak(true);

    //   // Check if user is authenticated for private routes
    //   if (!isLoggedIn()) {
    //     // throw redirect({
    //     //   to: "/",
    //     // });
    //   } else {
    //     // Set up API client with authentication token
    //     const token = (await getToken()) || "";
    //     client.interceptors.request.use((request, options) => {
    //       request.headers.set("Authorization", `Bearer ${token}`);
    //       return request;
    //     });
    //   }
    // }
  },
  component: () => <RootComponent />,
});

const RootComponent = () => {
  const { pathname } = useLocation();
  const { data: user } = useAppSelector((state) => state.user);
  const auth = useAuth();
  const isPublic = isPublicRoute(pathname);

  // console.log("RootComponent pathname", pathname, "isPublic", isPublic, "profile", profile);

  if (isPublic) {
    return (
      <PublicLayout>
        <Outlet />
      </PublicLayout>
    );
  }

  // If user is not authenticated, show public layout
  if (!user) {
    return (
      <PublicLayout>
        <Outlet />
      </PublicLayout>
    );
  }

  // Special layout for document preview
  if (pathname.includes("/doc-preview/")) {
    return (
      <DocPreviewLayout>
        <Outlet />
      </DocPreviewLayout>
    );
  }

  // Handle logout function
  const handleLogout = async () => {
    try {
      await auth.signoutRedirect();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Default app layout for authenticated users
  return (
    <AppLayout userProfile={user} onLogout={handleLogout}>
      <Outlet />
    </AppLayout>
  );
};
