import { createFile<PERSON>out<PERSON>, useLocation, useNavigate, useRoute<PERSON>ontext, useRouter, useSearch } from "@tanstack/react-router";
import type {
  GetAllParams,
  JobDataTypes,
  JobDisplayColumnsTypes,
  VariantDisplayColumnsTypes,
} from "@types";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { getVariantsAllApiV1CoreVariantsGetAllPostOptions } from "@/api/_client/@tanstack/react-query.gen";
import { useJobsData } from "@/api/core/jobs/hooks_data/useJobsData";
import { useVariantsData } from "@/api/crm/variants/hooks_data/useVariantsData";
import { useCreateVariantMutation } from "@/api/crm/variants/hooks_mutations/useCreateVariantMutation";
import { useUpdateVariantMutation } from "@/api/crm/variants/hooks_mutations/useUpdateVariantMutation";
import { Badge } from "@/components/_shadcn/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import { DataTable } from "@/components/data-table/data-table";
import type { ExportConfigType, PathStateTypes } from "@/components/data-table/defaults.ts";
import { type GetColumnsOptions, getColumns } from "@/dosiero/routes/main/jobs/-components/columns.tsx";
import { searchSchema } from "@/dosiero/searchSchema";
import type { RouteContext } from "@/types/router";
import {
  cleanEmptyParams,

} from "@/utils/cleanEmptyParams";
import { useInternalNavigate } from "@/utils/navigation";
// import { useInternalNavigate } from "@/utils/navigation";
import type { RootStateTypes } from "@/utils/redux/store";
import customToolbarContent from "./-components/custom-toolbar-content";
import { tableConfig, tableDefaultVariant } from "./-components/table-config";


export const Route = createFileRoute("/main/jobs/")({
  validateSearch: searchSchema,
  component: RouteComponent,
});

function RouteComponent() {
  const reloadCount = useRef(0);
  reloadCount.current += 1;
  console.log("%c RELOAD COUNT", "color: red; font-weight: bold;", reloadCount.current);

  const { t } = useTranslation();
  const navigate = useNavigate();
  const search = Route.useSearch();
  const { internalNavigate } = useInternalNavigate();

  // Clean up OAuth callback parameters from URL while preserving other search parameters
  useEffect(() => {
    console.log("%cCleaning up OAuth callback parameters", "color: blue; font-weight: bold;");
    const searchParams = new URLSearchParams(window.location.search);
    const oauthParams = ['code', 'state', 'session_state', 'iss'];
    const hasOAuthParams = oauthParams.some(param => searchParams.has(param));

    if (hasOAuthParams) {
      // Remove OAuth params while keeping other params
      oauthParams.forEach(param => searchParams.delete(param));
      const cleanSearch = searchParams.toString();
      const newUrl = `${window.location.pathname}${cleanSearch ? `?${cleanSearch}` : ''}${window.location.hash || ''}`;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, []);

  const isInternal = search._internal === "1";
  console.log("%c <<<< INDEX isInternal  >>>>", "color: green; font-weight: bold;", isInternal);

  const { id: path } = Route.useMatch();
  const { org_id, profile_id, user_id, locale = "pl" } = useRouteContext({ from: path });
  console.log("%c <<<< INDEX profile_id  >>>>", "color: green; font-weight: bold;", profile_id);
  const [variant, setVariant] = useState<VariantDisplayColumnsTypes>(tableDefaultVariant);
  console.log("%c >>> INDEX VARIANT STATE", "color: green; font-weight: bold;", variant);


  const { dataJobs, errorJobs, isLoadingJobs } = useJobsData(variant?.server_ops ?? {});
  const { dataVariants, errorVariants, isLoadingVariants } = useVariantsData({
    filters: { profile_id: profile_id, path: path },
  });
  const updateVariantMutation = useUpdateVariantMutation();
  const createVariantMutation = useCreateVariantMutation();



  // // on nvigete away actions
  // useEffect(() => {
  //   // Subscribe to the onBeforeNavigate event
  //   const unsubscribe = router.subscribe("onBeforeNavigate", ({ toLocation }) => {
  //     // Check if navigating away from "/objects/new"
  //     if (toLocation.pathname !== `/main/jobs/`) {
  //       console.log("Navigating away from /main/jobs/");
  //     }
  //   });

  //   return () => {
  //     // Clean up the subscription
  //     unsubscribe();
  //   };
  // }, [router]);

  const selectVariantsData = [
    { ...tableDefaultVariant, profile_id: profile_id, path: path },
    { ...tableDefaultVariant, name: "Custom", id: -1, profile_id: profile_id, path: path },
    ...(dataVariants?.data || []),
  ]


  // const [initialSearch, setInitialSearch] = useState({});
  // // Manually parse search parameters on initial load
  // useEffect(() => {
  //   console.error("URL cleaning useEffect");
  //   if (Object.keys(search).length === 0 && window.location.search) {
  //     try {
  //       const urlSearchParams = new URLSearchParams(window.location.search);
  //       const rawSearch = Object.fromEntries(urlSearchParams);
  //       const parsedSearch = searchSchema.parse(rawSearch); // Validate with your schema
  //       setInitialSearch(parsedSearch);
  //     } catch (error) {
  //       console.error("Failed to parse search params:", error);
  //     }
  //   }
  // }, [search]);
  // const effectiveSearch = Object.keys(search).length > 0 ? search : initialSearch;
  // console.log("%cEffective Search:", "color: orange; font-weight: bold;", effectiveSearch);


  // This ref helps us track if we've already processed the variant initialization
  const initialVariantProcessed = useRef(false);

  useEffect(() => {
    const activeVariant = dataVariants?.data?.find((variant) => variant.is_active) || null;
    console.log("%c <<<< INDEX USEEFFECT STARTED  >>>> dataVariants.length", "color: blue; font-weight: bold;", dataVariants?.data.length);
    // if (!dataVariants?.data) return;
    console.log("%c <<<< INDEX initialVariantProcessed  >>>>", "color: blue; font-weight: bold;", initialVariantProcessed.current);
    if (!isInternal) {
      if (!initialVariantProcessed.current) {
        console.log('%cEXTERNAL REQUEST - SETTING PROCESSED TRUE', 'color: blue; font-weight: bold;');
        // set variant to custom with search from url
        setVariant({
          ...tableDefaultVariant,
          id: -1,
          name: "Custom",
          path: path,
          profile_id: profile_id,
          server_ops: cleanEmptyParams(tableDefaultVariant.server_ops),
          client_ops: search
        });
        initialVariantProcessed.current = true;
      } else {
        console.log('%cEXTERNAL REQUEST - DOING NOTHING', 'color: blue; font-weight: bold;');
      }
    } else if (isInternal) {
      if (!initialVariantProcessed.current) {
        if (activeVariant) {
          console.log('%cIIIIII    Internal navigation   IIIIIII  Processing active variant:', 'color: blue; font-weight: bold;', cleanEmptyParams(activeVariant.client_ops));

          setVariant({
            ...activeVariant,
            server_ops: cleanEmptyParams(activeVariant.server_ops),
            client_ops: cleanEmptyParams(activeVariant.client_ops)
          });
          navigate({
            search: cleanEmptyParams(activeVariant.client_ops || {}) as any,
            replace: true
          });
          initialVariantProcessed.current = true;
        } else {
          console.log('%cIIIIII    Internal navigation   IIIIIII - active variant already processed', 'color: blue; font-weight: bold;');
          console.log("No active variant found, using default variant")
          setVariant({
            ...tableDefaultVariant,
            path: path,
            profile_id: profile_id,
            server_ops: cleanEmptyParams(tableDefaultVariant.server_ops),
            client_ops: cleanEmptyParams(tableDefaultVariant.client_ops)
          });
          navigate({
            search: cleanEmptyParams(tableDefaultVariant.client_ops || {}) as any,
            replace: true
          });
        }
      }
    }
  }, [dataVariants, isInternal, navigate, path, profile_id, search]);

  // console.log("%cqueryArgs <<<< INDEX  >>>>", "color: blue; font-weight: bold;", queryArgs);

  // Export configuration for CSV/Excel export
  const exportConfig: ExportConfigType = {
    entityName: "jobs",
    locale: locale,
    columnMapping: {
      // only these column will be included in XLS export!!
      start_date: t("forms.JobForm.startDate.label", "start_date"),
      name: t("forms.JobForm.name.label", "name"),
      status: t("forms.JobForm.status.label", "status"),
      type: t("forms.JobForm.type.label", "type"),
      budget: t("forms.JobForm.budget.label", "budget"),
    },
    // Corresponds to the order in columnMapping
    columnWidths: [{ wch: 25 }, { wch: 30 }, { wch: 20 }],
    headers: ["Start date", "Job name", "Status", "Type"],
  };



  if (errorJobs || errorVariants) {
    return <div>Error: {errorJobs?.message || errorVariants?.message}</div>;
  }

  const handleVariantChange = (value: string) => {
    // console.log('%chandleVariantChange', 'color: green; font-weight: bold;', value);
    const newVariant = selectVariantsData?.find((v) => v.name === value);
    // console.log('%cnewVariant', 'color: green; font-weight: bold;', newVariant);
    if (!newVariant) return;
    const oldVariant = { ...variant }

    // console.log('%cChanging variant to:', 'color: green; font-weight: bold;', value);

    // update variants data in db: current.is_active = false, selected.is_active = true
    if (oldVariant.id <= 0) {
      // we change from local (default or custom) to db variant
      if (newVariant.id > 0) {
        setVariant(newVariant)
        updateVariantMutation.mutate({
          body: [

            {
              path: path,
              deactivate_id: oldVariant.id,
              id: newVariant.id,
              is_active: true,
            },
          ],
        }, {
          onSuccess: () => {

            // console.log('Variant updated successfully DEFAULT => DB');
            internalNavigate({
              search: cleanEmptyParams(newVariant.client_ops || {}) as any,
              replace: true
            });
          }
        })
      } else if (newVariant.id <= 0) {
        setVariant(newVariant)
        // console.log('Variant updated successfully DEFAULT => DEFAULT');
        internalNavigate({
          search: cleanEmptyParams(newVariant.client_ops || {}),
          replace: true
        });
      }
    } else if (oldVariant.id > 0) {
      // we change from db to default or other db variant
      // if vairiant id = 0 backend will not deactivate anything
      if (newVariant.id > 0) {
        // we change to db variant
        setVariant(newVariant)
        updateVariantMutation.mutate({
          body: [
            {
              path: path,
              deactivate_id: oldVariant.id,
              id: newVariant.id,
              is_active: true,
            },
          ],
        }, {
          onSuccess: () => {

            // console.log('Variant updated successfully DB => DB OR DEFAULT');
            internalNavigate({ search: cleanEmptyParams(newVariant.client_ops), replace: true });
          }
        })
      } else if (newVariant.id <= 0) {
        // we change from local (default or custom) to db variant
        setVariant(newVariant)
        updateVariantMutation.mutate({
          body: [
            {

              id: oldVariant.id,
              is_active: false,
            },
          ],
        }, {
          onSuccess: () => {

            // console.log('Variant updated successfully DB => DB OR DEFAULT');
            internalNavigate({ search: cleanEmptyParams(newVariant.client_ops), replace: true });
          }
        })
      }
    };
  }

  const dataLoading = isLoadingJobs || isLoadingVariants;

  // console.log('%csearch', 'color: orange; font-weight: bold;', search);

  return (
    <div className="p-4">

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold mb-4">Jobs</h1>
          <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
            R: {reloadCount.current}
          </Badge>
        </div>
        <Select value={variant.name || ""} onValueChange={handleVariantChange}>
          <SelectTrigger className="w-[440px]">
            <SelectValue>{variant.name || "Select variant"}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {selectVariantsData.map((v) => (
              <SelectItem key={v.id} value={v.name || ""} disabled={v.name === "Custom"}>

                {v.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <DataTable<JobDisplayColumnsTypes, VariantDisplayColumnsTypes>
        data={dataJobs?.data as JobDisplayColumnsTypes[]}
        serverPagination={dataJobs?.pagination ? {
          pageIndex: dataJobs.pagination.pageIndex,
          pageSize: dataJobs.pagination.pageSize,
          totalItems: dataJobs.pagination.totalItems ?? 0, // Provide a default value
        } : undefined}
        isLoading={dataLoading}
        error={errorJobs || undefined}
        tableConfig={{ ...tableConfig, path: path }}
        getColumns={getColumns}
        exportConfig={exportConfig}
        customToolbarContent={customToolbarContent}
        pathState={{ ...search }}
        // pathState={variant.client_ops}
        // pathState={{ ...variant.client_ops, ...search }}
        variant={variant}
        setVariant={setVariant}
        selectVariantsData={selectVariantsData}
        createVariantMutation={createVariantMutation}
        updateVariantMutation={updateVariantMutation}
      />
    </div>
  );
}
