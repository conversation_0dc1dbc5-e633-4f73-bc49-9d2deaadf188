import type { ColumnDef, ColumnMeta, Row, Table } from "@tanstack/react-table";
import type { JobDisplayColumnsTypes } from "@types";
import { format, parseISO } from "date-fns";
import { MoreHorizontal } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Button } from "@/components/_shadcn/components/ui/button.tsx";
import { Checkbox } from "@/components/_shadcn/components/ui/checkbox.tsx";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/_shadcn/components/ui/dropdown-menu.tsx";
// Import components
import { DataTableColumnHeader } from "@/components/data-table/column-header.tsx";
import type { PathStateTypes, TableConfig } from "@/components/data-table/defaults.ts";
import { DataTableRowActions } from "@/dosiero/routes/main/jobs/-components/data-table-row-actions.tsx";
import { defaultConfig } from "@/dosiero/routes/main/jobs/-components/table-config.ts";

const localeMap: { [key: string]: string } = {
  en: "en-US",
  pl: "pl-PL",
};

export interface GetColumnsOptions {
  handleRowDeselection: ((rowId: string) => void) | null;
  locale?: string;
  tableConfig?: TableConfig;
  columns: string[] | [];
}

export const getColumns = (options: GetColumnsOptions): ColumnDef<JobDisplayColumnsTypes>[] => {
  const {
    handleRowDeselection,
    locale = 'pl',
    tableConfig = defaultConfig,
    columns = []
  } = options;

  const baseColumns: ColumnDef<JobDisplayColumnsTypes>[] = [
    {
      accessorKey: "start_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Start Date" translationKey="forms.JobForm.startDate.label" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("start_date"));
        return (
          <div className="font-medium">
            {date.toLocaleDateString(localeMap[locale] || localeMap.pl, {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            })}
          </div>
        );
      },
    },
    {
      accessorKey: "end_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="End Date" translationKey="forms.JobForm.endDate.label" />
      ),
      cell: ({ row }) => {
        const rawDate = row.getValue("end_date");
        const parsedDate = rawDate ? parseISO(rawDate.toString()) : null;
        const formatted =
          parsedDate && !Number.isNaN(parsedDate as unknown as number) ? format(parsedDate, "dd-MM-yyyy") : "—";
        return <div>{formatted}</div>;
      },
      aggregationFn: "max",
      aggregatedCell: ({ getValue }) => <div className="font-semibold">Max: {format(getValue() as Date, "dd-MM-yyyy")}</div>,
      size: 120,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" translationKey="forms.JobForm.name.label" />
      ),
      cell: ({ row }) => (
        <Button
          variant="ghost"
          className="h-auto p-0 font-medium hover:bg-transparent hover:text-blue-900 cursor-pointer"
          onClick={() => {
            navigator.clipboard.writeText(row.original.name?.toString() || "");
            toast.success("Name copied to clipboard");
          }}
        >
          {row.getValue("name")}
        </Button>
      ),
      size: 120,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" translationKey="forms.JobForm.status.label" />
      ),
      cell: ({ row }) => <div>{row.getValue("status")}</div>,
      aggregationFn: "count",
      aggregatedCell: ({ getValue }) => <div className="font-semibold">{getValue() as number}</div>,
      size: 250,
    },
    {
      accessorKey: "type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Type" translationKey="forms.JobForm.type.label" />
      ),
      cell: ({ row }) => <div>{row.getValue("type")}</div>,
      // getGroupingValue: row => `${row.getValue("type")}`, // by what value will be aggregated
      // aggregatedCell: ({ getValue }) => <div className="font-semibold">77777</div>,
      size: 200,
    },
    {
      accessorKey: "tag",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tag" translationKey="forms.JobForm.tag.label" />
      ),
      cell: ({ row }) => <div>{row.getValue("tag")}</div>,
      size: 120,
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" translationKey="forms.JobForm.description.label" />
      ),
      cell: ({ row }) => <div className="truncate max-w-[240px]">{row.getValue("description")}</div>,
      size: 250,
    },
    {
      accessorKey: "lang",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Lang" translationKey="forms.JobForm.lang.label" />
      ),
      cell: ({ row }) => <div>{row.getValue("lang")}</div>,
      size: 120,
    },
    {
      accessorKey: "is_public",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Public" translationKey="forms.JobForm.isPublic.label" />
      ),
      cell: ({ row }) => {
        const value = row.getValue("is_public") as boolean | null;
        return <div>{value ? "Yes" : "No"}</div>;
      },
      size: 120,
    },
    {
      accessorKey: "budget",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Budget" translationKey="forms.JobForm.budget.label" />
      ),
      cell: ({ row, column }) => {
        const value = row.getValue("budget") as number | null;
        return (
          <div>
            {value !== null && value !== undefined ? Number(value).toLocaleString(localeMap[locale] || locale) : "—"}
          </div>
        );
      },
      aggregationFn: "mean",
      aggregatedCell: ({ getValue }) => <div className="font-semibold">
        Avg: {Number(getValue() as number).toLocaleString(localeMap[locale] || locale, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
      </div>,
      size: 120,
    },
    {
      accessorKey: "org_id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Org Id" translationKey="forms.JobForm.orgId.label" />
      ),
      cell: ({ row }) => <div>{row.getValue("org_id")}</div>,
      size: 120,
    },
    {
      accessorKey: "created_by",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created By" translationKey="forms.JobForm.createdBy.label" />
      ),
      cell: ({ row }) => <div>{row.getValue("created_by")}</div>,
      size: 120,
    },
  ];

  // Filter baseColumns if specific columns are requested
  const filteredBaseColumns = columns?.length > 0
    ? baseColumns.filter(col => 'accessorKey' in col && columns.includes(col.accessorKey))
    : baseColumns;

  const resultColumns = [...filteredBaseColumns];

  // Add expander column first if enabled
  if (tableConfig.enableExpander) {
    resultColumns.unshift(
      {
        id: "expander",
        header: () => <div>Expander</div>,
        // Only show the expander for rows that are grouped or can expand
        cell: ({ row }) => {
          console.log("Row in expander:", row.id, "Can expand:", row.getCanExpand(), "Is grouped:", row.getIsGrouped());

          // Only show for parent rows (grouped headers)
          if (row.getIsGrouped()) {
            return (
              <Button
                variant="ghost"
                className="p-0 font-bold text-lg bg-gray-100 hover:bg-gray-200 rounded-full w-6 flex items-center justify-center"
                onClick={(e) => {
                  e.stopPropagation();
                  row.toggleExpanded();
                  console.log("Toggled row:", row.id, "New expanded state:", row.getIsExpanded());
                }}
              >
                {row.getIsExpanded() ? "▾" : "▸"}
              </Button>
            );
          }
          return null;
        },
        aggregatedCell: ({ row }) => (
          <Button
            variant="ghost"
            className="h-auto p-0"
            onClick={(e) => {
              e.stopPropagation();
              row.toggleExpanded();
            }}>
            {row.getIsExpanded() ? "▾" : "▸"}
          </Button>
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      },
    );
  }

  // Add selection column if enabled
  if (tableConfig.enableRowSelection) {
    resultColumns.unshift({
      id: "select",
      header: ({ table }) => (
        <div className="pl-2 truncate">
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-0.5 cursor-pointer"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="truncate">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => {
              console.log("value", value);
              if (value) {
                console.log("row", row, "value", value, "row is checked", row.getIsSelected());
                row.toggleSelected(true);
              } else {
                console.log("row", row, "value", value);
                row.toggleSelected(false);
                // If we have a deselection handler, use it for better cross-page tracking
                if (handleRowDeselection) {
                  handleRowDeselection(row.id);
                }
              }
            }}
            aria-label="Select row"
            className="translate-y-0.5 cursor-pointer"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
      size: 50,
    },);
  }

  // Add actions column last if enabled
  if (tableConfig.enableActions) {
    resultColumns.push({
      id: 'actions',
      header: '',
      cell: ({ row, table }) => <DataTableRowActions row={row} table={table} />,
      size: 120,
    });
  }

  return resultColumns;
};
