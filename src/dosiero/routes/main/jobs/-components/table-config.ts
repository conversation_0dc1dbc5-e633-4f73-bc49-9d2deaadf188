import type { VariantDisplayColumnsTypes } from "@/api/_client";
import type { PathStateTypes, TableConfig } from "@/components/data-table/defaults";

// Default configuration
export const defaultConfig: TableConfig = {
  enableActions: true, // Actions enabled by default
  enableExpander: false, // Expander disabled by default
  enableRowSelection: true, // Row selection enabled by default
  enableKeyboardNavigation: false, // Keyboard navigation disabled by default
  enableClickRowSelect: false, // Clicking row to select disabled by default
  enablePagination: true, // Pagination enabled by default
  enableSearch: true, // Search enabled by default
  enableColumnFilters: true, // Column filters enabled by default
  enableDateFilter: true, // Date filter enabled by default
  enableColumnVisibility: true, // Column visibility options enabled by default
  enableExport: true, // Data export enabled by default
  enableUrlState: true, // URL state persistence enabled by default
  enableColumnResizing: true, // Column resizing enabled by default
  enableToolbar: true, // Toolbar enabled by default
  size: "default", // Default size for buttons and inputs
  columnResizingTableId: "jobs", // No table ID by default
  idField: "id",
  dateColumn: "start_date",
  pageSizeOptions: [20, 50, 100, 500, 1000, 3000, 5000],
  path: "", // inserted dynamically
};

export const tableDefaultVariant: VariantDisplayColumnsTypes = {
  id: 0,
  name: "Admin defaults",
  path: "",
  profile_id: 0,
  is_active: false,
  client_ops: {
    columnFilters: undefined,
    columnSizing: {},
    columnSizingInfo: {},
    rowSelection: {},
    rowPinning: {
      top: [],
      bottom: [],
    },
    expanded: {},
    grouping: [],
    sorting: [],
    columnPinning: {},
    columnOrder: [],
    columnVisibility: undefined,
    pagination: {
      pageIndex: 0,
      pageSize: 20,
    },
    start_date: null,
    end_date: null,
    date_column: "start_date",
  },
  server_ops: {
    filters: null,
    org_id: null,
    date_column: "start_date",
    start_date: null,
    end_date: null,
    page_index: 0,
    page_size: null,
    search: null,
    order: [{ column_name: "start_date", direction: "asc" }],
    columns: null,
  },
};

/**
 * Hook to provide table configuration
 * Allows overriding default configuration
 */
export function useTableConfig(overrideConfig?: Partial<TableConfig>): TableConfig {
  // Merge default config with any overrides
  const config = { ...defaultConfig, ...overrideConfig };

  return config;
}

export const tableConfig = defaultConfig;
