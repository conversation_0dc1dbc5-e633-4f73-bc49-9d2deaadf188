import { DevTool } from "@hookform/devtools";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import type { JobStatus } from "@types";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
// ** Import 3rd Party Libs
import type { z } from "zod";
import type { JobDisplayColumnsTypes } from "@/api/_client";
import { zJobCreateTypes, zJobDisplayTypes, zJobStatus } from "@/api/_client/zod.gen";
import { defaultJob } from "@/api/core/jobs/defaultJob";
import { useCreateJobMutation } from "@/api/core/jobs/hooks_mutations/useCreateJobMutation";
import { useDeleteJobMutation } from "@/api/core/jobs/hooks_mutations/useDeleteJobMutation";
import { useUpdateJobMutation } from "@/api/core/jobs/hooks_mutations/useUpdateJobMutation";
import { Button } from "@/components/_shadcn/components/ui/button";
import { Checkbox } from "@/components/_shadcn/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/_shadcn/components/ui/form";
import { Input } from "@/components/_shadcn/components/ui/input";
// ** Import UI Components
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import { Textarea } from "@/components/_shadcn/components/ui/textarea";
import { EndDateTimePicker } from "@/components/_ui/end-datetime-picker";
import { StartDateTimePicker } from "@/components/_ui/start-datetime-picker";
import FieldInfo from "@/components/field-info";
import { useAppSelector } from "@/utils/redux/hooks";

const JobStatusEnum = ["DRAFT", "CONFIRMED", "IN_PROGRESS", "COMPLETED", "CANCELLED"] as JobStatus[];

const JobTypes = [
  "CLIENT_SERVICES",
  "CLIENT_GOODS",
  "VENDOR",
  "CONTRACTOR",
  "MEMBER",
  "MEDIA",
  "EMPLOYEE",
  "DIRECTOR",
  "ADMIN",
] as const; // Add other types if they exist

type FormValues = z.infer<typeof zJobCreateTypes>;

export function JobCreateRhf() {
  const { t, i18n } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const userId = useAppSelector((state) => state.user?.data?.id);
  const orgId = useAppSelector((state) => state.user?.data?.curr_org_id);
  const createJobMutation = useCreateJobMutation();

  const form = useForm<FormValues>({
    resolver: zodResolver(zJobCreateTypes),
    defaultValues: useMemo(() => ({
      ...defaultJob,
      org_id: orgId,
      created_by: userId,
      status: "DRAFT" as JobStatus,
      type: "CLIENT_SERVICES",
      lang: i18n.language,
    }), [userId, orgId, i18n.language]),
  });

  const handleCreateJob = (data: FormValues) => {
    console.log("Creating job", data);
    createJobMutation.mutate({ body: [data] });
  };

  return (
    <>
      <div className="w-full">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleCreateJob)} className="space-y-4">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-9">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.type.label")}
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder={t("forms.JobForm.typePlaceholder.label")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {JobTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {t(`types.job.${type}.label`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-6 col-start-1">
                <FormField
                  control={form.control}
                  name="start_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.startDate.label")}
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <StartDateTimePicker
                          startDate={field.value as string}
                          setStartDate={(value) => {
                            field.onChange(value);
                            if (form.getValues("end_date") && new Date(value) > new Date(form.getValues("end_date"))) {
                              form.setValue("end_date", value);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-6">
                <FormField
                  control={form.control}
                  name="end_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.endDate.label")}
                      </FormLabel>
                      <FormControl>
                        <EndDateTimePicker
                          endDate={field.value as string}
                          setEndDate={field.onChange}
                          minDate={form.watch("start_date")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-12">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.name.label")}
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder={t("forms.JobForm.name.placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-12">
                <FormField
                  control={form.control}
                  name="tag"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.tag.label")}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder={t("forms.JobForm.tagPlaceholder.label")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-9">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.status.label")}
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder={t("forms.JobForm.statusPlaceholder.label")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {JobStatusEnum.map((status) => (
                            <SelectItem key={status} value={status}>
                              {t(`forms.JobForm.statusTypes.${status}.label`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-12">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                        {t("forms.JobForm.description.label")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("forms.JobForm.descriptionPlaceholder.label")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="col-span-12">
                <FormField
                  control={form.control}
                  name="is_public"
                  render={({ field }) => (
                    <FormItem className="flex items-start gap-2 mb-4">
                      <FormControl>
                        <Checkbox
                          className="w-5 h-5"
                          checked={field.value as boolean}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                          {t("forms.JobForm.is_public.label")}
                        </FormLabel>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex flex-col">
              <div className="flex justify-end gap-2">
                <Button
                  type="submit"
                  disabled={!form.formState.isValid || createJobMutation.isPending}
                >
                  {createJobMutation.isPending ? "..." : t("common.save.label")}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={(e) => {
                    e.preventDefault();
                    form.reset();
                  }}
                >
                  {t("common.reset.label")}
                </Button>
              </div>
              {!form.formState.isValid && (
                <p className="text-red-500 text-sm mt-2">{t("forms.common.invalidForm.label")}</p>
              )}
            </div>
          </form>
        </Form>
      </div>
      <DevTool control={form.control} />
    </>
  );
}