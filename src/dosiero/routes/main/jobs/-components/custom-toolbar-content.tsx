import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/_shadcn/components/ui/button";
import { Modal } from "@/components/_system/modal";

type CustomToolbarContentProps = {
  getAllItems: () => any;
  selectedRows: any[];
  totalSelectedCount: number;
  resetSelection: () => void;
};

// display custom action if items selected
const CustomToolbarContent = ({
  getAllItems,
  selectedRows,
  totalSelectedCount,
  resetSelection,
}: CustomToolbarContentProps) => {
  const [modalOpen, setModalOpen] = useState(false);
  const { t } = useTranslation();
  if (totalSelectedCount === 0) {
    return null; // Or return a custom placeholder
  }

  const handleDelete = () => {
    console.log("Items to delete:", selectedRows);
    resetSelection();
    setModalOpen(false);
  };

  return (
    <>
      <div className="flex items-center justify-between w-full ">
        <div className="flex-1 text-sm text-muted-foreground mr-4">
          {t("components.dataTable.pagination.selected.label")} {totalSelectedCount} {t("components.dataTable.pagination.of.label")} {getAllItems()?.length} {t("components.dataTable.pagination.rows.label")}.
        </div>
        <Button
          variant="destructive"
          size="sm"
          onClick={() => {
            setModalOpen(true);
            console.log("Items to delete:", selectedRows);

          }}
        >
          {t("components.dataTable.toolbar.deleteSelected.label")}
        </Button>
      </div>
      <Modal title={t("common.delete.label")} open={modalOpen} setOpen={setModalOpen}>
        <p>{`Delete ${totalSelectedCount} items?`}</p>
        <div className="flex gap-2 justify-end">
          <Button onClick={() => handleDelete()} variant="destructive">{t("common.delete.label")}</Button>
          <Button onClick={() => setModalOpen(false)}>{t("common.cancel.label")}</Button>
        </div>

      </Modal>
    </>
  );
};

export default CustomToolbarContent;
