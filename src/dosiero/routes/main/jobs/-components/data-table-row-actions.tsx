import type { ColumnDef, Row, Table } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import type { z } from "zod";
import type { JobDisplayColumnsTypes } from "@/api/_client/types.gen";
import { useDeleteJobMutation } from "@/api/core/jobs/hooks_mutations/useDeleteJobMutation";
import { Button } from "@/components/_shadcn/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/_shadcn/components/ui/dropdown-menu";
import { Modal } from "@/components/_system/modal";
import type { JobUpdateFormValues, JobUpdateTypes } from "@/dosiero/routes/main/jobs/-components/Job-validation";
import { JobUpdateForm } from "@/dosiero/routes/main/jobs/-components/job-update-form";

// The row-actions file and the DataTableRowActions component did not exist.
// This is a placeholder for the actions column.
// You can add buttons and functionality here.
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const DataTableRowActions = ({
  row,
  table,
}: {
  row: Row<JobDisplayColumnsTypes>;
  table: Table<JobDisplayColumnsTypes>;
}) => {
  const { t } = useTranslation();
  const [modalEditOpen, setModalEditOpen] = useState(false);
  const [modalDeleteOpen, setModalDeleteOpen] = useState(false);
  const deleteJobMutation = useDeleteJobMutation();

  const handleDelete = () => {
    console.log("Item to delete:", row.original);
    deleteJobMutation.mutate({
      path: {
        item_id: row.original.id
      }
    });
    setModalDeleteOpen(false);
  };

  return (
    <>
      <div className="relative flex items-center justify-end gap-2 w-full">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-10 w-10 p-0"
            >
              <span className="sr-only">{t("common.openMenu.label")}</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" forceMount>
            <DropdownMenuLabel>{t("components.dataTable.rowActions.label")}</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={() => {
                navigator.clipboard.writeText(row.original.id.toString());
                toast.success("ID copied to clipboard");
              }}
            >
              Copy ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />

            <DropdownMenuItem>View</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setModalEditOpen(true)}>Edit</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setModalDeleteOpen(true)}>Delete</DropdownMenuItem>

          </DropdownMenuContent>
        </DropdownMenu>

      </div>
      <Modal title={t("common.edit.label")} open={modalEditOpen} setOpen={setModalEditOpen}>
        <JobUpdateForm data={row.original as JobUpdateTypes} setModalOpen={setModalEditOpen} />
      </Modal>
      <Modal title={t("common.delete.label")} open={modalDeleteOpen} setOpen={setModalDeleteOpen}>
        <p>{`Delete item with ID ${row.original.id}?`}</p>
        <div className="flex gap-2 justify-end">
          <Button onClick={() => handleDelete()} variant="destructive">{t("common.delete.label")}</Button>
          <Button onClick={() => setModalDeleteOpen(false)}>{t("common.cancel.label")}</Button>
        </div>

      </Modal>
    </>
  );
};
