/**
 * JobCreateTypes
 */

import { z } from "zod";

/**
 * JobStatus
 */
export const zJobStatus = z.enum(["DRAFT", "CONFIRMED", "IN_PROGRESS", "COMPLETED", "CANCELLED"]);

/**
 * JobCreateTypes
 */
export const zJobCreateTypes = z.object({
  created_by: z.number().int(),
  name: z.string().min(1, { message: "forms.JobForm.validation.name.min.label" }),
  tag: z.union([z.string(), z.null()]).optional(),
  description: z
    .union([z.string().min(1, { message: "forms.JobForm.validation.description.min.label" }), z.null()])
    .optional(),
  lang: z.string(),
  json_metadata: z.union([z.object({}), z.null()]).optional(),
  proposal_id: z.union([z.number().int(), z.null()]).optional(),
  contrahent_id: z.union([z.number().int(), z.null()]).optional(),
  status: zJobStatus,
  type: z.string({ message: "forms.JobForm.validation.type.label" }),
  is_public: z.boolean(),
  start_date: z
    .string({ message: "forms.JobForm.validation.startDate.label" })
    .datetime({ message: "forms.JobForm.validation.datetime.label" }),
  end_date: z
    .string({ message: "forms.JobForm.validation.endDate.label" })
    .datetime({ message: "forms.JobForm.validation.datetime.label" })
    .optional(),
  budget: z.number(),
  org_id: z.number().int(),
  accepted_offer_id: z.union([z.number().int(), z.null()]).optional(),
  objects: z.union([z.array(z.number().int()), z.null()]).optional(),
  is_schedule: z.boolean(),
  is_regular: z.boolean(),
  sch_dates: z.union([z.array(z.string().date()), z.null()]).optional(),
  sch_start_date: z.union([z.string().date(), z.null()]).optional(),
  sch_end_date: z.union([z.string().date(), z.null()]).optional(),
  sch_interval: z.union([z.string(), z.null()]).optional(),
  interval_day: z.union([z.number().int(), z.null()]).optional(),
});

/**
 * JobUpdateTypes
 */
export const zJobUpdateTypes = z.object({
  id: z.number().int(),
  updated_by: z.number().int().optional().default(1),
  created_by: z.union([z.number().int(), z.null()]).optional(),
  name: z.union([z.string(), z.null()]).optional(),
  tag: z.union([z.string(), z.null()]).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  lang: z.union([z.string(), z.null()]).optional(),
  json_metadata: z.union([z.object({}), z.null()]).optional(),
  contrahent_id: z.union([z.number().int(), z.null()]).optional(),
  status: z.union([zJobStatus, z.null()]).optional(),
  type: z.union([z.string(), z.null()]).optional(),
  is_public: z.union([z.boolean(), z.null()]).optional(),
  start_date: z
    .string({ message: "forms.JobForm.validation.start_date.label" })
    .datetime({ message: "forms.JobForm.validation.datetime.label" })
    .optional(),
  end_date: z
    .string({ message: "forms.JobForm.validation.end_date.label" })
    .datetime({ message: "forms.JobForm.validation.datetime.label" })
    .optional(),
  budget: z.union([z.number(), z.null()]).optional(),
  org_id: z.union([z.number().int(), z.null()]).optional(),
  accepted_offer_id: z.union([z.number().int(), z.null()]).optional(),
  is_schedule: z.union([z.boolean(), z.null()]).optional(),
  is_regular: z.union([z.boolean(), z.null()]).optional(),
  sch_dates: z.union([z.array(z.string().date()), z.null()]).optional(),
  sch_start_date: z.union([z.string().date(), z.null()]).optional(),
  sch_end_date: z.union([z.string().date(), z.null()]).optional(),
  sch_interval: z.union([z.string(), z.null()]).optional(),
  interval_day: z.union([z.number().int(), z.null()]).optional(),
});

export type JobCreateTypes = z.infer<typeof zJobCreateTypes>;
export type JobCreateFormValues = Partial<z.infer<typeof zJobCreateTypes>>;

export type JobUpdateTypes = z.infer<typeof zJobUpdateTypes>;
export type JobUpdateFormValues = Partial<z.infer<typeof zJobUpdateTypes>>;

// we MUST include all fields we want validated
export const defaultJob: JobCreateFormValues = {
  created_by: 0,
  name: "",
  //   tag: "",
  //   description: "",
  lang: "pl",
  //   json_metadata: {},
  contrahent_id: null,
  status: "DRAFT",
  is_public: false,
  //   start_date: "",
  //   end_date: "",
  budget: 0,
  org_id: 0,
  accepted_offer_id: null,
  is_schedule: false,
  is_regular: false,
  sch_dates: [],
  sch_start_date: null,
  sch_end_date: null,
  sch_interval: null,
  interval_day: null,
};

// we MUST include all fields we want validated
export const defaultJobUpdate: JobUpdateFormValues = {
  id: 0,
  //   updated_by: 0,
  created_by: 0,
  name: "",
  //   tag: "",
  //   description: "",
  lang: "pl",
  //   json_metadata: {},
  contrahent_id: null,
  status: "DRAFT",
  is_public: false,
  //   start_date: "",
  //   end_date: "",
  budget: 0,
  org_id: 0,
  accepted_offer_id: null,
  is_schedule: false,
  is_regular: false,
  sch_dates: [],
  sch_start_date: null,
  sch_end_date: null,
  sch_interval: null,
  interval_day: null,
};
