import type { Any<PERSON>ield<PERSON><PERSON> } from "@tanstack/react-form";
import { useForm, useStore } from "@tanstack/react-form";
import { useQueryClient } from "@tanstack/react-query";
import type { JobStatus } from "@types";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
// ** Import 3rd Party Libs
import type { z } from "zod";
import { useUpdateJobMutation } from "@/api/core/jobs/hooks_mutations/useUpdateJobMutation";
import { Button } from "@/components/_shadcn/components/ui/button";
import { Checkbox } from "@/components/_shadcn/components/ui/checkbox";
import { Input } from "@/components/_shadcn/components/ui/input";
// ** Import UI Components
import { Label } from "@/components/_shadcn/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import { Textarea } from "@/components/_shadcn/components/ui/textarea";
import { EndDateTimePicker } from "@/components/_ui/end-datetime-picker";
import { StartDateTimePicker } from "@/components/_ui/start-datetime-picker";
import FieldInfo from "@/components/field-info";
import type { JobUpdateFormValues, JobUpdateTypes } from "@/dosiero/routes/main/jobs/-components/Job-validation";
import { defaultJob, zJobStatus, zJobUpdateTypes } from "@/dosiero/routes/main/jobs/-components/Job-validation";
import { getUpdatedFields, onFieldChange, sanitizeInput } from "@/utils/formHelpers";
import { useAppSelector } from "@/utils/redux/hooks";


const JobStatusEnum = ["DRAFT", "CONFIRMED", "IN_PROGRESS", "COMPLETED", "CANCELLED"] as JobStatus[];

const JobTypes = [
  "CLIENT_SERVICES",
  "CLIENT_GOODS",
  "VENDOR",
  "CONTRACTOR",
  "MEMBER",
  "MEDIA",
  "EMPLOYEE",
  "DIRECTOR",
  "ADMIN",
] as const; // Add other types if they exist

type FormProps = {
  data: JobUpdateTypes;
  setModalOpen: (open: boolean) => void;
}



export function JobUpdateForm({ data, setModalOpen }: FormProps) {
  const { t } = useTranslation();
  const [changedFields, setChangedFields] = useState<Set<keyof JobUpdateFormValues>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const userId = useAppSelector((state) => state.user?.data?.id);

  const updateJobMutation = useUpdateJobMutation();



  // Store initial values in a ref to prevent re-renders
  const initialFormState = useMemo(() => {
    return {
      ...data,
      updated_by: userId

    } as JobUpdateFormValues;
  }, [data, userId]);

  const form = useForm({
    defaultValues: initialFormState,
    validators: {
      onChange: zJobUpdateTypes,
    },
    onSubmit: async ({ value }) => {
      console.log("Editing job", value);
      // @ts-expect-error - We're validating on user input
      handleUpdateJob(value);
    },
  });

  const handleUpdateJob = (value: JobUpdateTypes) => {

    const updatedFields = getUpdatedFields<JobUpdateFormValues>(value, changedFields)

    console.log("updatedFields", updatedFields);
    console.log("changedFields", Array.from(changedFields));

    updateJobMutation.mutate({ body: [{ ...updatedFields, id: data?.id as number, updated_by: userId }] }, {
      onSettled: () => {
        setModalOpen(false);
      },
    });
  };



  // // DEV ONLY !!!
  const formErrorMap = useStore(form.store, (state) => state.errorMap);
  console.log("errorMap", formErrorMap);
  const formState = useStore(form.store, (state) => state);
  console.log("formState", formState);

  return (
    <>
      <pre>{JSON.stringify(form.state.values, null, 2)}</pre>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        <div className="grid grid-cols-12 gap-2">
          <div className="col-span-9">
            <form.Field
              name="type"
              children={(field) => {
                return (
                  <div className="flex flex-col ">
                    <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2" htmlFor={field.name}>
                      {t("forms.JobForm.type.label")}
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <Select
                      value={field.state.value || ""}
                      onValueChange={(value) => {
                        field.handleChange(value);
                        onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t("forms.JobForm.typePlaceholder.label")} />
                      </SelectTrigger>
                      <SelectContent>
                        {JobTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {t(`types.job.${type}.label`)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldInfo field={field} />
                  </div>
                );
              }}
            />
          </div>
          <div className="col-span-6 col-start-1">
            <form.Field
              name="start_date"
              children={(field) => {
                return (
                  <>
                    {" "}
                    <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                      {t("forms.JobForm.startDate.label")}
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <StartDateTimePicker
                      startDate={field.state.value as string}
                      setStartDate={(value) => {
                        field.handleChange(value);
                        onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                      }}
                    />
                    <FieldInfo field={field} />
                  </>
                );
              }}
            />
          </div>
          <div className="col-span-6">
            <form.Field
              name="end_date"
              children={(field) => {
                return (
                  <>
                    {" "}
                    <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2">
                      {t("forms.JobForm.endDate.label")}
                    </Label>
                    <EndDateTimePicker
                      endDate={field.state.value as string}
                      setEndDate={(value) => {
                        field.handleChange(value);
                        onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                      }}
                    />
                    <FieldInfo field={field} />
                  </>
                );
              }}
            />
          </div>
          <div className="col-span-12">
            <form.Field
              name="name"
              children={(field) => {
                // console.log("field name", field);
                return (
                  <>
                    <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2" htmlFor={field.name}>
                      {t("forms.JobForm.name.label")}
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <Input
                      type="text"
                      id={field.name}
                      name={field.name}
                      placeholder={t("forms.JobForm.namePlaceholder.label")}
                      value={field.state.value || ""}
                      onBlur={field.handleBlur}
                      onChange={(e) => {
                        console.log("e.target.value", e.target.value);
                        const sanitazed = sanitizeInput(e.target.value);
                        console.log("sanitazed name:", sanitazed);
                        field.handleChange(sanitazed);
                        onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                      }}
                    />
                    <FieldInfo field={field} />
                  </>
                );
              }}
            />
          </div>
          <div className="col-span-12">
            <form.Field
              name="tag"
              children={(field) => (
                <div className="flex flex-col">
                  <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2" htmlFor={field.name}>
                    {t("forms.JobForm.tag.label")}
                  </Label>
                  <Input
                    type="text"
                    id={field.name}
                    name={field.name}
                    placeholder={t("forms.JobForm.tagPlaceholder.label")}
                    value={field.state.value || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => {
                      field.handleChange(e.target.value || null);
                      onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                    }}
                  />
                  <FieldInfo field={field} />
                </div>
              )}
            />
          </div>
          <div className="col-span-9">
            <form.Field
              name="status"
              children={(field) => (
                <div className="flex flex-col">
                  <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2" htmlFor={field.name}>
                    {t("forms.JobForm.status.label")}
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Select
                    value={field.state.value || ""}
                    onValueChange={(value) => {
                      field.handleChange(value as JobStatus);
                      onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={t("forms.JobForm.statusPlaceholder.label")} />
                    </SelectTrigger>
                    <SelectContent>
                      {JobStatusEnum.map((status) => (
                        <SelectItem key={status} value={status}>
                          {t(`forms.JobForm.statusTypes.${status}.label`)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FieldInfo field={field} />
                </div>
              )}
            />
          </div>
          <div className="col-span-12">
            <form.Field
              name="description"
              children={(field) => (
                <div className="flex flex-col ">
                  <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2" htmlFor={field.name}>
                    {t("forms.JobForm.description.label")}
                  </Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    placeholder={t("forms.JobForm.descriptionPlaceholder.label")}
                    value={field.state.value || undefined}
                    onBlur={field.handleBlur}
                    onChange={(e) => {
                      field.handleChange(e.target.value);
                      onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                    }}
                  />
                  <FieldInfo field={field} />
                </div>
              )}
            />
          </div>
          <div className="col-span-12">
            <form.Field
              name="is_public"
              children={(field) => (
                <div className="flex items-start gap-2 mb-4">
                  <Checkbox
                    className="w-5 h-5"
                    id={field.name}
                    name={field.name}
                    checked={field.state.value || false}
                    onCheckedChange={(checked) => {
                      field.handleChange(checked as boolean);
                      onFieldChange({ fieldPath: field.name, setChangedFields, form, data });
                    }}
                  />
                  <Label className="block text-sm font-medium text-gray-700 mb-1 ml-2" htmlFor={field.name}>
                    {t("forms.JobForm.is_public.label")}
                  </Label>
                  <FieldInfo field={field} />
                </div>
              )}
            />
          </div>
        </div>

        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
          children={([canSubmit, isSubmitting]) => (
            <>
              <div className="flex justify-end gap-2">
                <Button type="submit" disabled={!canSubmit || changedFields.size === 0}>
                  {isSubmitting ? "..." : t("common.save.label")}
                </Button>
                <Button
                  type="reset"
                  variant="outline"
                  onClick={(e) => {
                    // Avoid unexpected resets of form elements (especially <select> elements)
                    e.preventDefault();
                    form.reset();
                  }}
                >
                  {t("common.reset.label")}
                </Button>
              </div>
              {!canSubmit && <p className="text-red-500 text-sm mt-2">{t("forms.common.invalidForm.label")}</p>}
            </>
          )}
        />
      </form>
    </>
  );
}
