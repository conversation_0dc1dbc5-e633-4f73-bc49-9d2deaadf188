import { QueryClient, QueryClientProvider, useQueryClient } from "@tanstack/react-query";
import { createRouter, RouterProvider } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { hasAuth<PERSON>ara<PERSON>, useAuth } from "react-oidc-context";
import { Provider as ReduxProvider, useSelector } from "react-redux";
import { client } from "@/api/_client/client.gen";
import { Spinner } from "@/components/_system/Spinner";
import { useUserData } from "@/hooks/useUserData.ts";
import type { RouteContext } from "@/types/router";
import store, { type RootStateTypes } from "@/utils/redux/store";
import { router } from "@/utils/router";

// import { routeTree } from "./routeTree.gen";

// Log all PUBLIC_ environment variables used in the codebase
// console.log("PUBLIC envs:", {
//   PUBLIC_API_BASE_URL: import.meta.env.PUBLIC_API_BASE_URL,
//   PUBLIC_KC_URL: import.meta.env.PUBLIC_KC_URL,
//   PUBLIC_KC_REALM: import.meta.env.PUBLIC_KC_REALM,
//   PUBLIC_KC_CLIENT_ID: import.meta.env.PUBLIC_KC_CLIENT_ID,
//   PUBLIC_ENV: import.meta.env.PUBLIC_ENV,
// });

function App() {
  const auth = useAuth();
  const [hasTriedSignin, setHasTriedSignin] = useState(false);
  const queryClient = useQueryClient();
  const { isLoading: isProfileLoading } = useUserData();
  const curr_org_id = useSelector((state: RootStateTypes) => state.user?.data?.curr_org_id || 0);
  const curr_profile_id = useSelector((state: RootStateTypes) => state.user?.data?.curr_profile_id || 0);
  const id = useSelector((state: RootStateTypes) => state.user?.data?.id || 0);
  // const { curr_acc_period_id } = useSelector((state: RootStateTypes) => state.org);

  // console.log("auth token", auth.user?.access_token);
  // console.log("auth    ", auth);
  const context = {
    queryClient,
    org_id: curr_org_id,
    profile_id: curr_profile_id,
    user_id: id,
    locale: "pl",
  };

  /**
   * Automatic sign-in
   *
   * See {@link https://github.com/authts/react-oidc-context?tab=readme-ov-file#automatic-sign-in}
   */
  useEffect(() => {
    if (!hasAuthParams() && !auth.isAuthenticated && !auth.activeNavigator && !auth.isLoading && !hasTriedSignin) {
      auth.signinRedirect();
      setHasTriedSignin(true);
    }
  }, [auth, hasTriedSignin]);

  if (auth.isLoading || isProfileLoading) {
    return (
      <div className="flex justify-center items-center h-24 mt-48">
        <Spinner size={96} message="Loading... (it may take a while for the first time, just have some coffee~ ☕️)" />
      </div>
    );
  }

  if (auth.error || !auth.isAuthenticated) {
    return (
      <div role="alert" className="alert alert-error">
        {auth.error ? (
          <Spinner
            size={96}
            message="😬 Ops, login error: {auth.error.message} (checkout Keycloak status and configuration)"
          />
        ) : (
          <Spinner
            size={96}
            message="🤔 You're still not authenticated, and I don't know why... Maybe you can find out! "
          />
        )}
      </div>
    );
  }



  // Register the router instance for type safety
  // declare module "@tanstack/react-router" {
  //   interface Register {
  //     router: typeof router;
  //   }
  // }

  return (
    // <ReduxProvider store={store}>
    // <QueryClientProvider client={queryClient}>
    <RouterProvider router={router} context={context} />
    // </QueryClientProvider>
    // </ReduxProvider>
  );
}

export default App;
