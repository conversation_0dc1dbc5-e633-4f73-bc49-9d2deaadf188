{"common": {"loading": {"label": "Loading..."}, "error": {"label": "Error"}, "success": {"label": "Success"}, "save": {"label": "Save"}, "update": {"label": "Save changes"}, "cancel": {"label": "Cancel"}, "close": {"label": "Close"}, "delete": {"label": "Delete"}, "edit": {"label": "Edit"}, "create": {"label": "Create"}, "register": {"label": "Register"}, "search": {"label": "Search"}, "filter": {"label": "Filter"}, "noResults": {"label": "No results"}, "required": {"label": "Required"}, "updating": {"label": "Updating..."}, "updateSuccess": {"label": "Successfully updated"}, "updateError": {"label": "Failed to update"}, "thumbnail": {"label": "Thumbnail for {{name}}"}, "failed": {"label": "Failed!"}, "proposal": {"label": "Proposal"}, "job": {"label": "Job"}, "jobs": {"label": "Jobs"}, "offer": {"label": "Offer"}, "task": {"label": "Task"}, "tasks": {"label": "Tasks"}, "addTask": {"label": "Add task"}, "voting": {"label": "Voting"}, "accept": {"label": "Accept"}, "reject": {"label": "Reject"}, "acceptOffer": {"label": "Accept offer"}, "rejectOffer": {"label": "Reject offer"}, "acceptJob": {"label": "Accept job"}, "rejectJob": {"label": "Reject job"}, "acceptProposal": {"label": "Accept proposal"}, "rejectProposal": {"label": "Reject proposal"}, "objects": {"label": "Objects"}, "object": {"label": "Object"}, "remove": {"label": "Remove trip"}, "attachements": {"label": "Attachments"}, "comments": {"label": "Comments"}, "noTasks": {"label": "No tasks"}, "noJobs": {"label": "No jobs"}, "noTransactions": {"label": "No transactions"}, "addDescription": {"label": "Add description"}, "hideDescription": {"label": "Hide description"}, "of": {"label": "of"}}, "modules": {"dashboard": {"label": "Dashboard", "items": {"overview": {"label": "Dashboard"}, "documents": {"label": "Documents"}, "jobs": {"label": "Jobs", "description": "Jobs of organization", "items": {"create": {"label": "Add job", "description": "Add new job"}, "calendar-jobs-objects": {"label": "Calendar jobs", "description": "Calendar jobs and related objects"}, "view": {"label": "Job details", "description": "Job details of organization"}, "edit": {"label": "Edit job", "description": "Edit job of organization"}}}, "offers": {"label": "Offers"}}}, "member": {"label": "Member", "description": "Member of organization", "items": {"emails": {"label": "Emails", "description": "Emails of member"}, "proposals": {"label": "Proposals", "description": "Proposals of member"}, "comments": {"label": "Comments", "description": "Comments of member"}, "finances": {"label": "Finances", "description": "Finances of member"}, "member-settings": {"label": "Settings", "description": "Settings of member", "items": {"profile": {"label": "Active profile", "description": "Active profile settings"}, "email-settings": {"label": "Email settings", "description": "Email settings of member"}}}, "profiles": {"label": "Orgs/Profiles", "description": "Orgs/Profiles of member", "items": {"create": {"label": "New Org/Profile", "description": "Create new Org/Profile"}}}}}, "communication": {"label": "Communication", "items": {"polls": {"label": "Polls"}, "proposal-comments": {"label": "Proposal Comments"}, "notices": {"label": "Notices"}, "forum-topics": {"label": "Forum Topics"}, "emails": {"label": "Emails"}}}, "proposals": {"label": "Proposals", "items": {"create": {"label": "Create Proposal"}, "active": {"label": "Active"}, "accepted": {"label": "Accepted"}, "rejected": {"label": "Rejected"}}, "job": {"name": {"label": "Job name"}}, "task": {"name": {"label": "Task name"}, "description": {"label": "Task description"}}, "drawer": {"newJob": {"label": "New Job"}, "selectObjects": {"label": "Select Objects"}, "newTask": {"label": "New Task"}}, "actions": {"addJob": {"label": "Add Job"}, "selectObjects": {"label": "Select Objects"}}}, "objects": {"label": "Objects", "description": "Organization objects", "items": {"new": {"label": "New Object"}, "list": {"label": "Objects Table"}}}, "documents": {"label": "Documents", "description": "Documents of organization", "items": {"list": {"label": "Documents list", "description": "Documents list of organization", "items": {"resolutions": {"label": "Resolutions", "description": "Resolutions list of organization"}, "contracts": {"label": "Contracts", "description": "Contracts list of organization"}, "invoices": {"label": "Invoices", "description": "Invoices list of organization"}, "formal": {"label": "Formal", "description": "Formal list of organization"}}}, "create": {"label": "Create document", "description": "Create document for organization"}, "templates": {"label": "Templates", "description": "Templates of documents for organization"}}}, "schedule": {"label": "Schedule", "items": {"jobs": {"label": "Jobs"}, "tasks": {"label": "Tasks"}, "payments": {"label": "Payments"}, "liabilities": {"label": "Liabilities"}}}, "jobs": {"label": "Jobs", "items": {"active-offers": {"label": "Active Offers"}, "current": {"label": "Current"}, "upcoming": {"label": "Upcoming"}, "finished": {"label": "Finished"}, "archived": {"label": "Archived"}}}, "crm": {"label": "CRM", "description": "CRM", "items": {"types": {"label": "Types", "description": "Types of contrahents", "items": {"admins": {"label": "Admins", "description": "Admins of organization"}, "members": {"label": "Members", "description": "Members of organization"}, "vendors": {"label": "Vend<PERSON>", "description": "Suppliers of goods"}, "contractors": {"label": "Contractors", "description": "Contractors of services"}, "media": {"label": "Media", "description": "Media of organization"}, "clients-goods": {"label": "Clients Goods", "description": "Clients of goods"}, "clients-services": {"label": "Clients Services", "description": "Clients of services"}, "employees": {"label": "Employees", "description": "Employees of organization"}, "directors": {"label": "Directors", "description": "Directors of organization"}}}, "contacts": {"label": "Contacts", "description": "Contacts details"}, "addresses": {"label": "Addresses", "description": "Addresses details"}}}, "money": {"label": "Money", "items": {"transactions": {"label": "Transactions", "items": {"create": {"label": "New transaction"}, "templates": {"label": "Templates"}, "templates_create": {"label": "Add template"}, "schedule": {"label": "Scheduled transactions"}, "schedule_create": {"label": "Add scheduled transaction"}, "import": {"label": "Import csv"}}}, "ballance": {"label": "Balance"}, "trips": {"label": "Trips", "items": {"create": {"label": "New trip"}}}, "reports": {"label": "Reports", "items": {"ballance_sheet": {"label": "Ballance sheet"}, "profit": {"label": "Profit-Loss"}, "zlecenia": {"label": "Jobs"}, "kontrahenci": {"label": "Contrahents"}}}, "payroll": {"label": "Payroll", "items": {"run": {"label": "Pay day"}, "history": {"label": "History"}}}, "settings": {"label": "Settings", "items": {"accounting_periods": {"label": "Accounting periods"}, "accounts": {"label": "Accounts"}, "milage_tresholds": {"label": "Mileage thresholds"}}}}}, "org-config": {"label": "Organization Configuration", "description": "Organization configuration", "items": {"members": {"label": "Members", "description": "Organization members list"}, "storage": {"label": "Files", "description": "Organization files configuration"}, "notices": {"label": "Notices", "description": "Organization notices list", "items": {"create": {"label": "New Notice", "description": "Create new notice"}}}, "contrahents": {"label": "Contrahents", "description": "Organization contrahents list", "items": {"create": {"label": "New Contrahent", "description": "Create new contrahent"}}}, "job-templates": {"label": "Job Templates", "description": "Job templates list", "items": {"create": {"label": "Create Template", "description": "Create new job template"}}}, "accounting": {"label": "Accounting", "description": "Organization accounting configuration", "items": {"accounts": {"label": "Accounting accounts", "description": "Organization accounting accounts list"}, "accounts-sets": {"label": "Accounting sets", "description": "Organization accounting sets list"}, "accounting-periods": {"label": "Accounting periods", "description": "Organization accounting periods list"}}}, "milage": {"label": "<PERSON><PERSON>", "description": "Organization milage configuration", "items": {"vehicles": {"label": "Vehicles", "description": "Organization vehicles list"}, "milage-tresholds": {"label": "Milage thresholds", "description": "Organization milage thresholds list"}}}}}, "superadmin": {"label": "Superadmin", "description": "Superadmin", "items": {"users": {"label": "Users", "description": "Users list", "items": {"register": {"label": "Register", "description": "Register new user"}, "update": {"label": "Update", "description": "Update user"}, "delete": {"label": "Delete", "description": "Delete user"}}}, "orgs": {"label": "Organizations", "description": "Organizations list", "items": {"create": {"label": "Create Organization", "description": "Create new organization"}}}, "object-types": {"label": "Object Types", "description": "Object types list"}, "contrahents-types": {"label": "Contrahent Types", "description": "Contrahent types list"}, "email-types": {"label": "Email Types", "description": "Email types list"}}}}, "components": {"CalendarJobsObjects": {"objects": {"label": "Objects"}, "saveConfig": {"label": "Save configuration"}, "cellOptions": {"label": "Cell options"}, "addJob": {"label": "Add job"}, "jobOptions": {"label": "Job options"}, "viewNewPage": {"label": "Job details (new page)"}, "fullEdit": {"label": "Full edit (new page)"}, "daysBack": {"label": "Days back"}, "daysForward": {"label": "Days forward"}}, "fileDisplay": {"loadingFile": {"label": "Loading file..."}, "fileRestricted": {"label": "Unable to display the file. It may be restricted."}, "openDirectly": {"label": "Try opening the file directly"}, "unsupportedType": {"label": "Unsupported file type. Download the file instead:"}, "downloadFile": {"label": "Download File"}, "fetchError": {"label": "Error fetching file data"}}, "proposalPage": {"drawer": {"newJob": {"label": "New Job"}, "selectObjects": {"label": "Select Objects"}, "newTask": {"label": "Add Task", "name": {"label": "Task"}, "description": {"label": "Task description"}, "withPhoto": {"label": "Photo required to confirm"}, "order": {"label": "Order"}}}, "actions": {"addJob": {"label": "Add Job"}, "selectObjects": {"label": "Select Objects"}}}, "tplAccountsTable": {"cloneModal": {"title": {"label": "<PERSON><PERSON> Template Account"}, "newSetName": {"label": "New set name"}, "confirm": {"label": "<PERSON><PERSON>"}}, "cloneSet": {"label": "Clone to new set"}, "cloneToOrg": {"label": "Clone to org"}, "deleteSet": {"label": "Delete set"}, "newAccount": {"label": "New account"}}, "emailSettings": {"pageTitle": {"label": "Email Notifications"}, "pageDescription": {"label": "Manage email notification preferences. Select which updates you want to receive."}, "new_comment_on_proposal": {"label": "New comment on observed proposal"}, "new_comment_on_my_proposal": {"label": "New comment on your proposal"}, "new_proposal": {"label": "New proposal created"}, "proposal_voting_started": {"label": "Proposal voting started"}, "proposal_new_vote": {"label": "New vote cast on observed proposal"}, "daily_voting_reminder": {"label": "Daily voting reminder"}, "end_of_voting": {"label": "End of voting notification"}, "new_job": {"label": "New job published matching your interests"}, "new_offer": {"label": "New offer received"}, "new_transaction": {"label": "New transaction occurred"}, "switchOn": {"label": "ON"}, "switchOff": {"label": "OFF"}, "languageToggle": {"label": "Change Language"}}, "storage": {"authorizeButton": {"label": "Authorize Google Drive"}, "removeAuthorization": {"label": "Remove authorization"}, "selectFiles": {"label": "Select files to upload"}, "selectFilesPlaceholder": {"label": "Click to select files"}, "uploadFiles": {"label": "Upload Files"}, "selectSingleFile": {"label": "Select a file to upload"}, "selectSingleFilePlaceholder": {"label": "Click to select a file"}, "uploadFile": {"label": "Upload File"}, "authError": {"label": "Failed to authorize storage"}, "authSuccess": {"label": "Storage authorized successfully"}, "camera": {"title": {"label": "Take Photo"}, "capture": {"label": "Capture Photo"}}, "fileDisplay": {"loading": {"label": "Loading files..."}, "error": {"label": "Error loading files"}, "noFiles": {"label": "No files available"}, "fileName": {"label": "File name"}, "fileId": {"label": "File ID"}, "url": {"label": "URL"}}}, "forms": {"ProposalForm": {"titleEdit": {"label": "Edit Proposal"}, "titleNew": {"label": "Create New Proposal"}, "title": {"label": "Title"}, "description": {"label": "Description"}, "motivation": {"label": "Motivation"}, "proposedBudget": {"label": "Proposed Budget"}, "budget": {"label": "Budget"}, "isFormal": {"label": "Is Formal?"}, "votingByShares": {"label": "Voting By Shares?"}, "rownowagaResolution": {"label": "Rownowaga Resolution"}, "options": {"ADMIN": {"label": "<PERSON><PERSON> is making the decision"}, "REJECT": {"label": "We reject the proposal"}, "ACCEPT": {"label": "We accept the proposal"}}, "proposedEndDate": {"label": "Proposed End Date"}, "status": {"label": "Status"}, "status.options.draft": {"label": "Draft"}, "status.options.voting": {"label": "Voting"}, "status.options.accepted": {"label": "Accepted"}, "status.options.acceptedInProgress": {"label": "Accepted - In Progress"}, "status.options.acceptedCompleted": {"label": "Accepted - Completed"}, "status.options.rejected": {"label": "Rejected"}, "status.options.cancelled": {"label": "Cancelled"}}, "ObjectForm": {"titleNew": {"label": "New object of: "}, "titleEdit": {"label": "Editing: "}, "name": {"label": "Name"}, "description": {"label": "Description"}, "objectType": {"label": "Object Type"}, "length": {"label": "Length"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "height": {"label": "Height"}, "surface": {"label": "Surface"}, "volume": {"label": "Volume"}, "circumference": {"label": "Circumference"}, "calculateVolume": {"label": "Calculate Volume"}, "isBranch": {"label": "Is Branch"}, "error": {"label": "Error occurred while updating object"}, "updateSuccess": {"label": "Object updated successfully"}}, "UserProfileForm": {"titleEdit": {"label": "Edit User Profile"}, "titleNew": {"label": "New User Profile"}, "displayName": {"label": "Display Name"}, "description": {"label": "Description"}, "isCompany": {"label": "Is Company"}, "isVoting": {"label": "Is Voting"}, "shares": {"label": "Shares"}, "objectId": {"label": "Object ID"}, "znaczacaOperacjaLimit": {"label": "Significant Operation Limit"}}, "profile": {"title": {"label": "Profile Settings"}, "displayName": {"label": "Display Name", "placeholder": "Enter your display name", "description": "This is your public display name", "error": "Display name must be at least 2 characters."}, "description": {"label": "Description", "placeholder": "Tell us a little bit about yourself"}, "company": {"label": "Company Account", "description": "Mark this account as a company account"}, "voting": {"label": "Is Voting", "description": "Enable voting rights for this account"}, "shares": {"label": "Shares", "placeholder": "Number of shares"}, "organization": {"label": "Organization Name", "placeholder": "Enter organization name"}, "operationLimit": {"label": "Operation Limit", "placeholder": "Enter operation limit"}, "updateSuccess": {"label": "Profile updated."}, "submit": {"label": "Update Profile"}}, "SystemType": {"name": {"label": "System Name"}, "description": {"label": "Description"}, "systemType": {"label": "System Type"}, "nameError": {"label": "Name must be at least 2 characters long"}, "selectType": {"label": "Select system type"}, "types": {"water": {"label": "Water"}, "electric": {"label": "Electric"}, "gas": {"label": "Gas"}, "lowVoltage": {"label": "Low Voltage"}, "ventilation": {"label": "Ventilation"}, "other": {"label": "Other"}}}, "AddSystem": {"addSystem": {"label": "Add System"}, "addSystemFile": {"label": "Add System File"}, "selectSystem": {"label": "Select System"}}, "org": {"titleNew": {"label": "New Organization"}, "titleEdit": {"label": "Edit Organization"}, "name": {"label": "Organization Name"}, "description": {"label": "Description"}, "isPublic": {"label": "Public Organization"}, "isFormal": {"label": "Formal Organization"}, "nip": {"label": "NIP"}, "regon": {"label": "REGON"}, "maxImgWidth": {"label": "Maximum Image Width"}, "totalShares": {"label": "Total Shares"}, "votingDays": {"label": "Voting Days"}, "membersByAdmin": {"label": "Members managed by <PERSON><PERSON>"}, "error": {"label": "Error occurred while updating organization"}, "updateSuccess": {"label": "Organization updated successfully"}}, "s3": {"titleEdit": {"label": "Edit S3 Storage Configuration"}, "titleNew": {"label": "New S3 Storage Configuration"}, "bucketName": {"label": "Bucket Name"}, "region": {"label": "Region"}, "accessKeyId": {"label": "Access Key ID"}, "secretAccessKey": {"label": "Secret Access Key"}, "accountId": {"label": "Account ID"}, "endpoint": {"label": "Endpoint"}}, "S3Form": {"bucketRequired": {"label": "Bucket name is required"}}, "JobForm": {"titleEdit": {"label": "Edit job"}, "titleNew": {"label": "New job"}, "id": {"label": "Id"}, "createdAt": {"label": "Created At"}, "updatedAt": {"label": "Updated At"}, "createdBy": {"label": "Created By"}, "updatedBy": {"label": "Updated By"}, "name": {"label": "Name"}, "description": {"label": "Description"}, "lang": {"label": "Language"}, "jsonMetadata": {"label": "JSON Metadata"}, "proposalId": {"label": "Proposal Id"}, "status": {"label": "Status"}, "startDate": {"label": "Start Date"}, "endDate": {"label": "End Date"}, "budget": {"label": "Budget"}, "orgId": {"label": "Organization Id"}, "acceptedOfferId": {"label": "Accepted Offer Id"}}, "OfferForm": {"titleEdit": {"label": "Edit Offer"}, "titleNew": {"label": "<PERSON> Offer"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated By"}, "name": {"label": "Name"}, "description": {"label": "Description"}, "lang": {"label": "Language"}, "jsonMetadata": {"label": "JSON Metadata"}, "jobId": {"label": "Job ID"}, "contractorId": {"label": "Contractor ID"}, "contactId": {"label": "Contact ID"}, "orgId": {"label": "Organization ID"}, "price": {"label": "Price"}, "startDate": {"label": "Start Date"}, "finishDate": {"label": "Finish Date"}, "expiryDate": {"label": "Expiry Date"}, "status": {"label": "Status"}}, "OrgAccountForm": {"titleEdit": {"label": "Edit account"}, "titleNew": {"label": "New account"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated By"}, "name": {"label": "Name"}, "nameShort": {"label": "Short Name"}, "label": {"label": "Label"}, "labelShort": {"label": "Short Label"}, "description": {"label": "Description"}, "groupId": {"label": "Group"}, "type": {"label": "Type"}, "isDebitMinus": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> minus"}, "accNumber": {"label": "Account Number"}, "currency": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "parentId": {"label": "Parent ID"}, "isBranch": {"label": "Branch"}, "isPlaceholder": {"label": "Placeholder"}, "isActive": {"label": "Active"}, "balance": {"label": "Balance"}, "isSyntetyczne": {"label": "Syntetyczne"}, "enabled": {"label": "Enabled"}}, "TransactionForm": {"titleEdit": {"label": "Edit transaction"}, "titleNew": {"label": "New transaction"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated By"}, "name": {"label": "Transaction name"}, "description": {"label": "Transaction description"}, "date": {"label": "Transaction date"}, "type": {"label": "Transaction type"}, "dueDate": {"label": "Payment due date"}, "amount": {"label": "Amount"}, "jobId": {"label": "Job ID"}, "comment": {"label": "Comment"}, "objectId": {"label": "Object ID"}, "postedAt": {"label": "Posted at"}, "custRef": {"label": "Customer reference"}, "ourRef": {"label": "Our reference"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "Contractor ID"}, "setId": {"label": "Set ID"}, "contrahent": {"label": "Contractor"}, "transaction": {"label": "Transaction"}, "createTransaction": {"label": "Create transaction"}, "updateTransaction": {"label": "Update transaction"}, "isSaved": {"label": "Save as new template"}, "splits": {"account": {"label": "Account"}, "credit": {"label": "Credit"}, "debit": {"label": "Debit"}, "addSplit": {"label": "Add split"}}, "validation": {"nameLength": "Transaction name must have at least 3 characters", "amount": "Transaction amount must be greater than 0", "splitsBalance": "Sum of credits and debits must be equal to the transaction amount", "accountRequired": "All splits must have an account selected", "contrahentTypeRequired": "Contrahent type is required"}, "isSchedule": {"label": "Multiple times"}, "isRegular": {"label": "Reg<PERSON>rly"}, "repeat": {"label": "Repeat"}, "dayly": {"label": "Daily"}, "weekly": {"label": "Weekly"}, "monthly": {"label": "Monthly"}, "yearly": {"label": "Yearly"}, "startDate": {"label": "Start date"}, "endDate": {"label": "End date"}, "frequency": {"label": "Frequency"}, "customDates": {"label": "Custom dates"}, "pickDates": {"label": "Pick dates"}}, "OrgTransactionForm": {"titleEdit": {"label": "Edit transaction"}, "titleNew": {"label": "New transaction"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated By"}, "name": {"label": "Transaction name"}, "description": {"label": "Transaction description"}, "date": {"label": "Transaction date"}, "type": {"label": "Transaction type"}, "dueDate": {"label": "Payment due date"}, "amount": {"label": "Amount"}, "jobId": {"label": "Job ID"}, "comment": {"label": "Comment"}, "objectId": {"label": "Object ID"}, "postedAt": {"label": "Posted at"}, "custRef": {"label": "Customer reference"}, "ourRef": {"label": "Our reference"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "Contractor ID"}, "setId": {"label": "Set ID"}, "contrahent": {"label": "Contractor"}, "transaction": {"label": "Transaction"}, "createTransaction": {"label": "Create transaction"}, "updateTransaction": {"label": "Update transaction"}, "splits": {"account": {"label": "Account"}, "accountDescription": {"label": "Enabled account"}, "credit": {"label": "Credit"}, "debit": {"label": "Debit"}, "addSplit": {"label": "Add split"}}, "validation": {"nameLength": "Transaction name must have at least 3 characters", "amount": "Transaction amount must be greater than 0", "splitsBalance": "Sum of credits and debits must be equal to the transaction amount", "accountRequired": "All splits must have an account selected"}}, "ContrahentForm": {"titleEdit": {"label": "<PERSON> contra<PERSON>t"}, "titleNew": {"label": "New contrahent"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated By"}, "name": {"label": "Contrahent name"}, "description": {"label": "Contrahent description"}, "date": {"label": "Contrahent date"}, "type": {"label": "Contrahent type"}, "dueDate": {"label": "Payment due date"}, "amount": {"label": "Amount"}, "jobId": {"label": "Job ID"}, "comment": {"label": "Comment"}, "objectId": {"label": "Object ID"}, "postedAt": {"label": "Posted at"}, "custRef": {"label": "Customer reference"}, "ourRef": {"label": "Our reference"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "Contrahent ID"}, "setId": {"label": "Set ID"}, "contrahent": {"label": "Con<PERSON>hen<PERSON>"}, "transaction": {"label": "Transaction"}, "createTransaction": {"label": "Create transaction"}, "updateTransaction": {"label": "Update transaction"}}, "TripForm": {"titleEdit": {"label": "Edit trip"}, "titleNew": {"label": "New trip"}, "id": {"label": "ID"}, "startDate": {"label": "Start date"}, "endDate": {"label": "End date"}, "name": {"label": "Trip name"}, "nameDescription": {"label": "For repeating trips"}, "description": {"label": "Description"}, "startOdometer": {"label": "Start odometer"}, "endOdometer": {"label": "End odometer"}, "distance": {"label": "Distance"}, "startLoc": {"label": "Start"}, "endLoc": {"label": "End"}, "submitEdit": {"label": "Save changes"}}, "AddressForm": {"titleEdit": {"label": "Edit address"}, "titleNew": {"label": "New address"}, "name": {"label": "Address name"}, "namePlaceholder": {"label": "np. home, work, client A..."}, "nameDescription": {"label": "In other components we often use addresses by name"}, "description": {"label": "Description"}, "street": {"label": "Street"}, "streetNo": {"label": "Street number"}, "localNo": {"label": "Local number"}, "city": {"label": "City"}, "postalCode": {"label": "Postal code"}, "country": {"label": "Country"}, "area1": {"label": "State"}, "area2": {"label": "County"}, "lon": {"label": "Longitude"}, "lat": {"label": "Latitude"}, "location": {"label": "Location"}}, "ContactForm": {"titleEdit": {"label": "Edit contact"}, "titleNew": {"label": "New contact"}, "name": {"label": "Name"}, "namePlaceholder": {"label": "np. <PERSON>"}, "description": {"label": "Description"}, "phone": {"label": "Phone"}, "email": {"label": "E-mail"}, "region": {"label": "Region"}}, "CommentForm": {"create": {"label": "Add comment"}}}, "userMenu": {"emails": {"label": "Emails"}, "applications": {"label": "Applications"}, "forumTopics": {"label": "Forum Topics"}, "comments": {"label": "Comments"}, "myFinances": {"label": "My Finances"}, "settings": {"label": "Settings"}, "userProfile": {"label": "User Profile"}, "emailSettings": {"label": "<PERSON><PERSON>s"}, "myOrgs": {"label": "My Organizations"}}, "errors": {"somethingWentWrong": {"label": "Something went wrong"}, "pageNotFound": {"label": "Page not found"}, "unauthorized": {"label": "Unauthorized"}, "sessionExpired": {"label": "Session expired"}}, "sidebar": {"selectValue": {"label": "Select value"}, "frameworks": {"react": {"label": "React"}, "angular": {"label": "Angular"}, "vue": {"label": "<PERSON><PERSON>"}, "svelte": {"label": "Svelte"}}}, "objects": {"info": {"object_name": {"label": "Object Name"}, "edit_object": {"label": "Edit Object"}, "delete_object": {"label": "Delete Object"}, "title": {"label": "Dimensions"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "height": {"label": "Height"}, "length": {"label": "Length"}, "surface": {"label": "Surface"}, "volume": {"label": "Volume"}, "circumference": {"label": "Circumference"}, "notAvailable": {"label": "Not available"}, "photos": {"label": "Photos"}, "photos_description": {"label": "Object photos"}, "documents": {"label": "Documents"}, "documents_description": {"label": "Object documents"}, "systems": {"label": "Systems"}, "systems_description": {"label": "Object systems"}, "other_photos": {"label": "Other Photos"}, "other_photos_description": {"label": "Additional photos of this object"}, "applications": {"label": "Applications"}, "applications_description": {"label": "Object applications"}, "jobs": {"label": "Jobs"}, "jobs_description": {"label": "Object jobs"}}, "objectTypes": {"building": {"label": "Building"}, "flat": {"label": "Flat"}, "garage": {"label": "Garage"}, "storage": {"label": "Storage"}, "other": {"label": "Other"}, "green_area": {"label": "Green area"}, "land_parcel": {"label": "Land parcel"}, "open_space": {"label": "Open space"}, "private_flat": {"label": "Private flat"}, "members_flat": {"label": "Members flat"}, "room": {"label": "Room"}, "root": {"label": "Root object"}, "element": {"label": "Element"}, "equipment": {"label": "Equipment"}, "vehicle": {"label": "Vehicle"}, "folder": {"label": "Folder"}, "collection": {"label": "Collection"}}, "edit": {"basic_data": {"label": "Basic data", "name": {"label": "Name"}, "description": {"label": "Description"}, "object_type": {"label": "Object type"}, "dimensions": {"label": "Dimensions"}, "calculations": {"label": "Calculations"}, "measurements": {"label": "Measurements"}, "tree_level": {"label": "Tree level"}, "is_branch": {"label": "Is branch"}}, "photos": {"label": "Photos"}, "documents": {"label": "Documents"}, "systems": {"label": "Systems"}, "other_photos": {"label": "Other Photos"}, "applications": {"label": "Applications"}, "jobs": {"label": "Jobs"}}}, "dataTable": {"toolbar": {"filterPlaceholder": {"label": "Search term..."}, "add": {"label": "Add"}, "deleteSelected": {"label": "Delete selected"}, "columns": {"label": "Columns"}, "noColumns": {"label": "No columns"}, "resetColumnsOrder": {"label": "Reset columns order"}, "export": {"label": "Export"}, "exportSelected": {"label": "Export selected"}, "exportCurrentPage": {"label": "Export current page"}, "exportAllPages": {"label": "Export all pages"}, "search": {"label": "Search"}, "settings": {"label": "Settings"}, "view": {"label": "View"}, "filterString": {"label": "Search term..."}, "startDate": {"label": "Start date"}, "endDate": {"label": "End date"}, "applyFilters": {"label": "Apply filters"}, "applySettings": {"label": "Apply settings"}, "getData": {"label": "Get data"}, "newVariantName": {"label": "Variant name"}, "saveAsDefaultVariant": {"label": "Save as default variant"}, "saveVariant": {"label": "Save variant"}, "createVariant": {"label": "Create variant"}, "resetFilters": {"label": "Reset filters"}}, "pagination": {"selected": {"label": "Selected"}, "rows": {"label": "rows"}, "rowsPerPage": {"label": "Rows per page"}, "page": {"label": "Page"}, "of": {"label": "of"}}}}, "objects": {"info": {"object_name": {"label": "Object Name"}, "edit_object": {"label": "Edit object"}, "delete_object": {"label": "Delete object"}, "title": {"label": "Object dimensions"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "height": {"label": "Height"}, "length": {"label": "Length"}, "surface": {"label": "Surface"}, "volume": {"label": "Volume"}, "circumference": {"label": "Circumference"}, "notAvailable": {"label": "Not available"}, "photos": {"label": "Photos"}, "photos_description": {"label": "Basic photos of the object"}, "documents": {"label": "Documents"}, "documents_description": {"label": "Documents associated with the object"}, "systems": {"label": "Systems"}, "systems_description": {"label": "Installations in the object"}, "other_photos": {"label": "Other Photos"}, "other_photos_description": {"label": "Additional photos of this object"}, "applications": {"label": "Applications"}, "applications_description": {"label": "Applications associated with this object"}, "jobs": {"label": "Jobs"}, "jobs_description": {"label": "Jobs associated with this object"}}, "objectTypes": {"building": {"label": "Building"}, "flat": {"label": "Flat"}, "garage": {"label": "Garage"}, "storage": {"label": "Storage"}, "other": {"label": "Other"}, "green_area": {"label": "Green area"}, "land_parcel": {"label": "Land parcel"}, "open_space": {"label": "Open space"}, "private_flat": {"label": "Private flat"}, "members_flat": {"label": "Members flat"}, "room": {"label": "Room"}, "root": {"label": "Object root"}, "element": {"label": "Element"}, "equipment": {"label": "Equipment"}, "vehicle": {"label": "Vehicle"}, "folder": {"label": "Folder"}, "collection": {"label": "Collection"}}, "edit": {"basic_data": {"label": "Basic data", "name": {"label": "Name"}, "description": {"label": "Description"}, "object_type": {"label": "Object type"}, "dimensions": {"label": "Dimensions"}, "calculations": {"label": "Calculations"}, "measurements": {"label": "Measurements"}, "tree_level": {"label": "Tree level"}, "is_branch": {"label": "Is branch"}}, "photos": {"label": "Photos"}, "documents": {"label": "Documents"}, "systems": {"label": "Systems"}, "other_photos": {"label": "Other photos"}, "applications": {"label": "Applications"}, "jobs": {"label": "Jobs"}}}, "forms": {"AccountingPeriodForm": {"titleEdit": {"label": "Edit accounting period"}, "titleNew": {"label": "New accounting period"}, "name": {"label": "Name"}, "namePlaceholder": {"label": "optional"}, "description": {"label": "Description"}, "start": {"label": "Start"}, "end": {"label": "End"}}, "AddressForm": {"titleEdit": {"label": "Edit address"}, "titleNew": {"label": "New address"}, "name": {"label": "Name"}, "namePlaceholder": {"label": "e.g. home, work, client A..."}, "nameDescription": {"label": "In other components we often select addresses by name"}, "description": {"label": "Description"}, "street": {"label": "Street"}, "streetNo": {"label": "Street number"}, "localNo": {"label": "Apartment number"}, "city": {"label": "City"}, "postalCode": {"label": "Postal code"}, "country": {"label": "Country"}, "area1": {"label": "Province"}, "area2": {"label": "County"}, "lon": {"label": "Longitude"}, "lat": {"label": "Latitude"}, "location": {"label": "Location"}}, "AddSystem": {"addSystem": {"label": "Add System"}, "addSystemFile": {"label": "Add System File"}, "selectSystem": {"label": "Select System"}}, "ContactForm": {"titleEdit": {"label": "Edit contact"}, "titleNew": {"label": "New contact"}, "name": {"label": "Name"}, "namePlaceholder": {"label": "e.g. <PERSON>"}, "description": {"label": "Description"}, "phone": {"label": "Phone"}, "email": {"label": "Email"}, "region": {"label": "Region"}}, "ContrahentForm": {"titleEdit": {"label": "Edit user"}, "titleNew": {"label": "New user"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated by"}, "name": {"label": "Name"}, "description": {"label": "Description"}, "type_id": {"label": "User type"}, "defaultAccounts": {"label": "Accounting accounts"}, "keywords": {"label": "Keywords"}, "region": {"label": "Region"}, "isBusiness": {"label": "Business"}, "nip": {"label": "Tax ID"}, "isVendor": {"label": "Product supplier"}, "isContractor": {"label": "Service provider"}, "isRegular": {"label": "Regular"}, "dueDate": {"label": "Due date"}}, "CommentForm": {"create": {"label": "Add comment"}}, "JobForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "Nowezadanie"}, "id": {"label": "Id"}, "createdAt": {"label": "Utworzono"}, "updatedAt": {"label": "Zak<PERSON>ali<PERSON>wan<PERSON>"}, "createdBy": {"label": "Utworzone przez"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "lang": {"label": "Język"}, "jsonMetadata": {"label": "Metadane JSON"}, "proposalId": {"label": "Id propoz<PERSON><PERSON><PERSON>"}, "status": {"label": "Status"}, "startDate": {"label": "Data rozpoczęcia"}, "endDate": {"label": "Data zakończenia"}, "budget": {"label": "Budżet"}, "orgId": {"label": "Id organizacji"}, "acceptedOfferId": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oferty"}}, "MilageTresholdForm": {"titleEdit": {"label": "Edit mileage thresholds"}, "titleNew": {"label": "New mileage threshold"}, "name": {"label": "Name"}, "namePlaceholder": {"label": "optional"}, "description": {"label": "Description"}, "level": {"label": "Level"}, "levelPlaceholder": {"label": "0, 1, 2..."}, "value": {"label": "Threshold kilometers"}, "rate": {"label": "Rate"}}, "NoticeForm": {"titleEdit": {"label": "Editing: "}, "titleNew": {"label": "New notice"}, "name": {"label": "Title"}, "namePlaceholder": {"label": "Notice title"}, "description": {"label": "Content"}, "descriptionPlaceholder": {"label": "Notice content"}, "isActive": {"label": "Active"}, "expiryDate": {"label": "Expiry date"}}, "ObjectForm": {"titleNew": {"label": "Nowy obiekt w: "}, "titleEdit": {"label": "Edytujesz: "}, "name": {"label": "Name", "placeholder": {"label": "Enter object name"}, "error": "Name must have at least 2 characters"}, "description": {"label": "Description", "placeholder": {"label": "Enter object description"}}, "objectType": {"label": "Object type", "placeholder": {"label": "Select object type"}}, "vehicleOwner": {"label": "Vehicle owner", "placeholder": {"label": "Select vehicle owner"}}, "length": {"label": "Length", "placeholder": {"label": "Enter length"}}, "width": {"label": "<PERSON><PERSON><PERSON>", "placeholder": {"label": "Enter width"}}, "height": {"label": "Height", "placeholder": {"label": "Enter height"}}, "calculateVolume": {"label": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>"}}, "surface": {"label": "Powierzchnia", "placeholder": {"label": "Powierzchnia"}}, "volume": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "circumference": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "treeLevel": {"label": "Poziom drzewa", "placeholder": {"label": "Wprowadź poziom drzewa"}}, "isBranch": {"label": "<PERSON><PERSON>"}, "createSuccess": {"label": "Obiekt został utworzony pomyślnie"}, "error": {"label": "Wystą<PERSON>ł błąd podczas aktualizacji obiektu"}, "updateSuccess": {"label": "Obiekt został pomyślnie zaktualizowany"}}, "objectType": {"title": {"label": "New object type"}, "name": {"label": "Name"}, "label": {"label": "Label"}, "description": {"label": "Description"}, "nameError": {"label": "Name must have at least 2 characters"}, "labelError": {"label": "Label is required"}, "organization": {"label": "Organization"}}, "OfferForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "Nowa oferta"}, "id": {"label": "ID"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "lang": {"label": "Język"}, "jsonMetadata": {"label": "Metadane JSON"}, "jobId": {"label": "ID zlecenia"}, "contractorId": {"label": "ID kontrahenta"}, "contactId": {"label": "ID kontaktu"}, "orgId": {"label": "ID organizacji"}, "price": {"label": "<PERSON><PERSON>"}, "startDate": {"label": "Data rozpoczęcia"}, "finishDate": {"label": "Data zakończenia"}, "expiryDate": {"label": "<PERSON>"}, "status": {"label": "Status"}}, "OrgForm": {"titleNew": {"label": "New Organization"}, "titleEdit": {"label": "Edit Organization"}, "name": {"label": "Organization Name"}, "description": {"label": "Description"}, "isPublic": {"label": "Public Organization"}, "isFormal": {"label": "Formal Organization"}, "nip": {"label": "NIP"}, "regon": {"label": "REGON"}, "maxImgWidth": {"label": "Maximum Image Width"}, "totalShares": {"label": "Total Shares"}, "votingDays": {"label": "Voting Days"}, "membersByAdmin": {"label": "Members managed by <PERSON><PERSON>"}, "error": {"label": "Error occurred while updating organization"}, "updateSuccess": {"label": "Organization updated successfully"}}, "OrgAccountForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> konto"}, "titleNew": {"label": "Nowe konto"}, "id": {"label": "ID"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "nameShort": {"label": "Skrocona nazwa"}, "label": {"label": "Etykietka"}, "labelShort": {"label": "Skrocona etykieta"}, "description": {"label": "Opis"}, "groupId": {"label": "Grupa"}, "type": {"label": "<PERSON><PERSON> konta"}, "isDebitMinus": {"label": "isDebitMinus"}, "level": {"label": "Poziom"}, "accNumber": {"label": "<PERSON><PERSON>r konta"}, "currency": {"label": "<PERSON><PERSON><PERSON>"}, "parentId": {"label": "ID rodzica"}, "isBranch": {"label": "Ma subkonta"}, "isPlaceholder": {"label": "Placeholder"}, "isActive": {"label": "Aktywne"}, "balance": {"label": "<PERSON><PERSON>"}, "isSyntetyczne": {"label": "Syntetyczne"}, "enabled": {"label": "Aktywne"}}, "OrgSplitForm": {"titleEdit": {"label": "Edit split"}, "titleNew": {"label": "New split"}, "name": {"label": "Name"}, "description": {"label": "Description"}, "account": {"label": "Account"}, "credit": {"label": "Credit"}, "debit": {"label": "Debet"}, "date": {"label": "Date"}}, "OrgTransactionForm": {"titleEdit": {"label": "Edit transaction"}, "titleNew": {"label": "New transaction"}, "contrahentDescription": {"label": "Each transaction requires a contractor. The 'PLUS' icon adds a new one."}, "templateDescription": {"label": "Populates transaction data from a template"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated by"}, "name": {"label": "Transaction name"}, "description": {"label": "Transaction description"}, "date": {"label": "Transaction date"}, "type": {"label": "Transaction type"}, "dueDate": {"label": "Due date"}, "amount": {"label": "Amount"}, "jobId": {"label": "Job ID"}, "comment": {"label": "Comment"}, "objectId": {"label": "Object ID"}, "postedAt": {"label": "Posted date"}, "custRef": {"label": "Customer reference"}, "ourRef": {"label": "Our system reference"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "Contractor ID"}, "setId": {"label": "Set ID"}, "contrahent": {"label": "Contractor"}, "transaction": {"label": "Transaction"}, "createTransaction": {"label": "Save transaction"}, "updateTransaction": {"label": "Update transaction"}, "splits": {"account": {"label": "Account"}, "accountDescription": {"label": "Active account"}, "credit": {"label": "Credit"}, "debit": {"label": "Debit"}, "addSplit": {"label": "Add split"}}, "validation": {"nameLength": "Transaction name must be at least 3 characters long", "amount": "Transaction amount must be greater than 0", "splitsBalance": "The sum of debits and credits must equal the transaction amount", "accountRequired": "All splits must have an account selected"}}, "ProfileForm": {"titleEdit": {"label": "Edit user profile"}, "titleNew": {"label": "Create new user profile"}, "displayName": {"label": "Display name"}, "description": {"label": "Description"}, "isCompany": {"label": "Is company"}, "isVoting": {"label": "Is voting"}, "shares": {"label": "Shares"}, "objectId": {"label": "Object ID"}, "znaczacaOperacjaLimit": {"label": "Significant operation limit"}, "dashboardModules": {"label": "Dashboard modules", "proposals": {"label": "Proposals"}, "contracts": {"label": "Contracts"}, "jobs": {"label": "Jobs"}, "trips": {"label": "Trips"}, "transactions": {"label": "Transactions"}, "problems": {"label": "Problems"}, "votings": {"label": "Votings"}}, "calendarJobs": {"label": "Calendar jobs", "objects": {"label": "Objects"}, "period": {"label": "Period: range of days"}, "periodBackDays": {"label": "Back days"}, "periodForwardDays": {"label": "Forward days"}}}, "ProposalForm": {"titleEdit": {"label": "Edit proposal"}, "titleNew": {"label": "Create new proposal"}, "title": {"label": "Title"}, "description": {"label": "Description"}, "motivation": {"label": "Motivation"}, "proposedBudget": {"label": "Proposed budget"}, "budget": {"label": "Budget"}, "isFormal": {"label": "Is formal?"}, "votingByShares": {"label": "Voting by shares?"}, "rownowagaResolution": {"label": "Rownowaga resolution"}, "proposedEndDate": {"label": "Proposed end date"}, "status": {"label": "Status"}, "status.options.draft": {"label": "Draft"}, "status.options.voting": {"label": "Voting"}, "status.options.accepted": {"label": "Accepted"}, "status.options.acceptedInProgress": {"label": "Accepted - In Progress"}, "status.options.acceptedCompleted": {"label": "Accepted - Completed"}, "status.options.rejected": {"label": "Rejected"}, "status.options.cancelled": {"label": "Cancelled"}}, "ProposalType": {"titleError": {"label": "Tytuł musi mieć co najmniej 2 znaki"}, "langError": {"label": "Język musi mieć co najmniej 2 znaki"}, "proposal_typeError": {"label": "Typ wniosku musi mieć co najmniej 2 znaki"}, "statusError": {"label": "Status musi mieć co najmniej 2 znaki"}}, "s3": {"titleEdit": {"label": "Edytuj konfigurację magazynu S3"}, "titleNew": {"label": "Nowa konfiguracja magazynu S3"}, "bucketName": {"label": "<PERSON><PERSON><PERSON>"}, "region": {"label": "Region"}, "accessKeyId": {"label": "ID klucza dostępu"}, "secretAccessKey": {"label": "Tajny klucz dostępu"}, "accountId": {"label": "ID konta"}, "endpoint": {"label": "Punkt końcowy"}}, "S3Form": {"bucketRequired": {"label": "<PERSON><PERSON><PERSON> <PERSON>u jest wymagana"}}, "SystemType": {"name": {"label": "Nazwa systemu"}, "description": {"label": "Opis"}, "systemType": {"label": "Typ systemu"}, "nameError": {"label": "Nazwa musi mieć co najmniej 2 znaki"}, "selectType": {"label": "Wybierz typ systemu"}, "types": {"water": {"label": "W<PERSON>"}, "electric": {"label": "Elektryka"}, "gas": {"label": "Gaz"}, "lowVoltage": {"label": "Niskie napięcie"}, "ventilation": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "other": {"label": "<PERSON><PERSON>"}}}, "TransactionForm": {"titleEdit": {"label": "Edit transaction"}, "titleNew": {"label": "New transaction"}, "titleSaved": {"label": "Transaction template"}, "titleSchedule": {"label": "Scheduled transactions"}, "id": {"label": "ID"}, "updatedBy": {"label": "Updated by"}, "name": {"label": "Transaction name"}, "description": {"label": "Transaction description"}, "date": {"label": "Transaction date"}, "type": {"label": "Transaction type"}, "dueDate": {"label": "Due date"}, "amount": {"label": "Amount"}, "jobId": {"label": "Job ID"}, "comment": {"label": "Comment"}, "objectId": {"label": "Object ID"}, "postedAt": {"label": "Posted date"}, "custRef": {"label": "Customer reference"}, "ourRef": {"label": "Our system reference"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "Contractor ID"}, "setId": {"label": "Set ID"}, "contrahent": {"label": "Contractor"}, "transaction": {"label": "Transaction"}, "createTransaction": {"label": "Save transaction"}, "updateTransaction": {"label": "Update transaction"}, "isSaved": {"label": "Template"}, "splits": {"account": {"label": "Account"}, "credit": {"label": "Credit"}, "debit": {"label": "Debit"}, "addSplit": {"label": "Add split"}}, "validation": {"nameLength": "Transaction name must be at least 3 characters long", "amount": "Transaction amount must be greater than 0", "splitsBalance": "The sum of debits and credits must equal the transaction amount", "accountRequired": "All splits must have an account selected", "contrahentTypeRequired": "Contrahent type is required", "date": "Transaction date is required"}, "isSchedule": {"label": "Future"}, "isRegular": {"label": "Regular"}, "repeat": {"label": "Frequency"}, "daily": {"label": "Daily"}, "weekly": {"label": "Weekly"}, "monthly": {"label": "Monthly"}, "yearly": {"label": "Yearly"}, "startDate": {"label": "Start date"}, "endDate": {"label": "End date"}, "frequency": {"label": "Frequency"}, "customDates": {"label": "Selected dates"}, "pickDates": {"label": "Pick dates"}}, "TripForm": {"titleEdit": {"label": "Edit trip"}, "titleNew": {"label": "New trip"}, "id": {"label": "ID"}, "startDate": {"label": "Start date"}, "endDate": {"label": "End date"}, "name": {"label": "Trip name"}, "nameDescription": {"label": "For recurring trips"}, "description": {"label": "Description"}, "distance": {"label": "Distance"}, "startOdometer": {"label": "Start odometer"}, "endOdometer": {"label": "End odometer"}, "startLocId": {"label": "Start location"}, "endLocId": {"label": "End location"}, "submitEdit": {"label": "Save trip"}}, "TypeForm": {"titleEdit": {"label": "Edit type"}, "titleNew": {"label": "New type"}, "name": {"label": "Name"}, "description": {"label": "Description"}, "order": {"label": "Display order"}}, "UserForm": {"titleEdit": {"label": "Edit user"}, "titleNew": {"label": "New user"}, "firstName": {"label": "First name"}, "lastName": {"label": "Last name"}, "lang": {"label": "Language"}, "jsonMetadata": {"label": "<PERSON><PERSON><PERSON>"}, "description": {"label": "Description"}, "phone": {"label": "Phone"}, "email": {"label": "Email"}, "emailVerified": {"label": "Email verified"}, "isSuperuser": {"label": "Superuser"}, "isAdmin": {"label": "Admin"}, "isLimited": {"label": "Limited"}, "isDisabled": {"label": "Disabled"}, "isInCredit": {"label": "In Credit"}, "isTemp": {"label": "Temp"}, "isDeleted": {"label": "Deleted"}}}, "userMenu": {"emails": {"label": "Emails"}, "applications": {"label": "Applications"}, "forumTopics": {"label": "Forum Topics"}, "comments": {"label": "Comments"}, "myFinances": {"label": "My finances"}, "settings": {"label": "Settings"}, "userProfile": {"label": "Active profile"}, "emailSettings": {"label": "Email settings"}, "myOrgs": {"label": "My organizations"}, "proposals": {"label": "Proposals"}}, "errors": {"somethingWentWrong": {"label": "Something went wrong"}, "pageNotFound": {"label": "Page not found"}, "unauthorized": {"label": "Unauthorized"}, "sessionExpired": {"label": "Session expired"}}, "sidebar": {"selectValue": {"label": "Select value"}, "frameworks": {"react": {"label": "React"}, "angular": {"label": "Angular"}, "vue": {"label": "<PERSON><PERSON>"}, "svelte": {"label": "Svelte"}}}, "storage": {"authorizeGoogleButton": {"label": "Authorize Google Drive"}, "removeAuthorization": {"label": "Remove authorization"}, "selectFiles": {"label": "Select files to upload"}, "selectFilesPlaceholder": {"label": "Click to select files"}, "uploadFiles": {"label": "Upload files"}, "selectSingleFile": {"label": "Select file to upload"}, "selectSingleFilePlaceholder": {"label": "Click to select file"}, "uploadFile": {"label": "Upload file"}, "fileDisplay": {"loading": {"label": "Loading files..."}, "error": {"label": "Error loading files"}, "noFiles": {"label": "No files available"}, "fileName": {"label": "File name"}, "fileId": {"label": "File ID"}, "url": {"label": "URL"}}, "camera": {"title": {"label": "Take a photo"}, "capture": {"label": "Take a photo"}}, "authError": {"label": "Failed to authorize storage"}, "authSuccess": {"label": "Successfully authorized storage"}}, "contrahentTypes": {"default": {"header": {"label": "Contrahent type"}, "ADMIN": {"label": "Administrators"}, "MEMBER": {"label": "Organization members"}, "VENDOR": {"label": "Goods suppliers"}, "CONTRACTOR": {"label": "Service suppliers"}, "MEDIA": {"label": "Media suppliers"}, "EMPLOYEE": {"label": "Employees"}, "CLIENT_GOODS": {"label": "Goods recipients"}, "CLIENT_SERVICES": {"label": "Service recipients"}, "DIRECTOR": {"label": "Director of company with o.o."}}, "job": {"ADMIN": {"label": "Administrator's work"}, "MEMBER": {"label": "Member's actions"}, "VENDOR": {"label": "Goods purchases"}, "CONTRACTOR": {"label": "Service purchases"}, "MEDIA": {"label": "Media payments"}, "EMPLOYEE": {"label": "Payroll"}, "CLIENT_GOODS": {"label": "Providing goods"}, "CLIENT_SERVICES": {"label": "Providing services"}, "DIRECTOR": {"label": "Director's operations"}}, "profile": {"ADMIN": {"label": "Administrator", "tag": "Adm"}, "MEMBER": {"label": "Member", "tag": "<PERSON><PERSON>"}, "VENDOR": {"label": "Goods supplier", "tag": "Ven"}, "CONTRACTOR": {"label": "Service supplier", "tag": "Con"}, "MEDIA": {"label": "Media supplier", "tag": "Med"}, "EMPLOYEE": {"label": "Employee", "tag": "Emp"}, "CLIENT_GOODS": {"label": "Goods recipient", "tag": "GRec"}, "CLIENT_SERVICES": {"label": "Service recipient", "tag": "Ser"}, "DIRECTOR": {"label": "Director", "tag": "<PERSON><PERSON>"}}}}