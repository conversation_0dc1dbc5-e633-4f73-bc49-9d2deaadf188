{"common": {"loading": {"label": "Ładowanie..."}, "error": {"label": "Błąd"}, "success": {"label": "Sukces"}, "save": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "reset": {"label": "Reset"}, "apply": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "update": {"label": "Zapisz zmiany"}, "cancel": {"label": "<PERSON><PERSON><PERSON>"}, "close": {"label": "Zamknij"}, "delete": {"label": "Usuń"}, "edit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "create": {"label": "Utwórz"}, "register": {"label": "Zarejestruj"}, "search": {"label": "Szukaj"}, "filter": {"label": "Filtruj"}, "noResults": {"label": "Brak wyników"}, "required": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "updating": {"label": "Aktualizacja..."}, "updateSuccess": {"label": "Pomyślnie zaktualizowano"}, "updateError": {"label": "<PERSON><PERSON> udało się zaktualizować"}, "thumbnail": {"label": "Miniatura dla {{name}}"}, "proposal": {"label": "Wniosek"}, "job": {"label": "<PERSON><PERSON><PERSON>"}, "jobs": {"label": "Zadania"}, "offer": {"label": "<PERSON><PERSON><PERSON>"}, "task": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tasks": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addTask": {"label": "<PERSON><PERSON><PERSON>"}, "voting": {"label": "Głosowanie"}, "accept": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reject": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "acceptOffer": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>e"}, "rejectOffer": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "acceptJob": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "rejectJob": {"label": "<PERSON><PERSON><PERSON><PERSON> zadanie"}, "acceptProposal": {"label": "Zaak<PERSON><PERSON><PERSON><PERSON> wniosek"}, "rejectProposal": {"label": "<PERSON><PERSON><PERSON><PERSON> wniosek"}, "failed": {"label": "Niepowodzenie!"}, "objects": {"label": "Obiekty"}, "object": {"label": "Obiekt"}, "optional": {"label": "opcjonalnie"}, "remove": {"label": "<PERSON><PERSON><PERSON> t<PERSON>"}, "attachements": {"label": "Załączniki"}, "comments": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "noTasks": {"label": "<PERSON><PERSON>"}, "noJobs": {"label": "Brak zadań"}, "noTransactions": {"label": "Brak transakcji"}, "addDescription": {"label": "<PERSON><PERSON><PERSON> opis"}, "hideDescription": {"label": "<PERSON><PERSON><PERSON><PERSON> opis"}, "add": {"label": "<PERSON><PERSON><PERSON>"}, "rowsSelected": {"label": "w<PERSON><PERSON> wy<PERSON>nych"}, "rowsPerPage": {"label": "Wierszy na stronę:"}, "of": {"label": "z"}, "page": {"label": "Strona"}}, "profile": {"form": {"title": {"label": "Ustawienia profilu"}, "displayName": {"label": "Nazwa wyświetlana", "placeholder": "Wprowadź nazwę wyświetlaną", "description": "To jest <PERSON>ja publiczna nazwa wyświetlana", "error": "Nazwa wyświetlana musi mieć co najmniej 2 znaki."}, "description": {"label": "Opis", "placeholder": "Opowiedz nam trochę o sobie"}, "company": {"label": "<PERSON><PERSON> firmowe", "description": "Oznacz to konto jako konto firmowe"}, "voting": {"label": "Gł<PERSON><PERSON><PERSON>", "description": "Włącz prawa do głosowania dla tego konta"}, "shares": {"label": "Udziały", "placeholder": "Liczba udziałów"}, "organization": {"label": "Nazwa organizacji", "placeholder": "Wprowadź nazwę organizacji"}, "operationLimit": {"label": "Limit opera<PERSON>", "placeholder": "Wprowadź limit operacji"}, "updateSuccess": {"label": "<PERSON><PERSON>."}, "submit": {"label": "Aktualiz<PERSON>j profil"}}}, "modules": {"dashboard": {"label": "Pulpit", "description": "Pulpit organizacji", "items": {"overview": {"label": "Pulpit", "description": "Pulpit organizacji"}, "documents": {"label": "Dokumenty", "description": "Dokumenty organizacji"}, "problems": {"label": "<PERSON><PERSON>", "description": "Problemy organizacji", "items": {"create": {"label": "<PERSON><PERSON>ł<PERSON>ś problem", "description": "Zgłoś nowy problem"}}}, "jobs": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> organ<PERSON>", "items": {"create": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>ą pracę"}, "calendar-jobs-objects": {"label": "Kalendarz prac", "description": "Kalendarz prac i powiązanych obiektów"}, "view": {"label": "Szczegóły pracy", "description": "Szczegóły pracy organizacji"}, "edit": {"label": "Edycja zadania", "description": "Edycja zadania organizacji"}}}, "offers": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> organiza<PERSON>"}}}, "member": {"label": "Użytkownik", "description": "Użytkownik organizacji", "items": {"emails": {"label": "E-maile", "description": "E-maile użytkownika"}, "proposals": {"label": "<PERSON><PERSON><PERSON>", "description": "Wnioski użytkownika"}, "comments": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Komentarze użytkownika"}, "finances": {"label": "Finanse", "description": "Finanse użytkownika"}, "member-settings": {"label": "Ustawienia", "description": "Ustawienia użytkownika", "items": {"profile": {"label": "Aktywny profil", "description": "Aktywny profil użytkownika"}, "email-settings": {"label": "Ustawienia e-mail", "description": "Ustawienia e-mail użytkownika"}}}, "profiles": {"label": "Organizacje/Profile", "description": "Lista organizacji i profili użytkownika", "items": {"create": {"label": "Nowa Organizacja/Profil", "description": "<PERSON><PERSON>j nową organizację/profil"}}}}}, "communication": {"label": "Komunikacja", "description": "Komunikacja organizacji", "items": {"polls": {"label": "<PERSON><PERSON><PERSON>", "description": "Ankiety organizacji"}, "proposal-comments": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Komentarze Uchwał organizacji"}, "notices": {"label": "Ogłoszenia", "description": "Ogłoszenia organizacji"}, "forum-topics": {"label": "Tematy forum", "description": "Tematy forum organizacji"}, "emails": {"label": "Wysłane e-maile", "description": "Wysłane e-maile organizacji"}}}, "proposals": {"label": "<PERSON><PERSON><PERSON>", "description": "Wnioski organizacji", "items": {"list": {"label": "Lista wniosków", "description": "Lista wniosków organizacji"}, "create": {"label": "<PERSON><PERSON> wniosek", "description": "Nowy wniosek organizacji"}, "active": {"label": "Aktywne", "description": "Aktywne wnioski organizacji"}, "accepted": {"label": "Zaak<PERSON>ptowane", "description": "Zaakceptowane wnioski organizacji"}, "rejected": {"label": "Odrzucone", "description": "Odrzucone wnioski organizacji"}, "job": {"name": "Nazwa zlecenia"}, "task": {"name": "Nazwa zadania", "description": "Opis zadania"}, "drawer": {"newJob": "Nowe zlecenie", "selectObjects": "Wybierz obiekty", "newTask": "Nowe zadanie"}, "actions": {"addJob": "<PERSON><PERSON>j <PERSON>", "selectObjects": "Wybierz obiekty"}}}, "documents": {"label": "Dokumenty", "description": "Dokumenty organizacji", "items": {"list": {"label": "Lista dokumentów", "description": "Lista dokumentów organizacji", "items": {"resolutions": {"label": "Uchwały", "description": "Lista uchwał organizacji"}, "contracts": {"label": "Umowy", "description": "Lista umów organizacji"}, "invoices": {"label": "Faktury", "description": "Lista faktur organizacji"}, "formal": {"label": "Formalne", "description": "Lista formalnych dokumentów organizacji"}}}, "create": {"label": "Nowy dokument", "description": "Nowy dokument organizacji"}, "templates": {"label": "S<PERSON><PERSON><PERSON><PERSON>", "description": "Szablony dokumentów organizacji"}}}, "schedule": {"label": "Harmonogram", "description": "Harmonogram organizacji", "items": {"jobs": {"label": "<PERSON><PERSON>", "description": "Lista zadań organizacji"}, "tasks": {"label": "Zadania", "description": "Lista zadań organizacji"}, "payments": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Lista płatności organizacji"}, "liabilities": {"label": "Zobowiązania", "description": "Lista zobowiązań organizacji"}}}, "jobs": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> organ<PERSON>", "items": {"active-offers": {"label": "Aktywne oferty", "description": "Lista aktywnych ofert organizacji"}, "current": {"label": "Bieżące", "description": "Lista bieżących zadań organizacji"}, "upcoming": {"label": "Przyszłe", "description": "Lista przyszłych zadań organizacji"}, "finished": {"label": "Zakonczone", "description": "Lista zakończonych zadań organizacji"}, "archived": {"label": "Zarchiwizowane", "description": "Lista zarchiwizowanych zadań organizacji"}}}, "contrahents": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kontrahenci organizacji", "items": {"list": {"label": "Lista", "description": "Lista kontrahentów organizacji"}, "create": {"label": "<PERSON><PERSON><PERSON>", "description": "Dodaj nowego kontrahenta do organizacji"}, "contacts": {"label": "Kontakty", "description": "Lista kontaktów kontrahentów organizacji"}, "addresses": {"label": "Ad<PERSON>y", "description": "Lista adresów kontrahentów organizacji"}}}, "crm": {"label": "CRM", "description": "CRM", "items": {"types": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON>", "items": {"admins": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON>"}, "members": {"label": "Członkowie", "description": "Członkowie organizacji"}, "vendors": {"label": "Dostawcy produktów", "description": "Dostawcy produktów"}, "contractors": {"label": "Dostawcy usług", "description": "Dostawcy usług"}, "media": {"label": "Dostawcy mediów", "description": "Dostawcy mediów"}, "clients-goods": {"label": "Odbiorcy produktów", "description": "Odbiorcy produktów"}, "clients-services": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "employees": {"label": "Pracownicy", "description": "Pracownicy organizacji"}, "directors": {"label": "Dyrektorzy", "description": "Dyrektorzy organizacji"}}}, "contacts": {"label": "Kontakty", "description": "Kontakty kontrahentów"}, "addresses": {"label": "Ad<PERSON>y", "description": "Adresy kontrahentów"}}}, "money": {"label": "Finanse", "description": "Finanse organizacji", "items": {"transactions": {"label": "Transakcje", "description": "Transakcje organizacji", "items": {"create": {"label": "Nowa transakcja", "description": "Dodaj nową transakcję do organizacji"}, "templates": {"label": "Szablony transakcji", "description": "Lista szablonów transakcji organizacji"}, "templates_create": {"label": "<PERSON><PERSON><PERSON>", "description": "Dodaj nowy szablon transakcji do organizacji"}, "schedule": {"label": "Planowane", "description": "Lista planowanych transakcji organizacji"}, "schedule_create": {"label": "<PERSON><PERSON><PERSON>", "description": "Dodaj nową planowaną transakcję do organizacji"}, "import": {"label": "Import csv", "description": "Importuj transakcje z pliku csv"}}}, "ballance": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> organ<PERSON>"}, "trips": {"label": "Przejazdy", "items": {"create": {"label": "Nowy przejazd", "description": "Dodaj nowy przejazd do organizacji"}}}, "reports": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> organ<PERSON>", "items": {"ballance_sheet": {"label": "Ballance sheet", "description": "Raport ballance sheet organizacji"}, "profit": {"label": "Przychody-Koszty", "description": "Raport przychodów i kosztów organizacji"}, "zlecenia": {"label": "Zlecenia", "description": "Raport zleceń organizacji"}, "contrahents": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Raport kontrahentów organizacji"}}}, "payroll": {"label": "Pracownicy", "description": "Pracownicy organizacji", "items": {"run": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wypłaty pracowników organizacji"}, "history": {"label": "Historia", "description": "Historia pracowników organizacji"}}}, "settings": {"label": "Ustawienia", "description": "Ustawienia organizacji", "items": {"accounts": {"label": "Konta księgowe", "description": "Konta księgowe organizacji"}, "accounting_periods": {"label": "Okresy księgowe", "description": "Okresy księgowe organizacji"}, "milage_tresholds": {"label": "Kilometrówka: limity", "description": "Kilometrówka: limity organizacji"}}}}}, "objects": {"label": "Obiekty", "description": "Obiekty organizacji", "items": {"new": {"label": "<PERSON><PERSON>j obiekt", "description": "Dodaj nowy obiekt do organizacji"}, "list": {"label": "Obiekty: <PERSON><PERSON><PERSON>", "description": "Obiekty organizacji w formie tabeli"}}}, "org-config": {"label": "Konfiguracja", "description": "Konfiguracja organizacji", "items": {"members": {"label": "Członkowie", "description": "Lista członków organizacji"}, "storage": {"label": "Pliki", "description": "Konfiguracja plików organizacji"}, "notices": {"label": "Powiadomienia", "description": "Lista powiadomień", "items": {"create": {"label": "<PERSON><PERSON>j powiadomienie", "description": "Stwórz nowe powiadomienie"}}}, "contrahents": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Lista kontrahentów organizacji", "items": {"create": {"label": "<PERSON><PERSON><PERSON>", "description": "Stwórz nowego kontrahenta"}, "register": {"label": "Rejestruj użytkownika", "description": "Rejestruj nowego użytkownika"}}}, "job-templates": {"label": "Szablony prac", "description": "Lista szablonów prac", "items": {"create": {"label": "<PERSON><PERSON><PERSON>", "description": "Stwórz nowy szablon"}}}, "accounting": {"label": "Ksi<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Konfiguracja księgowości organizacji", "items": {"accounts": {"label": "Konta księgowe", "description": "Lista kont księgowych organizacji"}, "accounts-sets": {"label": "Zestawy kont", "description": "Lista zestawów kont księgowych organizacji"}, "accounting-periods": {"label": "Okresy księgowe", "description": "Lista okresów księgowych organizacji"}}}, "milage": {"label": "Kilometrówka", "description": "Konfiguracja kilometrówki organizacji", "items": {"vehicles": {"label": "Po<PERSON>zdy", "description": "Lista pojazdów organizacji"}, "milage-tresholds": {"label": "Okresy kilometrówka", "description": "Lista okresów kilometrówka organizacji"}}}}}, "superadmin": {"label": "Superadmin", "description": "Superadmin", "items": {"users": {"label": "Użytkownicy", "description": "Lista użytkowników", "items": {"register": {"label": "Rejestruj", "description": "Rejestruj nowego użytkownika"}, "update": {"label": "Aktualizuj", "description": "Aktualizuj użytkownika"}, "delete": {"label": "Usuń", "description": "Usuń użytkownika"}}}, "orgs": {"label": "Organizacje", "description": "Lista organizacji", "items": {"create": {"label": "Stwórz organizację", "description": "Stwórz nową organizację"}}}, "object-types": {"label": "Typy o<PERSON>któw", "description": "Lista typów obiektów"}, "contrahents-types": {"label": "<PERSON><PERSON>", "description": "Lista typów kontrahentów"}, "email-types": {"label": "Typy e-maili", "description": "Lista typów e-maili"}}}}, "components": {"CalendarJobsObjects": {"objects": {"label": "Obiekty"}, "saveConfig": {"label": "Zapisz konfigurację"}, "cellOptions": {"label": "Opcje komórki"}, "addJob": {"label": "<PERSON><PERSON><PERSON>"}, "jobOptions": {"label": "Opcje zadania"}, "viewNewPage": {"label": "Szczegóły zadania (nowa strona)"}, "fullEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> (nowa strona)"}, "daysBack": {"label": "Dni przeszłe"}, "daysForward": {"label": "Dni przyszłe"}}, "fileDisplay": {"loadingFile": {"label": "Ładowanie pliku..."}, "fileRestricted": {"label": "Nie można wyświetlić pliku. Może być zastrz<PERSON>ż<PERSON>."}, "openDirectly": {"label": "Spróbuj otworzyć plik bezpośrednio"}, "unsupportedType": {"label": "Nieobsługiwany typ pliku. Pobierz plik:"}, "downloadFile": {"label": "Pobierz plik"}, "fetchError": {"label": "Błąd podczas pobierania danych pliku"}}, "proposalPage": {"drawer": {"newJob": {"label": "Nowe zadanie"}, "selectObjects": {"label": "<PERSON><PERSON>j o<PERSON>"}, "newTask": {"label": "<PERSON><PERSON><PERSON>", "description": {"label": "<PERSON><PERSON>"}, "name": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "withPhoto": {"label": "Konieczne zdjęcie do potwierdzenia"}, "order": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "proposalComments": {"label": "Komentarze do wniosku"}}, "actions": {"addJob": {"label": "<PERSON><PERSON><PERSON>"}, "selectObjects": {"label": "<PERSON><PERSON>j o<PERSON>"}}}, "tplAccountsTable": {"cloneModal": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "newSetName": {"label": "Nazwa nowego zestawu"}, "confirm": {"label": "K<PERSON><PERSON><PERSON>"}, "cloneButton": {"label": "K<PERSON><PERSON><PERSON>"}}, "newAccount": {"label": "Nowe konto "}, "cloneSet": {"label": "Klonuj do nowego zestawu"}, "cloneToOrg": {"label": "Klonuj do organizacji"}, "deleteSet": {"label": "Usuń zestaw"}}, "emailSettings": {"pageTitle": {"label": "Powiadomienia Email"}, "pageDescription": {"label": "Zarządzaj preferencjami powiadomień email. <PERSON><PERSON><PERSON><PERSON>, jakie aktualizacje chcesz otrzymywać."}, "new_comment_on_proposal": {"label": "Nowy komentarz do obserwowanej propozycji"}, "new_comment_on_my_proposal": {"label": "Nowy komentarz do Twojej propozycji"}, "new_proposal": {"label": "Utworzono nową propozycję"}, "proposal_voting_started": {"label": "Rozpoczęto głosowanie nad propozycją"}, "proposal_new_vote": {"label": "Oddano nowy głos w obserwowanej propozycji"}, "daily_voting_reminder": {"label": "Codzienne przypomnienie o głosowaniu"}, "end_of_voting": {"label": "Powiadomienie o zakończeniu okresu głosowania"}, "new_job": {"label": "Opublikowano nową ofertę pracy pasującą do Twoich zainteresowań"}, "new_offer": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> ofertę"}, "new_transaction": {"label": "Wystąpiła nowa transakcja"}, "switchOn": {"label": "WŁ"}, "switchOff": {"label": "WYŁ"}, "languageToggle": {"label": "Zmień Język"}}, "dataTable": {"toolbar": {"filterPlaceholder": {"label": "Szukana fraza..."}, "add": {"label": "<PERSON><PERSON><PERSON>"}, "deleteSelected": {"label": "Usuń wybrane"}, "columns": {"label": "<PERSON><PERSON><PERSON>"}, "noColumns": {"label": "Brak kolumn"}, "resetColumnsOrder": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "export": {"label": "Eksportuj"}, "exportSelected": {"label": "Eksportuj wybrane"}, "exportCurrentPage": {"label": "Eksportuj aktualna stronę"}, "exportAllPages": {"label": "Eksportuj wszystkie strony"}, "search": {"label": "Wyszukiwanie"}, "settings": {"label": "Ustawienia"}, "view": {"label": "Widok"}, "filterString": {"label": "Szukana fraza..."}, "startDate": {"label": "Data początkowa"}, "endDate": {"label": "Data końcowa"}, "applyFilters": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> fi<PERSON>"}, "applySettings": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "getData": {"label": "<PERSON><PERSON><PERSON> dane"}, "newVariantName": {"label": "<PERSON><PERSON><PERSON>"}, "saveAsDefaultVariant": {"label": "Zapisz jako domy<PERSON>lny wariant"}, "saveVariant": {"label": "Zapisz wariant"}, "createVariant": {"label": "Utwórz wariant"}, "resetFilters": {"label": "<PERSON><PERSON><PERSON><PERSON> filtry"}}, "pagination": {"selected": {"label": "Wybrano"}, "rows": {"label": "w<PERSON><PERSON>"}, "rowsPerPage": {"label": "Wierszy na stronę"}, "page": {"label": "Strona"}, "of": {"label": "z"}}}}, "objects": {"info": {"object_name": {"label": "Nazwa obiektu"}, "edit_object": {"label": "Ed<PERSON><PERSON>j obiekt"}, "delete_object": {"label": "Us<PERSON>ń obiekt"}, "title": {"label": "Wymiary obiektu"}, "width": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "length": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "surface": {"label": "Powierzchnia"}, "volume": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "circumference": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notAvailable": {"label": "<PERSON><PERSON> da<PERSON>"}, "photos": {"label": "Zdjęcia"}, "photos_description": {"label": "Podstawowe zdjecia obiektu"}, "documents": {"label": "Dokumenty"}, "documents_description": {"label": "Dokumenty powiązane z tym obiektem"}, "systems": {"label": "Instalacje"}, "systems_description": {"label": "Instalacje w obiekcie"}, "other_photos": {"label": "Inne zdjęcia"}, "other_photos_description": {"label": "Dodatkowe zdjęcia tego obiektu"}, "applications": {"label": "Aplikacje"}, "applications_description": {"label": "Aplikacje powiązane z tym obiektem"}, "jobs": {"label": "Zadania"}, "jobs_description": {"label": "Zadania związane z tym obiektem"}}, "objectTypes": {"building": {"label": "Budynek"}, "flat": {"label": "Mieszkanie"}, "garage": {"label": "Garaż"}, "storage": {"label": "Magazyn"}, "other": {"label": "Inny"}, "green_area": {"label": "Powierzchnia zielona"}, "land_parcel": {"label": "Działka"}, "open_space": {"label": "Otwarta powierzchnia"}, "private_flat": {"label": "<PERSON><PERSON> p<PERSON>"}, "members_flat": {"label": "Lokal członka"}, "room": {"label": "Pomieszczenie"}, "root": {"label": "Obiekt bazowy"}, "element": {"label": "Element"}, "equipment": {"label": "Urząd<PERSON><PERSON>"}, "vehicle": {"label": "Pojazd"}, "folder": {"label": "Folder"}, "collection": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "edit": {"basic_data": {"label": "Podstawowe dane", "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "object_type": {"label": "Typ obiektu"}, "dimensions": {"label": "Wymiary"}, "calculations": {"label": "O<PERSON><PERSON>zen<PERSON>"}, "measurements": {"label": "Pomiary"}, "tree_level": {"label": "Poziom drzewa"}, "is_branch": {"label": "Jest gałęzią"}}, "photos": {"label": "Zdjęcia"}, "documents": {"label": "Dokumenty"}, "systems": {"label": "Instalacje"}, "other_photos": {"label": "Inne zdjęcia"}, "applications": {"label": "Aplikacje"}, "jobs": {"label": "Zadania"}}}, "forms": {"common": {"invalidForm": {"label": "Formularz niekompletny lub zawiera błędy"}, "required": {"label": "<PERSON><PERSON><PERSON><PERSON> wymagana"}, "invalid": {"label": "<PERSON><PERSON><PERSON><PERSON>rawidł<PERSON>"}}, "AccountingPeriodForm": {"titleEdit": {"label": "Edytuj okres księgowy"}, "titleNew": {"label": "Nowy okres księgowy"}, "name": {"label": "Nazwa"}, "namePlaceholder": {"label": "opcjonalnie"}, "description": {"label": "Opis"}, "start": {"label": "Początek"}, "end": {"label": "Koniec"}}, "AddressForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "<PERSON><PERSON> adres"}, "name": {"label": "Nazwa"}, "namePlaceholder": {"label": "np. dom, praca, klient A..."}, "nameDescription": {"label": "W innych komponentach czesto wybieramy adresy po nazwie"}, "description": {"label": "Opis"}, "street": {"label": "Ulica"}, "streetNo": {"label": "<PERSON><PERSON>r ulicy"}, "localNo": {"label": "Numer lokalu"}, "city": {"label": "<PERSON><PERSON>"}, "postalCode": {"label": "<PERSON><PERSON>"}, "country": {"label": "<PERSON><PERSON>"}, "area1": {"label": "Wojewodztwo"}, "area2": {"label": "<PERSON><PERSON><PERSON>"}, "lon": {"label": "Dlugosc geograficzna"}, "lat": {"label": "Szerokosc geograficzna"}, "location": {"label": "Lokalizacja"}}, "AddSystem": {"addSystem": {"label": "Dodaj System"}, "addSystemFile": {"label": "<PERSON><PERSON><PERSON>"}, "selectSystem": {"label": "Wybierz System"}}, "ContactForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "titleNew": {"label": "Nowy kontakt"}, "name": {"label": "Nazwa"}, "namePlaceholder": {"label": "np. <PERSON>"}, "description": {"label": "Opis"}, "phone": {"label": "Telefon"}, "email": {"label": "E-mail"}, "region": {"label": "Region"}}, "ContrahentForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "<PERSON><PERSON> k<PERSON>"}, "id": {"label": "ID"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "type_id": {"label": "<PERSON><PERSON>"}, "accountsDescription": {"label": "Pomocne przy importach CSV"}, "keywordsDescription": {"label": "Słowa kluczowe dla importów CSV"}, "defaultAccounts": {"label": "Konta księgowe"}, "keywords": {"label": "Słowa kluczowe"}, "region": {"label": "Region"}, "isBusiness": {"label": "Firma"}, "isVendor": {"label": "Dostawca produktów"}, "isContractor": {"label": "Dostawca usług"}, "isRegular": {"label": "Regularny"}, "dueDate": {"label": "<PERSON><PERSON><PERSON>"}}, "CommentForm": {"create": {"label": "<PERSON><PERSON><PERSON>"}}, "JobForm": {"addJob": {"label": "<PERSON><PERSON><PERSON>"}, "entityName": {"label": "<PERSON><PERSON><PERSON>"}, "titleEdit": {"label": "Edytujesz: "}, "titleNew": {"label": "Nowa praca"}, "id": {"label": "Id"}, "createdAt": {"label": "Utworzono"}, "updatedAt": {"label": "Zak<PERSON>ali<PERSON>wan<PERSON>"}, "createdBy": {"label": "Utworzone przez"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "namePlaceholder": {"label": "Wprowadź nazwę pracy"}, "tag": {"label": "Tag"}, "tagPlaceholder": {"label": "Short name for UI elements"}, "description": {"label": "Opis"}, "descriptionPlaceholder": {"label": "<PERSON><PERSON> pracy (opcjonalnie)"}, "lang": {"label": "Język"}, "jsonMetadata": {"label": "Metadane JSON"}, "proposalId": {"label": "Id propoz<PERSON><PERSON><PERSON>"}, "status": {"label": "Status"}, "startDate": {"label": "Data rozpoczęcia"}, "endDate": {"label": "Data zakończenia"}, "budget": {"label": "Budżet"}, "is_public": {"label": "Publiczne oferty"}, "orgId": {"label": "Id organizacji"}, "acceptedOfferId": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oferty"}, "type": {"label": "<PERSON><PERSON>"}, "typePlaceholder": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "isPublic": {"label": "Publiczna"}, "statusTypes": {"DRAFT": {"label": "<PERSON><PERSON><PERSON> rob<PERSON>"}, "CONFIRMED": {"label": "Zatwierdzona"}, "IN_PROGRESS": {"label": "<PERSON> trakcie"}, "COMPLETED": {"label": "Zakończona"}, "CANCELLED": {"label": "Anulowana"}}, "jobTemplate": {"label": "Szablon"}, "jobTemplatePlaceholder": {"label": "opcjonalnie"}, "validation": {"name": {"label": "<PERSON><PERSON><PERSON> wymagana", "min": {"label": "Nazwa za krótka"}}, "type": {"label": "<PERSON><PERSON> wym<PERSON>y"}, "startDate": {"label": "Data rozpoczęcia wymagana"}, "endDate": {"label": "Data zakończenia wymagana"}, "budget": {"label": "<PERSON><PERSON><PERSON> wymagany"}, "datetime": {"label": "Nieprawidłowy format daty"}}}, "JobTemplateForm": {"titleEdit": {"label": "Edytujesz: "}, "titleNew": {"label": "<PERSON><PERSON> s<PERSON>"}, "name": {"label": "Nazwa"}, "namePlaceholder": {"label": "Wprowadź nazwę szablonu"}, "description": {"label": "Opis"}, "descriptionPlaceholder": {"label": "Wprowadź opis szablonu"}, "lang": {"label": "Język"}, "jsonMetadata": {"label": "Metadane JSON"}, "templateType": {"label": "<PERSON><PERSON><PERSON>"}, "templateTypePlaceholder": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "templateTypes": {"VENDOR": {"label": "Pozyskanie produktów"}, "CONTRACTOR": {"label": "Pozyskanie usług"}, "MEMBER": {"label": "<PERSON><PERSON><PERSON>"}, "CLIENT_GOODS": {"label": "Sprzedaż towarów"}, "CLIENT_SERVICES": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> usług"}, "MEDIA": {"label": "Media"}, "EMPLOYEE": {"label": "Pracownik"}, "DIRECTOR": {"label": "Dyrektor spólki z o.o."}}, "jobTemplate": {"label": "Szablon"}, "jobTemplatePlaceholder": {"label": "opcjonalnie"}}, "MilageTresholdForm": {"titleEdit": {"label": "Edyt<PERSON>j progi kilometrowki"}, "titleNew": {"label": "Nowy prog kilometrowki"}, "name": {"label": "Nazwa"}, "namePlaceholder": {"label": "opcjonalnie"}, "description": {"label": "Opis"}, "level": {"label": "Poziom"}, "levelPlaceholder": {"label": "0, 1, 2..."}, "value": {"label": "Graniczna ilość kilometrow"}, "rate": {"label": "Stawka"}}, "NoticeForm": {"titleEdit": {"label": "Edytujesz: "}, "titleNew": {"label": "Nowe powiadomienie"}, "name": {"label": "<PERSON><PERSON><PERSON>"}, "namePlaceholder": {"label": "Tytuł powiadomienia"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "descriptionPlaceholder": {"label": "T<PERSON><PERSON>ć powiadomienia"}, "isActive": {"label": "Aktywne"}, "expiryDate": {"label": "<PERSON>"}}, "ObjectForm": {"titleNew": {"label": "Nowy obiekt w: "}, "titleEdit": {"label": "Edytujesz: "}, "name": {"label": "Nazwa", "placeholder": {"label": "Wprowadź nazwę obiektu"}, "error": "Nazwa musi mieć co najmniej 2 znaki"}, "description": {"label": "Opis", "placeholder": {"label": "Wprowadź opis obiektu"}}, "objectType": {"label": "Typ obiektu", "placeholder": {"label": "Wybierz typ obiektu"}}, "vehicleOwner": {"label": "Właściciel pojazdu", "placeholder": {"label": "<PERSON><PERSON>bierz właściciela pojazdu"}}, "length": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "Wprow<PERSON><PERSON> długoś<PERSON>"}}, "width": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> szeroko<PERSON>"}}, "height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "<PERSON>prow<PERSON><PERSON> wysokość"}}, "calculateVolume": {"label": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>"}}, "surface": {"label": "Powierzchnia", "placeholder": {"label": "Powierzchnia"}}, "volume": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "circumference": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "treeLevel": {"label": "Poziom drzewa", "placeholder": {"label": "Wprowadź poziom drzewa"}}, "isBranch": {"label": "<PERSON><PERSON>"}, "createSuccess": {"label": "Obiekt został utworzony pomyślnie"}, "error": {"label": "Wystą<PERSON>ł błąd podczas aktualizacji obiektu"}, "updateSuccess": {"label": "Obiekt został pomyślnie zaktualizowany"}}, "objectType": {"title": {"label": "Nowy typ obiektu"}, "name": {"label": "Nazwa"}, "label": {"label": "Etykieta"}, "description": {"label": "Opis"}, "nameError": {"label": "Nazwa musi mieć co najmniej 2 znaki"}, "labelError": {"label": "<PERSON><PERSON><PERSON><PERSON> jest wymagana"}, "organization": {"label": "Organizacja"}}, "OfferForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "Nowa oferta"}, "id": {"label": "ID"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "lang": {"label": "Język"}, "jsonMetadata": {"label": "Metadane JSON"}, "jobId": {"label": "ID zlecenia"}, "contractorId": {"label": "ID kontrahenta"}, "contactId": {"label": "ID kontaktu"}, "orgId": {"label": "ID organizacji"}, "price": {"label": "<PERSON><PERSON>"}, "startDate": {"label": "Data rozpoczęcia"}, "finishDate": {"label": "Data zakończenia"}, "expiryDate": {"label": "<PERSON>"}, "status": {"label": "Status"}}, "OrgForm": {"titleNew": {"label": "Nowa Organizacja"}, "titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "name": {"label": "Nazwa Organizacji"}, "description": {"label": "Opis"}, "isPublic": {"label": "Organizacja Publiczna"}, "isFormal": {"label": "Organizacja Formalna"}, "nip": {"label": "NIP"}, "regon": {"label": "REGON"}, "maxImgWidth": {"label": "Maks<PERSON>al<PERSON>ć Zdje<PERSON>"}, "totalShares": {"label": "<PERSON><PERSON>"}, "votingDays": {"label": "Dni na Głosowanie"}, "membersByAdmin": {"label": "Tylko administrator do<PERSON><PERSON> członków"}, "error": {"label": "Wystąpił błąd podczas aktualizacji organizacji"}, "updateSuccess": {"label": "Organizacja została pomyślnie zaktualizowana"}}, "OrgAccountForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> konto"}, "titleNew": {"label": "Nowe konto"}, "id": {"label": "ID"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa"}, "nameShort": {"label": "Skrocona nazwa"}, "label": {"label": "Etykietka"}, "labelShort": {"label": "Skrocona etykieta"}, "description": {"label": "Opis"}, "groupId": {"label": "Grupa"}, "type": {"label": "<PERSON><PERSON> konta"}, "isDebitMinus": {"label": "isDebitMinus"}, "level": {"label": "Poziom"}, "accNumber": {"label": "<PERSON><PERSON>r konta"}, "currency": {"label": "<PERSON><PERSON><PERSON>"}, "parentId": {"label": "ID rodzica"}, "isBranch": {"label": "Ma subkonta"}, "isPlaceholder": {"label": "Placeholder"}, "isActive": {"label": "Aktywne"}, "balance": {"label": "<PERSON><PERSON>"}, "isSyntetyczne": {"label": "Syntetyczne"}, "enabled": {"label": "Aktywne"}}, "OrgSplitForm": {"titleEdit": {"label": "Edytuj split"}, "titleNew": {"label": "Nowy split"}, "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "account": {"label": "Ko<PERSON>"}, "credit": {"label": "<PERSON><PERSON><PERSON>"}, "debit": {"label": "Debet"}, "date": {"label": "Data"}}, "OrgTransactionForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "Nowa transakcja"}, "contrahentLabel": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "contrahentDescription": {"label": "Każda transakcja wymaga kontrahenta. Ikona 'PLUS' doda<PERSON> now<PERSON>."}, "templateLabel": {"label": "<PERSON>za<PERSON><PERSON> (opcjonalnie)"}, "templateDescription": {"label": "Wypełnii dane transakcji z szablonu"}, "id": {"label": "ID"}, "type": {"label": "<PERSON><PERSON>"}, "typeDescription": {"label": "Typ operacji"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa <PERSON>akcji"}, "description": {"label": "Opis <PERSON>"}, "date": {"label": "Data transakcji"}, "dueDate": {"label": "<PERSON><PERSON><PERSON>"}, "amount": {"label": "K<PERSON><PERSON>"}, "jobId": {"label": "Id zlecenia"}, "comment": {"label": "Komentarz"}, "objectId": {"label": "Id obiektu"}, "postedAt": {"label": "Data wprowadzenia"}, "custRef": {"label": "Referencja klienta"}, "ourRef": {"label": "Referencja naszego systemu"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "<PERSON>d k<PERSON>"}, "setId": {"label": "<PERSON><PERSON>"}, "contrahent": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "transaction": {"label": "Transakcja"}, "createTransaction": {"label": "<PERSON><PERSON><PERSON>z <PERSON>"}, "updateTransaction": {"label": "Zaktualizuj <PERSON>cje"}, "splits": {"account": {"label": "Ko<PERSON>"}, "accountDescription": {"label": "Aktywne konto"}, "credit": {"label": "<PERSON><PERSON><PERSON>"}, "debit": {"label": "Debet"}, "addSplit": {"label": "Dodaj split"}}, "validation": {"nameLength": "Nazwa transakcji musi mieć co najmniej 3 znaki", "amount": "Kwota transakcji musi byćwiększa niż 0", "splitsBalance": "Suma debetów i kredytów musi być równa kwocie transakcji", "accountRequired": "Wszystkie podziały muszą mieć wybrane konto", "contrahentTypeRequired": "Rod<PERSON>j kontrahenta musi być wybrany"}}, "ProblemForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>u "}, "titleNew": {"label": "Zgłoszenie problemu "}, "description": {"label": "Opis problemu"}, "status": {"label": "Status problemu"}, "closedDate": {"label": "Data rozwiązania problemu"}, "objects": {"label": "Powiązane obiekty"}, "createProblem": {"label": "<PERSON><PERSON><PERSON><PERSON> problem"}, "updateProblem": {"label": "Zak<PERSON><PERSON><PERSON><PERSON> problem"}, "validation": {"nameLength": "Nazwa problemu musi mieć co najmniej 3 znaki", "descriptionLength": "Opis problemu musi mieć co najmniej 3 znaki", "statusRequired": "Status problemu jest wymagany", "closedDateRequired": "Data zamknięcia problemu jest wymagana", "objectsRequired": "Obiekty są wymagane"}}, "ProfileForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> profil "}, "titleNew": {"label": "<PERSON><PERSON> profil "}, "name": {"label": "Nazwa profilu"}, "displayName": {"label": "Wyświetlana nazwa użytkownika"}, "roles": {"label": "Role"}, "lang": {"label": "Język"}, "description": {"label": "Opis"}, "isCompany": {"label": "<PERSON><PERSON> firma"}, "isVoting": {"label": "<PERSON><PERSON>"}, "shares": {"label": "Udziały"}, "objectId": {"label": "ID obiektu"}, "znaczacaOperacjaLimit": {"label": "Limit znaczącej operacji"}, "dashboardModules": {"label": "<PERSON><PERSON><PERSON><PERSON>", "proposals": {"label": "<PERSON><PERSON><PERSON>"}, "jobs": {"label": "<PERSON><PERSON>"}, "trips": {"label": "Przejazdy"}, "transactions": {"label": "Transakcje"}, "problems": {"label": "<PERSON><PERSON>"}, "notices": {"label": "Powiadomienia"}, "contrahent_jobs": {"label": "<PERSON><PERSON>"}, "contrahent_transactions": {"label": "Transakcje kontrahentów"}}, "member": {"label": "Członkowie", "isVoting": {"label": "<PERSON><PERSON>"}, "shares": {"label": "Udziały"}, "znaczacaOperacjaLimit": {"label": "Limit znaczącej operacji"}, "objects": {"label": "Obiekty"}}, "calendarJobs": {"label": "Kalendarz prac", "objects": {"label": "Kalendarz:Obiekty"}, "period": {"label": "Kalendarz:<PERSON><PERSON> (zakres dni)"}, "periodBackDays": {"label": "Dni wstecz"}, "periodForwardDays": {"label": "Dni w przód"}}}, "ProposalForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>"}, "titleNew": {"label": "Utwórz nowy wniosek"}, "title": {"label": "<PERSON><PERSON><PERSON>"}, "description": {"label": "Opis"}, "motivation": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "proposedBudget": {"label": "Proponowany budżet"}, "budget": {"label": "Budżet"}, "isFormal": {"label": "C<PERSON> formalny?"}, "votingByShares": {"label": "Głosowanie udziałami?"}, "rownowagaResolution": {"label": "Rezolucja Równowaga", "options": {"ADMIN": {"label": "Decyzja administratora"}, "REJECT": {"label": "Wniosek odrzucamy"}, "ACCEPT": {"label": "Wniosek realizujemy"}}}, "proposedEndDate": {"label": "Proponowana data zakończenia"}, "status": {"label": "Status"}, "status.options.draft": {"label": "<PERSON><PERSON><PERSON> rob<PERSON>"}, "status.options.voting": {"label": "Głosowanie"}, "status.options.accepted": {"label": "Zaakceptowany"}, "status.options.acceptedInProgress": {"label": "Zaakceptowany - w trakcie realizacji"}, "status.options.acceptedCompleted": {"label": "Zaakceptowany - zakończony"}, "status.options.rejected": {"label": "Odrzucony"}, "status.options.cancelled": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "ProposalType": {"titleError": {"label": "Tytuł musi mieć co najmniej 2 znaki"}, "langError": {"label": "Język musi mieć co najmniej 2 znaki"}, "proposal_typeError": {"label": "Typ wniosku musi mieć co najmniej 2 znaki"}, "statusError": {"label": "Status musi mieć co najmniej 2 znaki"}}, "s3": {"titleEdit": {"label": "Edytuj konfigurację magazynu S3"}, "titleNew": {"label": "Nowa konfiguracja magazynu S3"}, "bucketName": {"label": "<PERSON><PERSON><PERSON>"}, "region": {"label": "Region"}, "accessKeyId": {"label": "ID klucza dostępu"}, "secretAccessKey": {"label": "Tajny klucz dostępu"}, "accountId": {"label": "ID konta"}, "endpoint": {"label": "Punkt końcowy"}}, "S3Form": {"bucketRequired": {"label": "<PERSON><PERSON><PERSON> <PERSON>u jest wymagana"}}, "SystemType": {"name": {"label": "Nazwa systemu"}, "description": {"label": "Opis"}, "systemType": {"label": "Typ systemu"}, "nameError": {"label": "Nazwa musi mieć co najmniej 2 znaki"}, "selectType": {"label": "Wybierz typ systemu"}, "types": {"water": {"label": "W<PERSON>"}, "electric": {"label": "Elektryka"}, "gas": {"label": "Gaz"}, "lowVoltage": {"label": "Niskie napięcie"}, "ventilation": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "other": {"label": "<PERSON><PERSON>"}}}, "TransactionForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "titleNew": {"label": "Nowa transakcja"}, "titleSaved": {"label": "Szablon transakcji"}, "titleSchedule": {"label": "Planowane <PERSON>je"}, "id": {"label": "ID"}, "updatedBy": {"label": "Zaktualizowane przez"}, "name": {"label": "Nazwa <PERSON>akcji"}, "description": {"label": "Opis <PERSON>"}, "date": {"label": "Data transakcji"}, "type": {"label": "Typ <PERSON>cji"}, "dueDate": {"label": "<PERSON><PERSON><PERSON>"}, "amount": {"label": "K<PERSON><PERSON>"}, "jobId": {"label": "Id zlecenia"}, "comment": {"label": "Komentarz"}, "objectId": {"label": "Id obiektu"}, "postedAt": {"label": "Data wprowadzenia"}, "custRef": {"label": "Referencja klienta"}, "ourRef": {"label": "Referencja naszego systemu"}, "memo": {"label": "Memo"}, "contrahentId": {"label": "<PERSON>d k<PERSON>"}, "setId": {"label": "<PERSON><PERSON>"}, "contrahent": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "transaction": {"label": "Transakcja"}, "createTransaction": {"label": "<PERSON><PERSON><PERSON>z <PERSON>"}, "updateTransaction": {"label": "Zaktualizuj <PERSON>cje"}, "isSaved": {"label": "Szablon"}, "splits": {"account": {"label": "Ko<PERSON>"}, "credit": {"label": "<PERSON><PERSON><PERSON>"}, "debit": {"label": "Debet"}, "addSplit": {"label": "Dodaj split"}}, "validation": {"nameLength": "Nazwa transakcji musi mieć co najmniej 3 znaki", "amount": "Kwota transakcji musi byćwiększa niż 0", "splitsBalance": "Suma debetów i kredytów musi być równa kwocie transakcji", "accountRequired": "Wszystkie podziały muszą mieć wybrane konto", "contrahentTypeRequired": "<PERSON><PERSON> kont<PERSON>a musi by<PERSON> wybrany", "date": "Data transakcji jest wymagana"}, "isSchedule": {"label": "Przyszłe"}, "isRegular": {"label": "<PERSON>nie"}, "repeat": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "daily": {"label": "Codziennie"}, "weekly": {"label": "Co tydzień"}, "monthly": {"label": "Co miesiąc"}, "yearly": {"label": "Raz w roku"}, "startDate": {"label": "Data rozpoczecia"}, "endDate": {"label": "Data zakonczenia"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customDates": {"label": "<PERSON><PERSON><PERSON><PERSON> daty"}, "pickDates": {"label": "<PERSON><PERSON><PERSON><PERSON> daty"}}, "TripForm": {"titleEdit": {"label": "Edytuj przejazd"}, "titleNew": {"label": "Nowy przejazd"}, "id": {"label": "ID"}, "startDate": {"label": "Data rozpoczecia"}, "endDate": {"label": "Data zakonczenia"}, "name": {"label": "Nazwa przejazdu"}, "nameDescription": {"label": "Dla powtarzających się przejazdów"}, "description": {"label": "Opis"}, "distance": {"label": "Odleglosc"}, "startOdometer": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "endOdometer": {"label": "Odometr koncowy"}, "startLocId": {"label": "Lokalizacja poczatkowa"}, "endLocId": {"label": "Lokalizacja koncowa"}, "submitEdit": {"label": "Zapisz przejazd"}}, "UserForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON>j <PERSON>kown<PERSON>"}, "titleNew": {"label": "Nowy uzytkownik"}, "firstName": {"label": "<PERSON><PERSON>"}, "lastName": {"label": "Nazwisko"}, "lang": {"label": "Język"}, "jsonMetadata": {"label": "Metadane"}, "description": {"label": "Opis"}, "phone": {"label": "Telefon"}, "email": {"label": "Email"}, "emailVerified": {"label": "<PERSON><PERSON>"}, "isSuperuser": {"label": "Superuser"}, "isAdmin": {"label": "Admin"}, "isLimited": {"label": "Limited"}, "isDisabled": {"label": "Disabled"}, "isInCredit": {"label": "In Credit"}, "isTemp": {"label": "Temp"}, "isDeleted": {"label": "Deleted"}}, "TypeForm": {"titleEdit": {"label": "<PERSON><PERSON><PERSON><PERSON> typ"}, "titleNew": {"label": "Nowy typ"}, "name": {"label": "Nazwa"}, "description": {"label": "Opis"}, "order": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wyświetlania"}}}, "userMenu": {"emails": {"label": "E-maile"}, "applications": {"label": "Aplikacje"}, "forumTopics": {"label": "Tematy forum"}, "comments": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "myFinances": {"label": "<PERSON><PERSON> finanse"}, "settings": {"label": "Ustawienia"}, "userProfile": {"label": "Aktywny profil"}, "emailSettings": {"label": "Ustawienia e-mail"}, "myOrgs": {"label": "<PERSON>je organ<PERSON>"}, "proposals": {"label": "<PERSON><PERSON><PERSON>"}}, "errors": {"somethingWentWrong": {"label": "Coś poszło nie tak"}, "pageNotFound": {"label": "Strona nie znaleziona"}, "unauthorized": {"label": "Brak uprawnień"}, "sessionExpired": {"label": "<PERSON><PERSON><PERSON> w<PERSON>"}}, "sidebar": {"selectValue": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "frameworks": {"react": {"label": "React"}, "angular": {"label": "Angular"}, "vue": {"label": "<PERSON><PERSON>"}, "svelte": {"label": "Svelte"}}}, "storage": {"authorizeGoogleButton": {"label": "Autoryzuj Google Drive"}, "removeAuthorization": {"label": "Usuń autoryzację"}, "selectFiles": {"label": "Wybierz pliki do przesłania"}, "selectFilesPlaceholder": {"label": "<PERSON><PERSON><PERSON><PERSON>, aby wy<PERSON>ć pliki"}, "uploadFiles": {"label": "Prześlij pliki"}, "selectSingleFile": {"label": "Wybierz plik do przesłania"}, "selectSingleFilePlaceholder": {"label": "<PERSON><PERSON><PERSON><PERSON>, aby wy<PERSON>ć plik"}, "uploadFile": {"label": "Prześ<PERSON>j plik"}, "fileDisplay": {"loading": {"label": "Ładowanie plików..."}, "error": {"label": "Błąd podczas ładowania plików"}, "noFiles": {"label": "Brak dostępnych plików"}, "fileName": {"label": "Nazwa pliku"}, "fileId": {"label": "ID pliku"}, "url": {"label": "URL"}}, "camera": {"title": {"label": "Zrób zdjęcie"}, "capture": {"label": "Zrób zdjęcie"}}, "authError": {"label": "Nie udało się autoryzować magazynu"}, "authSuccess": {"label": "Pomyślnie autoryzowano magazyn"}}, "types": {"header": {"label": "<PERSON><PERSON>"}, "default": {"ADMIN": {"label": "<PERSON><PERSON>"}, "MEMBER": {"label": "Czlonkowie organizacji"}, "VENDOR": {"label": "Zakupy - dostawcy produktów"}, "CONTRACTOR": {"label": "Dostawcy usług"}, "MEDIA": {"label": "Dostawcy mediów"}, "EMPLOYEE": {"label": "Pracownicy"}, "CLIENT_GOODS": {"label": "Odbio<PERSON>y <PERSON>ów"}, "CLIENT_SERVICES": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "DIRECTOR": {"label": "Dyrektor spólki z o.o."}}, "job": {"ADMIN": {"label": "<PERSON><PERSON>a"}, "MEMBER": {"label": "Czynnoś<PERSON> członkowskie"}, "VENDOR": {"label": "Pozyskiwanie produktów"}, "CONTRACTOR": {"label": "Pozyskiwanie usług"}, "MEDIA": {"label": "Płatności za media"}, "EMPLOYEE": {"label": "Zatrudnienie"}, "CLIENT_GOODS": {"label": "Sprzedaż towarów"}, "CLIENT_SERVICES": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> usług"}, "DIRECTOR": {"label": "Operacje <PERSON>"}}, "profile": {"ADMIN": {"label": "Administrator", "tag": "Adm"}, "MEMBER": {"label": "Członek", "tag": "<PERSON><PERSON>"}, "VENDOR": {"label": "Dostawca produktów", "tag": "DPr"}, "CONTRACTOR": {"label": "Dostawca usług", "tag": "DUs"}, "MEDIA": {"label": "Dostawca mediów", "tag": "Med"}, "EMPLOYEE": {"label": "Pracownik", "tag": "Prac"}, "CLIENT_GOODS": {"label": "Klient: towary", "tag": "KTow"}, "CLIENT_SERVICES": {"label": "Klient: usł<PERSON><PERSON>", "tag": "KUs"}, "DIRECTOR": {"label": "Dyrektor", "tag": "Dyr"}}}, "problemStatus": {"REPORTED": {"label": "Zgłoszony"}, "IN_PROGRESS": {"label": "<PERSON> trakcie"}, "CLOSED": {"label": "Zamknięty"}}}