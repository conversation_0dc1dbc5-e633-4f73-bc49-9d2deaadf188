import { router } from "@/utils/router";
import type { ToOptions } from '@tanstack/react-router';

type NavOpts = ToOptions<typeof router> & {
  replace?: boolean;
};

export function useInternalNavigate() {
  function internalNavigate(opts: NavOpts) {
    const { replace = false, ...restOpts } = opts;
    const search = (opts.search as any) 
      ? { ...(opts.search as any), _internal: "1" } 
      : { _internal: "1" };
      
    router.navigate({
      ...restOpts,
      search,
      replace,
    });
  }

  return { internalNavigate };
}
