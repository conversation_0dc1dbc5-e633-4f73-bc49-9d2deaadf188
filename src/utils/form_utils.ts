export const normalizeValue = (value: any): any => {
  if (typeof value === "string") {
    // 1. Trim leading/trailing spaces.
    // 2. Reduce multiple spaces to a single space.
    const trimmedValue = value.trim().replace(/\s+/g, " ") || null;

    // If trimmedValue is "true" or "false", convert to boolean.
    if (trimmedValue === "true") {
      return true;
    } else if (trimmedValue === "false") {
      return false;
    }

    // Check if trimmedValue is non-null and consists only of digits.
    if (trimmedValue && /^[0-9]+$/.test(trimmedValue)) {
      // Convert the string of digits to a number.
      return Number(trimmedValue);
    }

    // Otherwise, return the trimmed string or null if empty.
    return trimmedValue;
  }

  // If the original value is an empty string, return null; otherwise return unchanged.
  return value === "" ? null : value;
};

export function normalizeFormData(formData: any): any {
  if (Array.isArray(formData)) {
    // If it’s an array, recurse for each element
    return formData.map(normalizeFormData);
  }

  if (formData !== null && typeof formData === "object") {
    // If it’s an object, recurse for each property
    return Object.fromEntries(
      Object.entries(formData).map(([key, value]) => [
        key,
        normalizeFormData(value),
      ])
    );
  }

  // If it's a number or boolean, convert to string
  if (typeof formData === "number" || typeof formData === "boolean") {
    return String(formData);
  }

  // Otherwise, return as-is
  return formData;
}
