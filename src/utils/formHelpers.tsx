import type { Any<PERSON>ield<PERSON><PERSON> } from "@tanstack/react-form";

// export function FieldInfo({ field }: { field: AnyFieldApi }) {
//   if ((!field.state.meta.isTouched || !field.state.meta.isValid) && field.state.meta.errors.length > 0) {
//     return (
//       <p className="text-red-500 ml-1 text-xs mt-1 ml-2 mb-1">
//         <em>{field.state.meta.errors.map((error) => error.message).join(", ")}</em>
//       </p>
//     );
//   } else if ((!field.state.meta.isTouched || !field.state.meta.isValid) && field.store.state.meta.errors.length > 0) {
//     return (
//       <p className="text-red-500 ml-1 text-xs mt-1 ml-2 mb-1">
//         <em>{field.store.state.meta.errors.map((error) => error.message).join(", ")}</em>
//       </p>
//     );
//   } else {
//     return null;
//   }
// }

type OnFieldChangeTypes = {
  fieldPath: string;
  setChangedFields: React.Dispatch<React.SetStateAction<Set<keyof FormData>>>;
  form: any;
  data?: any;
};

export const onFieldChange = <FormValues extends object>({
  fieldPath,
  setChangedFields,
  form,
  data,
}: {
  fieldPath: any;
  setChangedFields: React.Dispatch<React.SetStateAction<Set<keyof FormValues>>>;
  form: any; // Temporarily using any to avoid import issues
  data?: any;
}) => {
  // Handle nested paths like 'json_metadata.dashboard_modules.proposals'
  const getNestedValue = (obj: any, path: string) => {
    return path.split(".").reduce((o, p) => (o || {})[p], obj);
  };

  const currentValue = getNestedValue(form.state.values, fieldPath);
  const originalValue = data ? getNestedValue(data, fieldPath) : undefined;

  // For objects/arrays, do a deep comparison
  const isEqual = (a: any, b: any): boolean => {
    if (a === b) return true;
    if (a == null || b == null) return false;
    if (typeof a !== "object" || typeof b !== "object") return a === b;

    const aKeys = Object.keys(a);
    const bKeys = Object.keys(b);

    if (aKeys.length !== bKeys.length) return false;

    return aKeys.every((key) => isEqual(a[key], b[key]));
  };

  setChangedFields((prev) => {
    const newSet = new Set(prev);
    if (!isEqual(currentValue, originalValue)) {
      newSet.add(fieldPath as keyof FormValues);
    } else {
      newSet.delete(fieldPath as keyof FormValues);
    }
    return newSet;
  });
};

export const getUpdatedFields = <FormValues extends object>(value: FormValues, changedFields: Set<keyof FormValues>) => {

  // Create an object to hold the updated fields
  const updatedFields: Partial<FormValues> = {};

  // Process each changed field
  for (const fieldPath of changedFields) {
    // Handle nested paths
    const pathParts = (fieldPath as string).split(".");
    let current = value as any;

    // Traverse the path to get the value
    for (let i = 0; i < pathParts.length; i++) {
      if (current === undefined || current === null) break;
      current = current[pathParts[i]];
    }

    // Set the value in updatedFields
    if (pathParts.length === 1) {
      updatedFields[fieldPath as keyof FormValues] = current;
    } else {
      // For nested fields, we need to build the nested structure
      let target: any = updatedFields;
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!target[part]) {
          target[part] = {};
        }
        target = target[part];
      }
      target[pathParts[pathParts.length - 1]] = current;
    }
  }

  return updatedFields;
};

/**
 * Sanitizes form input values by trimming whitespace, converting empty strings,
 * empty arrays, and empty objects to null, and recursively processing nested structures.
 * Preserves non-string types such as numbers and booleans.
 *
 * @param {*} input - The input value to sanitize, which can be a string, array, object,
 *                    number, boolean, null, or undefined.
 * @returns {*} - The sanitized value: null for empty strings/arrays/objects or
 *                undefined inputs, trimmed strings, sanitized arrays/objects, or
 *                unchanged non-string types.
 */
export function sanitizeInput(input: any): any {
  // Handle null or undefined inputs
  if (input === null || input === undefined) {
    return null;
  }

  // Handle string inputs
  if (typeof input === 'string') {
    const trimmed = input.trim();
    return trimmed === '' ? null : trimmed;
  }

  // Handle array inputs
  if (Array.isArray(input)) {
    const sanitizedArray = input
      .map(item => sanitizeInput(item))
      .filter(item => item !== null);
    return sanitizedArray.length === 0 ? null : sanitizedArray;
  }

  // Handle object inputs
  if (typeof input === 'object' && input !== null) {
    const sanitizedObject = {};
    for (const [key, value] of Object.entries(input)) {
      const sanitizedValue = sanitizeInput(value);
      if (sanitizedValue !== null) {
        sanitizedObject[key] = sanitizedValue;
      }
    }
    return Object.keys(sanitizedObject).length === 0 ? null : sanitizedObject;
  }

  // Handle number, boolean, and other types
  return input;
}




// Helper function to normalize string values
export const normalizeString = (value: unknown): unknown => {
  if (typeof value === "string") {
    // Trim leading/trailing spaces and reduce multiple spaces to single space
    return value.trim().replace(/\s+/g, " ") || null;
  }
  return value === "" ? null : value;
};

export const normalizeDataForDB = (data = {}) => {
  const normalizedData = {};

  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      let value = data[key];

      // Handle json_metadata field
      if (key === "json_metadata") {
        // console.log("value", value);
        if (value === null || (typeof value === "object" && Object.keys(value).length === 0)) {
          value = null; // Set to null if it's null or an empty object
        }
        // Otherwise, keep the object as is
      }
      // Convert boolean strings to booleans
      else if (value === "true") {
        value = true;
      } else if (value === "false") {
        value = false;
      }
      // // Convert strings containing only digits to numbers
      // else if (typeof value === "string" && /^[0-9]+$/.test(value)) {
      //   value = Number(value);
      // }
      else if (typeof value === "string" && value === "") {
        value = null;
      }
      // Convert all other values to strings
      // else {
      //   value = value.toString();
      // }

      normalizedData[key] = value;
    }
  }

  return normalizedData;
};

export const denormalizeDataFromDB = (data = {}) => {
  const denormalizedData = {};

  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      let value = data[key];

      // Handle json_metadata field
      if (key === "json_metadata") {
        if (value === null) {
          value = {}; // Convert null to an empty object
        }
        // Otherwise, keep the object as is
      }
      // Convert null or undefined to empty string
      else if (value === null || value === undefined) {
        value = "";
      }
      // Convert booleans to strings
      // else if (typeof value === "boolean") {
      //   value = value.toString();
      // }
      // Convert numbers to strings
      else if (typeof value === "number") {
        value = value.toString();
      }
      // Convert objects to JSON strings
      else if (typeof value === "object") {
        value = JSON.stringify(value);
      }
      // // Convert other types to strings
      // else if (typeof value !== "string") {
      //   value = String(value);
      // }

      denormalizedData[key] = value;
    }
  }

  return denormalizedData;
};
