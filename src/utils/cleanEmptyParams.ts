export const DEFAULT_PAGE_INDEX = 0;
export const DEFAULT_PAGE_SIZE = 20;

import type { ClientOptionsTypes } from "@/api/_client/types.gen";

// export const cleanEmptyParams = (search: Record<string, any>) => {
//   const newSearch = { ...search };
//   Object.keys(newSearch).forEach((key) => {
//     const value = newSearch[key];
//     if (
//       value === undefined ||
//       value === "" ||
//       (typeof value === "number" && isNaN(value)) ||
//       (Array.isArray(value) && value.length === 0)
//     )
//       delete newSearch[key];
//   });

//   // if (newSearch.pageIndex === DEFAULT_PAGE_INDEX) delete newSearch.pageIndex;
//   // if (newSearch.pageSize === DEFAULT_PAGE_SIZE) delete newSearch.pageSize;

//   return newSearch;
// };

export const cleanEmptyParams = <T extends Record<string, any>>(search: T | null | undefined): Partial<T> => {
  if (!search) return {};
  
  const newSearch = { ...search } as Partial<T>;
  
  const isEmpty = (value: unknown): boolean => {
    if (value === undefined || value === null || value === "") return true;
    if (typeof value === "number" && Number.isNaN(value)) return true;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === "object" && value !== null) {
      return Object.keys(cleanEmptyParams(value as Record<string, any>)).length === 0;
    }
    return false;
  };

  for (const key in newSearch) {
    const value = newSearch[key];
    
    if (isEmpty(value)) {
      delete newSearch[key];
    } else if (typeof value === "object" && value !== null && !Array.isArray(value)) {
      const cleaned = cleanEmptyParams(value as Record<string, any>);
      if (Object.keys(cleaned).length === 0) {
        delete newSearch[key];
      } else {
        // Use type assertion to handle the recursive type assignment
        (newSearch as any)[key] = cleaned;
      }
    }
  }
  
  return newSearch;
};
