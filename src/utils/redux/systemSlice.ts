import { createSlice } from "@reduxjs/toolkit";

const SYSTEM: SystemTypes = {
  code: "",
  module: "dashboard",
  sidebarOpen: true,
  orgName: "",
  pageTitle: "",
  theme: "",
};

export interface SystemTypes {
  code: string;
  module: string;
  sidebarOpen: boolean;
  orgName: string;
  pageTitle: string;
  theme: string;
}

const initialState: SystemTypes = { ...SYSTEM };

export const systemSlice = createSlice({
  name: "system",
  initialState: initialState,

  reducers: {
    updateSystem: (state, action) => {
      return action.payload;
    },
    updateSystemField: (state, action) => {
      for (const key in action.payload) {
        if (Object.prototype.hasOwnProperty.call(state, key)) {
          state[key] = action.payload[key];
        }
      }
    },
    reset: () => {
      return SYSTEM;
    },
  },
});

export const { reset, updateSystem, updateSystemField } = systemSlice.actions;

export default systemSlice.reducer;
