import { configureStore } from "@reduxjs/toolkit";
import type {
  KeycloakUserTypes,
  OrgDisplayTypes,
  ProfileDisplayTypes,
  TypeDisplayTypes,
} from "@/api/_client/types.gen";
import type { ObjectsTypes } from "./objectsSlice";
import objectsReducer from "./objectsSlice";
import orgReducer from "./orgSlice";
import profileReducer from "./profileSlice";
import type { SystemTypes } from "./systemSlice";
import systemReducer from "./systemSlice";
import typesReducer from "./typesSlice";
import userProfileReducer from "./userProfileSlice";
import userReducer, { type UserState } from "./userSlice";

export interface RootStateTypes {
  profile: ProfileDisplayTypes;
  org: OrgDisplayTypes;
  system: SystemTypes;
  user: (UserState & { roles: string[] }) | null;
  objects: ObjectsTypes;
  types: TypeDisplayTypes[];
}

const store = configureStore({
  reducer: {
    profile: profileReducer,
    org: orgReducer,
    system: systemReducer,
    user: userReducer,
    userProfile: userProfileReducer,
    objects: objectsReducer,
    types: typesReducer,
  },
  devTools: import.meta.env.PUBLIC_ENV === "dev",
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
