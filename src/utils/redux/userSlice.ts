import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type { KeycloakUserTypes } from "@/api/_client/types.gen";

const USER: KeycloakUserTypes = {
  id: 1,
  keycloak_id: "",
  first_name: "",
  last_name: "",
  lang: "",
  json_metadata: null,
  description: null,
  phone: null,
  email: null,
  curr_profile_id: 0,
  curr_org_id: 0,
  profiles: [],
};

export type UserState = {
  data: KeycloakUserTypes | null;
  isLoading: boolean;
  error: Error | null;
};

const initialState: UserState = { data: USER, isLoading: false, error: null };

export const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state: UserState, action: PayloadAction<KeycloakUserTypes | null>) => {
      state.data = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setUserLoading: (state: UserState, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setUserError: (state: UserState, action: PayloadAction<Error | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    resetUser: (state: UserState) => {
      state.data = null;
      state.isLoading = false;
      state.error = null;
    },
    updateUserField: (state: UserState, action: PayloadAction<{ field: string; value: any }>) => {
      const { field, value } = action.payload;
      if (state.data) {
        state.data = {
          ...state.data,
          [field]: value,
        };
      }
    },
  },
});

export const { setUser, setUserLoading, setUserError, resetUser, updateUserField } = userSlice.actions;

export default userSlice.reducer;
