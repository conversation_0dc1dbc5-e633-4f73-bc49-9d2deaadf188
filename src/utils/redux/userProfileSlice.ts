import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { UserProfile, UserProfileState } from "./types";
import type { RootState } from "./store";

const initialState: UserProfileState = {
  data: null,
  isLoading: false,
  error: null,
};

export const userProfileSlice = createSlice({
  name: "userProfile",
  initialState,
  reducers: {
    setUserProfile: (state, action: PayloadAction<UserProfile | null>) => {
      state.data = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setUserProfileLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setUserProfileError: (state, action: PayloadAction<Error | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearUserProfile: (state) => {
      state.data = null;
      state.isLoading = false;
      state.error = null;
    },
  },
});

export const {
  setUserProfile,
  setUserProfileLoading,
  setUserProfileError,
  clearUserProfile,
} = userProfileSlice.actions;

export const selectUserProfile = (state: RootState) => state.userProfile;

export default userProfileSlice.reducer;
