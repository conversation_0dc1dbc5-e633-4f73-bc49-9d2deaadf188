import { createSlice } from "@reduxjs/toolkit";
import type { TypeDisplayTypes } from "@/api/_client/types.gen";

const TYPES = [
  {
    id: 8,
    name: "VENDOR",
    description: "Contrahent we buy goods from",
    json_metadata: null,
    order: 1,
  },
  {
    id: 7,
    name: "CONTRACTOR",
    description: "Contrahent we buy services from",
    json_metadata: null,
    order: 2,
  },
  {
    id: 5,
    name: "CLIENT_GOODS",
    description: "Client we sell goods to",
    json_metadata: null,
    order: 3,
  },
  {
    id: 4,
    name: "CLIENT_SERVICES",
    description: "Client we sell services to",
    json_metadata: null,
    order: 4,
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON>",
    description: "Supplier of media",
    json_metadata: null,
    order: 5,
  },
  {
    id: 6,
    name: "MEMBER",
    description: "Organization member",
    json_metadata: null,
    order: 6,
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    description: "Org employee",
    json_metadata: null,
    order: 7,
  },
  {
    id: 9,
    name: "DIRECTO<PERSON>",
    description: "Director",
    json_metadata: null,
    order: 8,
  },
  {
    id: 10,
    name: "AD<PERSON><PERSON>",
    description: "Organization administrator",
    json_metadata: null,
    order: 9,
  },
];

export const typesSlice = createSlice({
  name: "types",
  initialState: TYPES,

  reducers: {
    setTypes: (state, action) => {
      return action.payload;
    },
    updateTypesField: (state, action) => {
      for (const key in action.payload) {
        if (Object.prototype.hasOwnProperty.call(state, key)) {
          state[key] = action.payload[key];
        }
      }
    },

    reset: () => {
      return TYPES;
    },
  },
});

export const { reset, setTypes, updateTypesField } = typesSlice.actions;

export default typesSlice.reducer;
