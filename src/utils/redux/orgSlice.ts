import { createSlice } from "@reduxjs/toolkit";
import type { OrgDisplayTypes } from "@/api/_client/types.gen";

const DEFAULT_ORG = {
  id: 0,
  created_at: "",
  updated_at: "",
  created_by: 0,
  updated_by: null,
  lang: "",
  name: "",
  description: null,
  is_public: false,
  address_id: null,
  is_formal: false,
  nip: null,
  regon: null,
  max_img_width: 0,
  total_shares: 0,
  accounts_set_id: null,
  voting_days: 0,
  members_by_admin: false,
  is_current: false,
  json_metadata: {
    storage: {
      storage_type: "",
      email: "",
      tokens: {
        access_token: "",
        scope: "",
        id_token: "",
        expiry_date: 0,
        refresh_token: "",
        token_type: "",
      },
      s3: {
        bucket_name: "",
        region: "",
        access_key_id: "",
        secret_access_key: "",
        account_id: "",
        endpoint: "",
      },
      dropbox: {
        access_token: "",
        account_id: "",
        folder: "",
      },
    },
  },
};

const DEFAULT_JSON_METADATA = {
  storage: {
    storage_type: "",
    email: "",
    tokens: {
      access_token: "",
      scope: "",
      id_token: "",
      expiry_date: 0,
      refresh_token: "",
      token_type: "",
    },
  },
};
const GOOGLE_STORAGE = {
  storage_type: "google",
  email: "",
  tokens: {
    access_token: "",
    scope: "",
    id_token: "",
    expiry_date: 0,
    refresh_token: "",
    token_type: "",
  },
};
const S3_STORAGE = {
  storage_type: "s3",
  email: "",
  tokens: {
    access_token: "",
    scope: "",
    id_token: "",
    expiry_date: 0,
    refresh_token: "",
    token_type: "",
  },
};

const initialState: OrgDisplayTypes = DEFAULT_ORG;

export const orgSlice = createSlice({
  name: "org",
  initialState,
  reducers: {
    updateOrg: (state, action) => {
      return action.payload;
    },
    updateOrgField: (state, action) => {
      // Check if action.payload has a 'field' property (old format)
      if (action.payload.field && "value" in action.payload) {
        const { field, value } = action.payload;
        if (state && field in state) {
          state[field] = value;
        }
      } else {
        // Handle direct key-value format
        Object.entries(action.payload).forEach(([field, value]) => {
          if (state && field in state) {
            state[field] = value;
          }
        });
      }
    },
    updateOrgFields: (state, action) => {
      return {
        ...state,
        ...action.payload,
      };
    },
    resetOrg: () => {
      return DEFAULT_ORG;
    },

    resetStorage: (state) => {
      return {
        ...state,
        json_metadata: DEFAULT_JSON_METADATA,
      };
    },
    setStorageType: (state, action) => {
      // if (state && state.json_metadata && state.json_metadata.storage) {
      //   state.json_metadata.storage.storage_type = action.payload;
      //
      if (state.json_metadata === null) {
        state.json_metadata = DEFAULT_JSON_METADATA;
      }
      if (action.payload === "google") {
        state.json_metadata!.storage = GOOGLE_STORAGE;
      } else if (action.payload === "s3") {
        state.json_metadata!.storage = S3_STORAGE;
      }
    },
  },
});

export const { updateOrg, updateOrgField, updateOrgFields, resetOrg, resetStorage, setStorageType } = orgSlice.actions;

export default orgSlice.reducer;
