import { createSlice } from "@reduxjs/toolkit";
import type { ProfileDisplayTypes } from "@/api/_client/types.gen";
import { defaultProfile } from "@/api/crm/profiles/defaultProfile.ts";

// const defaultProfile = {
//   id: 0,
//   name: "",
//   user_id: 0,
//   type_id: 0,
//   created_at: "",
//   updated_at: "",
//   created_by: 0,
//   updated_by: null,
//   lang: "",
//   json_metadata: {
//     dashboard_modules: {
//       proposals: true,
//       contracts: true,
//       jobs: true,
//       trips: true,
//       transactions: true,
//       problems: true,
//       votings: true,
//     },
//     jobs: {
//       calendar_objects: [],
//       calendar_period: [0, 7],
//     },
//     member: {
//       is_voting: false,
//       shares: "",
//       znaczaca_operacja_limit: "",
//     },
//     query_args: [
//       {
//         id: "",
//         name: "default",
//         path: "/app/dashboard/jobs/",
//         server_args: {
//           org_id: 0,
//           is_public: false,
//           contrahent_id: undefined,
//           date_column: "start_date",
//           start_date: undefined,
//           end_date: undefined,
//           statuses: [],
//           page_index: 0,
//           page_size: 500,
//           search: [],
//           order: [],
//         },
//         client_args: {
//           pagination: {
//             pageIndex: 0,
//             pageSize: 5,
//           },
//           sorting: [],
//           columnFilters: [],
//           columnOrder: [],
//           columnVisibility: {},
//           globalFilter: "",
//           startDate: "",
//           endDate: "",
//         },
//       }
//     ],

//   },
//   description: null,
//   display_name: null,
//   is_company: false,

//   org_id: 0,
//   is_current: false,
//   org_name: null,
//   object_id: null,
// };

const initialState: ProfileDisplayTypes = { ...defaultProfile };

export const currProfileSlice = createSlice({
  name: "profile",
  initialState: initialState,

  reducers: {
    updateProfile: (_state, action) => {
      return action.payload;
    },
    updateProfileField: (state, action) => {
      const { field, value } = action.payload;
      if (field in state) {
        state[field] = value;
      }
    },
    updateCalendarJobsObjects: (state, action) => {
      if (!state.json_metadata) {
        state.json_metadata = { dashboard_modules: {} as any, jobs: { calendar_objects: [], calendar_period: [0, 7] } };
      }
      if (!state.json_metadata.jobs) {
        state.json_metadata.jobs = { calendar_objects: [], calendar_period: [0, 7] };
      }
      state.json_metadata.jobs.calendar_objects = action.payload;
    },
    updateCalendarJobsPeriod: (state, action) => {
      if (!state.json_metadata) {
        state.json_metadata = { dashboard_modules: {} as any, jobs: { calendar_objects: [], calendar_period: [0, 7] } };
      }
      if (!state.json_metadata.jobs) {
        state.json_metadata.jobs = { calendar_objects: [], calendar_period: [0, 7] };
      }
      state.json_metadata.jobs.calendar_period = action.payload;
    },
    resetProfile: () => {
      return defaultProfile;
    },
  },
});

export const { resetProfile, updateProfile, updateProfileField, updateCalendarJobsObjects, updateCalendarJobsPeriod } =
  currProfileSlice.actions;

export default currProfileSlice.reducer;
