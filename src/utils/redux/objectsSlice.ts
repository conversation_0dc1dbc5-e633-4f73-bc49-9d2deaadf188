import { createSlice } from "@reduxjs/toolkit";

const OBJECTS: ObjectsTypes = {
  objectsTreeMode: "navigate",
  selectedNodes: [],
};

export interface ObjectsTypes {
  objectsTreeMode: "navigate" | "selectOne" | "selectMany";
  selectedNodes: any[];
}

const initialState: ObjectsTypes = { ...OBJECTS };

export const objectsSlice = createSlice({
  name: "objects",
  initialState: initialState,

  reducers: {
    updateObjects: (state, action) => {
      return action.payload;
    },
    updateObjectsField: (state, action) => {
      for (const key in action.payload) {
        if (Object.prototype.hasOwnProperty.call(state, key)) {
          state[key] = action.payload[key];
        }
      }
    },

    setSelectedNodes: (state, action) => {
      state.selectedNodes = action.payload;
    },

    updateSelectedNodes: (state, action) => {
      const { node, checked } = action.payload;
      if (state.objectsTreeMode === "selectOne") {
        state.selectedNodes = checked ? [node] : [];
      } else if (state.objectsTreeMode === "selectMany") {
        if (checked) {
          state.selectedNodes = [...state.selectedNodes, node];
        } else {
          state.selectedNodes = state.selectedNodes.filter(
            (item) => item.id !== node.id
          );
        }
      }
    },
    reset: () => {
      return OBJECTS;
    },
  },
});

export const {
  reset,
  updateObjects,
  updateObjectsField,
  updateSelectedNodes,
  setSelectedNodes,
} = objectsSlice.actions;

export default objectsSlice.reducer; 