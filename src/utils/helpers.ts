// get mimetype from file extesion
const mimeTypes = {
  // Images
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
  png: "image/png",
  gif: "image/gif",
  bmp: "image/bmp",
  webp: "image/webp",
  tiff: "image/tiff",
  ico: "image/x-icon",
  jfif: "image/jpeg",
  svg: "image/svg+xml",
  heic: "image/heic",
  bat: "image/bat",
  indd: "application/x-indesign",

  // Documents
  pdf: "application/pdf",
  doc: "application/msword",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ppt: "application/vnd.ms-powerpoint",
  pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  txt: "text/plain",
  rtf: "application/rtf",
  odt: "application/vnd.oasis.opendocument.text",
  ods: "application/vnd.oasis.opendocument.spreadsheet",
  odp: "application/vnd.oasis.opendocument.presentation",

  // Open Documents
  ott: "application/vnd.oasis.opendocument.text-template",
  ots: "application/vnd.oasis.opendocument.spreadsheet-template",
  otp: "application/vnd.oasis.opendocument.presentation-template",
  odg: "application/vnd.oasis.opendocument.graphics",
  otg: "application/vnd.oasis.opendocument.graphics-template",

  // Spreadsheets
  csv: "text/csv",
  tsv: "text/tab-separated-values",

  // Audio
  mp3: "audio/mpeg",
  wav: "audio/wav",

  // Video
  mp4: "video/mp4",
  avi: "video/x-msvideo",

  // Archives
  zip: "application/zip",
  rar: "application/x-rar-compressed",
  tar: "application/x-tar",
  "7z": "application/x-7z-compressed",

  // Others
  json: "application/json",
  xml: "application/xml",
  html: "text/html",
  js: "application/javascript",
  css: "text/css",
};

export function getMimeType(filename: string) {
  // Extract the extension from the filename
  const extension = filename.split(".").pop() || "";
  // Look up the MIME type based on the extension
  return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
}
// Example usage
// const mimeType = getMimeType('jpg');  // 'image/jpeg'

export function toFileName(inputString) {
  // Map of Polish characters to their English equivalents
  const polishToEnglishMap = {
    ą: "a",
    ć: "c",
    ę: "e",
    ł: "l",
    ń: "n",
    ó: "o",
    ś: "s",
    ź: "z",
    ż: "z",
    Ą: "A",
    Ć: "C",
    Ę: "E",
    Ł: "L",
    Ń: "N",
    Ó: "O",
    Ś: "S",
    Ź: "Z",
    Ż: "Z",
  };

  // Replace each Polish character using the map
  let fileName = inputString.replace(/[ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/g, (char) => {
    return polishToEnglishMap[char] || char;
  });

  // Replace spaces with underscores
  fileName = fileName.replace(/\s+/g, "_");

  // Remove all non-alphanumeric characters and underscores (after Polish chars are replaced)
  fileName = fileName.replace(/[^a-zA-Z0-9_]/g, "");

  // Remove any leading or trailing underscores
  fileName = fileName.replace(/^_+|_+$/g, "");

  return fileName;
}

export function formatDate(isoString) {
  // Create a Date object from the ISO string
  const date = new Date(isoString);

  // Extract day, month, and year
  const day = String(date.getDate()).padStart(2, "0"); // Ensure 2 digits
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const year = date.getFullYear();

  // Return the formatted date as DD-MM-YYYY
  return `${day}-${month}-${year}`;
}

// Utility function to get the group from the account number string
export const getGroupFromString = (accountNumber: string) => {
  if (!accountNumber || typeof accountNumber !== "string") {
    throw new Error("Invalid account number string");
  }
  // Extract the first character and convert it to a number
  const group = parseInt(accountNumber.charAt(0), 10);
  if (isNaN(group)) {
    throw new Error("Invalid group in account number");
  }
  return group;
};

// Utility function to get the level from the account number string
export const getLevelFromString = (accountNumber: string) => {
  if (typeof accountNumber !== "string" || !/^[0-9]+(-[0-9]+)*$/.test(accountNumber)) {
    throw new Error("Invalid account number format");
  }

  // Split the account number by hyphens to determine the levels
  const segments = accountNumber.split("-");

  // Level 0 is determined by a single digit followed by "00"
  if (segments.length === 1 && /^\d00$/.test(segments[0])) {
    return 0;
  }

  // Level 1 is determined by a single digit and two digits not "00"
  if (segments.length === 1 && /^\d(?!00)\d{2}$/.test(segments[0])) {
    return 1;
  }

  // For account numbers with multiple segments (e.g., "211-01")
  // Level 2 is determined by having 2 segments
  if (segments.length === 2) {
    return 2;
  }

  // Additional levels are determined by the number of segments (dashes indicate further levels)
  return segments.length;
};

// import { ObjectTypeDisplayTypes } from "@/api/_client/types.gen";

// /**
//  * Groups object types by their group property
//  * @param objectTypes Array of object types
//  * @returns Array of grouped object types
//  */
// export const objectTypesByGroup = (objectTypes: ObjectTypeDisplayTypes[]) => {
//   // Define explicit group order
//   const groupOrder = ["Przestrzenne", "Elektromechaniczne", "Niematerialne", "Inne"];

//   // Create a map to store items by group
//   const groupMap: Record<string, ObjectTypeDisplayTypes[]> = {};

//   // Initialize all groups with empty arrays
//   groupOrder.forEach((group) => {
//     groupMap[group] = [];
//   });

//   // Add "Other" group for items with undefined group
//   groupMap["Other"] = [];

//   // Group items by their group property
//   objectTypes.forEach((item) => {
//     // Use 'Other' as default group if group is null or undefined
//     const groupName = item.group || "Other";

//     // If the group is not in our predefined list, put it in "Other"
//     const targetGroup = groupOrder.includes(groupName) ? groupName : "Other";

//     // Add the item to its group
//     groupMap[targetGroup].push(item);
//   });

//   // Sort items in each group by name
//   Object.keys(groupMap).forEach((group) => {
//     groupMap[group].sort((a, b) => a.name.localeCompare(b.name));
//   });

//   // Convert the map to the desired array format in the specified order
//   const result = groupOrder.map((group) => ({
//     group,
//     items: groupMap[group],
//   }));

//   // Add "Other" group at the end if it has any items
//   if (groupMap["Other"].length > 0 && !groupOrder.includes("Other")) {
//     result.push({
//       group: "Other",
//       items: groupMap["Other"],
//     });
//   }

//   return result;
// };

// // New helper: map job type to allowed contrahent options for Select components
// import type { ContrahentDisplayTypes } from "@/api/_client/types.gen";

// /**
//  * Returns an array of objects compatible with Mantine's <Select /> data prop, filtered
//  * according to the current job type.
//  *
//  * Mapping rules:
//  *  - contractor          -> client_services
//  *  - vendor              -> client_goods
//  *  - client_services     -> contractor
//  *  - client_goods        -> vendor
//  *  - any other value     -> matches itself (e.g. media -> media)
//  *
//  * @param contrahents  Array with all available contrahents
//  * @param jobType      Currently selected job type
//  */
// export const getContrahentSelectOptions = (
//   contrahents: ContrahentDisplayTypes[] | undefined,
//   jobType: string | null | undefined,
// ) => {
//   // console.log("contrahents", contrahents);
//   // console.log("jobType", jobType);
//   if (!contrahents || !jobType) return [];

//   // Normalize case – API may return upper-case enum names (e.g. CLIENT_SERVICES)
//   const key = jobType.toUpperCase();

//   // Define cross-mapping between job types and allowed contrahent types (all upper-case)
//   const typeMap: Record<string, string> = {
//     CONTRACTOR: "CLIENT_SERVICES",
//     VENDOR: "CLIENT_GOODS",
//     CLIENT_SERVICES: "CONTRACTOR",
//     CLIENT_GOODS: "VENDOR",
//   };

//   const targetType = typeMap[key] ?? key; // same type for 1-to-1 mapping

//   return contrahents
//     .filter((c) => c.types?.some((t) => t.name === targetType))
//     .map((c) => ({ value: c.id.toString(), label: c.name }));
// };

export const removeIdFromObjects = <T extends { id: string }>(objects: T[]) => {
  if (!objects) return [];
  return objects.map((obj) => ({ ...obj, id: undefined }) as Omit<T, "id">);
};
