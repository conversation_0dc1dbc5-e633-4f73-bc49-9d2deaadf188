// src/contexts/upload.tsx

import axios from "axios";
import { get, set } from "idb-keyval";
import React, { createContext, type ReactNode, useContext, useEffect, useReducer, useRef } from "react";
import { useConnectionQuality } from "@/hooks/useConnectionQuality";
//   import { toast } from "@/components/ui/use-toast";

/* ────────────── types ────────────── */
export type Status = "pending" | "queued" | "uploaded" | "error";
export type Quality = "good" | "poor" | "offline";

export interface Item {
  id: string;
  name: string;
  size: number;
  type: string;
  status: Status;
  progress: number; // 0–100
  file?: File; // only in current session
}

/* ────────────── state / reducer ────────────── */
interface State {
  items: Item[];
  queue: number;
  quality: Quality;
}

type Action =
  | { type: "SET_ITEMS"; items: Item[] }
  | { type: "ADD_ITEM"; item: Item }
  | { type: "UPDATE_ITEM"; id: string; status: Status }
  | { type: "PROGRESS"; id: string; pct: number }
  | { type: "REMOVE_ITEM"; id: string }
  | { type: "SET_QUEUE"; length: number }
  | { type: "SET_QUALITY"; quality: Quality };

function reducer(state: State, act: Action): State {
  switch (act.type) {
    case "SET_ITEMS":
      return { ...state, items: act.items };

    case "ADD_ITEM":
      return { ...state, items: [...state.items, act.item] };

    case "UPDATE_ITEM":
      return {
        ...state,
        items: state.items.map((i) => (i.id === act.id ? { ...i, status: act.status, progress: 100 } : i)),
      };

    case "PROGRESS":
      return {
        ...state,
        items: state.items.map((i) => (i.id === act.id ? { ...i, progress: act.pct } : i)),
      };

    case "REMOVE_ITEM":
      return { ...state, items: state.items.filter((i) => i.id !== act.id) };

    case "SET_QUEUE":
      return { ...state, queue: act.length };

    case "SET_QUALITY":
      return { ...state, quality: act.quality };

    default:
      return state;
  }
}

/* ────────────── context helpers ────────────── */
interface UploadCtx {
  state: State;
  addFiles: (files: File[]) => void;
  removeItem: (id: string) => void;
}

const UploadContext = createContext<UploadCtx | undefined>(undefined);
export const useUpload = () => {
  const ctx = useContext(UploadContext);
  if (!ctx) throw new Error("useUpload must be used inside <UploadProvider>");
  return ctx;
};

/* ────────────── constants ────────────── */
const DB_KEY = "uploads-v1";

/* ────────────── provider ────────────── */
export function UploadProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(reducer, {
    items: [],
    queue: 0,
    quality: navigator.onLine ? "good" : "offline",
  });

  const quality = useConnectionQuality();
  const swRef = useRef<ServiceWorker | null>(null);

  /* 1 ▸ load saved metadata once */
  useEffect(() => {
    (async () => {
      const stored = (await get(DB_KEY)) as Item[] | undefined;
      if (stored) dispatch({ type: "SET_ITEMS", items: stored });
    })();
  }, []);

  /* 2 ▸ persist metadata on every change (strip File) */
  useEffect(() => {
    const meta = state.items.map(({ file, ...rest }) => rest);
    set(DB_KEY, meta).catch(console.error);
  }, [state.items]);

  /* 3 ▸ service-worker wiring */
  useEffect(() => {
    if (!("serviceWorker" in navigator)) return;

    navigator.serviceWorker.ready.then((reg) => {
      swRef.current = reg.active;
      swRef.current?.postMessage({ type: "GET_QUEUE_LENGTH" });

      navigator.serviceWorker.addEventListener("message", (ev) => {
        if (ev.data?.type === "QUEUE_LENGTH") {
          dispatch({ type: "SET_QUEUE", length: ev.data.length });
        }
      });
    });
  }, []);

  /* 4 ▸ forward connection quality */
  useEffect(() => {
    dispatch({ type: "SET_QUALITY", quality });
    swRef.current?.postMessage({ type: "CONNECTION_QUALITY", value: quality });
  }, [quality]);

  /* 5 ▸ helpers */
  async function addFiles(files: File[]) {
    for (const file of files) {
      const id = crypto.randomUUID();
      const item: Item = {
        id,
        name: file.name,
        size: file.size,
        type: file.type,
        status: "pending",
        progress: 0,
        file,
      };
      dispatch({ type: "ADD_ITEM", item });

      const body = new FormData();
      body.append("file", file);

      try {
        await axios.post("/api/upload", body, {
          onUploadProgress: (ev) => {
            if (ev.total) {
              const pct = Math.round((ev.loaded / ev.total) * 100);
              dispatch({ type: "PROGRESS", id, pct });
            }
          },
        });

        dispatch({ type: "UPDATE_ITEM", id, status: "uploaded" });
        // toast({ title: `${file.name} uploaded`, duration: 3000 });
      } catch {
        dispatch({ type: "UPDATE_ITEM", id, status: "queued" });
        /* the SW’s Background-Sync will replay; status flips later */
      }
    }
  }

  function removeItem(id: string) {
    dispatch({ type: "REMOVE_ITEM", id });
    swRef.current?.postMessage({ type: "CANCEL_ITEM", id }); // optional
  }

  const value: UploadCtx = { state, addFiles, removeItem };
  return <UploadContext.Provider value={value}>{children}</UploadContext.Provider>;
}

// import axios from "axios";
// import { get, set } from "idb-keyval";
// import React, { createContext, type ReactNode, useContext, useEffect, useReducer, useRef } from "react";
// import { useConnectionQuality } from "@/hooks/useConnectionQuality";

// // ---------- helper ----------
// const DB_KEY = "uploads-v1";

// // ---------- types ----------
// export type Status = "pending" | "queued" | "uploaded" | "error";
// export type Quality = "good" | "poor" | "offline";

// export interface Item {
//   id: string; // uuid (stable key)
//   name: string;
//   size: number;
//   type: string;
//   status: Status;
//   progress: number; // 0-100
//   file?: File; // present only in current session
// }

// // ---------- state / reducer ----------
// interface State {
//   items: Item[];
//   queue: number;
//   quality: Quality;
// }

// type Action =
//   | { type: "SET_ITEMS"; items: Item[] }
//   | { type: "ADD_FILES"; files: File[] }
//   | { type: "UPDATE_ITEM"; id: string; status: Status }
//   | { type: "REMOVE_ITEM"; id: string }
//   | { type: "SET_QUEUE"; length: number }
//   | { type: "SET_QUALITY"; quality: Quality }
//   | { type: "PROGRESS"; id: string; pct: number };

// function reducer(state: State, act: Action): State {
//   switch (act.type) {
//     case "SET_ITEMS":
//       return { ...state, items: act.items };
//     case "ADD_FILES":
//       return {
//         ...state,
//         items: [
//           ...state.items,
//           ...act.files.map((f) => ({
//             id: crypto.randomUUID(),
//             name: f.name,
//             size: f.size,
//             type: f.type,
//             status: "pending" as Status,
//             progress: 0,
//             file: f,
//           })),
//         ],
//       };
//     case "UPDATE_ITEM":
//       return {
//         ...state,
//         items: state.items.map((i) => (i.id === act.id ? { ...i, status: act.status } : i)),
//       };
//     case "REMOVE_ITEM":
//       return { ...state, items: state.items.filter((i) => i.id !== act.id) };
//     case "SET_QUEUE":
//       return { ...state, queue: act.length };
//     case "SET_QUALITY":
//       return { ...state, quality: act.quality };
//     case "PROGRESS":
//       return {
//         ...state,
//         items: state.items.map((i) => (i.id === act.id ? { ...i, progress: act.pct } : i)),
//       };
//     default:
//       return state;
//   }
// }

// // ---------- context ----------
// interface UploadCtx {
//   state: State;
//   addFiles: (files: File[]) => void;
//   removeItem: (id: string) => void;
// }

// const UploadContext = createContext<UploadCtx | undefined>(undefined);
// export const useUpload = () => {
//   const ctx = useContext(UploadContext);
//   if (!ctx) throw new Error("useUpload must be used inside <UploadProvider>");
//   return ctx;
// };

// // ---------- provider ----------
// export function UploadProvider({ children }: { children: ReactNode }) {
//   const [state, dispatch] = useReducer(reducer, {
//     items: [],
//     queue: 0,
//     quality: navigator.onLine ? "good" : "offline",
//   });

//   const quality = useConnectionQuality();
//   const swRef = useRef<ServiceWorker | null>(null);

//   /* ⇢ 1.  Load saved items on first mount */
//   useEffect(() => {
//     (async () => {
//       const stored = (await get(DB_KEY)) as Item[] | undefined;
//       if (stored) dispatch({ type: "SET_ITEMS", items: stored });
//     })();
//   }, []);

//   /* ⇢ 2.  Persist every change to IndexedDB (metadata only) */
//   useEffect(() => {
//     // strip transient File objects before persisting
//     set(
//       DB_KEY,
//       state.items.map(({ file, ...rest }) => rest),
//     ).catch(console.error);
//   }, [state.items]);

//   /* ⇢ 3.  SW wiring (queue length + quality forwarding) */
//   useEffect(() => {
//     if (!("serviceWorker" in navigator)) return;

//     navigator.serviceWorker.ready.then((reg) => {
//       swRef.current = reg.active;
//       swRef.current?.postMessage({ type: "GET_QUEUE_LENGTH" });

//       navigator.serviceWorker.addEventListener("message", (ev) => {
//         if (ev.data?.type === "QUEUE_LENGTH") {
//           dispatch({ type: "SET_QUEUE", length: ev.data.length });
//         }
//       });
//     });
//   }, []);

//   useEffect(() => {
//     dispatch({ type: "SET_QUALITY", quality });
//     swRef.current?.postMessage({ type: "CONNECTION_QUALITY", value: quality });
//   }, [quality]);

//   /* ⇢ 4.  helpers -------------------------------------------------- */
//   async function addFiles(files: File[]) {
//     for (const file of files) {
//       const id = crypto.randomUUID();
//       dispatch({ type: "ADD_FILES", files: [file] });

//       const body = new FormData();
//       body.append("file", file);

//       try {
//         await axios.post("/api/upload", body, {
//           onUploadProgress: (ev) => {
//             if (ev.total) {
//               const pct = Math.round((ev.loaded / ev.total) * 100);
//               dispatch({ type: "PROGRESS", id, pct });
//             }
//           },
//         });
//         dispatch({ type: "UPDATE_ITEM", id, status: "uploaded" });
//       } catch {
//         /* offline → SW queued the request */
//         dispatch({ type: "UPDATE_ITEM", id, status: "queued" });
//       }
//     }
//   }

//   function removeItem(id: string) {
//     dispatch({ type: "REMOVE_ITEM", id });
//     swRef.current?.postMessage({ type: "CANCEL_ITEM", id }); // optional
//   }

//   /* ⇢ 5.  provide -------------------------------------------------- */
//   const value: UploadCtx = { state, addFiles, removeItem };
//   return <UploadContext.Provider value={value}>{children}</UploadContext.Provider>;
// }
