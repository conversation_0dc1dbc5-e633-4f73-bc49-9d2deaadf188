import type { Query<PERSON><PERSON> } from "@tanstack/react-query";
import { MutationCache, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createRouter } from "@tanstack/react-router";
import { toast } from "sonner";
import { routeTree } from "@/dosiero/routeTree.gen";
import type { RouteContext } from "@/types/router";

// import { queryClient } from "@/dosiero/main";

declare module "@tanstack/react-query" {
    interface Register {
        mutationMeta: {
            invalidatesQuery?: QueryKey;
            successMessage?: string;
            errorMessage?: string;
        };
    }
}

// Initialize QueryClient
export const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            staleTime: 1000 * 60 * 5, // 5 minutes
        },
    },
    mutationCache: new MutationCache({
        onSuccess: (_data, _variables, _context, mutation) => {
            if (mutation.meta?.successMessage) {
                toast.success(mutation.meta.successMessage);
            }
        },
        onError: (_error, _variables, _context, mutation) => {
            if (mutation.meta?.errorMessage) {
                toast.error(mutation.meta.errorMessage);
            }
        },
        onSettled: (_data, _error, _variables, _context, mutation) => {
            if (mutation.meta?.invalidatesQuery) {
                queryClient.invalidateQueries({
                    queryKey: mutation.meta.invalidatesQuery,
                });
            }
        },
    }),
});
// Define custom route context
interface CustomRouteContext extends RouteContext {
    isInternal: boolean;
}


export const router = createRouter({
    routeTree,
    basepath: "/app",

    context: {
        queryClient,
        //   org_id: curr_org_id || 0,
        //   profile_id: curr_profile_id,
        //   user_id: id,
        // Default to false (external navigation) when first loading the app
        // Will be set to true for internal navigation like router links and variant changes
        isInternal: false,
        // curr_acc_period_id: curr_acc_period_id,
    },
    // defaultPendingComponent: () => <Loading color="blue" message="Loading default pending component..." />,
    // defaultNotFoundComponent: () => <NotFound />,
    // defaultErrorComponent: ({ error }) => (
    //   <ErrorBoundary FallbackComponent={ErrorFallback}>
    //     <ErrorFallback error={error} />
    //   </ErrorBoundary>
    // ),
    defaultPreload: "intent",
    scrollRestoration: true,
    defaultStructuralSharing: true,
    defaultPreloadStaleTime: 0,
});