/**
 * Date utilities to handle timezone issues and standardize date operations across the application
 */

/**
 * Converts a Date object to an ISO date string (YYYY-MM-DD) without timezone issues
 * @param date The Date object to convert
 * @returns ISO date string (YYYY-MM-DD)
 */

import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
dayjs.extend(timezone);

export function dateToISOString(date: Date | null | undefined): string | undefined {
  if (!date) return undefined;

  // Create a new date and set to noon to avoid timezone issues
  const adjustedDate = new Date(date);
  adjustedDate.setHours(12, 0, 0, 0);

  // Return the ISO date string (YYYY-MM-DD)
  return adjustedDate.toISOString().split("T")[0];
}

/**
 * Converts an ISO date string (YYYY-MM-DD) to a Date object set to noon
 * @param isoString ISO date string (YYYY-MM-DD)
 * @returns Date object set to noon
 */
export function isoStringToDate(isoString: string | null | undefined): Date | undefined {
  if (!isoString) return undefined;

  // Create a new date from the ISO string and set to noon
  const date = new Date(isoString);
  date.setHours(12, 0, 0, 0);

  return date;
}

/**
 * Gets today's date as an ISO string (YYYY-MM-DD) without timezone issues
 * @returns Today's date as ISO string (YYYY-MM-DD)
 */
export function getTodayISOString(): string {
  const today = new Date();
  return dateToISOString(today) as string;
}

/**
 * Format a date for display in the UI (e.g., DD-MM-YYYY)
 * @param date Date object or ISO string
 * @returns Formatted date string
 */
export function formatDateForDisplay(date: Date | string | null | undefined): string {
  if (!date) return "";

  // Convert string to Date if needed
  const dateObj = typeof date === "string" ? new Date(date) : date;

  // Extract day, month, and year
  const day = String(dateObj.getDate()).padStart(2, "0");
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const year = dateObj.getFullYear();

  // Return formatted date
  return `${day}-${month}-${year}`;
}

/**
 * Custom hook-compatible onChange handler for Mantine DatePickerInput
 * @param value The date value from the date picker
 * @param onChange The state setter function
 */
export function handleDatePickerChange(value: Date | null, onChange: (value: string | undefined) => void): void {
  onChange(dateToISOString(value));
}

/**
 * Gets the start date of the current year in ISO format (YYYY-MM-DD)
 * @returns Start date of current year as ISO string (YYYY-MM-DD)
 */
export function getStartCurrentYearDate(): string {
  const currentDate = new Date();
  const startYearDate = new Date(currentDate.getFullYear(), 0, 1); // January 1st of current year
  return dateToISOString(startYearDate) as string;
}

/**
 * Gets the end date of the current year in ISO format (YYYY-MM-DD)
 * @returns End date of current year as ISO string (YYYY-MM-DD)
 */
export function getEndCurrentYearDate(): string {
  const currentDate = new Date();
  const endYearDate = new Date(currentDate.getFullYear(), 11, 31); // December 31st of current year
  return dateToISOString(endYearDate) as string;
}

export function dateTimeDisplay(value: string) {
  return dayjs(value).tz("Europe/Warsaw").format("DD-MM-YYYY HH:mm");
}
export function dateDisplay(value: string) {
  return dayjs(value).tz("Europe/Warsaw").format("DD-MM-YYYY");
}

/**
 * Generates a Date object with the current date and time set to 12:00
 * @returns Date object with current date and time set to 12:00
 */
export function todayNoonDateTime(): string {
  const today = new Date();
  today.setHours(12, 0, 0, 0); // Set time to 12:00:00.000
  return dayjs(today).tz("Europe/Warsaw").format("YYYY-MM-DD HH:mm");
}

/**
 * Generates a Date object with tomorrow's date and time set to 12:00
 * @returns Date object with tomorrow's date and time set to 12:00
 */
export function tomorrowNoonDateTime(): string {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1); // Add 1 day to get tomorrow
  tomorrow.setHours(12, 0, 0, 0); // Set time to 12:00:00.000
  return dayjs(tomorrow).tz("Europe/Warsaw").format("YYYY-MM-DD HH:mm");
}
