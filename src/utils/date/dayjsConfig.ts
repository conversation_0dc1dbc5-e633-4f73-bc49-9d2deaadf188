// Configure dayjs with all required plugins
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Import locales
import 'dayjs/locale/pl';
import 'dayjs/locale/en';

// Extend dayjs with plugins
dayjs.extend(localeData);
dayjs.extend(customParseFormat);
dayjs.extend(localizedFormat);
dayjs.extend(utc);
dayjs.extend(timezone);

// Set default locale
dayjs.locale('pl');

// Create a wrapper function to ensure locale is properly applied
export function formatWithLocale(date: string | Date | null | undefined, format: string, locale: string = 'pl'): string {
  if (!date) return '';
  
  // Ensure we have a valid date object
  const dayjsObj = typeof date === 'string' ? dayjs(date) : dayjs(date);
  
  // Apply locale and format
  return dayjsObj.locale(locale).format(format);
}

export default dayjs;
