"use client";

import { Check, ChevronsUpDown, GalleryVerticalEnd } from "lucide-react";
import * as React from "react";
import { Badge } from "@/components/_shadcn/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/_shadcn/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/_shadcn/components/ui/sidebar";

export function ProfileSwitcher({ profiles, defaultProfile }: { profiles: string[]; defaultProfile: string }) {
  const [selectedProfile, setSelectedProfile] = React.useState(defaultProfile);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuButton
          size="lg"
          tooltip="Profile Switcher"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
            <GalleryVerticalEnd className="size-4" />
          </div>
          <div className="flex flex-row items-center gap-0.5 leading-none">
            <span className="font-medium">
              <Badge>TYPE</Badge>
            </span>
            <span className="ml-2">{selectedProfile}</span>
          </div>
          <ChevronsUpDown className="ml-auto" />
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-(--radix-dropdown-menu-trigger-width)" align="start">
        {profiles.map((profile) => (
          <DropdownMenuItem key={profile} onSelect={() => setSelectedProfile(profile)}>
            <span className="ml-2">TYPE</span> {profile}
            {profile === selectedProfile && <Check className="ml-auto" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
