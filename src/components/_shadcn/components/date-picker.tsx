"use client";

import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import * as React from "react";
import { Button } from "@/components/_shadcn/components/ui/button";
import { Calendar } from "@/components/_shadcn/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/_shadcn/components/ui/popover";
import { cn } from "@/components/_shadcn/lib/utils";

export function DatePicker({ date, setDate }: { date: string; setDate: (date: string) => void }) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          data-empty={!date}
          className="data-[empty=true]:text-muted-foreground justify-start text-left font-normal"
        >
          <CalendarIcon />
          {date ? format(date, "PPP") : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={new Date(date)}
          onSelect={(date) => setDate(format(date || "", "yyyy-MM-dd"))}
        />
      </PopoverContent>
    </Popover>
  );
}
