import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import {
  ClipboardList,
  Languages,
  LogOut,
  Mail,
  Mailbox,
  MessageSquareText,
  MoonStar,
  MoreVertical,
  Sun,
  User,
  UserCircle,
  Wallet,
} from "lucide-react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { updateUsersApiV1AuthUsersPutMutation } from "@/api/_client/@tanstack/react-query.gen";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/_shadcn/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/_shadcn/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/_shadcn/components/ui/sidebar";
import { useInternalNavigate } from "@/utils/navigation";
// import { logout } from "@/utils/auth.js";
import { resetOrg } from "@/utils/redux/orgSlice";
import { resetProfile } from "@/utils/redux/profileSlice";
import type { RootStateTypes } from "@/utils/redux/store";
import { updateSystemField } from "@/utils/redux/systemSlice";
import { resetUser, updateUserField } from "@/utils/redux/userSlice";

// Theme management using Redux
const useColorScheme = () => {
  const dispatch = useDispatch();

  // Get initial theme from localStorage or system preference
  const initialTheme = localStorage.getItem('theme') ||
    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

  const theme = useSelector((state: RootStateTypes) => state.system.theme || initialTheme);

  // Sync theme to .dark class and notify Astro when theme changes
  useEffect(() => {
    const root = document.documentElement;
    root.classList.toggle('dark', theme === 'dark');


  }, [theme]);

  const setColorScheme = (newTheme: string) => {
    // Update Redux store with proper format
    dispatch(updateSystemField({ theme: newTheme }));
    // Persist to localStorage
    localStorage.setItem('theme', newTheme);
  };

  return {
    colorScheme: theme,
    setColorScheme,
  };
};

type NavUserProps = {
  user: {
    id: number;
    name: string;
    email: string;
    avatar: string;
  };
  onLogout: () => Promise<void>;
};

export function NavUser({ user, onLogout }: NavUserProps) {
  const { isMobile } = useSidebar();
  const { colorScheme, setColorScheme } = useColorScheme();
  const { t, i18n } = useTranslation();
  const { internalNavigate } = useInternalNavigate();
  const dispatch = useDispatch();

  const updateProfileMutation = useMutation(updateUsersApiV1AuthUsersPutMutation());

  const logoutUser = () => {
    // logout();
    // dispatch(resetUser());
    // dispatch(resetOrg());
    // dispatch(resetProfile());
    onLogout();
  };

  const updateLanguage = () => {
    const newLang = i18n.language === "pl" ? "en" : "pl";
    void i18n.changeLanguage(newLang);
    updateProfileMutation.mutate(
      {
        body: [
          {
            id: user.id,
            lang: i18n.language,
          },
        ],
      },
      {
        onSuccess: () => {
          dispatch(updateUserField({ lang: i18n.language }));
        },
      },
    );
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg grayscale">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.name}</span>
                <span className="text-muted-foreground truncate text-xs">{user.email}</span>
              </div>
              <MoreVertical className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{user.name}</span>
                  <span className="text-muted-foreground truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onSelect={() => internalNavigate({
                to: "/main/jobs/"
              })}>
                <Mail className="mr-2 h-4 w-4" />
                <span>{t("userMenu.jobs.label")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => internalNavigate({ to: "/member/proposals" })}>
                <ClipboardList className="mr-2 h-4 w-4" />
                <span>{t("userMenu.proposals.label")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => internalNavigate({ to: "/member/comments" })}>
                <MessageSquareText className="mr-2 h-4 w-4" />
                <span>{t("userMenu.comments.label")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => internalNavigate({ to: "/member/finances" })}>
                <Wallet className="mr-2 h-4 w-4" />
                <span>{t("userMenu.myFinances.label")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  internalNavigate({ to: "/member/organizations" });
                  dispatch(updateSystemField({ sidebarOpen: false }));
                }}
              >
                <User className="mr-2 h-4 w-4" />
                <span>{t("userMenu.myOrgs.label")}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>{t("userMenu.settings.label")}</DropdownMenuLabel>
            <DropdownMenuGroup>
              <DropdownMenuItem
                onSelect={() => {
                  internalNavigate({ to: "/main/jobs/" });
                  dispatch(updateSystemField({ sidebarOpen: false }));
                }}
              >
                <UserCircle className="mr-2 h-4 w-4" />
                <span>{t("userMenu.userProfile.label")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  internalNavigate({ to: "/main/jobs/" });
                  dispatch(updateSystemField({ module: "member" }));
                }}
              >
                <Mailbox className="mr-2 h-4 w-4" />
                <span>{t("userMenu.emailSettings.label")}</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => { console.log("colorScheme", colorScheme); setColorScheme(colorScheme === "light" ? "dark" : "light") }}>
                {colorScheme === "light" ? <MoonStar className="mr-2 h-4 w-4" /> : <Sun className="mr-2 h-4 w-4" />}
                <span>{colorScheme === "light" ? "Dark mode" : "Light mode"}</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => i18n.changeLanguage(i18n.language === "pl" ? "en" : "pl")}>
                <Languages className="mr-2 h-4 w-4" />
                <span>{i18n.language === "pl" ? "English" : "Polski"}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>Session</DropdownMenuLabel>
            <DropdownMenuItem
              onSelect={() => {
                internalNavigate({ to: "/" });
                logoutUser();
              }}
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>{t("logout")}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
