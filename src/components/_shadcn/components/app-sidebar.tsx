import {
  AudioWaveform,
  BookOpen,
  Bot,
  Calendar,
  ChevronDown,
  Command,
  Frame,
  GalleryVerticalEnd,
  Home,
  Inbox,
  // Map,
  PieChart,
  Search,
  Settings,
  Settings2,
  SquareTerminal,
} from "lucide-react";
import { ProfileSwitcher } from "@/components/_shadcn/components/profile-switcher";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/_shadcn/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/_shadcn/components/ui/sidebar";
import ModulesMenu from "@/components/_system/modules-menu";
import { NavUploads } from "./nav-uploads";
import { NavUser } from "./nav-user";

// Menu items.

const profiles = ["Profile 1", "Profile 2", "Profile 3"];

export function AppSidebar({ userProfile, onLogout }: { userProfile: any | undefined; onLogout: () => Promise<void> }) {
  return (
    <Sidebar>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <ProfileSwitcher profiles={profiles} defaultProfile="Profile 1" />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <ModulesMenu />
      </SidebarContent>
      <SidebarFooter className="mb-16">
        <NavUploads />
        {userProfile && <NavUser user={userProfile} onLogout={onLogout} />}
      </SidebarFooter>
    </Sidebar>
  );
}
