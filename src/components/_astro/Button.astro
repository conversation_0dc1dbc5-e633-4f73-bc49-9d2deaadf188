---
interface Props {
	href?: string;
	class?: string;
}

const { href, class: className, ...rest } = Astro.props;

const baseClasses = "appearance-none py-2 px-4 bg-purple-500 text-white font-semibold rounded-lg shadow-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-75";
---

{
	href ? (
		<a href={href} class:list={[baseClasses, className]} {...rest}>
			<slot />
		</a>
	) : (
		<button class:list={[baseClasses, className]} {...rest}>
			<slot />
		</button>
	)
}
