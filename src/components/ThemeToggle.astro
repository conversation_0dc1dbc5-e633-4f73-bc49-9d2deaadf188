---
// ThemeToggle.astro
---
<button id="themeToggle" class="p-2 rounded-full" aria-label="Toggle theme">
  <!-- Sun icon shown in light mode -->
  <svg id="sunIcon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4.243 1.757a1 1 0 011.414 0l.707.707a1 1 0 01-1.414 1.414l-.707-.707a1 1 0 010-1.414zM17 10a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zm-2.757 4.243a1 1 0 011.414 0l.707.707a1 1 0 01-1.414 1.414l-.707-.707a1 1 0 010-1.414zM10 18a1 1 0 01-1-1v-1a1 1 0 112 0v1a1 1 0 01-1 1zm-4.243-1.757a1 1 0 01-1.414 0l-.707-.707a1 1 0 111.414-1.414l.707.707a1 1 0 010 1.414zM3 10a1 1 0 01-1-1V8a1 1 0 112 0v1a1 1 0 01-1 1zm2.757-4.243a1 1 0 01-1.414 0l-.707-.707a1 1 0 011.414-1.414l.707.707a1 1 0 010 1.414zM10 5a5 5 0 100 10 5 5 0 000-10z"/>
  </svg>
  <!-- Moon icon shown in dark mode -->
  <svg id="moonIcon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
  </svg>
  <span class="sr-only">Toggle theme</span>
</button>

<style>
  /* By default, the sun is visible and the moon is not. */
  #moonIcon {
    display: none;
  }
  #sunIcon {
    display: inline-block;
  }

  /* When .dark is on a parent (like <html>), switch the icons. */
  :global(.dark) #sunIcon {
    display: none;
  }
  :global(.dark) #moonIcon {
    display: inline-block;
  }
</style>

<script is:inline>
// Initialize theme on load
function initTheme() {
  const root = document.documentElement;
  const savedTheme = localStorage.getItem('theme');
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const theme = savedTheme || (prefersDark ? 'dark' : 'light');
  
  // Set .dark class
  if (theme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
  
  // Dispatch to Redux if available
  if (window.dispatch) {
    window.dispatch({
      type: 'system/updateSystemField',
      payload: { field: 'theme', value: theme }
    });
  }
}

// Set initial theme
document.addEventListener('DOMContentLoaded', initTheme);

// Toggle theme on button click
document.getElementById('themeToggle')?.addEventListener('click', () => {
  const root = document.documentElement;
  const isDark = root.classList.contains('dark');
  const newTheme = isDark ? 'light' : 'dark';
  
  // Toggle .dark class
  if (newTheme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
  
  // Save preference
  localStorage.setItem('theme', newTheme);
  
});

// Listen for theme changes from Redux
if (window.subscribeToRedux) {
  window.subscribeToRedux((state) => {
    if (state?.system?.theme) {
      const root = document.documentElement;
      const isDark = root.classList.contains('dark');
      if ((state.system.theme === 'dark' && !isDark) || 
          (state.system.theme === 'light' && isDark)) {
        root.classList.toggle('dark');
        localStorage.setItem('theme', state.system.theme);
      }
    }
  });
}
</script>
