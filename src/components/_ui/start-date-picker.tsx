"use client";

import { Cross2Icon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { ArrowRightFromLine } from "lucide-react";
import * as React from "react";
import { Button, buttonVariants } from "@/components/_shadcn/components/ui/button";
import { Calendar } from "@/components/_shadcn/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/_shadcn/components/ui/popover";
import { cn } from "@/components/_shadcn/lib/utils";

export function StartDatePicker({
  startDate,
  setStartDate,
  className,
}: {
  startDate: string;
  setStartDate: (date: string) => void;
  className?: string;
}) {
  const [open, setOpen] = React.useState(false);
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            buttonVariants({ variant: "outline" }),
            "data-[empty=true]:text-muted-foreground justify-between text-left font-normal flex items-center",
          )}
          data-empty={!startDate}
        >
          <div className="flex items-center">
            <ArrowRightFromLine className="mr-2 h-4 w-4" />
            {startDate ? format(startDate, "yyyy-MM-dd") : <span>Start date</span>}
          </div>
          {startDate && (
            <Button
              variant="ghost"
              size="sm"
              className=" h-auto -mr-2"
              onClick={(e) => {
                e.stopPropagation();
                setStartDate("");
              }}
              aria-label="Clear date"
            >
              <Cross2Icon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={new Date(startDate)}
          onSelect={(date) => {
            setStartDate(format(date || "", "yyyy-MM-dd"));
            setOpen(false);
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
