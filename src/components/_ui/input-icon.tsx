import { Cross2Icon } from "@radix-ui/react-icons";
import { Input } from "@/components/_shadcn/components/ui/input";

interface BaseProps {
  value: string;
  setValue: (value: string) => void;
  placeholder?: string;
  icon?: React.ReactNode;
  onIconClick?: () => void;
  iconAriaLabel?: string;
  iconPosition?: "left" | "right";
  clearable?: boolean;
  disabled?: boolean;
  className?: string;
}

type Props = BaseProps &
  ({ clearable: true; setValue: (value: string) => void } | { clearable?: false; setValue?: (value: string) => void });

export function InputIcon({
  value,
  setValue,
  placeholder = "...",
  icon,
  onIconClick = () => setValue?.(""),
  iconAriaLabel = "Action",
  iconPosition = "left",
  clearable = false,
  disabled = false,
  className = "",
}: Props) {
  return (
    <div className="relative">
      <Input
        type="text"
        placeholder={placeholder}
        className={`peer block w-full rounded-md border py-[9px] text-sm ${iconPosition === "left" ? "pl-10" : "pr-10"
          } ${disabled ? "opacity-50 cursor-not-allowed" : ""} ${className}`}
        value={value}
        onChange={(e) => setValue?.(e.target.value)}
        disabled={disabled}
      />
      {clearable ? (
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            console.log("clear");
            setValue?.("");
          }}
          disabled={disabled}
          className={`absolute top-1/2 -translate-y-1/2 text-muted-foreground hover:opacity-80 rounded-sm disabled:opacity-50 disabled:cursor-not-allowed ${iconPosition === "left" ? "left-3" : "right-3"
            }`}
          aria-label={clearable ? "Clear" : iconAriaLabel}
        >
          <Cross2Icon />
        </button>
      ) : icon ? (
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            console.log("clear");
            onIconClick?.();
          }}
          disabled={disabled}
          className={`absolute top-1/2 -translate-y-1/2 text-muted-foreground hover:opacity-80 rounded-sm disabled:opacity-50 disabled:cursor-not-allowed ${iconPosition === "left" ? "left-3" : "right-3"
            }`}
          aria-label={clearable ? "Clear" : iconAriaLabel}
        >
          {icon}
        </button>
      ) : null}
    </div>
  );
}
