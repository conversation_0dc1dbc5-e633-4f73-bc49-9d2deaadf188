import { Cross2Icon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { ArrowRightToLine } from "lucide-react";
import * as React from "react";
import { Button, buttonVariants } from "@/components/_shadcn/components/ui/button";
import { Calendar } from "@/components/_shadcn/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/_shadcn/components/ui/popover";
import { cn } from "@/components/_shadcn/lib/utils";
import { TimePicker } from "@/components/_ui/time-picker";

export function EndDateTimePicker({
  endDate,
  setEndDate,
  // className,
}: {
  endDate: string;
  setEndDate: (date: string) => void;
  // className?: string;
}) {
  const [open, setOpen] = React.useState(false);
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            buttonVariants({ variant: "outline" }),
            "data-[empty=true]:text-muted-foreground justify-between text-left font-normal flex items-center",
          )}
          data-empty={!endDate}
        >
          <div className="flex items-center">
            <ArrowRightToLine className="mr-2 h-4 w-4" />
            {endDate ? format(endDate, "yyyy-MM-dd HH:mm") : <span>End date</span>}
          </div>
          {endDate && (
            <Button
              variant="ghost"
              size="sm"
              className="h-auto -mr-2"
              onClick={(e) => {
                e.stopPropagation();
                setEndDate("");
              }}
              aria-label="Clear date"
            >
              <Cross2Icon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={new Date(endDate)}
          onSelect={(date) => {
            if (date) {
              setEndDate(date.toISOString());
              setOpen(false);
            }
          }}
        />
        <div className="p-3 border-t border-border">
          <TimePicker
            setDate={(date) => setEndDate(date?.toISOString() ?? "")}
            date={endDate ? new Date(endDate) : undefined}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
}
