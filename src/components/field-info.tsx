import type { Any<PERSON>ield<PERSON><PERSON> } from "@tanstack/react-form";
import { useTranslation } from "react-i18next";

function FieldInfo({ field }: { field: AnyFieldApi }) {
    const { t } = useTranslation();
    if ((!field.state.meta.isTouched || !field.state.meta.isValid) && field.state.meta.errors.length > 0) {
        return (
            <p className="text-red-500 ml-1 text-xs mt-1 ml-2 mb-1">
                <em>{field.state.meta.errors.map((error) => t(error.message)).join(", ")}</em>
            </p>
        );
    } else if ((!field.state.meta.isTouched || !field.state.meta.isValid) && field.store.state.meta.errors.length > 0) {
        return (
            <p className="text-red-500 ml-1 text-xs mt-1 ml-2 mb-1">
                <em>{field.store.state.meta.errors.map((error) => t(error.message)).join(", ")}</em>
            </p>
        );
    } else {
        return null;
    }
}

export default FieldInfo;
