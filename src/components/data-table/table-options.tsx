"use client";

import type { ClientOptionsTypes, OrderColumn, SearchTerm, ServerOptions } from "@client/types.gen";
import { useLocation } from "@tanstack/react-router";
import type {
  Column,
  ColumnDef,
  ColumnFiltersState,
  ColumnOrderState,
  PaginationState,
  RowSelectionState,
  SortingState,
  Table,
  Updater
} from "@tanstack/react-table";
import { Check, Columns3, GripVertical, PlusCircle, RotateCcw, Server, X } from "lucide-react";
import * as React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import type { VariantDisplayColumnsTypes } from "@/api/_client/types.gen";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/_shadcn/components/ui/accordion";
import { Button } from "@/components/_shadcn/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/_shadcn/components/ui/command";
import { Input } from "@/components/_shadcn/components/ui/input";
import { Label } from "@/components/_shadcn/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/_shadcn/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import { Switch } from "@/components/_shadcn/components/ui/switch";
import { cn } from "@/components/_shadcn/lib/utils";
import { useRouteFilters } from "@/components/data-table/hooks/use-route-filters";
import type { GetColumnsOptions } from "@/dosiero/routes/main/jobs/-components/columns.tsx";
import { EndDatePicker } from "../_ui/end-date-picker";
import { StartDatePicker } from "../_ui/start-date-picker";

interface ColumnsOptionsProps<TData> {
  table: Table<TData>;
  getColumns: (options: GetColumnsOptions) => ColumnDef<TData>[];
  // visibleColumns: any;
  // setVisibleColumns: any;
  tableConfig: any;
  columnMapping?: Record<string, string>;
  size?: "sm" | "default" | "lg";
  pathState: ClientOptionsTypes;
  variant: any;
  setVariant: (variant: any) => void;
  tmpServerOps: any;
  setTmpServerOps: (ops: any) => void;
}


export function TableOptions<TData>({
  table,
  getColumns,
  tableConfig,
  columnMapping,
  size = "default",
  pathState,
  variant,
  setVariant,
  tmpServerOps,
  setTmpServerOps,
}: ColumnsOptionsProps<TData>) {
  //

  const { start_date, end_date } = tmpServerOps as ServerOptions;
  console.log("%c tmpServerOps <<<< TABLE OPTIONS local  >>>>", "color: green; font-weight: bold;", tmpServerOps);
  console.log("%c variant <<<< TABLE OPTIONS variant  >>>>", "color: green; font-weight: bold;", variant);


  const addSearchTerm = () => {
    setTmpServerOps({
      ...tmpServerOps,
      search: [...(tmpServerOps.search || []), { id: crypto.randomUUID(), term: "", column_name: "" }],
    });
  };

  const updateSearchTerm = (id: string, newTerm: Partial<{ term: string; column_name: string }>) => {
    setTmpServerOps({
      ...tmpServerOps,
      search: tmpServerOps.search.map((term: any) => (term.id === id ? { ...term, ...newTerm } : term)),
    });
  };

  const removeSearchTerm = (id: string) => {
    setTmpServerOps({
      ...tmpServerOps,
      search: tmpServerOps.search.filter((term: SearchTerm & { id: string }) => term.id !== id),
    });
  };

  const addSortTerm = () => {
    setTmpServerOps({
      ...tmpServerOps,
      order: [...(tmpServerOps.order || []), { id: crypto.randomUUID(), column_name: "", direction: "asc" }],
    });
  };

  const updateSortTerm = (id: string, newTerm: Partial<{ column_name: string; direction: "asc" | "desc" }>) => {
    setTmpServerOps({
      ...tmpServerOps,
      order: tmpServerOps.order.map((term: OrderColumn & { id: string }) => (term.id === id ? { ...term, ...newTerm } : term)),
    });
  };

  const removeSortTerm = (id: string) => {
    setTmpServerOps({ ...tmpServerOps, order: tmpServerOps.order.filter((term: OrderColumn & { id: string }) => term.id !== id) });
  };

  const handlePeriodChange = (period: string) => {
    setTmpServerOps((prev: VariantDisplayColumnsTypes) => ({
      ...prev,
      period: period,
      start_date: null,
      end_date: null,
      custom_start: null,
      custom_end: null,
    }));
  };

  // Get columns that can be hidden
  const tableColumns = React.useMemo(
    () => table.getAllColumns().filter((column) => typeof column.accessorFn !== "undefined" && column.getCanHide()),
    [table],
  );

  // Get column display label
  const getColumnLabel = useCallback(
    (column: Column<TData, unknown>) => {
      // First check if we have a mapping for this column
      if (columnMapping && column.id in columnMapping) {
        return columnMapping[column.id];
      }
      // Then check for meta label
      return (
        (column.columnDef.meta as { label?: string })?.label ??
        // Finally fall back to formatted column ID
        column.id.replace(/_/g, " ")
      );
    },
    [columnMapping],
  );

  const options = {
    handleRowDeselection: null,
    tableConfig,

  } as GetColumnsOptions;



  const allColumns = getColumns(options);
  console.log("allColumns", allColumns);
  // exclude columns with listed accessorKeys or ids
  const dataColumns = getColumns({ ...options });
  console.log("dataColumns", dataColumns);

  // in columns config we select which columns should be included in server payload
  // allColumns contain list of all schema columns
  // tmpServerOps.columns contain information which columns should be included in server payload
  // if tmpServerOps.columns is empty, null or undefined, then we include all columns
  // if we want to exclude column A, we need to set tmpServerOps.columns to ["column_B", "column_C"]
  // so tmpServerOps.columns may contain max allColumns.length - 1 columns

  const isIncluded = (accessorKey: string) => {
    return (
      !tmpServerOps.columns ||
      tmpServerOps.columns?.length === 0 ||
      tmpServerOps.columns?.includes(accessorKey)
    );
  };

  function handleSelectColumn(accessorKey: string) {
    const currentColumns = tmpServerOps.columns || [];

    if (isIncluded(accessorKey)) {
      // remove column
      if (currentColumns.length === 0) {
        const remainingColumnNames = dataColumns
          .filter((column: any) => column.accessorKey !== accessorKey)
          ?.map((column: any) => column.accessorKey);
        setTmpServerOps({ ...tmpServerOps, columns: remainingColumnNames });
      } else {
        const newColumns = currentColumns.filter((column: string) => column !== accessorKey);
        setTmpServerOps({ ...tmpServerOps, columns: newColumns });
      }
    } else {
      // add column
      const newColumnsArray = [...currentColumns, accessorKey];
      if (newColumnsArray.length === dataColumns.length) {
        setTmpServerOps({ ...tmpServerOps, columns: [] });
      } else {
        setTmpServerOps({
          ...tmpServerOps,
          columns: newColumnsArray,
        });
      }
    }
  }

  return (

    <Accordion type="single" collapsible className="w-full" defaultValue="data">
      <AccordionItem value="columns">
        <AccordionTrigger className="bg-zinc-200 p-2">
          <div className="flex items-center gap-2">
            <Columns3 className="h-4 w-4" />
            Columns
          </div>
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 text-balance p-2">
          <Command>
            {/* <CommandInput placeholder="Search columns..." /> */}
            <CommandList>
              <CommandEmpty>No columns found.</CommandEmpty>
              <CommandGroup>
                {dataColumns.map((column: any) => (
                  <CommandItem
                    key={column.accessorKey}
                    onSelect={() => handleSelectColumn(column.accessorKey)}
                  >
                    <span className="flex-grow truncate capitalize">{column.accessorKey}</span>
                    <Check
                      className={cn("ml-auto h-4 w-4", isIncluded(column.accessorKey) ? "opacity-100" : "opacity-0")}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
            </CommandList>
          </Command>
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="range">
        <AccordionTrigger className="bg-zinc-200 p-2">
          <div className="flex items-center">
            <Server className="mr-2 h-4 w-4" />
            Dates range
          </div>
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 text-balance p-2">
          <Select

            value={tmpServerOps.date_column}
            onValueChange={(value) => setTmpServerOps({ ...tmpServerOps, date_column: value })}
          >
            <div>
              <Label htmlFor="date_column">Date column</Label>
              <SelectTrigger id="date_column" className="w-full">
                <SelectValue placeholder="Select date column" />
              </SelectTrigger>
            </div>
            <SelectContent>
              {tableColumns.map((column) => (
                <SelectItem key={column.id} value={column.id}>
                  {getColumnLabel(column)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={tmpServerOps.period} defaultValue="DATES" onValueChange={handlePeriodChange}>
            <div>
              <Label htmlFor="period">Dates range</Label>
              <SelectTrigger id="period" className="w-full">
                <SelectValue placeholder="Select a relative date range" />
              </SelectTrigger>
            </div>

            <SelectContent>
              <SelectItem value="DATES">Select dates</SelectItem>
              <SelectItem value="TODAY">Today</SelectItem>
              <SelectItem value="YESTERDAY">Yesterday</SelectItem>
              <SelectItem value="LAST_7">Last 7 days</SelectItem>
              <SelectItem value="LAST_30">Last 30 days</SelectItem>
              <SelectItem value="THIS_WEEK">This week</SelectItem>
              <SelectItem value="LAST_WEEK">Last week</SelectItem>
              <SelectItem value="NEXT_WEEK">Next week</SelectItem>
              <SelectItem value="THIS_MONTH">This month</SelectItem>
              <SelectItem value="LAST_MONTH">Last month</SelectItem>
              <SelectItem value="NEXT_MONTH">Next month</SelectItem>
              <SelectItem value="THIS_YEAR">This year</SelectItem>
              <SelectItem value="LAST_YEAR">Last year</SelectItem>
              <SelectItem value="NEXT_YEAR">Next year</SelectItem>
              <SelectItem value="CUSTOM">Custom</SelectItem>
            </SelectContent>
          </Select>

          {tmpServerOps.period === "DATES" && (
            <div className="flex gap-4">
              <StartDatePicker
                startDate={tmpServerOps.start_date}
                setStartDate={(date) => setTmpServerOps({ ...tmpServerOps, start_date: date })}
              />
              <EndDatePicker
                endDate={tmpServerOps.end_date}
                setEndDate={(date) => setTmpServerOps({ ...tmpServerOps, end_date: date })}
              />
            </div>
          )}

          {tmpServerOps.period === "CUSTOM" && (
            <div className="flex gap-4">
              <div className="flex flex-col">
                <Label htmlFor="start">Start (+/-)</Label>
                <Input
                  id="start"
                  type="number"
                  value={tmpServerOps.custom_start ?? null}
                  onChange={(e) => setTmpServerOps({ ...tmpServerOps, custom_start: parseInt(e.target.value, 10) })}
                  placeholder="Number of days ago"
                  className="w-24"
                />
              </div>
              <div className="flex flex-col">
                <Label htmlFor="end">End (+/-)</Label>
                <Input
                  id="end"
                  type="number"
                  value={tmpServerOps.custom_end ?? null}
                  onChange={(e) => setTmpServerOps({ ...tmpServerOps, custom_end: parseInt(e.target.value, 10) })}
                  placeholder="Number of days ago"
                  className="w-24"
                />
              </div>
            </div>
          )}

          <div className="space-y-2">
            {(start_date || end_date) && (
              <div className="text-sm text-muted-foreground p-2 bg-muted rounded-md">
                <p>
                  <span className="font-semibold">From:</span> {start_date}
                </p>
                <p>
                  <span className="font-semibold">To:</span> {end_date}
                </p>
              </div>
            )}
          </div>

          <div className="space-y-2">

          </div>

        </AccordionContent>
      </AccordionItem>






      <AccordionItem value="search">
        <AccordionTrigger className="bg-zinc-200 p-2">
          <div className="flex items-center gap-2">
            <Columns3 className="h-4 w-4" />
            Search config
          </div>
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 text-balance p-2">


          {tmpServerOps?.search?.map((term: SearchTerm & { id: string }) => (
            <div key={term.id} className="flex items-center gap-2">
              <div className="flex flex-col">
                <Label htmlFor="term">Term</Label>
                <Input
                  type="text"
                  value={term.term}
                  onChange={(e) => updateSearchTerm(term.id, { term: e.target.value })}
                  placeholder="Enter search term"
                  className="w-full"
                />
              </div>
              <div className="flex flex-col">
                <Label htmlFor="column_name">Column name</Label>
                <Select
                  value={term.column_name}
                  onValueChange={(value) => updateSearchTerm(term.id, { column_name: value })}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select column" />
                  </SelectTrigger>
                  <SelectContent>
                    {tableColumns.map((column) => (
                      <SelectItem key={column.id} value={column.id}>
                        {getColumnLabel(column)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="ghost" size="icon" onClick={() => removeSearchTerm(term.id)}>
                <X className="h-4 w-4 text-red-500" />
              </Button>
            </div>
          ))}
          <Button variant="outline" size="sm" onClick={addSearchTerm}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Search Term
          </Button>

        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="sorting">
        <AccordionTrigger className="bg-zinc-200 p-2">
          <div className="flex items-center gap-2">
            <Columns3 className="h-4 w-4" />
            Sorting
          </div>
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 text-balance p-2">

          <div className="space-y-2">

            {tmpServerOps?.order.map((ord: OrderColumn & { id: string }) => (
              <div key={ord.id} className="flex items-center gap-2">
                <Select
                  value={ord.column_name}
                  onValueChange={(value) => updateSortTerm(ord.id, { column_name: value })}
                >
                  <div className="flex flex-col">
                    <Label htmlFor="column_name">Column name</Label>

                    <SelectTrigger id="column_name" className="w-full">
                      <SelectValue placeholder="Select column" />
                    </SelectTrigger>
                  </div>
                  <SelectContent>
                    {tableColumns.map((column) => (
                      <SelectItem key={column.id} value={column.id}>
                        {getColumnLabel(column)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select

                  value={ord.direction}
                  onValueChange={(value: "asc" | "desc") => updateSortTerm(ord.id, { direction: value })}
                >
                  <div className="flex flex-col">
                    <Label htmlFor="direction">Direction</Label>
                    <SelectTrigger id="direction" className="w-[120px]">
                      <SelectValue placeholder="Select direction" />
                    </SelectTrigger>
                  </div>
                  <SelectContent>
                    <SelectItem value="asc">Asc</SelectItem>
                    <SelectItem value="desc">Desc</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="ghost" size="icon" onClick={() => removeSortTerm(ord.id)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button variant="outline" size="sm" onClick={addSortTerm}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Sort
            </Button>
          </div>

        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="data_limit">
        <AccordionTrigger className="bg-zinc-200 p-2">
          <div className="flex items-center gap-2">
            <Columns3 className="h-4 w-4" />
            Data limit
          </div>
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 text-balance p-2">


          <div className="grid w-full max-w-sm items-center gap-1.5">

            <Label htmlFor="limit">Limit</Label>
            <Input
              id="limit"
              type="number"
              value={tmpServerOps.page_size}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? "" : Number(value);

                setTmpServerOps((prev: any) => {
                  if (numValue !== "" && numValue > 0) {
                    return { ...prev, page_size: numValue, page_index: 0 };
                  } else if (value === "") {
                    return { ...prev, page_size: "", page_index: null };
                  }
                  return { ...prev, page_size: numValue };
                });
              }}
              placeholder="Set limit"
            />
          </div>
          {tmpServerOps.page_size !== "" && (
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="pageIndex">Page</Label>
              <Input
                id="pageIndex"
                type="number"
                value={tmpServerOps.page_index + 1}
                onChange={(e) =>
                  setTmpServerOps({
                    ...tmpServerOps,
                    page_index: e.target.value === "" ? "" : Number(e.target.value) - 1,
                  })
                }
                placeholder="Set page index"
              />
            </div>
          )}


        </AccordionContent>
      </AccordionItem>
    </Accordion>

  );
}
