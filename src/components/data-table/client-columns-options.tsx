"use client";

import type { Column, Table } from "@tanstack/react-table";
import { Check, GripVertical, RotateCcw, Settings2 } from "lucide-react";
import * as React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/_shadcn/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/_shadcn/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/_shadcn/components/ui/popover";
import { cn } from "@/components/_shadcn/lib/utils";
import { useRouteFilters } from "@/components/data-table/hooks/use-route-filters";

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>;
  tableConfig: any;

  columnMapping?: Record<string, string>;
  size?: "sm" | "default" | "lg";
  variant?: any;
}

// Local storage key for column order
const COLUMN_ORDER_STORAGE_KEY = "data-table-column-order";

export function ClientColumnsOptions<TData>({
  table,
  tableConfig,

  columnMapping,
  size = "default",
  variant,
}: DataTableViewOptionsProps<TData>) {
  const { t } = useTranslation();
  // Get columns that can be hidden

  //   (variant?.server_ops?.columns?.includes(column.accessorKey)
  //     || column.id === "actions"
  //     || column.id === "select")
  // );
  const columns = React.useMemo(
    () => {
      const allColumns = table
        .getAllColumns()
        .filter(column => {
          return (
            typeof column.accessorFn !== 'undefined' &&
            column.getCanHide()
          );
        });

      const filteredColums = table
        .getAllColumns()
        .filter(column => {
          return (
            variant?.server_ops?.columns?.includes(column.id) &&
            typeof column.accessorFn !== 'undefined' &&
            column.getCanHide()
          );
        });

      if (variant?.server_ops?.columns?.length > 0) {
        return filteredColums;
      } else {
        return allColumns;
      }
    },
    [table, variant],
  );
  const { setFilters, toggleColumnVisibility } = useRouteFilters(tableConfig.path);

  // State for drag and drop
  const [draggedColumnId, setDraggedColumnId] = useState<string | null>(null);

  // Order columns based on the current table column order
  const columnOrder = table.getState().columnOrder;
  const orderedColumns = useMemo(() => {
    if (!columnOrder.length) {
      return columns;
    }

    // Create a new array with columns sorted according to the columnOrder
    return [...columns].sort((a, b) => {
      const aIndex = columnOrder.indexOf(a.id);
      const bIndex = columnOrder.indexOf(b.id);

      // If column isn't in the order array, put it at the end
      if (aIndex === -1) return 1;
      if (bIndex === -1) return -1;

      return aIndex - bIndex;
    });
  }, [columns, columnOrder]);

  // Handle drag start
  const handleDragStart = useCallback((e: React.DragEvent, columnId: string) => {
    setDraggedColumnId(columnId);
    e.dataTransfer.effectAllowed = "move";
    // This helps with dragging visuals
    if (e.dataTransfer.setDragImage && e.currentTarget instanceof HTMLElement) {
      e.dataTransfer.setDragImage(e.currentTarget, 20, 20);
    }
  }, []);

  // Handle drag over
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  }, []);

  // Handle drop
  const handleDrop = useCallback(
    (e: React.DragEvent, targetColumnId: string) => {
      e.preventDefault();

      if (!draggedColumnId || draggedColumnId === targetColumnId) return;

      // Get current column order
      const currentOrder =
        table.getState().columnOrder.length > 0
          ? [...table.getState().columnOrder]
          : table.getAllLeafColumns().map((d) => d.id);

      // Find indices
      const draggedIndex = currentOrder.indexOf(draggedColumnId);
      const targetIndex = currentOrder.indexOf(targetColumnId);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // Create new order by moving the dragged column
      const newOrder = [...currentOrder];
      newOrder.splice(draggedIndex, 1);
      newOrder.splice(targetIndex, 0, draggedColumnId);

      setFilters({ columnOrder: newOrder });

      setDraggedColumnId(null);
    },
    [draggedColumnId, setFilters, table.getState, table.getAllLeafColumns],
  );

  // Get column display label
  const getColumnLabel = useCallback(
    (column: Column<TData, unknown>) => {
      // First check if we have a mapping for this column
      if (columnMapping && column.id in columnMapping) {
        return columnMapping[column.id];
      }
      // Then check for meta label
      return (
        (column.columnDef?.meta as { label?: string })?.label ??
        // Finally fall back to formatted column ID
        column.id.replace(/_/g, " ")
      );
    },
    [columnMapping],
  );

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button aria-label="Toggle columns" variant="outline" size={size} className="ml-auto hidden lg:flex">
          <Settings2 className="mr-2 h-4 w-4" />
          {t("components.dataTable.toolbar.columns.label")}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-[220px] p-0">
        <Command>
          {/* <CommandInput placeholder="Search columns..." /> */}
          <CommandList>
            <CommandEmpty>{t("components.dataTable.toolbar.noColumns.label")}  </CommandEmpty>
            <CommandGroup>
              {orderedColumns.map((column) => (
                <CommandItem
                  key={column.id}
                  onSelect={() => { console.log(" toggling from client-columns-options: column id: ", column.id); toggleColumnVisibility(column.id) }}
                  //   onSelect={() => column.toggleVisibility(!column.getIsVisible())}
                  draggable
                  onDragStart={(e) => handleDragStart(e, column.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, column.id)}
                  className={cn(
                    "flex items-center cursor-grab",
                    draggedColumnId === column.id && "bg-accent opacity-50",
                  )}
                >
                  <GripVertical className="mr-2 h-4 w-4 cursor-grab" />
                  <span className="flex-grow truncate capitalize">{getColumnLabel(column)}</span>
                  <Check className={cn("ml-auto h-4 w-4", column.getIsVisible() ? "opacity-100" : "opacity-0")} />
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                onSelect={() => setFilters({ columnOrder: [] })}
                className="justify-center text-center cursor-pointer"
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                {t("components.dataTable.toolbar.resetColumnsOrder.label")}
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
