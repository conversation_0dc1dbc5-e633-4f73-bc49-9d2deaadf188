import { ArrowDownIcon, ArrowUpIcon, CaretSortIcon, EyeNoneIcon } from "@radix-ui/react-icons";
import { useLocation } from "@tanstack/react-router";
import type { Column } from "@tanstack/react-table";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/_shadcn/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/_shadcn/components/ui/dropdown-menu";
import { cn } from "@/components/_shadcn/lib/utils";
import { useRouteFilters } from "@/components/data-table/hooks/use-route-filters";

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>;
  title: string;
  translationKey?: string;
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  translationKey,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  const { t } = useTranslation();
  const location = useLocation();
  const { setFilters } = useRouteFilters(
    `${location.pathname.replace("/app", "")}/`,
  );

  // Get the display title - either translated or direct title
  const displayTitle = translationKey ? t(translationKey) : title;

  if (!column.getCanSort()) {
    return <div className={cn(className)}>{displayTitle}</div>;
  }

  // Get the current sort direction for this column
  const currentDirection = column.getIsSorted();

  // Use direct method to set sort with an explicit direction
  const setSorting = (direction: "asc" | "desc" | false) => {
    // If we're clearing sort, use an empty array
    if (direction === false) {
      column.toggleSorting(undefined, false);
      setFilters({
        sorting: [],
      });
      return;
    }

    // Set explicit sort with the direction
    // The second param (false) prevents multi-sort
    // column.toggleSorting(direction === "desc", false);
    setFilters({
      sorting: [{ id: column.id, desc: direction === "desc" }],
    });
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="data-[state=open]:bg-accent h-8 focus-visible:ring-0 focus-visible:ring-offset-0"
          >
            <span>{displayTitle}</span>
            {currentDirection === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4" />
            ) : currentDirection === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4" />
            ) : (
              <CaretSortIcon className="ml-2 h-4 w-4" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem onClick={() => setSorting("asc")}>
            <ArrowUpIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            {t("common.asc.label", "up trans")}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setSorting("desc")}>
            <ArrowDownIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            {t("common.desc.label", "down trans")}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setSorting(false)}>
            <ArrowDownIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            {t("common.none.label", "no trans")}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => column.toggleGrouping()}>
            {column.getIsGrouped() ? t("common.ungroup.label", "ungroup trans") : t("common.group.label", "group trans")}
          </DropdownMenuItem>

          {/* <DropdownMenuItem onClick={() => toggleColumnVisibility(column.id)}>
            <EyeNoneIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
            Hide
          </DropdownMenuItem> */}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
