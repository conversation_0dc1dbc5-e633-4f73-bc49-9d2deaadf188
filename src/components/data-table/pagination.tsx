"use client";

import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon } from "@radix-ui/react-icons";
import type { Table } from "@tanstack/react-table";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/_shadcn/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import { useRouteFilters } from "./hooks/use-route-filters";

const getButtonSizeClass = (size: "sm" | "default" | "lg") => {
  switch (size) {
    case "sm":
      return "h-7 w-7 p-0";
    case "lg":
      return "h-11 w-11 p-0";
    default:
      return "h-8 w-8 p-0";
  }
};

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  serverPagination?: {
    pageIndex: number;
    pageSize: number;
    totalItems: number;
  };
  totalSelectedItems?: number; // Total selected items across all pages
  pageSizeOptions?: number[]; // Custom page size options
  size?: "sm" | "default" | "lg"; // Size prop for components
}

export function DataTablePagination<TData>({
  table,
  serverPagination,
  totalSelectedItems = 0,
  pageSizeOptions = [10, 20, 30, 40, 50], // Default options if none provided
  size = "default",
}: DataTablePaginationProps<TData>) {
  // Convert 'lg' size to 'default' for SelectTrigger since it only accepts 'sm' | 'default'
  const selectSize = size === "lg" ? "default" : size;
  const { t } = useTranslation();
  const { filters, setFilters } = useRouteFilters("/main/jobs/");

  // console.log("table from pagination", table.getState());
  // console.log("cangetnextpage", table.getCanNextPage());
  // console.log("cangetpreviouspage", table.getCanPreviousPage());
  // console.log("pageCount", table.getPageCount());
  // console.log("totalItems", totalItems);
  // console.log("pageSize", table.getState().pagination.pageSize);
  // console.log("pageIndex", table.getState().pagination.pageIndex);
  // console.log("calculated pageCount", Math.ceil(totalItems / table.getState().pagination.pageSize));
  return (
    <div className="flex w-full flex-col items-center justify-between gap-4 overflow-auto px-2 py-1 sm:flex-row sm:gap-8">
      <div className="flex-1 text-sm text-muted-foreground">
        {t("components.dataTable.pagination.selected.label")} {" "}{totalSelectedItems}{" "} {t("common.of.label")}{" "}
        {serverPagination?.pageSize} ( {serverPagination?.totalItems} ) {t("components.dataTable.pagination.rows.label")}.
      </div>
      <div className="flex flex-col items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8">
        <div className="flex items-center space-x-2">
          <p className="whitespace-nowrap text-sm font-medium">{t("components.dataTable.pagination.rowsPerPage.label")}</p>
          <Select
            value={`${table.getState()?.pagination?.pageSize || 20}`}
            onValueChange={(value) => {
              setFilters({
                pagination: {
                  pageSize: Number(value) || 20,
                  pageIndex: filters.pagination?.pageIndex || 0,
                },
              });
            }}
          >
            <SelectTrigger className="cursor-pointer" size={selectSize}>
              <SelectValue placeholder={table.getState().pagination?.pageSize || 20} />
            </SelectTrigger>
            <SelectContent side="top" className="cursor-pointer">
              {pageSizeOptions.map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`} className="cursor-pointer">
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center justify-center text-sm font-medium">
          {t("components.dataTable.pagination.page.label")}{" "}
          {table.getState().pagination?.pageIndex + 1} {t("components.dataTable.pagination.of.label")}{" "}
          {Math.max(Math.ceil((serverPagination?.pageSize || 0) / (table.getState().pagination?.pageSize || 20)), 1)}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            aria-label="Go to first page"
            variant="outline"
            className={`${getButtonSizeClass(size)} hidden lg:flex cursor-pointer`}
            onClick={() => setFilters({ pagination: { pageIndex: 0, pageSize: filters.pagination?.pageSize || 20 } })}
          // onClick={() => table.setPagination({ pageIndex: 0, pageSize: table.getState().pagination.pageSize })}
          // disabled={!table.getCanPreviousPage()}
          >
            <DoubleArrowLeftIcon className="h-4 w-4" aria-hidden="true" />
          </Button>
          <Button
            aria-label="Go to previous page"
            variant="outline"
            className={`${getButtonSizeClass(size)} cursor-pointer`}
            onClick={() =>
              setFilters({
                pagination: {
                  ...filters.pagination,
                  pageIndex: table.getState().pagination?.pageIndex - 1,
                  pageSize: table.getState().pagination?.pageSize,
                },
              })
            }
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronLeftIcon className="h-4 w-4" aria-hidden="true" />
          </Button>
          <Button
            aria-label="Go to next page"
            variant="outline"
            className={`${getButtonSizeClass(size)} cursor-pointer`}
            onClick={() =>
              setFilters({
                pagination: {
                  pageIndex: table.getState().pagination?.pageIndex + 1,
                  pageSize: table.getState().pagination?.pageSize,
                },
              })
            }
            disabled={(table.getState().pagination?.pageIndex + 1) * table.getState().pagination?.pageSize >= (serverPagination?.pageSize || 0)}
          >
            <ChevronRightIcon className="h-4 w-4" aria-hidden="true" />
          </Button>
          <Button
            aria-label="Go to last page"
            variant="outline"
            className={`${getButtonSizeClass(size)} hidden lg:flex cursor-pointer`}
            onClick={() =>
              setFilters({
                pagination: {
                  pageIndex: table.getPageCount() - 1,
                  pageSize: table.getState().pagination?.pageSize,
                },
              })
            }
            disabled={(table.getState().pagination?.pageIndex + 1) * table.getState().pagination?.pageSize >= (serverPagination?.pageSize || 0)}
          >
            <DoubleArrowRightIcon className="h-4 w-4" aria-hidden="true" />
          </Button>
        </div>
      </div>
    </div>
  );
}
