export const DEFAULT_PAGE_INDEX = 0;
export const DEFAULT_PAGE_SIZE = 20;

export const cleanEmptyParams = (search: Record<string, any>) => {
  const newSearch = { ...search };
  Object.keys(newSearch).forEach((key) => {
    const value = newSearch[key];
    if (
      value === undefined ||
      value === "" ||
      (typeof value === "number" && isNaN(value)) ||
      (Array.isArray(value) && value.length === 0)
    )
      delete newSearch[key];
  });

  // if (newSearch.pageIndex === DEFAULT_PAGE_INDEX) delete newSearch.pageIndex;
  // if (newSearch.pageSize === DEFAULT_PAGE_SIZE) delete newSearch.pageSize;

  return newSearch;
};
