import {
  getRoute<PERSON><PERSON>,
  type NavigateOptions,
  type RegisteredRouter,
  type RouteIds,
  useNavigate,
} from "@tanstack/react-router";
import type {
  ColumnFiltersState,
  ColumnOrderState,
  ColumnPinningTableState,
  ColumnSizingInfoState,
  ColumnSizingTableState,
  ExpandedState,
  GroupingTableState,
  PaginationState,
  RowPinningTableState,
  RowSelectionState,
  SortingState,
  Updater,
  VisibilityState,
} from "@tanstack/react-table";
import type { ClientOptionsTypes } from "@types";
import {
  cleanEmptyParams,
  // DEFAULT_PAGE_INDEX,
  // DEFAULT_PAGE_SIZE,
} from "@/utils/cleanEmptyParams";

// export type ClientOptionsTypes = {
//   columnFilters?: ColumnFiltersState;
//   columnOrder?: ColumnOrderState;
//   columnPinning?: ColumnPinningTableState;
//   columnSizing?: ColumnSizingTableState;
//   columnSizingInfo?: ColumnSizingInfoState;
//   columnVisibility?: VisibilityState;
//   expanded?: ExpandedState;
//   globalFilter?: string;
//   // In TanStack Table v8, grouping is just an array of column IDs
//   grouping?: string[];
//   pagination?: PaginationState;
//   rowPinning?: RowPinningTableState;
//   rowSelection?: RowSelectionState;
//   sorting?: SortingState;

//   startDate?: string;
//   endDate?: string;
//   dateColumn?: string;
// };

export function useRouteFilters<T extends RouteIds<RegisteredRouter["routeTree"]>>(routeId: T) {
  const routeApi = getRouteApi(routeId);
  const navigate = useNavigate();
  const searchParams = routeApi.useSearch() as ClientOptionsTypes;
  // console.log("searchParams", searchParams);

  // Provide default values to prevent undefined states
  // const filters: ClientOptionsTypes = {
  //   columnFilters: searchParams.columnFilters ?? [],
  //   globalFilter: searchParams.globalFilter ?? "",
  //   sorting: searchParams.sorting ?? [],
  //   pageIndex: searchParams.pageIndex ?? DEFAULT_PAGE_INDEX,
  //   pageSize: searchParams.pageSize ?? DEFAULT_PAGE_SIZE,
  // };

  const setFilters = (partialFilters: Partial<ClientOptionsTypes>) => {
    console.log("current search", searchParams);
    console.log("provided partial state", partialFilters);
    const navigateOptions: NavigateOptions = {
      search: (prev: ClientOptionsTypes) => {
        const updatedSearch = { ...prev };

        // Process each key in the partial filters
        for (const [key, value] of Object.entries(partialFilters)) {
          if (value === undefined || value === null) {
            // Explicitly remove the key if value is undefined or null
            delete updatedSearch[key as keyof ClientOptionsTypes];
            continue;
          }

          // Handle objects (e.g., columnVisibility, expanded)
          if (typeof value === "object" && !Array.isArray(value)) {
            if (Object.keys(value).length === 0) {
              // If empty object, remove the key
              delete updatedSearch[key as keyof ClientOptionsTypes];
            } else {
              // Merge with existing object if it exists
              const existingValue = updatedSearch[key as keyof ClientOptionsTypes];
              updatedSearch[key as keyof ClientOptionsTypes] = {
                ...(typeof existingValue === "object" && existingValue !== null ? existingValue : {}),
                ...value,
              };
            }
          } else if (Array.isArray(value) && value.length === 0) {
            // Remove empty arrays
            delete updatedSearch[key as keyof ClientOptionsTypes];
          } else if (value === "") {
            // Remove empty strings
            delete updatedSearch[key as keyof ClientOptionsTypes];
          } else {
            // Set primitive values directly
            updatedSearch[key as keyof ClientOptionsTypes] = value;
          }
        }

        // Clean up any empty values that might have been left over
        const cleanedSearch = cleanEmptyParams(updatedSearch);

        console.log("updated and cleaned complete search params for navigation", cleanedSearch);
        return cleanedSearch;
      },
      replace: true,
    };
    console.log("<<<<<<<<<<<<<<<   USE ROUTE FILTERS  navigating:  >>>>> navigateOptions:", navigateOptions);
    navigate(navigateOptions);
  };

  const setSimpleFilters = (partialFilters: Partial<ClientOptionsTypes>) => {
    navigate({
      search: (prev: ClientOptionsTypes) => cleanEmptyParams({ ...prev, ...partialFilters }),
      replace: true,
    });
  };

  const setComplexFilters = (partialFilters: Partial<ClientOptionsTypes>) => {
    navigate({
      search: (prev: ClientOptionsTypes) => {
        const updatedSearch: Partial<ClientOptionsTypes> = { ...prev };

        for (const [key, value] of Object.entries(partialFilters)) {
          const typedKey = key as keyof ClientOptionsTypes;

          if (value === undefined || value === null) {
            delete updatedSearch[typedKey];
            continue;
          }

          if (typeof value === "object" && !Array.isArray(value)) {
            if (Object.keys(value).length === 0) {
              delete updatedSearch[typedKey];
            } else {
              const existingValue = updatedSearch[typedKey];
              if (typeof existingValue === "object" && existingValue !== null && !Array.isArray(existingValue)) {
                updatedSearch[typedKey] = { ...existingValue, ...value };
              } else {
                updatedSearch[typedKey] = value;
              }
            }
          }
        }
        return cleanEmptyParams(updatedSearch);
      },
      replace: true,
    });
  };

  const resetFilters = () => {
    navigate({ search: {}, replace: true });
  };

  const setAllFilters = (filters: ClientOptionsTypes) => {
    navigate({ search: filters, replace: true });
  };

  const toggleColumnVisibility = (columnId: string) => {
    console.log("column id", columnId);
    const currentVisibilityParam = searchParams.columnVisibility;
    console.log("currentVisibilityParam", currentVisibilityParam);
    let columnVisibility: VisibilityState = {};

    if (typeof currentVisibilityParam === "string") {
      try {
        const parsed = JSON.parse(currentVisibilityParam);
        if (typeof parsed === "object" && parsed !== null) {
          columnVisibility = parsed;
        }
      } catch {
        // Not a valid JSON string, start fresh.
      }
    } else if (typeof currentVisibilityParam === "object" && currentVisibilityParam !== null) {
      columnVisibility = currentVisibilityParam as VisibilityState;
    }

    const newVisibility = { ...columnVisibility };

    if (newVisibility[columnId] === false) {
      delete newVisibility[columnId];
    } else {
      newVisibility[columnId] = false;
    }
    console.log("newVisibility", newVisibility);

    setSimpleFilters({ columnVisibility: newVisibility });
  };

  const setExpandedState = (provided: any) => {
    // tanstack table is taking care of the parsing and serialization,
    // we work with pure objects

    // 1. Get current state, state shape:
    // expanded: {
    //     status:DRAFT: true
    // }
    // Parse current expanded state from search params (may be string)
    let current: Record<string, boolean> = {};
    if (typeof searchParams.expanded === "string") {
      try {
        current = JSON.parse(searchParams.expanded);
      } catch {
        current = {};
      }
    } else if (typeof searchParams.expanded === "object" && searchParams.expanded !== null) {
      current = { ...(searchParams.expanded as Record<string, boolean>) };
    }
    console.log("[setExpandedState] Current expanded state:", current);

    // 2. Get value from updater function
    const value = typeof provided === "function" ? provided() : provided;
    console.log("[setExpandedState] Processed provided  value:", value);

    // the value is in format
    // {
    //     "type:PART_TIME": true,
    //     "type:PART_TIME>status:DRAFT": true
    // }

    // 3. Create new state object we will mutate based on toggle logic
    // Check if exists in current state
    //
    // if exists - remove
    //
    // if does exist - add
    // Start with a copy of current so we can toggle keys intelligently
    const newState: Record<string, boolean> = { ...current };
    console.log("[setExpandedState] Initial newState (from current):", newState);

    // 4. Process all keys in the value object (typically just the clicked row id)
    for (const key of Object.keys(value)) {
      console.log(`[setExpandedState] Processing key: ${key}`);
      console.log(`[setExpandedState] value[${key}]:`, value[key]);
      console.log(`[setExpandedState] Boolean(value[${key}]):`, Boolean(value[key]));

      if (value[key]) {
        if (current && current[key]) {
          // Key already expanded → fold it
          console.log(`[setExpandedState] Folding (toggle) existing key: ${key}`);
          delete newState[key];
        } else {
          console.log(`[setExpandedState] Expanding key: ${key}`);
          newState[key] = true;
        }
      } else {
        console.log(`[setExpandedState] Folding key: ${key}`);
        // Remove key and all of its descendant keys
        Object.keys(newState).forEach((k) => {
          if (k === key || k.startsWith(`${key}>`)) {
            console.log(`[setExpandedState] Removing descendant/selected key: ${k}`);
            delete newState[k];
          }
        });

        // After removal, if parent has no children, remove parent as well
        if (key.includes(">")) {
          const parent = key.split(">").slice(0, -1).join(">");
          const parentHasOtherChildren = Object.keys(newState).some((k) => k.startsWith(`${parent}>`));
          if (!parentHasOtherChildren) {
            console.log(`[setExpandedState] Removing now-childless parent: ${parent}`);
            delete newState[parent];
          }
        }
        console.log(`[setExpandedState] Checking parent: ${parent}`);
        const hasChildren = Object.keys(newState).some((k) => k.startsWith(`${parent}>`));
        console.log(`[setExpandedState] Parent ${parent} has children:`, hasChildren);
        if (!hasChildren) {
          console.log(`[setExpandedState] Removing parent: ${parent}`);
          delete newState[parent];
        }
      }

      console.log("[setExpandedState] Updated newState:", newState);
    }

    // 4. Update URL with proper typing
    const newSearch = {
      ...searchParams,
      expanded: Object.keys(newState).length ? JSON.stringify(newState) : undefined,
    };
    console.log("[setExpandedState] Final state being set:", newSearch.expanded);

    navigate({
      search: newSearch as Record<string, string | undefined>,
      replace: true,
    });
  };

  // /**
  //  * Sets the grouping state in the URL
  //  * Handles both direct values and updater functions from TanStack Table
  //  * Only allows one level of grouping - always replaces existing grouping if present
  //  * @param updaterOrValue - The grouping state from the table or an updater function
  //  * the function is toggle type and returns ["col_name"] to toggle
  //  */
  // const setGroupingState = (updaterOrValue: any) => {
  //   // Get current grouping state from URL params. It can be a string or an array.
  //   const currentGrouping = (
  //     searchParams.grouping && Array.isArray(searchParams.grouping)
  //       ? searchParams.grouping
  //       : searchParams.grouping
  //         ? [searchParams.grouping]
  //         : []
  //   ) as string[];

  //   let newGroupingState: string[] | undefined;

  //   // Handle TanStack Table's updater function
  //   if (typeof updaterOrValue === "function") {
  //     const typedUpdater = updaterOrValue as (prev: string[]) => string[];
  //     const updatedState = typedUpdater(currentGrouping);

  //     // Enforce single level of grouping. The new group is the last one in the array.
  //     if (updatedState.length > 0) {
  //       newGroupingState = [updatedState[updatedState.length - 1]];
  //     } else {
  //       newGroupingState = undefined; // No grouping
  //     }
  //   } else {
  //     // Handle direct value
  //     const groupingValue = updaterOrValue as string[] | undefined;
  //     if (groupingValue && groupingValue.length > 0) {
  //       // Enforce single level of grouping
  //       newGroupingState = [groupingValue[groupingValue.length - 1]];
  //     } else {
  //       newGroupingState = undefined; // No grouping
  //     }
  //   }

  //   // Create a new search params object to avoid mutating the current state directly
  //   const newSearchParams: Partial<ClientOptionsTypes> = { ...searchParams };

  //   // Update grouping
  //   newSearchParams.grouping = newGroupingState;

  //   // If grouping is being removed, also clear expanded state
  //   if (newGroupingState === undefined) {
  //     newSearchParams.expanded = undefined;
  //   }

  //   // Reset page index to 0 whenever grouping changes
  //   const currentPageSize = searchParams.pagination?.pageSize ?? 10;
  //   newSearchParams.pagination = {
  //     pageIndex: 0,
  //     pageSize: currentPageSize,
  //   };

  //   // Navigate with the new, cleaned state
  //   navigate({ search: cleanEmptyParams(newSearchParams), replace: true });
  // };

  // multilevel grouping not tested
  //
  /**
   * Sets the grouping state in the URL
   * Handles both direct values and updater functions from TanStack Table
   * @param updaterOrValue - The grouping state from the table or an updater function
   */
  const setGroupingState = (updaterOrValue: any) => {
    // Get current grouping state from URL params. It can be a string or an array.
    const currentGrouping = (
      searchParams.grouping && Array.isArray(searchParams.grouping)
        ? searchParams.grouping
        : searchParams.grouping
          ? [searchParams.grouping]
          : []
    ) as string[];

    let newGroupingState: string[] | undefined;

    // Handle TanStack Table's updater function
    if (typeof updaterOrValue === "function") {
      const typedUpdater = updaterOrValue as (prev: string[]) => string[];
      newGroupingState = typedUpdater(currentGrouping);
      // If the result is an empty array, set to undefined to remove it from the URL
      if (newGroupingState.length === 0) {
        newGroupingState = undefined;
      }
    } else {
      newGroupingState = updaterOrValue as string[] | undefined;
      // If it's an empty array, set to undefined
      if (newGroupingState && newGroupingState.length === 0) {
        newGroupingState = undefined;
      }
    }

    // Create a new search params object to avoid mutating the current state directly
    const newSearchParams: Partial<ClientOptionsTypes> = { ...searchParams };

    // Update grouping
    newSearchParams.grouping = newGroupingState;

    // If grouping is being removed, also clear expanded state
    if (newGroupingState === undefined) {
      newSearchParams.expanded = undefined;
    }

    // Reset page index to 0 whenever grouping changes
    const currentPageSize = searchParams.pagination?.pageSize ?? 10;
    newSearchParams.pagination = {
      pageIndex: 0,
      pageSize: currentPageSize,
    };

    // Navigate with the new, cleaned state
    navigate({ search: cleanEmptyParams(newSearchParams), replace: true });
  };

  function setColumnSizing(updaterOrValue: any) {
    console.log("setColumnSizing called with:", updaterOrValue);
    
    // Get current column sizing from URL params
    const currentColumnSizing = searchParams.columnSizing || {};
    console.log("Current columnSizing from URL:", currentColumnSizing);
    
    let newColumnSizingState: Record<string, number> = {};
    
    // Handle both function updater and direct value
    if (typeof updaterOrValue === "function") {
      // If it's a function, call it with the current state to get the new state
      newColumnSizingState = updaterOrValue(currentColumnSizing);
    } else if (updaterOrValue && typeof updaterOrValue === 'object') {
      // If it's an object, use it directly
      newColumnSizingState = updaterOrValue;
    }
    
    console.log("New columnSizing state:", newColumnSizingState);
    
    // Only update if we have a valid new state
    if (Object.keys(newColumnSizingState).length > 0) {
      // Create a new search params object with the updated columnSizing
      const newSearchParams = {
        ...searchParams,
        columnSizing: newColumnSizingState
      };
      
      // Navigate with the new state
      navigate({
        search: cleanEmptyParams(newSearchParams),
        replace: true
      });
      
      console.log("URL updated with new columnSizing state");
    }
  }

  return {
    filters: searchParams,
    setFilters,
    setSimpleFilters,
    setComplexFilters,
    setAllFilters,
    resetFilters,
    toggleColumnVisibility,
    setExpandedState,
    setGroupingState,
    setColumnSizing,
  };
}
