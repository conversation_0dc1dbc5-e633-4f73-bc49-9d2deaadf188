/** biome-ignore-all lint/a11y/noStaticElementInteractions: <explanation> */

import {
  type ColumnDef,
  type ColumnResizeMode,
  type ColumnSizingTableState,
  type ExpandedState,
  flexRender,
  type GroupingTableState,
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getGroupedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import type { ClientOptionsTypes, VariantDisplayColumnsTypes } from "@types";
import { AlertCircle, ChevronRight } from "lucide-react";
import type * as React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { defaultVariant } from "@/api/crm/variants/defaultVariant";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/_shadcn/components/ui/alert";
import { Skeleton } from "@/components/_shadcn/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/_shadcn/components/ui/table";
import type {
  ColumnOrderUpdater,
  ExportConfigType,
  PaginationUpdater,
  PathStateTypes,
  RowSelectionUpdater,
  SortingUpdater,
  TableConfig,
} from "@/components/data-table/defaults";
import type { GetColumnsOptions } from "@/dosiero/routes/main/jobs/-components/columns.tsx";
import { cn } from "../_shadcn/lib/utils";
import { DataTableResizer } from "./data-table-resizer";
import { useRouteFilters } from "./hooks/use-route-filters";
import { useTableColumnResize } from "./hooks/use-table-column-resize";
import { DataTablePagination } from "./pagination";
import { DataTableToolbar } from "./toolbar";
// import { cleanupColumnResizing, initializeColumnSizes, trackColumnResizing } from "./utils/column-sizing";
// import { createConditionalStateHook } from "./utils/conditional-state";
import { isWithinDateRange } from "./utils/date-filter";
import { createKeyboardNavigationHandler } from "./utils/keyboard-navigation";
// Import core utilities
import { preprocessSearch } from "./utils/search";
import {
  createColumnFiltersHandler,
  createColumnSizingHandler,
  createColumnVisibilityHandler,
  createPaginationHandler,
  createSortingHandler,
  createSortingState,
} from "./utils/table-state-handlers";

interface DataTableProps<
  TData,
  TVariant extends VariantDisplayColumnsTypes = VariantDisplayColumnsTypes,
> {
  // Allow overriding the table configuration

  data: TData[];
  serverPagination?: {
    pageIndex: number;
    pageSize: number;
    totalItems: number;
  };
  error?: Error;
  isLoading?: boolean;
  tableConfig: TableConfig;
  // Column definitions generator
  getColumns: (options: GetColumnsOptions) => ColumnDef<TData>[];
  // Export configuration
  exportConfig: ExportConfigType;
  // Custom toolbar content render function
  customToolbarContent?: (props: {
    getAllItems: () => TData[];
    selectedRows: TData[];
    allSelectedIds: (string | number)[];
    totalSelectedCount: number;
    resetSelection: () => void;
  }) => React.ReactNode;
  pathState: ClientOptionsTypes;
  // queryArgs: TQueryArgs;
  // setQueryArgs: (args: TQueryArgs) => void;
  variant: TVariant;
  setVariant: (variant: TVariant) => void;
  selectVariantsData: TVariant[];
  createVariantMutation: any;
  updateVariantMutation: any;
}

export function DataTable<
  TData,
  TVariant extends VariantDisplayColumnsTypes = VariantDisplayColumnsTypes,
>({
  data,
  serverPagination,
  error,
  isLoading,
  tableConfig,
  getColumns,
  exportConfig,
  customToolbarContent,
  pathState,
  // queryArgs,
  // setQueryArgs,
  variant,
  setVariant,
  selectVariantsData,
  createVariantMutation,
  updateVariantMutation,
}: DataTableProps<TData, TVariant>) {
  const { t } = useTranslation();

  const idField = tableConfig.idField;
  // const pathState = variant.client_ops || defaultVariant.client_ops;

  // TanStack grouping state
  // const [grouping, setGrouping] = useState<string[]>(["type"]);
  // TanStack expanded state - use route filters
  const { setExpandedState, setGroupingState, setFilters, setColumnSizing } = useRouteFilters(
    tableConfig.path,
  );

  // Process the expanded state from the URL
  const initialExpandedState = useMemo(() => {
    if (!pathState?.expanded) return true; // Default to all expanded if not in URL
    if (pathState?.expanded && Object.keys(pathState?.expanded).length === 0)
      return false; // Empty object means nothing expanded
    return pathState?.expanded; // Otherwise use the state from URL
  }, [pathState?.expanded]);

  console.log("expanded initial state from path", initialExpandedState);

  // Table ID for localStorage storage - generate a default if not provided
  const tableId = tableConfig.columnResizingTableId || "no-config";
  // Use our custom hook for column resizing
  // const { columnSizing, setColumnSizing, resetColumnSizing } =
  //   useTableColumnResize(tableId, tableConfig.enableColumnResizing);

  // // Create conditional URL state hook based on config
  // const useConditionalUrlState = createConditionalStateHook(tableConfig.enableUrlState);
  // console.log("useConditionalUrlState", useConditionalUrlState);

  // States for API parameters using conditional URL state

  // // Internal states
  // const [isLoading, setIsLoading] = useState(true);
  // const [isError, setIsError] = useState(false);
  // const [error, setError] = useState<Error | null>(null);
  // const [data, setData] = useState<{
  //   data: TData[];
  //   pagination: {
  //     page: number;
  //     limit: number;
  //     total_pages: number;
  //     total_items: number;
  //   };
  // } | null>(null);

  // Column order state (managed separately from URL state as it's persisted in localStorage)
  const [columnOrder, setColumnOrder] = useState<string[]>([]);

  // PERFORMANCE FIX: Use only one selection state as the source of truth
  const [selectedItemIds, setSelectedItemIds] = useState<
    Record<string | number, boolean>
  >({});

  // Convert the sorting from URL to the format TanStack Table expects
  // const sorting = useMemo(() => createSortingState(sortBy, sortOrder), [sortBy, sortOrder]);

  // Filter and sort data items based on globalFilter, date range, and sorting state
  const dataItems = useMemo(() => {
    let filteredData = data || [];

    // Apply date filtering if we have a date column and date filters
    if (
      pathState?.date_column &&
      (pathState?.start_date || pathState?.end_date)
    ) {
      filteredData = filteredData.filter((item) => {
        const dateValue = item[pathState?.date_column as keyof typeof item];
        return isWithinDateRange(
          dateValue as string,
          pathState?.start_date,
          pathState?.end_date,
        );
      });
    }

    // Apply global filter (search string)
    if (pathState?.globalFilter) {
      const searchTerm = String(pathState.globalFilter).toLowerCase();
      filteredData = filteredData.filter((item) => {
        // Search through all string properties of the item
        return Object.entries(item as Record<string, unknown>).some(
          ([_, value]) => {
            if (typeof value === "string") {
              return value.toLowerCase().includes(searchTerm);
            }
            return false;
          },
        );
      });
    }

    return filteredData;
  }, [
    data,
    pathState?.globalFilter,
    pathState?.start_date,
    pathState?.end_date,
    pathState?.date_column,
  ]);

  // PERFORMANCE FIX: Derive rowSelection from selectedItemIds using memoization
  const rowSelection = useMemo(() => {
    if (!dataItems.length) return {};

    // Map selectedItemIds to row indices for the table
    const selection: Record<string, boolean> = {};

    dataItems.forEach((item, index) => {
      const itemId = String(item[idField as keyof TData]);
      if (selectedItemIds[itemId]) {
        selection[String(index)] = true;
      }
    });

    return selection;
  }, [dataItems, selectedItemIds, idField]);

  // Calculate total selected items - memoize to avoid recalculation
  const totalSelectedItems = useMemo(
    () => Object.keys(selectedItemIds).length,
    [selectedItemIds],
  );

  // PERFORMANCE FIX: Optimized row deselection handler
  const handleRowDeselection = useCallback(
    (rowId: string) => {
      if (!dataItems.length) return;

      const rowIndex = Number.parseInt(rowId, 10);
      const item = dataItems[rowIndex];

      if (item) {
        const itemId = String(item[idField as keyof typeof item]);
        setSelectedItemIds((prev) => {
          // Remove this item ID from selection
          const next = { ...prev };
          delete next[itemId];
          return next;
        });
      }
    },
    [dataItems, idField],
  );

  // Clear all selections
  const clearAllSelections = useCallback(() => {
    setSelectedItemIds({});
  }, []);

  // PERFORMANCE FIX: Optimized row selection handler
  const handleRowSelectionChange = useCallback(
    (updaterOrValue: RowSelectionUpdater | Record<string, boolean>) => {
      // Determine the new row selection value
      const newRowSelection =
        typeof updaterOrValue === "function"
          ? updaterOrValue(rowSelection)
          : updaterOrValue;

      // Batch update selectedItemIds based on the new row selection
      setSelectedItemIds((prev) => {
        const next = { ...prev };

        // Process changes for current page
        if (dataItems.length) {
          // First handle explicit selections in newRowSelection
          for (const [rowId, isSelected] of Object.entries(newRowSelection)) {
            const rowIndex = Number.parseInt(rowId, 10);
            if (rowIndex >= 0 && rowIndex < dataItems.length) {
              const item = dataItems[rowIndex];
              const itemId = String(item[idField as keyof typeof item]);

              if (isSelected) {
                next[itemId] = true;
              } else {
                delete next[itemId];
              }
            }
          }

          // Then handle implicit deselection (rows that were selected but aren't in newRowSelection)
          dataItems.forEach((item, index) => {
            const itemId = String(item[idField as keyof typeof item]);
            const rowId = String(index);

            // If item was selected but isn't in new selection, deselect it
            if (prev[itemId] && newRowSelection[rowId] === undefined) {
              delete next[itemId];
            }
          });
        }

        return next;
      });
    },
    [dataItems, rowSelection, idField],
  );

  // Get selected items data
  const getSelectedItems = useCallback(async () => {
    // If nothing is selected, return empty array
    if (totalSelectedItems === 0) {
      return [];
    }
    // Find items that are selected
    const items = dataItems.filter(
      (item) => selectedItemIds[String(item[idField as keyof typeof item])],
    );
    return items;
  }, [dataItems, selectedItemIds, totalSelectedItems, idField]);

  // Get all items on current page
  const getAllItems = useCallback((): TData[] => {
    // Return current page data
    return dataItems as TData[];
  }, [dataItems]);

  // Ref for the table container for keyboard navigation
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Get columns with the deselection handler (memoize to avoid recreation on render)
  const columns = useMemo(() => {
    // Only pass deselection handler if row selection is enabled
    // We return only columns which are included in server response
    const options = {
      handleRowDeselection: tableConfig.enableRowSelection
        ? handleRowDeselection
        : null,
      tableConfig,
      columns: variant?.server_ops?.columns,
    } as GetColumnsOptions;

    const columns = getColumns(options);
    // console.log("DATA TABLE RETURNING columns", columns);
    return columns;
  }, [
    getColumns,
    handleRowDeselection,
    tableConfig,
    variant?.server_ops?.columns,
  ]);

  // const handleColumnFiltersChange = useCallback(createColumnFiltersHandler(setColumnFilters), []);

  // const handleColumnVisibilityChange = useCallback(createColumnVisibilityHandler(setColumnVisibility), []);

  // const handlePaginationChange = useCallback(
  //   (updaterOrValue: PaginationUpdater<TData> | { pageIndex: number; pageSize: number }) => {
  //     // Extract the new pagination state
  //     const newPagination =
  //       typeof updaterOrValue === "function" ? updaterOrValue({ pageIndex: page - 1, pageSize }) : updaterOrValue;

  //     // Special handling: When page size changes, always reset to page 1
  //     if (newPagination.pageSize !== pageSize) {
  //       // First, directly update URL to ensure it's in sync
  //       const url = new URL(window.location.href);
  //       url.searchParams.set("pageSize", String(newPagination.pageSize));
  //       url.searchParams.set("page", "1"); // Always reset to page 1
  //       window.history.replaceState({}, "", url.toString());

  //       // Then update our state
  //       setPageSize(newPagination.pageSize);
  //       setPage(1);
  //       return;
  //     }

  //     // Only update page if it's changed - this handles normal page navigation
  //     if (newPagination.pageIndex + 1 !== page) {
  //       const setPagePromise = setPage(newPagination.pageIndex + 1);
  //       if (setPagePromise && typeof setPagePromise.catch === "function") {
  //         setPagePromise.catch((err) => {
  //           console.error("Failed to update page param:", err);
  //         });
  //       }
  //     }
  //   },
  //   [page, pageSize, setPage, setPageSize],
  // );

  // const handleColumnSizingChange = useCallback(
  //   (
  //     updaterOrValue:
  //       | ColumnSizingTableState
  //       | ((prev: ColumnSizingTableState) => ColumnSizingTableState),
  //   ) => {
  //     if (typeof updaterOrValue === "function") {
  //       setColumnSizing((current) => updaterOrValue(current));
  //     } else {
  //       setColumnSizing(updaterOrValue);
  //     }
  //   },
  //   [setColumnSizing],
  // );

  // Set up the table with memoized state
  // Debug data and columns
  console.log(
    "Table columns:",
    columns.map((c) => ({ id: c.id })),
  );
  console.log(
    "First data item:",
    dataItems.length > 0 ? dataItems[0] : "No data",
  );
  console.log("pathState", pathState);
  const table = useReactTable({
    data: dataItems,
    columns,
    state: {
      ...pathState,
      rowSelection,
      columnSizing: pathState.columnSizing || {},
      // pagination: pathState?.pagination ?? { pageIndex: 0, pageSize: 20 },
    },
    // getRowCanExpand: (row) => row.subRows?.length > 0,

    // COLUMN RESIZING
    columnResizeMode: "onEnd" as ColumnResizeMode,
    onColumnSizingChange: value => setColumnSizing(value),
    pageCount: Math.ceil(
      (serverPagination?.totalItems || 1) / (serverPagination?.pageSize || 1),
    ),
    enableRowSelection: tableConfig.enableRowSelection,
    enableColumnResizing: tableConfig.enableColumnResizing,
    manualPagination: false, // Set to false to enable client-side pagination
    manualSorting: false,
    manualFiltering: false,
    onRowSelectionChange: handleRowSelectionChange,
    // EXPANDED
    getExpandedRowModel: getExpandedRowModel(),
    onExpandedChange: (value) => {
      console.log("expanded value", value);
      // console.log("expanded value function result", value());

      // Use type assertion to handle the updater function properly
      // This is safe because we've updated the setExpandedState function to handle both
      const safeValue = value as ExpandedState;
      setExpandedState(safeValue);
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    // GROUPING
    // enableGrouping: true,
    getGroupedRowModel: getGroupedRowModel(),
    onGroupingChange: (value) => {
      console.log("grouping value", value);
      // console.log("grouping value function result", value());
      // All complex type handling logic is now in the hook
      setGroupingState(value);
    },
    // groupedColumnMode: false
    debugTable: true,
  });

  // Create keyboard navigation handler
  const handleKeyDown = useCallback(
    createKeyboardNavigationHandler(table, (row, rowIndex) => {
      // Example action on keyboard activation
    }),
    [],
  );

  console.log("table state", table.getState());

  // // Add an effect to validate page number when page size changes
  // useEffect(() => {
  //   // This effect ensures page is valid after page size changes
  //   const totalPages = data?.pagination.total_pages || 0;

  //   if (totalPages > 0 && page > totalPages) {
  //     setPage(1);
  //   }
  // }, [data?.pagination?.total_pages, page, setPage]);

  // // Initialize default column sizes when columns are available and no saved sizes exist
  // useEffect(() => {
  //   initializeColumnSizes(columns, tableId, setColumnSizing);
  // }, [columns, tableId, setColumnSizing]);

  // Handle column resizing
  // useEffect(() => {
  //   const isResizingAny = table
  //     .getHeaderGroups()
  //     .some((headerGroup) => headerGroup.headers.some((header) => header.column.getIsResizing()));
  //   trackColumnResizing(isResizingAny);
  //   // Cleanup on unmount
  //   return () => {
  //     cleanupColumnResizing();
  //   };
  // }, [table]);

  // Handle error state
  if (error) {
    return (
      <Alert variant="destructive" className="my-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load data:{" "}
          {error instanceof Error ? error.message : "Unknown error"}
        </AlertDescription>
      </Alert>
    );
  }
  // make table full width

  return (
    <div className="space-y-4">
      {tableConfig.enableToolbar && (
        <DataTableToolbar<TData, TVariant>
          table={table}
          getColumns={getColumns}
          // setSearch={setSearch}
          // setDateRange={setDateRange}
          totalSelectedItems={totalSelectedItems}
          deleteSelection={clearAllSelections}
          getSelectedItems={getSelectedItems}
          getAllItems={getAllItems}
          config={tableConfig}
          resetColumnSizing={() => {
            resetColumnSizing();
            // Force a small delay and then refresh the UI
            setTimeout(() => {
              window.dispatchEvent(new Event("resize"));
            }, 100);
          }}
          // resetColumnOrder={resetColumnOrder}
          exportConfig={exportConfig}
          // entityName={exportConfig.entityName}
          // columnMapping={exportConfig.columnMapping}
          // columnWidths={exportConfig.columnWidths}
          // headers={exportConfig.headers}
          customToolbarComponent={customToolbarContent?.({
            getAllItems,
            selectedRows: dataItems.filter(
              (item) =>
                selectedItemIds[String(item[idField as keyof typeof item])],
            ),
            allSelectedIds: Object.keys(selectedItemIds),
            totalSelectedCount: totalSelectedItems,
            resetSelection: clearAllSelections,
          })}
          pathState={pathState}
          // queryArgs={queryArgs}
          // setQueryArgs={setQueryArgs}
          variant={variant}
          setVariant={setVariant}
          selectVariantsData={selectVariantsData}
          createVariantMutation={createVariantMutation}
          updateVariantMutation={updateVariantMutation}
        />
      )}

      {/** biome-ignore lint/a11y/useAriaPropsSupportedByRole: <explanation> */}
      <div
        ref={tableContainerRef}
        className="overflow-y-auto rounded-md border table-container"
        aria-label="Data table"
        onKeyDown={
          tableConfig.enableKeyboardNavigation ? handleKeyDown : undefined
        }
      >
        <Table
          className={tableConfig.enableColumnResizing ? "resizable-table" : ""}
        >
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    className="px-2 py-2 relative text-left group/th"
                    key={header.id}
                    colSpan={header.colSpan}
                    scope="col"
                    tabIndex={-1}
                    style={{
                      width: header.getSize(),
                    }}
                    data-column-resizing={
                      header.column.getIsResizing() ? "true" : undefined
                    }
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                    {/* {tableConfig.enableColumnResizing &&
                      header.column.getCanResize() && (
                        <DataTableResizer header={header} table={table} />
                      )} */}

                    <div
                      {...{
                        onDoubleClick: () => header.column.resetSize(),
                        onMouseDown: header.getResizeHandler(),
                        onTouchStart: header.getResizeHandler(),
                        className: `resizer ${table.options.columnResizeDirection
                          } ${header.column.getIsResizing() ? 'isResizing' : ''
                          }`,
                        style: {
                          transform:
                            table.options.columnResizeMode === 'onEnd' &&
                              header.column.getIsResizing()
                              ? `translateX(${(table.options.columnResizeDirection ===
                                'rtl'
                                ? -1
                                : 1) *
                              (table.getState().columnSizingInfo
                                .deltaOffset ?? 0)
                              }px)`
                              : '',
                        },
                      }}
                    />
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {isLoading ? (
              // Loading state
              Array.from({ length: pathState?.pagination?.pageSize || 10 }).map(
                (_, i) => (
                  <TableRow
                    key={`loading-row-${crypto.randomUUID()}`}
                    tabIndex={-1}
                  >
                    {Array.from({ length: columns.length }).map(
                      (_, j, array) => (
                        <TableCell
                          key={`skeleton-cell-${crypto.randomUUID()}`}
                          className="px-4 py-2 truncate max-w-0 text-left"
                          tabIndex={-1}
                        >
                          <Skeleton className="h-6 w-full" />
                        </TableCell>
                      ),
                    )}
                  </TableRow>
                ),
              )
            ) : table.getRowModel().rows?.length ? (
              // Data rows
              table.getRowModel().rows.map((row, rowIndex) => (
                <TableRow
                  key={row.id}
                  id={`row-${rowIndex}`}
                  data-row-index={rowIndex}
                  data-state={row.getIsSelected() ? "selected" : undefined}
                  tabIndex={0}
                  aria-selected={row.getIsSelected()}
                  onClick={(e) => {
                    // Handle expand/collapse for grouped rows
                    console.log("row data: ", row);
                    if (row.getIsGrouped() || row.getCanExpand()) {
                      e.stopPropagation();
                      row.toggleExpanded();
                    } else if (tableConfig.enableClickRowSelect) {
                      row.toggleSelected();
                    }
                  }}
                  onFocus={(e) => {
                    // Add a data attribute to the currently focused row
                    for (const el of document.querySelectorAll(
                      '[data-focused="true"]',
                    )) {
                      el.removeAttribute("data-focused");
                    }
                    e.currentTarget.setAttribute("data-focused", "true");
                  }}
                  style={{
                    cursor:
                      row.getIsGrouped() || row.getCanExpand()
                        ? "pointer"
                        : "default",
                    backgroundColor: row.getIsGrouped()
                      ? "rgba(0, 0, 0, 0.05)"
                      : undefined,
                    fontWeight: row.getIsGrouped() ? "600" : "normal",
                  }}
                >
                  {row.getVisibleCells().map((cell, cellIndex) => {
                    let cellContent = null;

                    if (cell.getIsGrouped()
                      // && !row.getIsGrouped() // not sure what is it for...
                    ) {
                      // Hide grouped column in non-group rows (child rows)
                      cellContent = <button
                        {...{
                          onClick: row.getToggleExpandedHandler(),
                          style: {
                            cursor: row.getCanExpand()
                              ? 'pointer'
                              : 'normal',
                          },
                        }}
                      >
                        <div
                          className={'flex items-center gap-1'}
                        >
                          <ChevronRight className={cn(row.getIsExpanded() && 'rotate-90')} />
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                          ({row.subRows.length})
                        </div>
                      </button>
                    } else if (cell.getIsAggregated()) {
                      // in aggregated row
                      cellContent = flexRender(
                        cell.column.columnDef.aggregatedCell,
                        cell.getContext(),
                      );
                    } else if (cell.getIsPlaceholder()) {
                      // no content for placeholder cell
                      cellContent = null;
                    } else {
                      cellContent = flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      );
                    }

                    return (
                      <TableCell
                        className="px-4 py-2 truncate max-w-0 text-left"
                        key={cell.id}
                        id={`cell-${rowIndex}-${cellIndex}`}
                        data-cell-index={cellIndex}
                        onClick={() => {
                          console.log("cell clicked: ", cell);
                          console.log("cell aggregated: ", cell.getIsAggregated());
                          console.log("cell grouped: ", cell.getIsGrouped());
                          console.log("cell context: ", cell.getContext());
                        }}
                        style={{
                          paddingLeft: row.getIsGrouped()
                            ? "1rem"
                            : `${1 + row.depth * 1.5}rem`,
                          background: cell.getIsGrouped()
                            ? 'green'
                            : cell.getIsAggregated()
                              ? 'orange'
                              : cell.getIsPlaceholder()
                                ? 'blue'
                                : 'white',

                        }}
                      >
                        {cellContent}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              // No results
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-left truncate"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {tableConfig.enablePagination && (
        <DataTablePagination
          table={table}
          serverPagination={serverPagination}
          // totalItems={serverPagination?.totalItems || 0}
          totalSelectedItems={totalSelectedItems}
          pageSizeOptions={tableConfig.pageSizeOptions || [10, 20, 30, 40, 50]}
          size={tableConfig.size}
        />
      )}
    </div>
  );
}

// {row.getVisibleCells().map((cell, cellIndex) => (
//   <TableCell
//     className="px-4 py-2 truncate max-w-0 text-left"
//     key={cell.id}
//     id={`cell-${rowIndex}-${cellIndex}`}
//     data-cell-index={cellIndex}
//     style={{
//       paddingLeft: row.getIsGrouped()
//         ? "1rem"
//         : `${1 + row.depth * 1.5}rem`,
//     }}
//   >
//     {/* {flexRender(cell.column.columnDef.cell, cell.getContext())} */}
//     {flexRender(
//       cell.getIsAggregated()
//         ? cell.column.columnDef.aggregatedCell
//         : cell.column.columnDef.cell,
//       cell.getContext(),
//     )}
//   </TableCell>
// ))}
