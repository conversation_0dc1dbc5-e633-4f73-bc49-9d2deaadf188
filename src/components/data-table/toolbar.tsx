"use client";
import type { ClientOptionsTypes, GetAllParams, VariantDisplayColumnsTypes } from "@client/types.gen";
import { Cross2Icon } from "@radix-ui/react-icons";
import type {
  Column,
  ColumnDef,
  ColumnFiltersState,
  ColumnOrderState,
  PaginationState,
  RowSelectionState,
  SortingState,
  Table,
  Updater,
} from "@tanstack/react-table";
import { CheckSquare, EyeOff, MoveHorizontal, Settings, TrashIcon, Undo2 } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Button } from "@/components/_shadcn/components/ui/button";
import { Input } from "@/components/_shadcn/components/ui/input";
import { Label } from "@/components/_shadcn/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/_shadcn/components/ui/select";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/_shadcn/components/ui/sheet";
import { Modal } from "@/components/_system/modal";
import { InputIcon } from "@/components/_ui/input-icon";
import type { TableConfig } from "@/components/data-table/defaults";
import { useRouteFilters } from "@/components/data-table/hooks/use-route-filters";
import type { GetColumnsOptions } from "@/dosiero/routes/main/jobs/-components/columns.tsx";
import { JobCreateForm } from "@/dosiero/routes/main/jobs/-components/job-create-form.tsx";
import { JobCreateRhf } from "@/dosiero/routes/main/jobs/-components/job-create-rhf";
import { cleanEmptyParams } from "@/utils/cleanEmptyParams.ts";
import { removeIdFromObjects } from "@/utils/helpers";
import { useInternalNavigate } from "@/utils/navigation";
import { EndDatePicker } from "../_ui/end-date-picker";
import { StartDatePicker } from "../_ui/start-date-picker";
import { ClientColumnsOptions } from "./client-columns-options";
import { DataTableExport } from "./data-export";
import type { ExportConfigType } from "./defaults";
import type { PathStateTypes } from "./defaults.ts";
import { TableOptions } from "./table-options";
// import { formatDate } from "./utils/date-format";
// import { resetUrlState } from "./utils/deep-utils";
import { preprocessSearch } from "./utils/search";


// Helper functions for component sizing
const getInputSizeClass = (size: "sm" | "default" | "lg") => {
  switch (size) {
    case "sm":
      return "h-8";
    case "lg":
      return "h-11";
    default:
      return "";
  }
};

const getButtonSizeClass = (size: "sm" | "default" | "lg", isIcon = false) => {
  if (isIcon) {
    switch (size) {
      case "sm":
        return "h-8 w-8";
      case "lg":
        return "h-11 w-11";
      default:
        return "";
    }
  }
  switch (size) {
    case "sm":
      return "h-8 px-3";
    case "lg":
      return "h-11 px-5";
    default:
      return "";
  }
};

interface DataTableToolbarProps<TData, TVariant extends VariantDisplayColumnsTypes = VariantDisplayColumnsTypes> {
  table: Table<TData>;
  getColumns: (options: GetColumnsOptions) => ColumnDef<TData>[];
  totalSelectedItems?: number;
  deleteSelection?: () => void;
  getSelectedItems?: () => Promise<TData[]>;
  getAllItems?: () => TData[];
  config: TableConfig;
  resetColumnSizing?: () => void;
  // resetColumnOrder?: () => void;
  exportConfig: ExportConfigType;
  // entityName?: string;
  // columnMapping?: Record<string, string>;
  // columnWidths?: Array<{ wch: number }>;
  // headers?: string[];
  customToolbarComponent?: React.ReactNode;
  pathState: ClientOptionsTypes;
  variant: TVariant;
  setVariant: (variant: TVariant) => void;
  selectVariantsData: TVariant[];
  createVariantMutation: any;
  updateVariantMutation: any;
}

export function DataTableToolbar<TData, TVariant extends VariantDisplayColumnsTypes = VariantDisplayColumnsTypes>({
  table,
  getColumns,
  totalSelectedItems = 0,
  deleteSelection,
  getSelectedItems,
  getAllItems,
  config,
  resetColumnSizing,
  exportConfig,
  // entityName = "items",
  // columnMapping,
  // columnWidths,
  // headers,
  customToolbarComponent,
  pathState,
  variant,
  setVariant,
  selectVariantsData,
  createVariantMutation,
  updateVariantMutation,
}: DataTableToolbarProps<TData, TVariant>) {
  const { filters, setFilters } = useRouteFilters(config.path);
  const [modalOpen, setModalOpen] = useState(false);
  const { t } = useTranslation();

  // console.log("%cconfig    <<<<<  TOOLABAR  >>>>>>>>", "color: orange; font-weight: bold;", config);

  const { internalNavigate } = useInternalNavigate();

  // Initialize local state from URL parameters
  const [searchString, setSearchString] = useState<string>((filters.globalFilter as string) || "");
  const [localStartDate, setLocalStartDate] = useState<string>((filters.start_date as string) || "");
  const [localEndDate, setLocalEndDate] = useState<string>((filters.end_date as string) || "");
  const [tmpServerOps, setTmpServerOps] = useState(variant?.server_ops);
  // REDUNDANT ???
  // const [visibleColumns, setVisibleColumns] = useState<string[]>([]);
  // console.log("visibleColumns", visibleColumns);

  // useEffect(() => {
  //   if (!variant?.client_ops?.columnVisibility) {
  //     setVisibleColumns(
  //       getColumns(null)
  //         .map((col) => ("accessorKey" in col ? col.accessorKey : null))
  //         .filter((col): col is string => col !== null),
  //     );
  //   } else {
  //     setVisibleColumns([]);
  //   }
  // }, [variant?.client_ops?.columnVisibility, getColumns]);

  console.log("%c tmpServerOps <<<< TOOLABAR local  >>>>", "color: red; font-weight: bold;", tmpServerOps);
  console.log("%c variant <<<< TOOLABAR local  >>>>", "color: red; font-weight: bold;", variant);
  // Update local state when URL parameters change
  useEffect(() => {
    setSearchString((filters.globalFilter as string) || "");
    setLocalStartDate((filters.start_date as string) || "");
    setLocalEndDate((filters.end_date as string) || "");
  }, [filters.globalFilter, filters.start_date, filters.end_date]);

  const [newVariantName, setNewVariantName] = useState(() => (variant?.name || ""));
  // Update newVariantName when variant changes
  useEffect(() => {
    setNewVariantName(variant?.name || "");
    setTmpServerOps(
      {
        ...variant.server_ops,
        // add ids to search and order arrays
        search:
          variant.server_ops?.search && Array.isArray(variant.server_ops?.search)
            ? variant.server_ops.search.map((srch: { term: string; column_name: string }) => ({
              id: crypto.randomUUID(),
              ...srch,
            }))
            : variant.server_ops?.search || [],
        order: Array.isArray(variant.server_ops?.order)
          ? variant.server_ops.order.map((ord: { column_name: string; direction: string }) => ({
            id: crypto.randomUUID(),
            ...ord,
          }))
          : variant.server_ops?.order || [],
      }
    )
  }, [variant?.name, variant?.server_ops]);

  const tableColumns = useMemo(
    () => table.getAllColumns().filter((column) => typeof column.accessorFn !== "undefined" && column.getCanHide()),
    [table],
  );

  // Get column display label
  const getColumnLabel = useCallback(
    (column: Column<TData, unknown>) => {
      // First check if we have a mapping for this column
      if (exportConfig.columnMapping && column.id in exportConfig.columnMapping) {
        return exportConfig.columnMapping[column.id];
      }
      // Then check for meta label
      return (
        (column.columnDef.meta as { label?: string })?.label ??
        // Finally fall back to formatted column ID
        column.id.replace(/_/g, " ")
      );
    },
    [exportConfig.columnMapping],
  );


  const tableFiltered = table.getState().columnFilters.length > 0;

  // Get search value from table state as fallback
  const currentSearchFromTable = (table.getState().globalFilter as string) || "";

  const tableSearch = (table.getState().globalFilter as string) || "";

  // Determine if any filters are active
  const isFiltered = searchString !== "" || localStartDate !== "" || localEndDate !== "";

  // Create a ref to store the debounce timer
  // const searchDebounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchString(value);
  };

  // Get selected items data for export - this is now just for the UI indication
  // The actual data fetching happens in the export component
  const selectedItems = totalSelectedItems > 0 ? new Array(totalSelectedItems).fill({} as TData) : [];

  // Get all available items data for export
  const allItems = getAllItems ? getAllItems() : [];

  // Update url when local filters are empty
  // useEffect(() => {
  //   if (!isFiltered) {
  //     setFilters({
  //       globalFilter: "",
  //       startDate: "",
  //       endDate: "",
  //     });
  //   }
  // }, [isFiltered, setFilters]);


  // console.log("variant", variant);
  // console.log(
  //   "dateColumn exists and is visible:",
  //   variant?.client_ops?.date_column,
  //   table.getAllLeafColumns().some((column) => column.id === variant?.client_ops?.date_column),
  // );

  // console.log("config.enableDateFilter", config.enableDateFilter);
  // console.log("variant?.client_ops?.date_column", variant?.client_ops?.date_column);
  // console.log(
  //   "table.getAllLeafColumns().some((column) =column.id === variant.client_ops?.date_column",
  //   `$
  // {table.getAllLeafColumns().some((column) => column.id === variant.client_ops?.date_column)}`,
  // );

  function getVariantForDb(variant: VariantDisplayColumnsTypes, name: string) {
    const activeVariant = selectVariantsData?.find((v) => v.is_active);
    const activeVariantId = activeVariant?.id || 0;

    return {
      ...variant,
      deactivate_id: activeVariantId,
      name: name,
      is_active: true,
      client_ops: pathState,
      server_ops: {
        ...variant.server_ops,
        search: removeIdFromObjects(variant.server_ops?.search || []),
        order: removeIdFromObjects(variant.server_ops?.order || []),
      },
    }
  }

  function getUniqueName(name: string, allVariants: VariantDisplayColumnsTypes[]) {
    const possible = "abcdefghijklmnopqrstuvwxyz";
    let text = "";
    for (let i = 0; i < 3; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    const existingNames = allVariants.map((variant) => variant.name);
    if (existingNames.includes(name)) {
      return `${name}-${text}`
    } else {
      return name;
    }

  }

  const handleVariantUpdate = () => {
    toast.success("Variant saved");
    console.log("save new variant");
    const variantData = getVariantForDb(variant, newVariantName);
    updateVariantMutation.mutate({
      body: [variantData]
    }, {
      onSuccess: () => {
        toast.success("Variant updated");
        internalNavigate({
          to: config.path,
          search: cleanEmptyParams(variantData.client_ops),
          replace: true
        });
      }
    });
  };
  const handleVariantCreate = () => {
    // it shold run only if variant is inactive - so we dont have active variant
    // in all othrer cases we deal withe active variant

    if (newVariantName) {
      const uniqueName = getUniqueName(newVariantName, selectVariantsData || []);
      const variantData = getVariantForDb(variant, uniqueName);
      createVariantMutation.mutate({
        body: [variantData]
      }, {
        onSuccess: () => {
          toast.success("Variant created");
          internalNavigate({
            to: config.path,
            search: cleanEmptyParams(variantData.client_ops),
            replace: true
          });
        }
      });



    };
  }

  const handleApplySettings = () => {

    const cleanServerOps = {
      ...tmpServerOps,
      search: removeIdFromObjects(tmpServerOps.search || []),
      order: removeIdFromObjects(tmpServerOps.order || [])
    }
    setVariant({
      ...variant,
      server_ops: cleanServerOps
    })


  }

  return (
    <div className="flex flex-wrap items-center justify-between ">
      {totalSelectedItems === 0 ? (
        <div className="flex flex-1 flex-wrap items-center gap-2">
          {config.enableSearch && (
            <Input
              placeholder={t("components.dataTable.toolbar.filterString.label")}
              value={searchString}
              onChange={(e) => handleSearchChange(e)}
              className={`w-[150px] lg:w-[250px] ${getInputSizeClass(config.size)}`}
            />
          )}
          {/* Check if dateColumn exists and is visible (is not in table.state.columnsVisibility as false) */}

          {config.enableDateFilter &&
            variant?.client_ops?.date_column &&
            table.getAllLeafColumns().some((column) => column.id === variant.client_ops?.date_column) && (
              <div className="flex flex-row items-start gap-2">
                <StartDatePicker
                  className={`cursor-pointer ${getInputSizeClass(config.size)}`}
                  startDate={localStartDate || ""}
                  setStartDate={setLocalStartDate}
                />

                <EndDatePicker
                  className={`cursor-pointer ${getInputSizeClass(config.size)}`}
                  endDate={localEndDate}
                  setEndDate={setLocalEndDate}
                />

              </div>
            )}
          {true && (
            <Button
              type="button"
              onClick={() => {
                console.log("apply local filters", { searchString, localStartDate, localEndDate });
                setFilters({
                  globalFilter: preprocessSearch(searchString) || undefined,
                  start_date: localStartDate || undefined,
                  end_date: localEndDate || undefined,
                });
              }}
            >
              {t("components.dataTable.toolbar.applyFilters.label")}
            </Button>


          )}
          {isFiltered && (
            <Button
              variant="ghost"
              onClick={() => {
                setSearchString("");
                setLocalStartDate("");
                setLocalEndDate("");
                setFilters({
                  globalFilter: undefined,
                  start_date: undefined,
                  end_date: undefined,
                });
              }}
              className={getButtonSizeClass(config.size)}
            >
              {t("common.resetFilters.label")}
              <Cross2Icon className="ml-2 h-4 w-4" />
            </Button>
          )}

        </div>
      ) : (
        <div className="flex flex-start flex-wrap items-center gap-2">{customToolbarComponent}</div>
      )
      }


      <Modal title={t("forms.JobForm.addJob.label")} open={modalOpen} setOpen={setModalOpen}>
        <JobCreateForm setModalOpen={setModalOpen} />
      </Modal>
      <div>


      </div>
      <div className="flex items-end gap-2">
        <Button size="default" onClick={() => setModalOpen(true)}>
          {t("components.dataTable.toolbar.add.label")}
        </Button>
        <ClientColumnsOptions
          table={table}
          tableConfig={config}
          columnMapping={exportConfig.columnMapping}
          size={config.size}
          variant={variant}
        />
        {config.enableExport && (
          <DataTableExport
            table={table}
            data={allItems}
            selectedData={selectedItems}
            getSelectedItems={getSelectedItems}
            // entityName={entityName}
            // columnMapping={columnMapping}
            // columnWidths={columnWidths}
            // headers={headers}
            exportConfig={exportConfig}
            size={config.size}
          />
        )}

        {/* SHEET TABLE CONFIG */}
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className={getButtonSizeClass(config.size, true)}
              title="Table Settings"
            >
              <Settings className="h-4 w-4" />
              <span className="sr-only">Open table settings</span>
            </Button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>Settings</SheetTitle>
              <SheetDescription>Create variants and configure server data selection.</SheetDescription>
            </SheetHeader>
            <div className="m-1 flex flex-col gap-1">
              <p className="p-2 text-sm">Each variant includes client settings and server data configuration. Choose unique variant name (by default we add small suffix to avoid duplicates).</p>
              <Select

                value={tmpServerOps?.date_column || ""}
                onValueChange={(value) => setTmpServerOps({ ...tmpServerOps, date_column: value })}
              >
                <div>
                  <Label htmlFor="date_column">Date column</Label>
                  <SelectTrigger id="date_column" className="w-full">
                    <SelectValue placeholder="Select date column" />
                  </SelectTrigger>
                </div>
                <SelectContent>
                  {tableColumns.map((column) => (
                    <SelectItem key={column.id} value={column.id}>
                      {getColumnLabel(column)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="newVariantName">{t("common.newVariantName.label")}</Label>
                <InputIcon
                  value={newVariantName}
                  setValue={(value: string) => setNewVariantName(value)}
                  iconPosition="right"
                  placeholder={t("common.newVariantName.label")}
                  clearable={true}
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button

                  type="button"
                  onClick={() => {
                    handleVariantCreate()

                  }}
                >
                  {t("common.createVariant.label")}
                </Button>
                {/* for variants from database */}
                {variant.id > 0 && <Button

                  type="button"
                  onClick={() => {
                    handleVariantUpdate()

                  }}
                >
                  {t("common.saveVariant.label")}
                </Button>}

              </div>
              <TableOptions
                table={table}
                getColumns={getColumns}
                // visibleColumns={visibleColumns}
                // setVisibleColumns={setVisibleColumns}
                tableConfig={config}
                columnMapping={exportConfig.columnMapping}
                size={config.size}
                pathState={pathState}
                variant={variant}
                setVariant={setVariant}
                tmpServerOps={tmpServerOps}
                setTmpServerOps={setTmpServerOps}
              />
            </div>
            <SheetFooter>
              <Button
                type="button"
                onClick={() => {
                  // just remove id keys from order and search

                  handleApplySettings();


                }}
              >
                {t("common.getData.label")}
              </Button>
              <p className="p-2 text-sm">
                This will download data according to the settings above.
                To make the set permanent, save it as a variant.
              </p>



              <SheetClose asChild>
                <Button variant="outline">{t("common.close.label")}</Button>
              </SheetClose>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </div>

    </div>
  );
}
