import type { GetAllParams } from "@client/types.gen";
import type {
  ColumnFiltersState,
  ColumnOrderState,
  ExpandedState,
  PaginationState,
  RowSelectionState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";

export type PathStateTypes = {
  sorting: SortingState;
  pagination: PaginationState;
  columnFilters: ColumnFiltersState;
  columnOrder: ColumnOrderState;
  columnVisibility: VisibilityState;
  globalFilter: string;
  startDate?: string;
  endDate?: string;
  expanded?: ExpandedState;
};

// Types for table handlers
export type PaginationUpdater<Updater> = (prev: { pageIndex: number; pageSize: number }) => {
  pageIndex: number;
  pageSize: number;
};
export type SortingUpdater = (prev: { id: string; desc: boolean }[]) => { id: string; desc: boolean }[];
export type ColumnOrderUpdater = (prev: string[]) => string[];
export type RowSelectionUpdater = (prev: Record<string, boolean>) => Record<string, boolean>;

export type ExportConfigType = {
  entityName: string;
  locale: string;
  columnMapping: Record<string, string>;
  columnWidths?: Array<{ wch: number }>;
  headers?: string[];
};

/**
 * Table configuration options
 * This file provides centralized configuration for the data table features
 */
export interface TableConfig {
  // Enable/disable actions
  enableActions: boolean;

  // Enable/disable expander
  enableExpander: boolean;

  // Enable/disable row selection
  enableRowSelection: boolean;

  // Enable/disable keyboard navigation
  enableKeyboardNavigation: boolean;

  // Enable/disable clicking a row to select it
  enableClickRowSelect: boolean;

  // Enable/disable pagination
  enablePagination: boolean;

  // Enable/disable search
  enableSearch: boolean;

  // Enable/disable column filters
  enableColumnFilters: boolean;

  // Enable/disable date range filter
  enableDateFilter: boolean;

  // Enable/disable column visibility options
  enableColumnVisibility: boolean;

  // Enable/disable data export
  enableExport: boolean;

  // Enable/disable URL state persistence
  enableUrlState: boolean;

  // Enable/disable column resizing
  enableColumnResizing: boolean;

  // Enable/disable toolbar
  enableToolbar: boolean;

  // Control the size of buttons and inputs throughout the table
  // sm: small, default: standard, lg: large
  size: "sm" | "default" | "lg";

  // Unique ID for storing column sizing in localStorage
  // This allows multiple tables to have independent sizing states
  columnResizingTableId?: string;

  idField: string;
  dateColumn: string;
  pageSizeOptions: number[];
  path: string;
}

// const response = {
//   id: 10,
//   name: "Name (change me please)",
//   path: "/main/jobs/",
//   is_active: false,
//   client_ops: {
//     columnSizing: {},
//     columnSizingInfo: {},
//     rowSelection: {},
//     rowPinning: {
//       top: [],
//       bottom: [],
//     },
//     expanded: {},
//     grouping: [],
//     sorting: [],
//     columnFilters: [],
//     columnPinning: {},
//     columnOrder: [],
//     columnVisibility: {},
//     pagination: {
//       pageIndex: 0,
//       pageSize: 20,
//     },
//     start_date: null,
//     end_date: null,
//     date_column: null,
//     serverSearch: [],
//   },
//   server_ops: {
//     filters: null,
//     org_id: null,
//     date_column: null,
//     start_date: null,
//     end_date: null,
//     page_index: 0,
//     page_size: null,
//     search: null,
//     order: null,
//     columns: null,
//   },
// };
