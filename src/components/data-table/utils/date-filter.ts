/**
 * Date filtering utilities for data tables
 */

/**
 * Check if a date value is within the specified range
 * @param dateValue The date value to check (string, Date, or timestamp)
 * @param startDate The start date of the range (optional)
 * @param endDate The end date of the range (optional)
 * @returns True if the date is within range or if no range is specified
 */
export function isWithinDateRange(
  dateValue: string | Date | number | undefined,
  startDate?: string | null,
  endDate?: string | null,
): boolean {
  // If no date value or no range specified, return true (no filtering)
  if (!dateValue) return true;
  if (!startDate && !endDate) return true;

  // Convert the date value to a Date object
  const date =
    typeof dateValue === "string" ? new Date(dateValue) : dateValue instanceof Date ? dateValue : new Date(dateValue);

  // Parse start and end dates
  const start = startDate ? new Date(startDate) : null;
  const end = endDate ? new Date(endDate) : null;

  // Check if date is within range
  if (start && end) {
    // Set end date to end of day (23:59:59.999) to include the entire end date
    const endOfDay = new Date(end);
    endOfDay.setHours(23, 59, 59, 999);
    return date >= start && date <= endOfDay;
  } else if (start) {
    return date >= start;
  } else if (end) {
    // Set end date to end of day (23:59:59.999) to include the entire end date
    const endOfDay = new Date(end);
    endOfDay.setHours(23, 59, 59, 999);
    return date <= endOfDay;
  }

  return true;
}
