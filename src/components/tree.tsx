"use client";

import { <PERSON><PERSON><PERSON>Down, Circle } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/_shadcn/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/_shadcn/components/ui/collapsible";
import { Checkbox } from "./_shadcn/components/ui/checkbox";

type TreeItem = {
  id: string;
  label: string;
  children?: TreeItem[];
};

const data: TreeItem[] = [
  {
    id: "1",
    label: "Parent Item 1",
    children: [
      { id: "1.1", label: "Child Item 1.1" },
      {
        id: "1.2",
        label: "Child Item 1.2",
        children: [
          { id: "1.2.1", label: "Grandchild Item 1.2.1" },
          { id: "1.2.2", label: "Grandchild Item 1.2.2" },
        ],
      },
      { id: "1.3", label: "Child Item 1.3" },
    ],
  },
  {
    id: "2",
    label: "Parent Item 2",
    children: [
      { id: "2.1", label: "Child Item 2.1" },
      { id: "2.2", label: "Child Item 2.2" },
    ],
  },
  {
    id: "3",
    label: "Parent Item 3",
    children: [
      {
        id: "3.1",
        label: "Child Item 3.1",
        children: [
          { id: "3.1.1", label: "Grandchild Item 3.1.1" },
          {
            id: "3.1.2",
            label: "Grandchild Item 3.1.2",
            children: [
              { id: "*******", label: "Great-grandchild Item *******" },
              { id: "*******", label: "Great-grandchild Item *******" },
            ],
          },
        ],
      },
      { id: "3.2", label: "Child Item 3.2" },
    ],
  },
];

type TreeNodeProps = {
  item: TreeItem;
  level?: number;
  selectedItems: string[];
  expandedItems: string[];
  onOpen: (id: string) => void;
  onClose: (id: string) => void;
  handleAdd: (id: string) => void;
  handleRemove: (id: string) => void;
  variant?: "view" | "selectSingle" | "selectMultiple";
};

function TreeNode({
  item,
  level = 0,
  selectedItems,
  expandedItems,
  onOpen,
  onClose,
  handleAdd,
  handleRemove,
  variant,
}: TreeNodeProps) {
  //   const [isOpen, setIsOpen] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const checked = selectedItems.includes(item.id);
  const setChecked = (checked: boolean) => {
    if (checked) {
      console.log("checked", item.id);
      handleAdd(item.id);
    } else {
      console.log("unchecked", item.id);
      handleRemove(item.id);
    }
  };

  const isOpen = expandedItems.includes(item.id);

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={() => {
        if (expandedItems.includes(item.id)) {
          onClose(item.id);
        } else {
          onOpen(item.id);
        }
      }}
    >
      <div className="flex items-center justify-start">
        {variant !== "view" && <Checkbox checked={checked} onCheckedChange={setChecked} className="m-0 p-0" />}
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={`w-full justify-start px-2 py-1 text-left ${level === 0 ? "font-semibold" : ""}`}
          >
            {hasChildren ? (
              <ChevronDown
                className={`mr-2 h-4 w-4 shrink-0 transition-transform ${isOpen ? "rotate-0" : "-rotate-90"}`}
              />
            ) : (
              <div className="mr-2 h-2 w-2 shrink-0 fill-current" />
            )}
            {item.label}
          </Button>
        </CollapsibleTrigger>
      </div>
      {hasChildren && (
        <CollapsibleContent className="ml-4">
          {item.children!.map((child) => (
            <TreeNode
              key={child.id}
              item={child}
              level={level + 1}
              selectedItems={selectedItems}
              expandedItems={expandedItems}
              onOpen={onOpen}
              onClose={onClose}
              handleAdd={handleAdd}
              handleRemove={handleRemove}
              variant={variant}
            />
          ))}
        </CollapsibleContent>
      )}
    </Collapsible>
  );
}
type TreeProps = {
  treeData?: TreeItem[];
  variant?: "view" | "selectSingle" | "selectMultiple";
  defaultSelectedItems?: string[];
  defaultExpandedItems?: string[];
};

export default function Tree({
  treeData = data,
  variant = "view",
  defaultSelectedItems = [],
  defaultExpandedItems = [],
}: TreeProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>(defaultSelectedItems);
  const [expandedItems, setExpandedItems] = useState<string[]>(defaultExpandedItems);

  const handleAdd = (id: string) => {
    if (variant === "selectSingle") {
      setSelectedItems([id]);
    } else {
      setSelectedItems((prev) => [...prev, id]);
    }
  };

  const handleRemove = (id: string) => {
    if (variant === "selectSingle") {
      setSelectedItems([]);
    } else {
      setSelectedItems((prev) => prev.filter((item) => item !== id));
    }
  };

  const handleOnOpen = (id: string) => {
    setExpandedItems((prev) => [...prev, id]);
  };

  const handleOnClose = (id: string) => {
    setExpandedItems((prev) => prev.filter((item) => item !== id));
  };

  return (
    <div className="w-full max-w-md">
      {treeData?.map((item) => (
        <TreeNode
          key={item.id}
          item={item}
          selectedItems={selectedItems}
          expandedItems={expandedItems}
          onOpen={handleOnOpen}
          onClose={handleOnClose}
          handleAdd={handleAdd}
          handleRemove={handleRemove}
          variant={variant}
        />
      ))}
    </div>
  );
}
