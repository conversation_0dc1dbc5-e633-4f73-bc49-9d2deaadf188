const { useState, useEffect } = React;

// Initialize Dexie database
const db = new Dexie("FileUploadDB");
db.version(1).stores({
  pendingUploads: "++id,file,name",
});

function App() {
  const [files, setFiles] = useState([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [uploadStatus, setUploadStatus] = useState("");

  // Update online/offline status
  useEffect(() => {
    const updateOnlineStatus = () => setIsOnline(navigator.onLine);
    window.addEventListener("online", updateOnlineStatus);
    window.addEventListener("offline", updateOnlineStatus);
    return () => {
      window.removeEventListener("online", updateOnlineStatus);
      window.removeEventListener("offline", updateOnlineStatus);
    };
  }, []);

  // Handle file selection
  const handleFileChange = async (e) => {
    const selectedFiles = Array.from(e.target.files);
    setFiles(selectedFiles);
    if (isOnline) {
      await uploadFiles(selectedFiles);
    } else {
      await storeFilesLocally(selectedFiles);
      setUploadStatus("Files stored locally. Will upload when online.");
    }
  };

  // Upload files to server
  const uploadFiles = async (files) => {
    const formData = new FormData();
    files.forEach((file) => formData.append("files", file));
    try {
      const response = await fetch("https://your-server-endpoint/upload", {
        method: "POST",
        body: formData,
      });
      if (response.ok) {
        setUploadStatus("Files uploaded successfully!");
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      setUploadStatus("Upload failed. Storing locally.");
      await storeFilesLocally(files);
    }
  };

  // Store files in IndexedDB
  const storeFilesLocally = async (files) => {
    await db.pendingUploads.bulkPut(
      files.map((file) => ({
        file: file,
        name: file.name,
      })),
    );
    // Register sync event
    if ("serviceWorker" in navigator && "SyncManager" in window) {
      navigator.serviceWorker.ready.then((registration) => {
        registration.sync.register("upload-files");
      });
    }
  };

  // Check for pending uploads when online
  useEffect(() => {
    if (isOnline) {
      db.pendingUploads.toArray().then((pending) => {
        if (pending.length > 0) {
          setUploadStatus("Uploading pending files...");
          uploadFiles(pending.map((p) => p.file)).then(() => {
            db.pendingUploads.clear();
          });
        }
      });
    }
  }, [isOnline]);

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <h1 className="text-2xl font-bold mb-4">PWA File Upload</h1>
      <input type="file" multiple onChange={handleFileChange} className="mb-4 p-2 border rounded" />
      <p className="text-sm text-gray-600">Status: {isOnline ? "Online" : "Offline"}</p>
      <p className="text-sm text-gray-600">{uploadStatus}</p>
    </div>
  );
}

// ReactDOM.render(<App />, document.getElementById('root'));

// Register service worker
if ("serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker.register("/service-worker.js");
  });
}
