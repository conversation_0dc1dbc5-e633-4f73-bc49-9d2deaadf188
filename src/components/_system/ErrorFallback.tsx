// src/components/ui/ErrorFallback.tsx

import { useNavigate } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/_shadcn/components/ui/button";

interface ErrorFallbackProps {
  error?: Error;
  resetErrorBoundary?: () => void;
}

export function ErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  const navigate = useNavigate();
  console.log("error:", error);
  const errorMessage = error?.detail || "An unexpected error occurred";
  const errorStack = error?.stack || "";
  const statusCode = error instanceof Response ? error.status : 500;

  const handleReset = () => {
    if (resetErrorBoundary) {
      resetErrorBoundary();
    } else {
      // Fallback to navigating to home page
      navigate({ to: "/" });
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
        <div className="flex flex-col items-center gap-4">
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <AlertCircle size={80} color="red" />
          </motion.div>

          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3, duration: 0.6 }}>
            <h2>{statusCode === 500 ? "Server Error" : "Error Occurred"}</h2>
          </motion.div>

          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.6, duration: 0.6 }}>
            <p>{errorMessage}</p>
          </motion.div>

          {errorStack && (
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.6, duration: 0.6 }}>
              <p>{errorStack}</p>
            </motion.div>
          )}

          <div className="flex justify-center mt-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 260,
                damping: 20,
                delay: 0.9,
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="flex gap-4">
                <Button onClick={handleReset} size="lg">
                  Home page
                </Button>
                <Button onClick={() => window.location.reload()} size="lg">
                  Reload page
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
