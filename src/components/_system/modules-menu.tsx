import { ChevronRight } from "lucide-react";
import type React from "react";
import { useState } from "react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/_shadcn/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/_shadcn/components/ui/sidebar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/_shadcn/components/ui/tabs";
import { cn } from "@/components/_shadcn/lib/utils";
import { modules } from "@/components/_system/modules";
import Tree from "@/components/tree";

interface VerticalBorderedTabsProps {
  children?: React.ReactNode;
}

import { Link, useMatches } from "@tanstack/react-router";

interface NavItemProps {
  item: {
    label: string;
    value: string;
  };
  module: string;
}

function NavItem({ item, module }: NavItemProps) {
  // Get all active route matches
  const matches = useMatches();

  // Check if any of the active routes match this item's route
  const isActive = matches.some((match) => {
    // Get the pathname from the match
    const pathname = match.pathname;
    // Check if the pathname includes this item's value
    return pathname.includes(item.value);
  });

  return (
    // <SidebarMenuSubItem>
    <SidebarMenuSubButton asChild isActive={isActive}>
      <Link to={`${module}/${item.value}`}>
        <span>{item.label}</span>
      </Link>
    </SidebarMenuSubButton>
    // </SidebarMenuSubItem>
  );
}

const ModulesMenu = ({ children }: VerticalBorderedTabsProps) => {
  const [activeTab, setActiveTab] = useState(modules[0].value);

  // Find the currently active module
  const activeModule = modules.find((module) => module.value === activeTab);

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} orientation="vertical" className="w-full flex flex-row gap-0">
      <TabsList className="grid grid-cols-1 gap-0 w-fit p-0 divide-y border shrink-0">
        {modules.map((module) => (
          <TabsTrigger
            key={module.value}
            value={module.value}
            className={cn(
              "rounded-none first:rounded-t-md last:rounded-b-md bg-background h-10 w-11 p-0",
              "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
            )}
            title={module.label}
          >
            {module.icon && <module.icon className="h-4 w-4" />}
          </TabsTrigger>
        ))}
      </TabsList>

      <SidebarGroup>
        <div className="flex items-center gap-2 mb-4">
          {activeModule?.icon && <activeModule.icon className="h-5 w-5" />}
          <h2 className="text-xl font-semibold capitalize">{activeModule?.label || activeTab}</h2>
        </div>

        <SidebarMenu>
          {activeModule?.children &&
            activeModule.children.length > 0 &&
            activeModule.children.map((item) => (
              <SidebarMenuItem key={item.label}>
                <NavItem item={item} module={activeModule.value} />
              </SidebarMenuItem>
            ))}

          {/* Display the content passed as children */}
          <div className="flex-1">
            {children} <Tree />
          </div>
        </SidebarMenu>
      </SidebarGroup>
    </Tabs>
  );
};

export default ModulesMenu;
