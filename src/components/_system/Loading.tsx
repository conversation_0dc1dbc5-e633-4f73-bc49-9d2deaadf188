import type { SVGProps } from "react";
import { cn } from "@/components/_shadcn/lib/utils";

export interface ISVGProps extends SVGProps<SVGSVGElement> {
  size?: number;
  className?: string;
  message?: string;
  color?: string;
  fullScreen?: boolean;
}

const Loading = ({
  size = 72,
  message = "Loading...",
  className,
  color = "green",
  fullScreen = false,
  ...props
}: ISVGProps) => {
  const containerClasses = cn(
    "flex flex-col items-center justify-center gap-3",
    {
      "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm": fullScreen,
      "w-full h-full": !fullScreen,
    },
    className,
  );

  return (
    <div className={containerClasses}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        {...props}
        viewBox="0 0 24 24"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="animate-spin"
      >
        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
      </svg>
      {message && <p className="text-sm font-medium text-muted-foreground">{message}</p>}
    </div>
  );
};

export default Loading;
