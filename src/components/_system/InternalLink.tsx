import { Link, type LinkProps, useRouter } from '@tanstack/react-router';
import { forwardRef, useCallback } from 'react';

interface InternalLinkProps extends Omit<LinkProps, 'search'> {
    isInternal?: boolean;
    search?: LinkProps['search'] | Record<string, any>; // Allow standard search params
}

const InternalLink = forwardRef<HTMLAnchorElement, InternalLinkProps>(
    ({ isInternal = false, search, ...props }, ref) => {
        const router = useRouter();

        // Handle click to set context and merge search params
        const handleClick = useCallback(
            (e: React.MouseEvent<HTMLAnchorElement>) => {
                if (isInternal) {
                    // Prevent default Link behavior and use internalNavigate
                    e.preventDefault();
                    router.navigate({
                        to: props.to,
                        search: (prev) => ({
                            ...prev,
                            ...(typeof search === 'function' ? search(prev) : search),
                        }),
                        context: { isInternal: true },
                    });
                }
                // If not internal, let the default Link behavior handle navigation
            },
            [isInternal, search, props.to, router],
        );

        return (
            <Link
                {...props}
                search={search} // Pass search params for standard navigation
                onClick={handleClick}
                ref={ref}
            />
        );
    },
);

export default InternalLink;