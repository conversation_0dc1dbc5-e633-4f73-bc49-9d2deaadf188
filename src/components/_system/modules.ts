import type { LucideIcon } from "lucide-react";
import { Blocks, Coins, Files, FileText, LayoutDashboard, Setting<PERSON>, User, UserCog, Users } from "lucide-react";

// const moduleLabels = [
//   "dashboard",
//   "forum",
//   "applications",
//   "objects",
//   "schedule",
//   "documents",
//   "jobs",
//   "contractors",
//   "config",
//   "users",
//   "cashflow",
// ];

type Module = {
  value: string;
  default_location?: string;
  label: string;
  icon?: LucideIcon;
  enabled: boolean;
  roles: string[];
  children?: Module[];
};

export const modules: Module[] = [
  {
    value: "main",
    default_location: "/main",
    label: "main",
    icon: LayoutDashboard,
    enabled: true,
    roles: ["MEMBER", "CONTRACTOR", "ADMIN"],
    children: [
      {
        value: "dashboard",
        label: "dashboard",
        enabled: true,
        roles: ["MEMBER", "CONTRACTOR", "ADMIN"],
      },
      {
        value: "documents",
        label: "documents",
        enabled: true,
        roles: ["MEMBER", "CONTRACTOR", "ADMIN"],
      },
      {
        value: "problems",
        label: "problems",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "jobs",
        label: "jobs",
        enabled: true,
        roles: ["MEMBER", "CONTRACTOR", "ADMIN"],
      },
      {
        value: "calendar-jobs-objects",
        label: "calendar-jobs-objects",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "offers",
        label: "offers",
        enabled: true,
        roles: ["MEMBER", "CONTRACTOR", "ADMIN"],
      },
    ],
  },
  {
    value: "member",
    default_location: "/member/emails",
    label: "member",
    icon: User,
    enabled: true,
    roles: ["MEMBER", "ADMIN"],
    children: [
      { value: "emails", label: "emails", enabled: true, roles: ["MEMBER", "ADMIN"] },
      {
        value: "proposals",
        label: "proposals",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "comments",
        label: "comments",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "finances",
        label: "finances",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "profile",
        label: "profile",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "email-settings",
        label: "email-settings",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "profiles",
        label: "profiles",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
    ],
  },
  {
    value: "proposals",
    default_location: "/proposals",
    label: "proposals",
    icon: FileText,
    enabled: true,
    roles: ["MEMBER", "ADMIN"],
  },
  {
    value: "documents",
    default_location: "/documents/list",
    label: "documents",
    icon: Files,
    enabled: true,
    roles: ["MEMBER", "ADMIN"],
    children: [
      {
        value: "resolutions",
        label: "resolutions",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "contracts",
        label: "contracts",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "invoices",
        label: "invoices",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "formal",
        label: "formal",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "templates",
        label: "templates",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
    ],
  },

  // {
  //   value: "schedule",
  //   default_location: "/schedule/jobs",
  //   label: "schedule",
  //   icon: <IconCalendarStats size={32} />,
  //   enabled: true,
  //   children: [
  //     { value: "jobs", label: "jobs" },
  //     { value: "tasks", label: "tasks" },
  //     { value: "payments", label: "payments" },
  //     { value: "liabilities", label: "liabilities" },
  //   ],
  // },
  // {
  //   value: "documents",
  //   label: "documents",
  //   icon: <IconFiles size={32} />,
  //   enabled: true,
  //   children: [
  //     { value: "resolutions", label: "resolutions" },
  //     { value: "contracts", label: "contracts" },
  //     { value: "invoices", label: "invoices" },
  //     { value: "formal", label: "formal" },
  //   ],
  // },
  // {
  //   value: "jobs",
  //   label: "jobs",
  //   icon: <IconHammer size={32} />,
  //   enabled: true,
  //   children: [
  //     { value: "active-offers", label: "active-offers" },
  //     { value: "current", label: "current" },
  //     { value: "upcoming", label: "upcoming" },
  //     { value: "finished", label: "finished" },
  //     { value: "archived", label: "archived" },
  //   ],
  // },
  {
    value: "crm",
    default_location: "/crm/types",
    label: "crm",
    icon: Users,
    enabled: true,
    roles: ["MEMBER", "ADMIN"],
    children: [
      { value: "members", label: "members", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "vendors", label: "vendors", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "contractors", label: "contractors", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "media", label: "media", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "clients-goods", label: "clients-goods", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "clients-services", label: "clients-services", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "employees", label: "employees", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "directors", label: "directors", enabled: true, roles: ["MEMBER", "ADMIN"] },
      { value: "admins", label: "admins", enabled: true, roles: ["MEMBER", "ADMIN"] },
      {
        value: "contacts",
        label: "contacts",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "addresses",
        label: "addresses",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
    ],
  },
  {
    value: "money",
    default_location: "/money/ballance",
    label: "money",
    icon: Coins,
    enabled: true,
    roles: ["MEMBER", "ADMIN"],
    children: [
      {
        value: "ballance",
        label: "ballance",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "transactions",
        label: "transactions",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "templates",
        label: "templates",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "templates-create",
        label: "templates_create",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "schedule",
        label: "schedule",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "schedule-create",
        label: "schedule_create",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "import",
        label: "import",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },

      {
        value: "trips",
        label: "trips",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },

      {
        value: "payroll",
        label: "payroll",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "reports",
        label: "reports",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "ballance",
        label: "ballance_sheet",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "profit",
        label: "profit",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "jobs",
        label: "zlecenia",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      {
        value: "contrahents",
        label: "kontrahenci",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
      // {
      //   value: "settings",
      //   label: "settings",
      //   enabled: true,
      //   roles: ["MEMBER", "ADMIN"],
      //   children: [
      //     {
      //       value: "accounts",
      //       label: "accounts",
      //       enabled: true,
      //       roles: ["MEMBER", "ADMIN"],
      //     },
      //     {
      //       value: "accounting-periods",
      //       label: "accounting_periods",
      //       enabled: true,
      //       roles: ["MEMBER", "ADMIN"],
      //     },
      //     {
      //       value: "milage-tresholds",
      //       label: "milage_tresholds",
      //       enabled: true,
      //       roles: ["MEMBER", "ADMIN"],
      //     },
      //   ],
      // },
    ],
  },
  {
    value: "objects",
    default_location: "/objects/list",
    label: "objects",
    icon: Blocks,
    enabled: true,
    roles: ["MEMBER", "ADMIN"],
    children: [
      { value: "new", label: "new", enabled: true, roles: ["MEMBER", "ADMIN"] },
      {
        value: "list",
        label: "list",
        enabled: true,
        roles: ["MEMBER", "ADMIN"],
      },
    ],
  },

  {
    value: "org-config",
    default_location: "/org-config/storage",
    label: "org-config",
    icon: Settings,
    enabled: true,
    roles: ["ADMIN"],
    children: [
      // { value: "members", label: "members", enabled: true, roles: ["ADMIN"] },
      { value: "storage", label: "storage", enabled: true, roles: ["ADMIN"] },
      {
        value: "notices",
        label: "notices",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "contrahents",
        label: "contrahents",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "job-templates",
        label: "job-templates",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "accounts",
        label: "accounts",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "accounts-sets",
        label: "accounts-sets",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "accounting-periods",
        label: "accounting-periods",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "milage",
        label: "milage",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "vehicles",
        label: "vehicles",
        enabled: true,
        roles: ["ADMIN"],
      },
      {
        value: "milage-tresholds",
        label: "milage-tresholds",
        enabled: true,
        roles: ["ADMIN"],
      },
    ],
  },
  {
    value: "superadmin",
    default_location: "/superadmin/users",
    label: "superadmin",
    icon: UserCog,
    enabled: true,
    roles: ["SUPERADMIN"],
    children: [
      {
        value: "users",
        label: "users",
        enabled: true,
        roles: ["SUPERADMIN"],
      },
      {
        value: "orgs",
        label: "orgs",
        enabled: true,
        roles: ["SUPERADMIN"],
      },
      // {
      //   value: "accounts-sets",
      //   label: "accounts-sets",
      //   enabled: true,
      //   roles: ["SUPERADMIN"],
      // },
      {
        value: "object-types",
        label: "object-types",
        enabled: true,
        roles: ["SUPERADMIN"],
      },
      {
        value: "contrahents-types",
        label: "contrahents-types",
        enabled: true,
        roles: ["SUPERADMIN"],
      },
      {
        value: "email-types",
        label: "email-types",
        enabled: true,
        roles: ["SUPERADMIN"],
      },
      {
        value: "example-table",
        label: "example-table",
        enabled: true,
        roles: ["SUPERADMIN"],
      },
    ],
  },
];
