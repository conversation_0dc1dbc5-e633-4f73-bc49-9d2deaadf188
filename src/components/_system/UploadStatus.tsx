import { useEffect, useState } from "react";
import { Workbox } from "workbox-window";

export default function UploadWidget() {
  const [pending, setPending] = useState(0);

  useEffect(() => {
    if ("serviceWorker" in navigator) {
      const wb = new Workbox("/service-worker.js");
      wb.addEventListener("message", (event) => {
        if (event.data?.type === "QUEUE_LENGTH") {
          setPending(event.data.length);
        }
      });
      wb.register().then(() => {
        // Ask immediately after registration
        wb.messageSW({ type: "GET_QUEUE_LENGTH" });
      });
    }
  }, []);

  return (
    <div>
      {pending > 0 && (
        <p className="text-sm text-yellow-600">
          {pending} file{pending > 1 ? "s are" : " is"} waiting for upload…
        </p>
      )}
      {/* …your existing upload UI… */}
    </div>
  );
}
