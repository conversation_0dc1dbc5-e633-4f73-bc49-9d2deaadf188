// src/components/UploadManager.tsx

import { Check<PERSON>ircle2, Clock4, Loader2, X, XCircle } from "lucide-react";
import type { JSX } from "react";
import { Badge } from "@/components/_shadcn/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/_shadcn/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/_shadcn/components/ui/card";
import { Progress } from "@/components/_shadcn/components/ui/progress";
import { ScrollArea } from "@/components/_shadcn/components/ui/scroll-area";
import { type Status, useUpload } from "@/utils/contexts/UploadCtx";

const statusIcon: Record<Status, JSX.Element> = {
  uploaded: <CheckCircle2 className="h-4 w-4 text-emerald-600" />,
  pending: <Loader2 className="h-4 w-4 animate-spin text-blue-600" />,
  queued: <Clock4 className="h-4 w-4 text-yellow-600" />,
  error: <XCircle className="h-4 w-4 text-red-600" />,
};

const statusLabel: Record<Status, string> = {
  uploaded: "Uploaded",
  pending: "Uploading…",
  queued: "Queued",
  error: "Error",
};

function fmt(bytes: number) {
  const u = ["B", "KB", "MB", "GB", "TB"];
  let i = 0,
    v = bytes;
  while (v >= 1024 && i < u.length - 1) {
    v /= 1024;
    i++;
  }
  return `${v.toFixed(1)} ${u[i]}`;
}

export default function UploadManager() {
  const { state, removeItem } = useUpload();
  const { items, queue, quality } = state;

  return (
    <Card className="w-full max-w-xl">
      <CardHeader className="flex flex-row items-center justify-between gap-4">
        <h2 className="text-lg font-semibold">Uploads</h2>

        <div className="flex items-center gap-2">
          {quality !== "good" && <Badge variant="destructive">{quality === "offline" ? "Offline" : "Slow"}</Badge>}
          {queue > 0 && <Badge variant="secondary">{queue} waiting</Badge>}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {items.length === 0 ? (
          <p className="p-4 text-sm text-muted-foreground">No uploads yet.</p>
        ) : (
          <ScrollArea className="h-64">
            <ul className="divide-y divide-border">
              {items.map((i) => (
                <li key={i.id} className="flex items-center justify-between px-4 py-3">
                  <div className="flex items-center gap-3 truncate">
                    {statusIcon[i.status]}
                    <div className="min-w-0">
                      <p className="truncate text-sm">{i.name}</p>
                      <p className="truncate text-xs text-muted-foreground">{fmt(i.size)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 ml-4">
                    <div className="flex items-center gap-2">
                      {statusIcon[i.status]}
                      <span className="text-xs">{statusLabel[i.status]}</span>
                    </div>

                    {i.status === "pending" && (
                      <Progress
                        value={i.progress}
                        className="h-1 w-24 bg-muted/50"
                        aria-label={`${i.progress}% uploaded`}
                      />
                    )}

                    <Button variant="ghost" onClick={() => removeItem(i.id)} disabled={i.status === "pending"}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
