import { UploadIcon } from "lucide-react";
import type { ChangeEvent } from "react";
import { Button } from "@/components/_shadcn/components/ui/button";
import { useUpload } from "@/utils/contexts/UploadCtx";

interface Props {
  accept?: string;
  multiple?: boolean;
}

export default function NetworkUploader({ accept, multiple = true }: Props) {
  const { addFiles } = useUpload();

  function handleSelect(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files) {
      addFiles(Array.from(e.target.files));
      e.target.value = ""; // lets you re-pick the same file later
    }
  }

  return (
    <div className="flex items-center gap-2">
      {/* hidden native input */}
      <input
        id="file-picker"
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleSelect}
        className="hidden"
      />

      {/* the label *is* the button */}
      <Button asChild variant="secondary">
        <label htmlFor="file-picker" className="inline-flex cursor-pointer items-center gap-2">
          <UploadIcon className="h-4 w-4" />
          Select file{multiple ? "s" : ""}
        </label>
      </Button>
    </div>
  );
}
