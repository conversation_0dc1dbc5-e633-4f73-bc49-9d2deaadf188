---
import "@/styles/styles.css";
import "@/styles/tailwind/global.css";

export interface Props {
  title: string;
}

const { title } = Astro.props;
---

<html lang="en">
	<head>
		<script is:inline>
			// Initialize theme from localStorage or system preference
			const root = document.documentElement;
			const storedTheme = localStorage.getItem("theme");
			const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
			const initialTheme = storedTheme || (prefersDark ? "dark" : "light");
			const theme = initialTheme;
			
			// Apply theme class immediately to prevent flash of unstyled content
			if (initialTheme === 'dark') {
				root.classList.add('dark');
			} else {
				root.classList.remove('dark');
			}
			
			// Initialize Redux store with theme if available
			if (window.dispatch) {
				window.dispatch({
					type: 'system/updateSystemField',
					payload: { field: 'theme', value: initialTheme }
				});
			}
			localStorage.setItem("theme", theme);   // persist
		  </script>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<link rel="manifest" href="/manifest.json" />
	<title>{title}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />
    <!--     <link rel="stylesheet" href="style.css"> -->
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
	</head>
	<body>
		<slot />
		<script>
			function applyDark() {
			  const root = document.documentElement;
			  const isDark = localStorage.getItem("theme") === "dark";
			  root.classList.toggle("dark", isDark);
			}
	  
			// initial hydration
			document.addEventListener("astro:page-load", applyDark);
			// after every View-Transition
			document.addEventListener("astro:after-swap", applyDark);
		  </script>
	</body>
</html>

<script is:inline>
	if ('serviceWorker' in navigator) {
	  window.addEventListener('load', () => {
		navigator.serviceWorker
		  .register('/sw-v1.js')
		  .then(() => console.log('SW registered'))
		  .catch(console.error);
	  });
	}
	</script>
