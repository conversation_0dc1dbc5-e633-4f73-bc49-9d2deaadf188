import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import { AppSidebar } from "@/components/_shadcn/components/app-sidebar";
import { SidebarProvider, SidebarTrigger } from "@/components/_shadcn/components/ui/sidebar";
import { Toaster } from "@/components/_shadcn/components/ui/sonner";

const ENV = import.meta.env.PUBLIC_ENV;
console.log("ENV", ENV);

export default function AppLayout({
  children,
  userProfile,
  onLogout,
}: {
  children: React.ReactNode;
  userProfile: any | undefined;
  onLogout: () => Promise<void>;
}) {
  return (
    <>
      <SidebarProvider>
        <AppSidebar userProfile={userProfile} onLogout={onLogout} />
        <main className="w-full">
          <SidebarTrigger />
          {children}
        </main>
      </SidebarProvider>
      <Toaster />

      {ENV === "dev" && <TanStackRouterDevtools />}
      {ENV === "dev" && <ReactQueryDevtools />}
    </>
  );
}
