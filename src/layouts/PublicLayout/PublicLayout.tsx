import { Link, useNavigate } from "@tanstack/react-router";
import React, { useEffect, useState } from "react";
// import { getToken, isLoggedIn, login, logout } from "@/utils/auth";
import { LanguageContext } from "@/utils/i18n/LanguageContext.tsx";
import { translations } from "@/utils/i18n/landing_translations.ts";
import styles from "./PublicLayout.module.css";

// // Example usage
// if (!isLoggedIn()) {
//   await login()
// }

function Public({ children }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [language, setLanguage] = useState("en");
  const navigate = useNavigate();

  // Function to get translation based on key and current language
  const t = (key: string): string => {
    return translations[language]?.[key] ?? translations["en"]?.[key] ?? key; // Fallback chain: current -> english -> key
  };

  // Effect to load language from local storage on mount
  useEffect(() => {
    const savedLang = localStorage.getItem("language");
    if (savedLang && translations[savedLang]) {
      setLanguage(savedLang);
    }
    // Set document title
    document.title = t("pageTitle");
    // Set html lang attribute
    document.documentElement.lang = language;
  }, [language, t]); // Add t to dependency array as it depends on language state

  // Effect to update document title and lang attribute when language changes
  useEffect(() => {
    document.title = t("pageTitle");
    document.documentElement.lang = language;
  }, [language, t]);

  const handleLanguageChange = (lang: string) => {
    if (translations[lang]) {
      setLanguage(lang);
      localStorage.setItem("language", lang); // Persist selection
    }
  };

  const handleNavLinkClick = () => {
    if (isMenuOpen) {
      setIsMenuOpen(false);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      <div className={styles.landingPageWrapper}>
        {" "}
        {/* Added wrapper for base styles */}
        {/* Header Section */}
        <header className={styles.header}>
          <nav className={`${styles.container} ${styles.nav}`}>
            <div className={styles.logo} onClick={() => navigate({ to: "/" })}>
              Dosiero<strong>360</strong>
            </div>

            {/* Language Switcher */}
            <div className={styles.languageSwitcher}>
              <button
                data-lang="en"
                className={language === "en" ? styles.active : ""}
                onClick={() => handleLanguageChange("en")}
              >
                EN
              </button>
              <button
                data-lang="pl"
                className={language === "pl" ? styles.active : ""}
                onClick={() => handleLanguageChange("pl")}
              >
                PL
              </button>
            </div>
            {/* End Language Switcher */}

            <ul className={`${styles.navLinks} ${isMenuOpen ? styles.active : ""}`}>
              <li>
                <Link to="/#features"> {t("navFeatures")}</Link>
              </li>
              <li>
                <Link to="/#how-it-works"> {t("navHowItWorks")}</Link>
              </li>
              <li>
                <Link to="/#testimonials"> {t("navTestimonials")}</Link>
              </li>
              <li>
                <Link to="/public/jobs"> {t("navJobs")}</Link>
              </li>
              <li>
                <button
                  // href="#"
                  className={`${styles.btn} ${styles.btnSecondary}`}
                  // onClick={handleNavLinkClick}
                  onClick={() => {
                    login({
                      redirectUri: window.location.origin + "/dashboard",
                    });
                  }}
                >
                  {t("navLogin")}
                </button>
              </li>
            </ul>
            <button
              className={styles.menuToggle}
              aria-label={t("menuToggleLabel")}
              aria-expanded={isMenuOpen}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? t("menuToggleIconClose") : t("menuToggleIconOpen")}
            </button>
          </nav>
        </header>
        {/* --- main section --- */}
        <div className={`${styles.container} ${styles.main}`}>{children}</div>
        {/* Footer Section */}
        <footer className={`${styles.footer} ${styles.bgDark}`}>
          <div className={`${styles.container} ${styles.footerContent}`}>
            <div className={styles.footerLogo}>
              <a href="#" className={styles.logo}>
                Dosiero<strong>360</strong>
              </a>
              <p>{t("footerRights")}</p>
            </div>
          </div>
        </footer>
      </div>
    </LanguageContext.Provider>
  );
}

export default Public;
