/* --- Font Import (Consider moving to index.html or global scope if possible for performance) --- */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap");

/* --- Local "Variables" (Replaced CSS vars for isolation) --- */
/* Cannot use :root in modules effectively without global scope. Values embedded below. */
/* Example: --primary-color: #0077cc; */

/* --- Base Styles Applied to Wrapper --- */
/* Applying minimal "body-like" styles to the component's wrapper */
.landingPageWrapper {
  font-family: "Poppins", sans-serif;
  line-height: 1.7;
  color: #333333; /* --dark-color */
  background-color: #ffffff; /* --white-color */
  font-size: 16px; /* Base font size */
}

/* Minimal reset scoped within the component wrapper - Use with caution */
.landingPageWrapper *,
.landingPageWrapper *::before,
.landingPageWrapper *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Smooth scroll might be better handled globally or via JS in React */
/* html { scroll-behavior: smooth; } */

/* --- Container --- */
.container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 0 20px;
}

/* --- Typography --- */
/* Apply heading styles directly or via wrapper */
.landingPageWrapper h1,
.landingPageWrapper h2,
.landingPageWrapper h3,
.landingPageWrapper h4 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.75rem;
}

.landingPageWrapper h1 {
  font-size: 2.8rem;
}
.landingPageWrapper h2 {
  font-size: 2.2rem;
}
.landingPageWrapper h3 {
  font-size: 1.4rem;
}

.landingPageWrapper p {
  margin-bottom: 1rem;
  color: #777777; /* --gray-color */
}

.landingPageWrapper a {
  text-decoration: none;
  color: #0077cc; /* --primary-color */
  transition: color 0.3s ease;
}

.landingPageWrapper a:hover {
  color: #00b3b3; /* --secondary-color */
}

.landingPageWrapper ul {
  list-style: none;
}

.landingPageWrapper img,
.landingPageWrapper svg {
  max-width: 100%;
  height: auto;
  display: block;
}

/* --- Layout & Sections --- */
.sectionPadding {
  padding: 80px 0;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 1rem;
  color: #333333; /* --dark-color */
}

.sectionSubtitle {
  text-align: center;
  max-width: 600px;
  margin: 0 auto 4rem auto;
  font-size: 1.1rem;
  color: #777777; /* --gray-color */
}

.bgLight {
  background-color: #f4f4f4; /* --light-color */
}
.bgDark {
  background-color: #333333; /* --dark-color */
  color: #f4f4f4; /* --light-color */
}
.bgDark h2,
.bgDark h3,
.bgDark h4 {
  color: #ffffff; /* --white-color */
}
.bgDark p {
  color: rgba(255, 255, 255, 0.8);
}
.bgDark a {
  color: #00b3b3; /* --secondary-color */
}
.bgDark a:hover {
  color: #ffffff; /* --white-color */
}

/* --- Buttons --- */
.btn {
  display: inline-block;
  padding: 12px 28px;
  border-radius: 8px; /* --border-radius */
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btnPrimary {
  background-color: #ff8c00; /* --accent-color */
  color: #ffffff; /* --white-color */
  border-color: #ff8c00; /* --accent-color */
}
.btnPrimary:hover {
  background-color: #e67e00; /* Darker orange */
  border-color: #e67e00;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.btnSecondary {
  background-color: #00b3b3; /* --secondary-color */
  color: #ffffff; /* --white-color */
  border-color: #00b3b3; /* --secondary-color */
}
.btnSecondary:hover {
  background-color: #009999; /* Darker teal */
  border-color: #009999;
}

.btnOutline {
  background-color: transparent;
  color: #0077cc; /* --primary-color */
  border-color: #0077cc; /* --primary-color */
}
.btnOutline:hover {
  background-color: #0077cc; /* --primary-color */
  color: #ffffff; /* --white-color */
}

.btnLarge {
  padding: 15px 35px;
  font-size: 1.1rem;
}

/* --- Header & Navigation --- */
.header {
  background-color: #ffffff; /* --white-color */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 10px 0;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.logo {
  font-size: 1.8rem;
  font-weight: 300;
  color: #333333; /* --dark-color */
}
.logo strong {
  font-weight: 700;
  color: #0077cc; /* --primary-color */
}
.logo:hover {
  color: #333333; /* Prevent color change on hover */
}

.navLinks {
  display: flex;
  align-items: center;
}

.navLinks li {
  margin-left: 25px;
}

.navLinks a {
  color: #333333; /* --dark-color */
  font-weight: 400;
  padding-bottom: 5px; /* Space for potential border */
  position: relative;
}

.navLinks a:hover,
.navLinks a.active {
  /* Note: 'active' class may need specific handling in React Router */
  color: #0077cc; /* --primary-color */
}

.navLinks a::after {
  /* Underline effect on hover/active */
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #0077cc; /* --primary-color */
  transition: width 0.3s ease;
}

.navLinks a:hover::after,
.navLinks a.active::after {
  width: 100%;
}

.navLinks .btn {
  /* Specificity for button within nav links */
  margin-left: 25px;
  padding: 8px 20px;
  font-size: 0.9rem;
}

.menuToggle {
  display: none; /* Hidden on desktop */
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #333333; /* --dark-color */
  line-height: 1; /* Prevent extra space */
  padding: 5px; /* Easier to tap */
}

/* --- Language Switcher --- */
.languageSwitcher {
  margin-left: auto; /* Push to the right before nav links */
  margin-right: 25px;
  display: flex;
  gap: 5px;
}
.languageSwitcher button {
  background: none;
  border: 1px solid #cccccc;
  padding: 3px 8px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  border-radius: 4px;
  transition:
    background-color 0.2s ease,
    color 0.2s ease;
  color: #777777; /* --gray-color */
}
.languageSwitcher button.active {
  background-color: #0077cc; /* --primary-color */
  color: #ffffff; /* --white-color */
  border-color: #0077cc; /* --primary-color */
}
.languageSwitcher button:hover:not(.active) {
  background-color: #f4f4f4; /* --light-color */
  color: #333333; /* --dark-color */
}

/* --- Hero Section --- */
.hero {
  /* Subtle background pattern */
  background: linear-gradient(
      rgba(255, 255, 255, 0.9),
      rgba(255, 255, 255, 0.9)
    ),
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2f7' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  padding: 100px 0 60px 0;
  min-height: 40vh;
  display: flex;
  align-items: center;
}

.heroContent {
  display: flex;
  align-items: center;
  gap: 40px;
}

.heroText {
  flex: 1;
}

.heroText h1 {
  color: #333333; /* --dark-color */
  margin-bottom: 1.5rem;
}

.heroText p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  max-width: 550px;
}

.heroText .btn {
  /* Specificity for button within hero text */
  margin-right: 15px;
  margin-bottom: 10px; /* For stacking on mobile */
}

.heroImage {
  flex: 1;
  max-width: 450px; /* Control SVG size */
  margin: 0 auto;
}

.heroImage svg {
  width: 100%;
  height: auto;
}
.main {
  padding: 100px 0 60px 0;
  min-height: 80vh;
  /* display: flex;
  align-items: center; */
}

/* --- Features Section --- */
.features {
  /* Inherits section styles */
}
.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 3rem;
}

.featureCard {
  background-color: #ffffff; /* --white-color */
  padding: 30px;
  border-radius: 8px; /* --border-radius */
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* --box-shadow */
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.featureIcon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px auto;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.featureIcon svg {
  width: 35px; /* Adjust icon size */
  height: 35px;
}
/* Target h3 inside featureCard */
.featureCard h3 {
  margin-bottom: 10px;
  color: #0077cc; /* --primary-color */
}

/* --- How It Works Section --- */
.howItWorks {
  /* Inherits section styles */
}
.stepsContainer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align cards at the top */
  gap: 30px;
  margin-top: 3rem;
  position: relative; /* For connector positioning */
}

.stepCard {
  flex: 1;
  background-color: #ffffff; /* --white-color */
  padding: 30px;
  border-radius: 8px; /* --border-radius */
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* --box-shadow */
  position: relative;
  z-index: 1; /* Keep cards above connector */
}

.stepNumber {
  width: 40px;
  height: 40px;
  background-color: #00b3b3; /* --secondary-color */
  color: #ffffff; /* --white-color */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  margin: -50px auto 20px auto; /* Pulls number up */
  border: 4px solid #f4f4f4; /* --light-color; Creates space */
}

.stepCard h3 {
  color: #0077cc; /* --primary-color */
  margin-bottom: 10px;
}

/* Desktop connector line using pseudo-element */
@media (min-width: 768px) {
  .stepsContainer::before {
    content: "";
    position: absolute;
    top: 60px; /* Adjust based on card height/padding */
    left: 5%;
    right: 5%;
    height: 3px;
    background: linear-gradient(
      to right,
      transparent,
      #00b3b3,
      transparent
    ); /* --secondary-color */
    opacity: 0.3;
    z-index: 0;
  }
}

/* --- Testimonials Section --- */
.testimonials {
  /* Inherits section styles */
}
.testimonialGrid {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(300px, 1fr)
  ); /* Responsive grid */
  gap: 30px;
  margin-top: 3rem;
}

.testimonialCard {
  background-color: #ffffff; /* --white-color */
  padding: 30px;
  border-radius: 8px; /* --border-radius */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* --box-shadow */
  border-left: 5px solid #00b3b3; /* --secondary-color; Accent border */
  display: flex;
  flex-direction: column; /* Stack elements vertically */
}

.testimonialText {
  font-style: italic;
  color: #333333; /* --dark-color */
  margin-bottom: 1.5rem;
  flex-grow: 1; /* Allow text to take available space */
  position: relative;
  padding-left: 25px; /* Space for quote icon */
}

.testimonialText::before {
  content: "“"; /* Opening quote */
  position: absolute;
  left: 0;
  top: -10px;
  font-size: 3rem;
  color: #00b3b3; /* --secondary-color */
  opacity: 0.3;
  font-family: Georgia, serif; /* Serif for quotes */
  line-height: 1;
}

.testimonialAuthor {
  font-weight: 600;
  color: #0077cc; /* --primary-color */
  margin-bottom: 0.25rem; /* Smaller margin */
  margin-top: auto; /* Pushes author to bottom if card heights vary */
}

.testimonialSource {
  font-size: 0.9rem;
  color: #777777; /* --gray-color */
  margin-bottom: 0; /* Remove default p margin */
}

/* --- CTA Section --- */
.cta {
  background: linear-gradient(
    to right,
    #0077cc,
    #00b3b3
  ); /* --primary-color, --secondary-color */
  color: #ffffff; /* --white-color */
  text-align: center;
}

.ctaContent h2 {
  color: #ffffff; /* --white-color */
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.ctaContent p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Specificity for button inside CTA */
.cta .btnPrimary {
  background-color: #ffffff; /* --white-color */
  color: #0077cc; /* --primary-color */
  border-color: #ffffff; /* --white-color */
}
.cta .btnPrimary:hover {
  background-color: #f4f4f4; /* --light-color */
  color: #0077cc; /* --primary-color */
  border-color: #f4f4f4; /* --light-color */
}

/* --- Footer --- */
.footer {
  padding: 60px 0 30px 0;
  /* bgDark class applies background/text colors */
}

.footerContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.footerLogo {
  flex-basis: 30%;
  min-width: 200px; /* Prevent excessive shrinking */
}

.footerLogo .logo {
  /* Specificity */
  color: #ffffff; /* --white-color */
  margin-bottom: 1rem;
  display: block; /* Makes margin work */
}
.footerLogo .logo strong {
  color: #00b3b3; /* --secondary-color */
}
.footerLogo p {
  font-size: 0.9rem;
  /* Inherits color from bgDark */
}

.footerLinks {
  display: flex;
  gap: 40px;
  flex-grow: 1; /* Takes remaining space */
  justify-content: space-around; /* Distribute link sections */
  flex-wrap: wrap; /* Allow link sections to wrap */
}

.footerLinks h4 {
  margin-bottom: 1rem;
  color: #ffffff; /* --white-color */
  font-size: 1.1rem;
}

.footerLinks ul li {
  margin-bottom: 0.5rem;
}

.footerLinks ul li a {
  font-size: 0.95rem;
  /* color: rgba(255, 255, 255, 0.8); Inherits from .bgDark a now */
}
.footerLinks ul li a:hover {
  color: #ffffff; /* --white-color */
  text-decoration: underline;
}

/* --- Responsiveness --- */

/* Tablets and smaller desktops */
@media (max-width: 992px) {
  /* Use wrapper for specificity if needed */
  .landingPageWrapper h1 {
    font-size: 2.5rem;
  }
  .landingPageWrapper h2 {
    font-size: 2rem;
  }

  .heroContent {
    flex-direction: column;
    text-align: center;
  }
  .heroText {
    order: 2; /* Text below image */
    margin-top: 30px;
  }
  .heroText p {
    margin-left: auto;
    margin-right: auto;
  }
  .heroImage {
    order: 1; /* Image on top */
    max-width: 400px;
  }

  .featureGrid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  .stepsContainer {
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }
  .stepCard {
    width: 80%;
    max-width: 400px;
  }
  .stepsContainer::before {
    /* Hide background line on column layout */
    display: none;
  }

  .footerContent {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  .footerLogo {
    margin-bottom: 30px;
    flex-basis: auto; /* Reset basis */
  }
  .footerLinks {
    justify-content: center;
    gap: 30px;
    width: 100%; /* Ensure links take full width */
  }

  /* Adjust language switcher position if needed */
  .languageSwitcher {
    margin-right: 15px; /* Less space */
  }
}

/* Mobile devices */
@media (max-width: 767px) {
  .sectionPadding {
    padding: 60px 0;
  }
  .landingPageWrapper h1 {
    font-size: 2rem;
  }
  .landingPageWrapper h2 {
    font-size: 1.8rem;
  }
  .sectionSubtitle {
    font-size: 1rem;
    margin-bottom: 2.5rem;
  }

  /* Mobile Navigation */
  .navLinks {
    display: none; /* Hide links by default */
    flex-direction: column;
    position: absolute;
    top: 70px; /* Below header */
    left: 0;
    width: 100%;
    background-color: #ffffff; /* --white-color */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #f4f4f4; /* --light-color */
  }
  .navLinks.active {
    /* Use .active from JS state */
    display: flex; /* Show when active */
  }
  .navLinks li {
    margin: 10px 0;
    margin-left: 0; /* Reset margin */
  }
  .navLinks .btn {
    /* Specificity */
    margin-top: 15px;
    display: block; /* Make button easier to tap */
    width: fit-content; /* Size to content */
    margin-left: auto;
    margin-right: auto;
  }
  .menuToggle {
    display: block; /* Show hamburger icon */
    margin-left: 15px; /* Give it space */
  }

  /* Adjust Language Switcher */
  .languageSwitcher {
    /* Keep it visible, adjust margin as needed */
    margin-left: auto; /* Keep pushing right */
    margin-right: 5px; /* Less space before toggle */
  }
  /* Ensure nav container allows space for logo, switcher, and toggle */
  .nav {
    /* Ensure items don't excessively shrink */
    flex-wrap: nowrap;
  }

  .hero {
    padding: 60px 0 40px 0;
    min-height: auto; /* Adjust height */
  }
  .heroText p {
    font-size: 1.1rem;
  }
  .heroImage {
    max-width: 300px;
  }

  .featureGrid {
    grid-template-columns: 1fr; /* Stack features */
    gap: 20px;
  }

  .stepCard {
    width: 90%;
  }
  .stepNumber {
    margin: -45px auto 15px auto;
  }

  .ctaContent h2 {
    font-size: 2rem;
  }
  .ctaContent p {
    font-size: 1.1rem;
  }

  .footerLinks {
    flex-direction: column;
    gap: 25px;
    align-items: center;
  }
  .footerLinks div {
    /* Target the div containers for link groups */
    width: 100%;
  }

  /* Adjust testimonial responsiveness */
  .testimonialGrid {
    grid-template-columns: 1fr; /* Stack testimonials on mobile */
    gap: 20px;
  }
  .testimonialCard {
    padding: 25px;
  }
  .testimonialText {
    margin-bottom: 1rem;
  }
}
