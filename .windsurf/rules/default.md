---
trigger: always_on
---

use pnpm instead of npm
use modules versions from package.json
DO NOT REMOVE ANY COMMENTED OUT CODE AND CONSOLE LOGS!!
if possible use for...of instead of forEach
use shadcn components and tailwindcss classes for styling
installed shadcn ui components are located in /src/components/_shadcn folder
if required install new shadcn components using pnpm dlx shadcn@latest add <component-name>