// Load workbox from CDN
self.importScripts('https://storage.googleapis.com/workbox-cdn/releases/6.5.4/workbox-sw.js');

// Initialize workbox
workbox.setConfig({
  debug: false,
});

// Load specific workbox modules
workbox.loadModule('workbox-background-sync');
workbox.loadModule('workbox-routing');

// Get the required workbox components
const { Queue } = workbox.backgroundSync;
const { registerRoute } = workbox.routing;

const uploadQueue = new Queue("fileUploadQueue");

registerRoute(
  ({ request, url }) => request.method === "POST" && url.pathname.startsWith("/api/upload"),
  async ({ event, request }) => {
    try {
      return await fetch(request); // online – just pass through
    } catch {
      await uploadQueue.pushRequest({ request }); // offline – queue it
      broadcastQueueLength();
      return new Response(JSON.stringify({ queued: true }), {
        status: 202,
        headers: { "Content-Type": "application/json" },
      });
    }
  },
  "POST",
);

self.addEventListener("sync", (event) => {
  if (event.tag === "fileUploadQueue-sync") {
    event.waitUntil(uploadQueue.replayRequests().then(broadcastQueueLength));
  }
});

self.addEventListener("message", async (event) => {
  if (event.data === "GET_QUEUE_LENGTH") {
    const len = (await uploadQueue.getAll()).length;
    event.source?.postMessage({ type: "QUEUE_LENGTH", length: len });
  }
});

async function broadcastQueueLength() {
  const len = (await uploadQueue.getAll()).length;
  const clients = await self.clients.matchAll({ type: "window" });
  clients.forEach((c) => c.postMessage({ type: "QUEUE_LENGTH", length: len }));
}
