const CACHE_NAME = "pwa-file-upload-v1";
const urlsToCache = ["/", "/index.html"];

// Cache assets on install
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll(urlsToCache);
    }),
  );
});

// Serve cached assets
self.addEventListener("fetch", (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      return response || fetch(event.request);
    }),
  );
});

// Handle background sync for file uploads
self.addEventListener("sync", (event) => {
  if (event.tag === "upload-files") {
    event.waitUntil(syncFiles());
  }
});

async function syncFiles() {
  const db = new Dexie("FileUploadDB");
  db.version(1).stores({
    pendingUploads: "++id,file,name",
  });

  const pendingFiles = await db.pendingUploads.toArray();
  if (pendingFiles.length === 0) return;

  const formData = new FormData();
  pendingFiles.forEach(({ file }) => formData.append("files", file));

  try {
    const response = await fetch("https://your-server-endpoint/upload", {
      method: "POST",
      body: formData,
    });
    if (response.ok) {
      await db.pendingUploads.clear();
    }
  } catch (error) {
    console.error("Sync failed:", error);
  }
}
