{"name": "dosiero", "type": "module", "version": "0.0.1", "scripts": {"dev": "openapi-ts && astro dev", "build": "astro build", "build:dev": "astro build --mode dev", "build:demo": "astro build --mode demo", "build:prod": "astro build --mode prod", "preview": "astro preview --port 4321", "astro": "astro", "types": "openapi-ts"}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/react": "^4.3.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.1", "@react-email/render": "^1.1.3", "@redux-devtools/extension": "^3.3.0", "@reduxjs/toolkit": "^2.8.2", "@sentry/browser": "^9.33.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-form": "^1.12.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-router": "^1.123.0", "@tanstack/react-table": "^8.21.3", "@tanstack/router-plugin": "^1.123.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.11.0", "axios": "^1.10.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "framer-motion": "^12.19.2", "html-react-parser": "^5.2.5", "i18next": "^25.3.0", "i18next-browser-languagedetector": "^8.2.0", "idb-keyval": "^6.2.2", "keycloak-js": "^26.2.0", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "oidc-client-ts": "^3.3.0", "plop": "^4.0.1", "react": "^19.1.0", "react-compiler-runtime": "19.1.0-rc.2", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-email": "^4.0.17", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-imask": "^7.6.1", "react-oidc-context": "^3.3.0", "react-redux": "^9.2.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tanstack-table-search-params": "^0.9.0", "vite": "link:@tanstack/router-plugin/vite", "workbox-background-sync": "^7.3.0", "workbox-core": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-window": "^7.3.0", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@faker-js/faker": "^9.9.0", "@hey-api/openapi-ts": "^0.77.0", "@hookform/devtools": "^4.4.0", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router-devtools": "^1.123.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "class-variance-authority": "^0.7.1", "msw": "^2.10.3", "tw-animate-css": "^1.3.4"}}