import { defineConfig } from "@hey-api/openapi-ts";
// import { createClient } from "@hey-api/client-fetch";

// const client = createClient({
//   baseUrl: "http://127.0.0.1:8005",
// });

export default defineConfig({
  // input: "./openapi.json",
  input: "http://127.0.0.1:8005/openapi.json",
  output: {
    path: "src/api/_client",
  },

  plugins: [
    "@tanstack/react-query",
    "@hey-api/client-fetch",
    "zod"
    // {
    //   dates: true,
    //   name: "@hey-api/transformers",
    // },
    // {
    //   enums: "javascript",
    //   name: "@hey-api/typescript",
    // },
    // {
    //   name: "@hey-api/sdk",
    //   transformer: true,
    // },
  ],

  //   schemas: true,
  //   types: {
  //     enums: "javascript",
  //   },
  //   services: {
  //     asClass: false,
  //     // methodNameBuilder: "(operationId: string) => string";
  //     operationId: false,
  //   },
});

// import { defaultPlugins } from '@hey-api/openapi-ts';

// export default {
//   client: '@hey-api/client-fetch',
//   input: 'path/to/openapi.json',
//   output: 'src/client',
//   plugins: [
//     ...defaultPlugins,
//     '@tanstack/react-query',
//   ],
// };
