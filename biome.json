{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "files": {"experimentalScannerIgnores": ["_docs/**"]}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off"}, "style": {"noNonNullAssertion": "off"}, "correctness": {"noChildrenProp": "off", "noUnusedImports": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120}, "assist": {"actions": {"source": {"organizeImports": {"level": "on", "options": {"groups": ["@/", "@/tailwind", ":NODE:"]}}}}}}