// camelCase: changeFormatToThis
// snakeCase: change_format_to_this
// dashCase/kebabCase: change-format-to-this
// dotCase: change.format.to.this
// pathCase: change/format/to/this
// properCase/pascalCase: ChangeFormatToThis
// lowerCase: change format to this
// sentenceCase: Change format to this,
// constantCase: CHANGE_FORMAT_TO_THIS
// titleCase: Change Format To This

const fs = require("fs");
const path = require("path");

// Helper function to register partials from a directory
function registerPartials(directory, plop) {
  console.log("Registering partials from directory:", directory);

  fs.readdirSync(directory).forEach((file) => {
    if (file.endsWith(".hbs")) {
      const partialName = path.basename(file, ".hbs");
      const partialContent = fs.readFileSync(path.join(directory, file), "utf8");
      plop.setPartial(partialName, partialContent);
      console.log("Registering partial:", partialName, "partialContent", partialContent);
    }
  });
}

module.exports = (plop) => {
  // Register partials for the form generator
  const formFieldsDir = path.join(__dirname, "plop_templates/table_crud/partials");
  registerPartials(formFieldsDir, plop);

  console.log("Registered partials:", plop.getPartialList()); // Debugging

  // Example helper
  plop.setHelper("fieldTemplate", (fieldType) => {
    console.log("Field type received:", fieldType); // Debugging
    const template = `field-${fieldType}`;
    console.log("Looking for template:", template); // Debugging
    return template;
  });

  plop.setHelper("stringifyDefaultValue", (defaultValue, valueType) => {
    if (valueType === "string") {
      return new plop.handlebars.SafeString(`"${String(defaultValue).replace(/"/g, '\\"')}"`);
    }
    if (valueType === "number") {
      return Number(defaultValue);
    }
    if (valueType === "boolean") {
      return defaultValue === "true";
    }
    // If its a date or other type, just return as string,
    return new plop.handlebars.SafeString(`"${String(defaultValue).replace(/"/g, '\\"')}"`);
  });

  plop.setHelper("partialName", (inputType) => {
    // Add any special cases or mappings as needed.
    return `field-${inputType}`; // Default:  field-text, field-checkbox, etc.
  });

  plop.setGenerator("Table CRUD + Route + Table", {
    description: "Create folder output/ModelName with fields.json file",
    prompts: [
      {
        type: "input",
        name: "schemaName",
        message: "Schema name: eg. core",
      },
      {
        type: "input",
        name: "modelName",
        message: "Model name (CAPITALIZED, SINGULAR): eg. Org",
      },
    ],
    actions: (data) => {
      console.log("data:", data);
      // Read the fields.json file
      const fieldsPath = path.join(__dirname, "plop_templates", "table_crud", "output", data.modelName, "fields.json");
      if (!fs.existsSync(fieldsPath)) {
        throw new Error(`fields.json not found in plop_templates/output/${data.modelName}`);
      }

      data.fields = JSON.parse(fs.readFileSync(fieldsPath, "utf8"));

      return [
        {
          type: "add",
          path: `plop_templates/table_crud/output/${data.modelName}/${data.modelName}Form.tsx`,
          templateFile: "plop_templates/table_crud/form.tsx.hbs",
          data: {
            // Pass data to the partial
            modelName: data.modelName,
            fields: data.fields,
          },
        },
        {
          type: "add",
          path: `plop_templates/table_crud/output/${data.modelName}/${data.modelName}Route.tsx`,
          templateFile: "plop_templates/table_crud/route.tsx.hbs",
          data: {
            // Pass data to the partial
            modelName: data.modelName,
            fields: data.fields,
          },
        },
        {
          type: "add",
          path: `plop_templates/table_crud/output/${data.modelName}/use${data.modelName}Data.tsx`,
          templateFile: "plop_templates/table_crud/useData.tsx.hbs",
          data: {
            // Pass data to the partial
            modelName: data.modelName,
            fields: data.fields,
          },
        },
        {
          type: "add",
          path: `plop_templates/table_crud/output/${data.modelName}/${data.modelName}sTable.tsx`,
          templateFile: "plop_templates/table_crud/table.tsx.hbs",
          data: {
            // Pass data to the partial
            modelName: data.modelName,
            fields: data.fields,
          },
        },
        {
          type: "add",
          path: `plop_templates/table_crud/output/${data.modelName}/${data.modelName}sTableBareExpanding.tsx`,
          templateFile: "plop_templates/table_crud/table_bare_expanding.tsx.hbs",
          data: {
            // Pass data to the partial
            modelName: data.modelName,
            fields: data.fields,
          },
        },
        {
          type: "add",
          path: `plop_templates/table_crud/output/${data.modelName}/${data.modelName}sTableBareTree.tsx`,
          templateFile: "plop_templates/table_crud/table_bare_tree.tsx.hbs",
          data: {
            // Pass data to the partial
            modelName: data.modelName,
            fields: data.fields,
          },
        },
      ];
    },
  });

  plop.setGenerator("Standard form template", {
    description: "Create table without relationships",
    prompts: [
      {
        type: "input",
        name: "model_name",
        message: "Model name:eg, Org",
      },
    ],
    actions: [
      // generate form field in templates/output
      {
        type: "add",
        path: "plop_templates/output/{{pascalCase model_name}}Form.tsx",
        templateFile: "plop_templates/form/TemplateForm.tsx.hbs",
      },
    ],
  });

  plop.setHelper("asUrl", (str) => {
    return str.replace(/_/g, "-");
  });

  plop.setHelper("log", (message, obj) => {
    console.log(message, obj);

    return "";
  });
  plop.setHelper("json", (message, obj) => {
    console.log(message, JSON.stringify(obj, null, 2));
    return "";
  });
};
